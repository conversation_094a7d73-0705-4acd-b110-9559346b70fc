import { ref, computed } from "vue";
import InternalService from "@/services/InternalService";
import { loadingController } from "@ionic/vue";

export interface JupasChoiceConditionSet {
  id: string;
  name: string;
  conditions: any;
}

export function useJupasChoiceConditionSets() {
  const sets = ref<JupasChoiceConditionSet[]>([]);
  const selectedSetId = ref<any>(null);
  const loading = ref(false);

  const selectedSet = computed(() =>
    sets.value.find((set) => set.id === selectedSetId.value) || null
  );

  async function fetchSets() {
    loading.value = true;
    try {
      const data = await InternalService.getJUPASConditionSets();
      sets.value = data || [];
      if (!selectedSetId.value && sets.value.length > 0) {
        selectedSetId.value = sets.value[0].id;
      }
    } finally {
      loading.value = false;
    }
  }

  async function addSet(name: string, conditions: any) {
    const loading = await loadingController.create({ });
    await loading.present();
    const newSet = await InternalService.addJUPASConditionSet(name, conditions);
    sets.value.push(newSet);
    selectedSetId.value = newSet.id;
    loading.dismiss();
  }

  async function updateSet(id: string, newConditions: any) {
    const set = sets.value.find(s => s.id === id);
    if (set) {
      set.conditions = newConditions;
      await InternalService.updateJUPASConditionSet(id, set.name, newConditions);
    }
  }

  async function renameSet(id: string, newName: string) {
    const set = sets.value.find(s => s.id === id);
    if (set) {
      set.name = newName;
      await InternalService.updateJUPASConditionSet(id, newName, set.conditions);
    }
  }

  async function removeSet(id: string) {
    await InternalService.deleteJUPASConditionSet(id);
    sets.value = sets.value.filter((set) => set.id !== id);
    if (selectedSetId.value === id) {
      selectedSetId.value = sets.value[0]?.id || null;
    }
  }

  return {
    sets,
    selectedSetId,
    selectedSet,
    loading,
    fetchSets,
    addSet,
    updateSet,
    renameSet,
    removeSet,
  };
}