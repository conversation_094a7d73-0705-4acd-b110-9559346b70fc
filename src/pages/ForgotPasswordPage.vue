<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-title>
          {{ t('ForgotPasswordPage.forgotPassword') }}
        </ion-title>
        <ion-buttons slot="start">
          <ion-back-button default-href="/login"></ion-back-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    <ion-content :fullscreen="true">
      <ion-grid>
        <ion-row class="ion-justify-content-center">
          <ion-col class="ion-text-center"  size="12" size-md="8" size-lg="6" size-xl="4">
            <ion-card v-if="resetPswEmail == ''">
              <ion-card-header>
                <logo-img :withText="true"></logo-img>
              </ion-card-header>
              <ion-card-content>
                <ion-item>
                  <ion-label position="floating">{{ t('ForgotPasswordPage.loginEmail') }}</ion-label>
                  <ion-input inputmode="email" type="text" v-model="loginEmail" @keyup.enter="resetPassword(loginEmail)"></ion-input>
                </ion-item>
                <ion-button class="submit-btn" expand="block" @click="resetPassword(loginEmail)">
                  <ion-icon slot="end" :icon="mail"></ion-icon>
                  {{ t('ForgotPasswordPage.sendResetPasswordEmail') }}
                </ion-button>
              </ion-card-content>
            </ion-card>

            <ion-card v-else>
              <ion-card-header>
                <logo-img :withText="true"></logo-img>
                <ion-card-title>{{ t('ForgotPasswordPage.resetPswEmailSentTo') }} {{ resetPswEmail }}</ion-card-title>
              </ion-card-header>
              <ion-card-content>
                <p>{{ t('ForgotPasswordPage.checkEmailReset') }}</p>
                <ion-button class="submit-btn" expand="block" @click="goToLoginPage()">
                  <ion-icon slot="start" :icon="arrowBack"></ion-icon>
                  {{ t('ForgotPasswordPage.backToLogin') }}
                </ion-button>
              </ion-card-content>
            </ion-card>
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-content>
  </ion-page>
</template>

<script lang="ts">

import { IonPage, IonHeader, IonToolbar, IonTitle, IonContent,
        IonGrid, IonCol, IonRow, IonButtons, IonButton, IonIcon, IonBackButton,
        IonCard, IonCardHeader, IonCardTitle, IonCardContent, IonItem, IonLabel, IonInput,
        loadingController, toastController, } from '@ionic/vue';

// icons
import { mail, arrowBack, } from 'ionicons/icons';
import { ref } from 'vue';

// services
import AuthService from '@/services/AuthService';

import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';

export default {
  name: 'ForgotPasswordPage',
  components: { IonHeader, IonToolbar, IonTitle, IonContent, IonPage,
                IonGrid, IonCol, IonRow, IonButtons, IonButton, IonIcon, IonBackButton,
                IonCard, IonCardHeader, IonCardTitle, IonCardContent, IonItem, IonLabel, IonInput, },
  setup() {
    const loginEmail = ref("");
    const resetPswEmail = ref("");

    // methods
    const { t } = useI18n();
    const router = useRouter();
    const presentToast = async(msg: string, duration = 5000) => {
      const toast = await toastController.create({
        message: msg,
        duration
      });
      toast.present();
    }
    const resetPassword = async(email: string) => {
      if (email) {
        const loading = await loadingController.create({});
        await loading.present();
        try {
          await AuthService.resetPassword(email);
          resetPswEmail.value = email;
        } catch (e) {
          presentToast(e.message); // wrong email or wrong password
        }
        loading.dismiss();
      } else {
        presentToast(t('ForgotPasswordPage.enterLoginEmail'), 3000);
      }
    };

    const goToLoginPage = () => {
      resetPswEmail.value = "";
      router.replace('/login'); // go to forgot password page
    }
    
    return {
      t,

      // icons
      mail, arrowBack,
      
      // variables
      loginEmail, resetPswEmail,

      // methods
      resetPassword, goToLoginPage,
    }
  },
}
</script>

<style scoped>
  .submit-btn {
    margin-top: 20px;
  }
  ion-grid {
    height: 100%;
  }
  ion-row {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
</style>