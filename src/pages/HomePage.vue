<template>
  <ion-page>
    <!-- Page Header -->
    <page-header :selectedView="selectedView">
      <!-- Toggle between views of different roles -->
      <ion-toolbar class="view-switcher-toolbar" style="--min-height: 40px">
        <ion-segment class="view-switcher" mode="ios" v-model="selectedView" scrollable>
          <ion-segment-button value="home" style="--padding-start: 0; --padding-end: 0; min-width: 36px; width: min-content">
            <ion-icon size="small" :icon="home"></ion-icon>
          </ion-segment-button>
          <ion-segment-button value="university-client" v-if="!isSecondaryStudent">
            <ion-label class="ion-text-wrap">University staff</ion-label>
          </ion-segment-button>
          <ion-segment-button value="university-student" v-if="!isSecondaryStudent">
            <ion-label class="ion-text-wrap">University student</ion-label>
          </ion-segment-button>
          <ion-segment-button value="teacher" v-if="!isSecondaryStudent">
            <ion-label class="ion-text-wrap">Teacher / Principal</ion-label>
          </ion-segment-button>
          <ion-segment-button value="secondary-school-student" :disabled="user.id && !user.teacher && !user.isSecondaryStudent && !user.isAdmin">
            <ion-label class="ion-text-wrap">Secondary student</ion-label>
          </ion-segment-button>
          <ion-segment-button value="fdmt-staff" v-if="isFDMTStaff">
            <ion-label class="ion-text-wrap">FDMT staff</ion-label>
          </ion-segment-button>
          <ion-segment-button value="practitioner" disabled v-if="!isSecondaryStudent">
            <ion-label class="ion-text-wrap">Practitioner</ion-label>
          </ion-segment-button>
        </ion-segment>
      </ion-toolbar>
    </page-header>

    <!-- Page Content -->
    <ion-content style="--padding-bottom: 100px" :fullscreen="true" :scroll-events="true" @ionScroll="handleScroll">
      <!-- Home Banners -->
      <slides :homeBanners="true" v-if="selectedView == 'home'"></slides>

      <ion-grid class="ion-no-padding" fixed>
        <!-- University Client to be verified -->
        <ion-item class="ion-margin-vertical" color="light" lines="full"
                  v-if="user.isUniversityClient && (!user.clientId || user.clientId == 'To be assigned')">
          <ion-icon slot="start" :icon="alertCircle" color="danger"></ion-icon>
          <ion-label class="ion-text-wrap">
            <h3>Thank you for your registration. We will soon verify your identity and unlock below sections for you.</h3>
          </ion-label>
        </ion-item>

        <!--
          Ack Notice (Mainly for students applying for specific events)
          e.g. BusinessWork Band A workshop (acknowledgement / confirmation)
        -->
        <ion-item v-if="ackAppliedEvent" class="ion-margin-vertical" @click="onClickWhatsAppButton()"
                  color="light" lines="full" target="_blank" :href="user.waGroupLink" button detail>
          <ion-icon size="large" slot="start" :icon="checkmarkCircle" color="success" style="padding: 16px"></ion-icon>
          <ion-label class="ion-text-wrap">
            <h2>Thank you for applying for <b>{{ ackAppliedEvent.name }} ({{ ackAppliedEvent.formattedDateTime }})</b>!</h2>
            <p>Please click here to join the WhatsApp group assigned to you. We will keep you updated on the event via WhatsApp.</p>
          </ion-label>
        </ion-item>

        <!--
          Ack Notice for Interested Service (e.g. HealthcareWork), mainly for teacher referral
        -->
        <ion-item v-else-if="ackInterestedService" class="ion-margin-vertical" @click="onClickWhatsAppButton()"
                  color="light" lines="full" target="_blank" :href="user.waGroupLink" button detail>
          <ion-icon size="large" slot="start" :icon="checkmarkCircle" color="success" style="padding: 16px"></ion-icon>
          <ion-label class="ion-text-wrap">
            <h2>Thank you for your interest in <b>{{ ackInterestedService?.name }}</b>!</h2>
            <p>Please click here to join the WhatsApp group assigned to you and receive our support.</p>
          </ion-label>
        </ion-item>

        <!-- User WhatsApp Group Notice (for university staff no group will be assigned to them) -->
        <ion-item class="ion-margin-vertical" v-else-if="user.waGroupLink && user.userInWaGroup != true" @click="onClickWhatsAppButton()"
                  color="light" lines="full" target="_blank" :href="user.waGroupLink" button detail>
          <ion-icon slot="start" :icon="alertCircle" color="danger"></ion-icon>
          <ion-label class="ion-text-wrap">
            <h3>To continue using this app, please click here to join the WhatsApp group assigned to you.</h3>
          </ion-label>
        </ion-item>

        <div :class="{ 'disabled': (user.waGroupLink && user.userInWaGroup != true) }">

          <div class="spin" v-if="forceReload || loadingData">
            <ion-spinner></ion-spinner>
          </div>

          <div v-else>

            <!-- Public view -->
            <div v-if="selectedView == 'home'">
              <!-- Corporate video 
              <iframe class="responsive-embed" style="width: 100%; height: 500px" :src="`https://www.youtube.com/embed/VqOOa0MEJ-E`" frameborder="0" allowfullscreen></iframe>-->

              <!-- Our organzations -->
              <!-- https://docs.google.com/presentation/d/1t4lCl8TlPRqwNE-N-WDmjyilyxLOGQfHcHQ1iYENNAU/edit#slide=id.g2aafe430c0e_1_46 -->
              <iframe src="https://docs.google.com/presentation/d/e/2PACX-1vSlzoW3oyvKSKNtMs-IR4NYJW47p0UuDOYgULRhfPrAkYKdmgB_urEX8ENeQlgxh-G0_D32N0E9f8Sf/embed?start=false&loop=false&delayms=3000&rm=demo&chrome=false"
                      frameborder="0" style="width: 100%; height: calc(min(100vw, 1000px) * 0.8)" allowfullscreen="true" mozallowfullscreen="true" webkitallowfullscreen="true"></iframe>

              <!-- Parallax Banner 
              <div class="parallax-container">
                <div class="parallax-bg"></div>
              </div>-->

              <!-- Contact us -->
              <div style="background-image: url('https://fdmt.hk/imgproxy.php?url=https%3A%2F%2Fdocs.google.com%2Fpresentation%2Fd%2F1VLGNeYi1mHB-EZGbUxQG0Bz_vwPQ4dpDgn65HJkUyzo%2Fexport%2Fjpeg%3Fid%3D1VLGNeYi1mHB-EZGbUxQG0Bz_vwPQ4dpDgn65HJkUyzo%26pageid%3Dg2b9bc6ceeec_0_29')"
                    class="half-image"></div>
              
              <!-- Google Map -->
              <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3691.9983305383935!2d114.18265047604451!3d22.278053079702527!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x34040141c65fa42b%3A0x2402f0da20af3149!2sFDMT%20Consulting!5e0!3m2!1sen!2shk!4v1731731923484!5m2!1sen!2shk"
                      width="100%" style="border:0; height: 240px" allowfullscreen="" loading="lazy"></iframe>

              <p id="copyright" class="ion-text-center" style="color: #fff">
                <small>
                  Copyright © 2002 - 2024 Fundamentum Limited. All Rights Reserved | 
                  <a href="https://www.fdmt.hk/legal" target="_blank">Privacy statement</a> | <a href="https://www.fdmt.hk/legal" target="_blank">Site terms</a> | <a href="https://www.fdmt.hk/legal" target="_blank">Disclaimer</a>
                </small>
              </p>
            </div>

            <!-- Client view (university staff / professors) -->
            <div v-if="selectedView == 'university-client'">
              <div v-if="!user.id">
                <ion-button class="no-text-transform" expand="block" @click="openLoginModal('university-client')">
                  Enter (for registered clients)<br/>or Register (for new clients)
                </ion-button>
              </div>

              <div class="blue-bg-text ion-text-center" v-if="!user.isUniversityClient">
              <iframe src="https://docs.google.com/presentation/d/e/2PACX-1vRCpCEuSU4lh6A-ydSUE6-CpaUoGJhoKJrkK-Zg0vTv3WtgcCo7P-ORRrjLMuTcCP8Z326oCZr1oosN/embed?start=false&loop=true&delayms=3000&rm=demo&chrome=false"
                      frameborder="0" style="width: 100%; height: calc(min(100vw, 1000px) * 0.8)" allowfullscreen="true" mozallowfullscreen="true" webkitallowfullscreen="true"></iframe>
              </div>

              <div v-if="user.isUniversityClient">
                <ClientActionCardList></ClientActionCardList>
              </div>
            </div>

            <!-- University Student view -->
            <div v-if="selectedView == 'university-student'">
              <div v-if="user.isUniversityClient || user.isUniversityStudent">
                <UniStudentActionCardList></UniStudentActionCardList>
              </div>

              <div class="blue-bg-text" v-else>
                <!-- Bluebird Internship -->
                <ion-button class="ion-margin-bottom no-text-transform ion-text-wrap" expand="block" href="https://bluebird.fdmt.hk" target="_blank">
                  Bluebird: Internship (employed by FDMT) for UG year 1 & year 2, MPhil & PhD students
                </ion-button>

                <!-- Client department students -->
                <ion-button class="no-text-transform ion-text-wrap" expand="block" @click="openLoginModal('university-student')" v-if="!user.id">
                  AchieveBot: AI-powered job hunting support for students from our client department
                </ion-button>
              </div>
            </div>

            <!-- Teachers / principals' view -->
            <div v-if="selectedView == 'teacher'">
              <div v-if="user.teacher">
                <TeacherActionCardList :prefilledViewType="prefilledViewType"></TeacherActionCardList>
              </div>
              <div v-else>
                <ion-button class="no-text-transform" expand="block" @click="openLoginModal('teacher')" v-if="!user.id">
                  Enter (for registered teachers)<br/>or Register (for new teachers)
                </ion-button>

                <!-- https://docs.google.com/presentation/d/1RPg9c6Dseq9lbisOnpStyrp9JKGbmYmilB95uI_kTL4/edit#slide=id.p -->
                <iframe src="https://docs.google.com/presentation/d/e/2PACX-1vRYQdqNzqkYi6HV4IJejx3WFNLrVdUAFqtmoXww_DNvwIFEtOimo_ly4vYIzqqfB-ebaph8BS0DMuD_/embed?start=false&loop=false&delayms=3000"
                        frameborder="0" style="width: 100%; height: calc(min(100vw, 1000px) * 0.8)" allowfullscreen="true" mozallowfullscreen="true" webkitallowfullscreen="true"></iframe>
              </div>
            </div>

            <!-- Student's view -->
            <div v-if="selectedView == 'secondary-school-student'">
              <!-- Client department students -->
              <ion-button class="no-text-transform ion-text-wrap" expand="block" @click="openLoginModal('secondary-school-student')" v-if="!user.id">
                Enter (for registered students)<br />or Register (for new students)
              </ion-button>
              
              <div v-if="user.teacher || user.isSecondaryStudent">
                <SecStudentActionCardList></SecStudentActionCardList>
              </div>
            </div>

            <!-- FDMT staff view (mega student/teacher list) -->
            <div v-if="selectedView == 'fdmt-staff' && isFDMTStaff">
              <FDMTStaffActionCardList></FDMTStaffActionCardList>
            </div>
          </div>
        </div>
      </ion-grid>
    </ion-content>
  </ion-page>
</template>

<script lang="ts">
import { computed, reactive, ref, watch } from 'vue';

// icons
import { personCircle, compass, arrowForward, navigate, settingsOutline, checkmark, arrowBack,
        add, close, checkmarkCircle, alertCircle, pencil, peopleOutline, qrCodeOutline,
        createOutline, calendarOutline, calendarClearOutline, play, home, } from 'ionicons/icons';

// components
import { IonPage, IonToolbar, IonContent, IonModal, IonHeader, IonTitle,
        IonGrid, IonCol, IonRow, IonSpinner, IonNote, IonSegment, IonSegmentButton, IonLabel,
        IonButtons, IonButton, IonIcon, IonBadge, IonChip, IonList, IonItem,
        onIonViewDidEnter,
        onIonViewWillLeave,
        onIonViewDidLeave, } from '@ionic/vue';
import Slides from '@/components/shared/Slides.vue';
import LoginPage from '@/pages/LoginPage.vue';

// University
import ClientActionCardList from '@/components/client/ClientActionCardList.vue';
import UniStudentActionCardList from '@/components/university/UniStudentActionCardList.vue';

// Secondary School
import SecStudentActionCardList from '@/components/secondary/SecStudentActionCardList.vue';
import TeacherActionCardList from '@/components/teacher/TeacherActionCardList.vue';

// FDMT Internal Staff
import FDMTStaffActionCardList from '@/components/FDMTStaffActionCardList.vue';

// composables
import { useI18n } from 'vue-i18n';
import { useStore } from '@/store';
import { utils } from '@/composables/utils';
import { useRoute } from 'vue-router';
import config from '@/config';

export default {
  name: 'HomePage',
  components: { IonPage, IonToolbar, IonContent, IonModal, IonHeader, IonTitle,
                IonGrid, IonCol, IonRow, IonSpinner, IonNote, IonSegment, IonSegmentButton, IonLabel,
                IonButtons, IonButton, IonIcon, IonBadge, IonChip, IonList, IonItem,
                TeacherActionCardList, ClientActionCardList, SecStudentActionCardList, UniStudentActionCardList,
                FDMTStaffActionCardList, Slides, },
  setup() {
    // methods
    const { t } = useI18n();
    const store = useStore();
    const { openImageModal, openModal, getProxyImgLink, getQRCodeUrl, copyText, getIntakeYearOfDate, getCurrentIntakeYear, openBookingModal, } = utils();

    // state variables
    const forceReload = ref(false);
    const loadingData = computed(() => store.state.loadingUser || store.state.loadingData);
    const loadingPortalData = computed(() => store.state.loadingUser || store.state.loadingData || store.state.loadingPortalData);
    const settings = computed(() => store.state.settings);
    const user = computed(() => store.state.user);
    const selectedView = ref<any>("");
    const uniStudentSelectedView = ref<any>("internship");

    // Route params
    const route = useRoute();
    const { view: prefilledView, viewType: prefilledViewType } = route.query;

    // Registration Ack
    const ackAppliedEvent = computed(() => store.state.ackAppliedEvent);
    const ackInterestedService = computed(() => store.state.ackInterestedService);

    /**
     * INIT
     */
    const getInitialUserView = () => {
      const { teacher, isSecondaryStudent, isUniversityStudent, isUniversityClient, isEmployer, } = user.value;
      if (isUniversityStudent) return 'university-student';
      if (isSecondaryStudent) return 'secondary-school-student';
      if (isUniversityClient) return 'university-client';
      if (isEmployer) return 'employer';
      if (teacher) return 'teacher';
      return prefilledView || 'home' // visitor
    }
    const checkAddBlueBg = () => {
      if (selectedView.value == 'home' || !user.value.id) document.body.classList.add('blue-bg');
      else document.body.classList.remove('blue-bg');
    }
    onIonViewDidEnter(() => {
      forceReload.value = true;
      setTimeout(() => forceReload.value = false, 200);

      selectedView.value = getInitialUserView();
      checkAddBlueBg();
    });

    // Set initial view
    watch(user, () => {
      selectedView.value = getInitialUserView();
      checkAddBlueBg();
    });
    watch(selectedView, () => {
      checkAddBlueBg();

      // Switch role for admin users
      //if (user.value.isAdmin && ['teacher', 'secondary-school-student', 'university-student', 'university-client'].includes(selectedView.value)) {
      if (user.value.isAdmin) {
        let baseParams = { teacher: null, isSecondaryStudent: false, isUniversityStudent: false, isUniversityClient: false };
        if (selectedView.value == 'teacher') {
          store.commit('updateUser', { ...baseParams, teacher: store.state.teacher }); // Teacher view
          store.dispatch('getSchoolUsers', user.value.schoolId); // retrieve school users for teacher role
        } else {
          switch (selectedView.value) {
            case 'secondary-school-student':
              baseParams = { ...baseParams, isSecondaryStudent: true }; // Student view
              break;
            case 'university-student':
              baseParams = { ...baseParams, isUniversityStudent: true };
              break;
            case 'university-client':
              baseParams = { ...baseParams, isUniversityClient: true };
              store.dispatch('getProgramUsers', user.value.programId); // TBC: can browse list of university students
              break;
          }
          store.commit('updateUser', baseParams);
        }
        localStorage.setItem(config.sessionAppRoleField, selectedView.value);
      }
    })

    const handleScroll = (ev) => {
      const banner: any = document.querySelector('.parallax-bg');
      if (banner) {
        const scrollY = ev.detail.scrollTop;
        //banner.style.transform = `scale(1.5) translateY(${rate}px)`;
        const rate = scrollY * 0.1; // Adjust speed by changing this value
        banner.style.transform = `translate3d(0, ${rate}px, 0)`
      }
    }

    return {
      // icons
      personCircle, compass, arrowForward, navigate, settingsOutline, checkmark, arrowBack,
      add, close, checkmarkCircle, alertCircle, pencil, peopleOutline, qrCodeOutline,
      createOutline, calendarOutline, calendarClearOutline, play, home,

      // variables
      selectedView, uniStudentSelectedView, // Toggle view
      forceReload, loadingData, loadingPortalData,
      user, settings,
      ackAppliedEvent, ackInterestedService,
      prefilledViewType,

      // methods
      t, getProxyImgLink, openImageModal, getQRCodeUrl, copyText,
      handleScroll,

      onClickWhatsAppButton: () => {
        store.dispatch('setUserJoinedWAGroup');
        user.value.userInWaGroup = true;
      },

      openLoginModal: (prefilledRole) => {
        openModal(LoginPage, { prefilledRole }, "login-modal");
      },

      // Special tab conditions
      isSecondaryStudent: computed(() => user.value.isSecondaryStudent && !user.value.isAdmin), // hide other tabs for secondary students
      isFDMTStaff: computed(() => user.value.isAdmin), // for mega student list
    }
  }
}
</script>

<style scoped>
  ion-icon[slot="start"] {
    margin: 2px;
  }
  ion-item::part(native) {
    padding-inline-start: 0;
  }
  .view-switcher {
    grid-template-columns: auto 1fr;
  }
  .view-switcher ion-label {
    font-size: 0.8em;
  }
  #copyright a {
    color: #fff;
  }
  .parallax-container {
    width: 100vw;
    height: 40vh;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    overflow: hidden;
  }
  .parallax-bg {
    width: 100%;
    position: absolute;
    top: -20vh;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('../assets/website/banner.jpeg'); /* Replace with your image */
    background-position: center;
    /*background-size: cover;*/
  }
  @media only screen and (min-width: 768px) {
    .view-switcher ion-label {
      font-size: 1em;
    }
    .parallax-container {
      height: 80vh;
    }
    .parallax-bg {
      top: -40vh;
    }
  }
</style>