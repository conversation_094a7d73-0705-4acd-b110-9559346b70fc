<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-title>{{ t('UserProfilePage.termsAndConditions') }}</ion-title>
        <ion-buttons slot="start">
          <ion-back-button default-href="/profile"></ion-back-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    <ion-content :fullscreen="true">
      <!-- Data loaded -->
      <ion-grid fixed>
        <div class="ion-padding" v-html="settings.appTnc"></div>
      </ion-grid>
    </ion-content>
  </ion-page>
</template>

<script lang="ts">
// Vue reactivity
import { computed } from 'vue';

// components
import { IonPage, IonGrid, IonHeader, IonToolbar, IonTitle, IonContent,
        IonSpinner, IonList, IonButtons, IonBackButton, } from '@ionic/vue';

import { useI18n } from 'vue-i18n';
import { useStore } from '@/store';

export default {
  name: 'TermsAndConditionsPage',
  components: { IonPage, IonGrid, IonHeader, IonToolbar, IonTitle, IonContent,
                IonSpinner, IonList, IonButtons, IonBackButton, },
  setup() {
    const store = useStore();
    const settings = computed(() => store.state.settings);

    // 1. declare state variables (ref to make them reactive)
    //const loading = computed(() => store.state.loadingPosts);

    // methods or filters
    const { t } = useI18n();

    // 3. return variables & methods to be used in template HTML
    return {
      t,
      settings,
    }
  }
}
</script>