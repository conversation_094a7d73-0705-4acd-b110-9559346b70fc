<template>
  <ion-page>
    <ion-header>
      <ion-grid fixed>
        <ion-toolbar>
          <ion-title>{{ t('UserProfilePage.feedback') }}</ion-title>
          <ion-buttons slot="start">
            <ion-back-button mode="ios" default-href="/profile"></ion-back-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-grid>
    </ion-header>
    <ion-content :fullscreen="true">
      <ion-grid fixed>
        <ion-list>
          <ion-list-header color="primary">
            {{ t('FeedbackPage.contactInfo') }}
          </ion-list-header>
          <ion-item>
            <ion-label position="stacked">{{ t('FeedbackPage.nickname') }}</ion-label>
            <ion-input :placeholder="t('FeedbackPage.enterNickname')" v-model="name"></ion-input>
          </ion-item>
          <ion-item>
            <ion-label position="stacked">{{ t('FeedbackPage.email') }}</ion-label>
            <ion-input :placeholder="t('FeedbackPage.enterEmail')" type="email" v-model="email"></ion-input>
          </ion-item>
        </ion-list>

        <ion-list>
          <ion-list-header color="primary">
            {{ t('FeedbackPage.feedback') }}
          </ion-list-header>
          <ion-item>
            <ion-label position="stacked">{{ t('FeedbackPage.feedback') }}</ion-label>
            <ion-textarea :placeholder="t('FeedbackPage.enterFeedback')" v-model="feedback"></ion-textarea>
          </ion-item>
          <ion-item>
            <ion-label position="stacked">{{ t('photo') }}</ion-label>
            <ion-input type="file" accept="image/*" @change="onImageFileChange($event)"></ion-input>
            <ion-buttons slot="end" v-if="photo">
              <ion-button slot="icon-only" @click="clearUploadedImage()"><ion-icon :icon="close"></ion-icon></ion-button>
            </ion-buttons>
          </ion-item>
          <img v-if="photo" :src="photo" style="display: block; margin: auto" />
        </ion-list>

        <ion-button class="submit-btn" expand="block" @click="submitFeedback()">
          {{ t('submit') }}
        </ion-button>
      </ion-grid>
    </ion-content>
  </ion-page>
</template>

<script lang="ts">

// icons
import { close } from 'ionicons/icons';

// components
import { IonPage, IonHeader, IonToolbar, IonTitle, IonContent,
        IonGrid, IonRow, IonButtons, IonButton, IonIcon, IonBackButton,
        IonItem, IonLabel, IonList, IonListHeader,
        IonInput, IonTextarea,
        loadingController, toastController } from '@ionic/vue';

import { computed, ref } from 'vue';

// services
import CommonService from '@/services/CommonService';

import { useI18n } from 'vue-i18n';
import { useStore } from '@/store';
import { useRoute } from 'vue-router';

export default {
  name: 'FeedbackPage',
  components: { IonPage, IonHeader, IonToolbar, IonTitle, IonContent,
                IonGrid, IonRow, IonButtons, IonButton, IonIcon, IonBackButton,
                IonItem, IonLabel, IonList, IonListHeader,
                IonInput, IonTextarea, },
  setup() {
    // state variables
    const store = useStore();

    const user = computed(() => store.state.user);
    const name = ref(user.value.fullName || "");
    const email = ref(user.value.email || "");
    const feedback = ref("");
    const photo = ref<any>("");
    const photoFile = ref<any>(null);

    const route = useRoute();

    // methods
    const { t } = useI18n();
    const submitFeedback = async () => {
      const loading = await loadingController.create({});
      await loading.present();
      const res = await CommonService.createNewFeedback(name.value, email.value, feedback.value, photoFile.value);
      loading.dismiss();
      feedback.value = "";
      const toast = await toastController.create({
        message: t('FeedbackPage.feedbackSubmitted'),
        duration: 5000,
        position: 'top',
      });
      toast.present();
    }

    const onImageFileChange = (e: any) => {
      if (e.target.files && e.target.files[0]) {
        photoFile.value = e.target.files[0];
        const reader = new FileReader();
        reader.onload = (e: any) => {
          photo.value = e.target.result;
        }
        reader.readAsDataURL(photoFile.value); // convert to base64 string and preview it
        e.srcElement.value = null;
      }
    }
    const clearUploadedImage = () => {
      photo.value = "";
      photoFile.value = null;
    }
    
    return {
      // icons
      close,
      
      // variables
      name, email, feedback, photo, photoFile,

      // methods
      t, submitFeedback, onImageFileChange, clearUploadedImage,
    }
  },
}
</script>

<style scoped>
  .submit-btn {
    margin-top: 20px;
  }
</style>