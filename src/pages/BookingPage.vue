<template>
  <ion-page>
    <ion-header>
      <ion-grid fixed>
        <ion-toolbar>
          <ion-buttons slot="start" v-if="isModal">
            <!-- Mainly for jobEX online consultation -->
            <ion-button slot="icon-only" @click="closeModal()"><ion-icon :icon="arrowBack"></ion-icon></ion-button>
          </ion-buttons>

          <ion-title><ion-label class="ion-text-wrap">Book an appointment</ion-label></ion-title>

          <ion-title slot="end" class="ion-no-padding ion-text-right" style="flex: initial; padding-right: 3px">
            <logo-img></logo-img>
          </ion-title>
        </ion-toolbar>
      </ion-grid>
    </ion-header>
    <ion-content :fullscreen="true">
      <!-- Loading Data -->
      <div class="spin" v-if="loading">
        <ion-spinner></ion-spinner>
      </div>

      <ion-grid fixed v-else>
        <form id="booking-form" @submit.prevent="submitBooking()" v-if="section == 1">
          <!-- Booking Items -->
          <div>
            <ion-select fill="outline" label="Please choose*" label-placement="floating" interface="popover" v-model="booking.bookingItemId"
                        :disabled="bookingItemId != null">
              <ion-select-option v-for="item in bookingItems" :key="item.id" :value="item.id">
                [{{ item.durationMinutes }} mins] {{ item.title }}
              </ion-select-option>
            </ion-select>
            <input class="hidden-input"  type="text" v-model="booking.bookingItemId" required />
          </div>

          <div v-show="booking.bookingItemId">
            <!-- [AchieveJUPAS only] 1 date & 2 time -->
            <div v-if="isBookingAchieveJUPASBriefing">
              <!-- Target Date (filtered range) -->
              <div class="bottom-margin">
                <ion-input label="Target date*" label-placement="floating" id="targetDate" fill="outline" type="text" v-model="booking.targetDate" required></ion-input>
                <ion-popover trigger="targetDate" size="auto" :keep-contents-mounted="true">
                  <ion-datetime mode="ios" presentation="date" v-model="booking.targetDate" :showDefaultButtons="true" :min="formatDate(selectedBookingItem.startTime ? new Date(selectedBookingItem.startTime) : new Date(), 'YYYY-MM-DD')"
                                :is-date-enabled="isDateEnabled" @ionChange="formatTargetDate"></ion-datetime>
                </ion-popover>
              </div>

              <!-- Target Time 1 -->
              <ion-row class="bottom-margin">
                <ion-col size="6" style="padding-right: 16px"><ion-input label="Start (student briefing)*" label-placement="floating" fill="outline" type="time" v-model="booking.targetStartTime1" required></ion-input></ion-col>
                <ion-col size="6"><ion-input label="End (student briefing)*" label-placement="floating" fill="outline" type="time" v-model="booking.targetEndTime1" required></ion-input></ion-col>
              </ion-row>

              <!-- Target Time 2 -->
              <ion-row class="bottom-margin">
                <ion-col size="6" style="padding-right: 16px"><ion-input label="Start (teacher briefing)*" label-placement="floating" fill="outline" type="time" v-model="booking.targetStartTime2" required></ion-input></ion-col>
                <ion-col size="6"><ion-input label="End (teacher briefing)*" label-placement="floating" fill="outline" type="time" v-model="booking.targetEndTime2" required></ion-input></ion-col>
              </ion-row>
            </div>

            <!-- Booking Slots -->
            <div v-else-if="filteredBookingSlots().length > 0">
              <ion-select fill="outline" label="Select a time (HKT GMT+8)*" label-placement="floating" interface="popover" v-model="booking.bookingSlotId">
                <ion-select-option v-for="slot in filteredBookingSlots()" :key="slot.id" :value="slot.id">
                  {{ formatSlotTime(slot) }}
                </ion-select-option>
              </ion-select>
              <input class="hidden-input" type="text" v-model="booking.bookingSlotId" required />
            </div>

            <!-- Your name -->
            <ion-input fill="outline" label="Your full name*" label-placement="floating"
                        v-model="booking.fullName" enterkeyhint="next" required></ion-input>

            <!-- Preferred name -->
            <ion-input fill="outline" label="Your preferred name" label-placement="floating"
                        v-model="booking.preferredName" enterkeyhint="next"></ion-input>

            <!-- Email Address -->
            <ion-input fill="outline" label="Your email*" label-placement="floating"
                      inputmode="email" type="email" v-model="booking.email" enterkeyhint="next" required></ion-input>

            <!-- Mobile Number -->
            <ion-input fill="outline" label="Your HK mobile number*" label-placement="floating"
                      inputmode="numeric" v-model="booking.phone" maxlength="8" required
                      helper-text="We may send you updates about the appointment via WhatsApp."></ion-input>

            <!-- Teacher Only -->
            <div v-if="isTeacher">
              <ion-input fill="outline" :label="t('school')" label-placement="floating" type="text"
                          v-model="booking.schoolName" :clearInput="true" @click="openSchoolSelectModal()" required></ion-input>
              <ion-input fill="outline" label="Your role(s) in the school" label-placement="floating"
                          v-model="booking.schoolRoles"></ion-input>
            </div>

            <div v-else>
              <!-- Year of Study -->
              <ion-select fill="outline" label="Year of study*" label-placement="floating" interface="popover" v-model="booking.yearOfStudy">
                <ion-select-option v-for="year in OPTIONS.yearOfStudy" :key="year" :value="year">
                  {{ year }}
                </ion-select-option>
              </ion-select>
            </div>

            <!-- Remarks -->
            <ion-textarea v-model="booking.remarks" label-placement="floating" label="Remarks" placeholder="Optional"
                          style="margin-top: 5px" fill="outline" :rows="3"></ion-textarea>

            <ion-button expand="block" type="submit" shape="round" :disabled="!booking.targetDate || !booking.targetStartTime1 || !booking.targetEndTime1" v-if="isBookingAchieveJUPASBriefing">
              Submit
            </ion-button>
            <ion-button expand="block" type="submit" shape="round" :disabled="filteredBookingSlots().length == 0" v-else>
              {{ filteredBookingSlots().length == 0 ? 'No available timeslots' : 'Book it' }}
            </ion-button>
          </div>
        </form>
        
        <ion-row class="ion-justify-content-center" v-if="section == 2">
          <ion-col size="12">
            <ion-card class="ion-text-center">
              <ion-card-header v-if="booking.status == 'success'">
                <ion-icon color="success" class="icon-xxl" :icon="checkmarkCircle"></ion-icon>
                <ion-card-title>Thank you for your submission.</ion-card-title>
                <ion-card-subtitle>You will soon receive a confirmation message about your booking.</ion-card-subtitle>
              </ion-card-header>
              <ion-card-header v-else>
                <ion-icon color="warning" class="icon-xxl" :icon="alertCircleOutline"></ion-icon>
                <ion-card-title>Thank you for your submission.</ion-card-title>
                <ion-card-subtitle>The time of your booking is not yet confirmed. We will follow-up with you soon via WhatsApp.</ion-card-subtitle>
              </ion-card-header>
            </ion-card>
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-content>
  </ion-page>
</template>

<script setup>
// vue
import { defineProps, computed, onMounted, reactive, ref, watch } from 'vue';

// icons
import { close, checkmarkCircle, alertCircleOutline, arrowBack, } from 'ionicons/icons';

// components
import { IonPage, IonHeader, IonToolbar, IonTitle, IonContent, IonGrid, IonRow, IonCol, IonSpinner,
        IonButtons, IonButton, IonIcon, IonBackButton, IonCard, IonCardHeader, IonCardTitle, IonCardSubtitle,
        IonItem, IonLabel, IonList, IonListHeader, IonInput, IonTextarea, IonSelect, IonSelectOption,
        IonDatetime, IonPopover,
        modalController, loadingController, toastController, onIonViewDidEnter } from '@ionic/vue';
import SchoolSelectModal from '@/components/secondary/SchoolSelectModal.vue';

// services
import BookingService from '@/services/BookingService';

// composables
import { useI18n } from 'vue-i18n';
import { useStore } from '@/store';
import { useRoute } from 'vue-router';
import { utils } from '@/composables/utils';

const props = defineProps({
  isModal: {
    type: Boolean,
    default: false,
  },
  bookingItemId: {
    type: String,
    default: '',
  },
});
const { t } = useI18n();
const store = useStore();
const route = useRoute();
const { presentToast, formatDate, presentAlert, presentPrompt, closeModal, } = utils();
const { itemId, staffName, } = route.params;

// state variables
const user = computed(() => store.state.user);
const schools = computed(() => store.state.schools);
const loading = ref(false);
const section = ref(1);
const bookingItems = ref([]);
const bookingSlots = ref([]);
const reservedDates = ref([]); // Mainly for AchieveJUPAS briefing sessions (targetDate)
const booking = reactive({
  fullName: "",
  preferredName: "",
  email: "",
  phone: "",
  yearOfStudy: "", // for university students
  bookingItemId: props.bookingItemId || itemId || "",
  bookingSlotId: "",
  status: "",
  waGroupId: "",
  userId: "",
  remarks: "",
  schoolName: "", // for teacher
  schoolId: "", // for teacher
  schoolRoles: "", // for teacher

  // AchieveJUPAS only
  targetDate: null,
  targetStartTime1: null,
  targetEndTime1: null,
  targetStartTime2: null,
  targetEndTime2: null,
});
const selectedBookingItem = computed(() => bookingItems.value.find(bi => bi.id == booking.bookingItemId) || {});
const isTeacher = computed(() => (selectedBookingItem.value.targetUserType == 'Teacher'));

const generateTimeSlots = (startTime, endTime, slotDuration, minSlotInterval = 30) => {
  const slots = [];
  let currentTime = new Date(startTime);
  startTime = new Date(startTime);
  endTime = new Date(endTime);

  while (currentTime < endTime) {
    const slotEndTime = new Date(currentTime.getTime() + slotDuration * 60000);
    if (slotEndTime <= endTime && currentTime.getHours() >= 8 && currentTime.getHours() < 22) {
      const slot = {
        id: formatDate(currentTime.getTime(), 'YYYY MMM DD (ddd) HH:mm'),
        duration: slotDuration,
        startTime: new Date(currentTime.getTime()),
        endTime: new Date(slotEndTime.getTime()),
      };
      slots.push(slot);
    }
    const minSlotEndTime = new Date(currentTime.getTime() + minSlotInterval * 60000);
    currentTime = (minSlotEndTime > slotEndTime ? minSlotEndTime : slotEndTime);
  }
  return slots;
}

const filteredBookingSlots = () => {
  const bookingItem = selectedBookingItem.value;
  const filteredSlots = bookingSlots.value.filter(bs => bs.durationMinutes == bookingItem.durationMinutes);
  if (!bookingItem.startTime) return filteredSlots; // no start time & end time, show all slots matched duration

  // No end time (only show slots after start time)
  if (!bookingItem.endTime) return filteredSlots.filter(bs => new Date(bs.startTime) >= new Date(`${bookingItem.startTime}+08:00`));

  // Calculate timeslots based on start time & end time
  const slots = generateTimeSlots(`${bookingItem.startTime}+08:00`, `${bookingItem.endTime}+08:00`, bookingItem.durationMinutes || 30);
  if (slots.length < 50) return slots;

  // Filter filtered slots within the time range
  return filteredSlots.filter(bs =>  new Date(bs.startTime) >= new Date(`${bookingItem.startTime}+08:00`) && new Date(bs.startTime) <= new Date(`${bookingItem.endTime}+08:00`));
}
const formatSlotTime = (slot) => {
  return `${formatDate(slot.startTime, 'YYYY MMM DD (ddd) HH:mm')}-${formatDate(slot.endTime, 'HH:mm')}`;
}

// INIT: retrieve latest data
const retrieveBookingData = () => {
  loading.value = true;
  BookingService.getBookingData(staffName).then(res => {
    loading.value = false;
    bookingItems.value = res.bookingItems;
    bookingSlots.value = res.bookingSlots;
    reservedDates.value = res.reservedDates || [];
  })
}
retrieveBookingData();

const syncSchoolDetails = (schoolId) => {
  const school =  schools.value.find(s => s.id == schoolId);
  if (school) {
    booking.schoolName = `[${school.nameShort}] ${school.name}`;
    booking.absIntakeId = school.absIntakeId;
  }
}
const syncWithUserData = () => {
  const { id, fullName, waGroupId, preferredName, email, phone, yearOfStudy, schoolId, teacher, } = user.value;
  booking.fullName = fullName;
  booking.waGroupId = waGroupId;
  booking.preferredName = preferredName;
  booking.phone = phone;
  booking.email = email;
  booking.yearOfStudy = yearOfStudy;
  booking.userId = id;
  booking.schoolId = schoolId;
  booking.schoolRoles = teacher?.schoolRoles;

  syncSchoolDetails(schoolId);
  watch(schools, () => {
    syncSchoolDetails(schoolId);
  })
}
syncWithUserData();
watch(user, syncWithUserData);


const OPTIONS = {
  yearOfStudy: ['Year 1', 'Year 2', 'Year 3', 'Year 4', 'Year 5', 'Others', 'Alumni'],
}

const submitBooking = async () => {
  const bookingSlot = filteredBookingSlots().find(bs => bs.id == booking.bookingSlotId) || {};
  //const bookingSlot = bookingSlots.value.find(bs => bs.id == booking.bookingSlotId) || {};
  const bookingItem = selectedBookingItem.value;
  let description = bookingSlot.id ? `<b>${formatSlotTime(bookingSlot)}</b><br />` : '';
  description += `${bookingItem.title}`;
  description += `<br /><br />Full name: ${booking.fullName}`;
  if (booking.preferredName) description += `<br />Preferred name: ${booking.preferredName}`;
  description += `<br />Email: ${booking.email}`;
  description += `<br />Phone: ${booking.phone}`;
  if (booking.yearOfStudy) description += `<br />Year of study: ${booking.yearOfStudy}`;
  if (booking.schoolId) description += `<br />School: ${booking.schoolId.toUpperCase()}`;
  if (booking.schoolRoles) description += `<br />Role(s): ${booking.schoolRoles}`;
  if (booking.targetDate) description += `<br />Briefing date: ${booking.targetDate}`;
  if (booking.targetStartTime1) description += `<br />Start time (student): ${booking.targetStartTime1}`;
  if (booking.targetEndTime1) description += `<br />End time (student): ${booking.targetEndTime1}`;
  if (booking.targetStartTime2) description += `<br />Start time (teacher): ${booking.targetStartTime2}`;
  if (booking.targetEndTime2) description += `<br />End time (teacher): ${booking.targetEndTime2}`;

  presentPrompt(`Confirm booking?<br /><br />${description}`, async () => {
    const loading = await loadingController.create({});
    await loading.present();

    // create new booking record
    const res = await BookingService.createNewBooking(booking, bookingItem, bookingSlot);

    // handle failure case
    if (['success', 'pending'].includes(res)) {
      booking.status = res;
      section.value = 2; // go to thank you section
    } else {
      retrieveBookingData();
      presentAlert("Sorry, the time you chose is no longer available. Please choose another timeslot.");
    }
    loading.dismiss();
  });
}

let selectModalOpened = false;
const openSchoolSelectModal = async () => {
  if (!selectModalOpened) {
    selectModalOpened = true;
    const modal = await modalController.create({
      component: SchoolSelectModal,
      componentProps: { schools: schools.value }
    });
    modal.onDidDismiss().then(({ data }) => {
      if (data && data.selectedSchool) {
        booking.schoolName = data.selectedSchool;
        booking.schoolId = data.schoolId || data.selectedSchool;
      }
      selectModalOpened = false;
    });
    return modal.present();
  }
}

// AchieveJUPAS briefing session
const isDateEnabled = (dateString) => {
  const { startTime, endTime } = selectedBookingItem.value;
  const date = new Date(dateString);

  if (reservedDates.value.some(rd => new Date(rd).getTime() == date.getTime())) return false; // may already be reserved by other schools
  if (startTime && date.getTime() < new Date(`${startTime}+08:00`).getTime()) return false;
  if (endTime && date.getTime() > new Date(`${endTime}+08:00`).getTime()) return false;

  return date.getTime() >= new Date().getTime() && ![6, 0].includes(date.getDay());
}
const formatTargetDate = (ev) => {
  booking.targetDate = ev.detail.value.split("T")[0];
}
const isBookingAchieveJUPASBriefing = computed(() => {
  return ['bi0fd219e9', 'bi6ef13f36'].includes(booking.bookingItemId);
})

// Helper function to calculate end time
const calculateEndTime = (startTime, prevStartTime, currentEndTime, defaultDuration = 30) => {
  if (!startTime) return '';
  
  // Calculate previous duration if end time exists and is valid
  let duration = defaultDuration;
  if (prevStartTime && currentEndTime) {
    const [prevH, prevM] = prevStartTime.split(':').map(Number);
    const [endH, endM] = currentEndTime.split(':').map(Number);
    const diff = (endH * 60 + endM) - (prevH * 60 + prevM);
    
    // Only use the calculated duration if it's positive and less than 24 hours
    if (diff > 0 && diff < 1440) {
      duration = diff;
    }
  }

  // Calculate new end time
  const [hours, minutes] = startTime.split(':').map(Number);
  const totalMinutes = hours * 60 + minutes + duration;
  const newHours = Math.floor(totalMinutes / 60) % 24;
  const newMinutes = totalMinutes % 60;
  
  return `${String(newHours).padStart(2, '0')}:${String(newMinutes).padStart(2, '0')}`;
};

// Watch for changes in start times and update end times accordingly
watch(() => booking.targetStartTime1, (newStartTime, prevStartTime) => {
  if (newStartTime) booking.targetEndTime1 = calculateEndTime(newStartTime, prevStartTime, booking.targetEndTime1, 30); // 30 minutes duration
});
watch(() => booking.targetStartTime2, (newStartTime, prevStartTime) => {
  if (newStartTime) booking.targetEndTime2 = calculateEndTime(newStartTime, prevStartTime, booking.targetEndTime2, 30); // 30 minutes duration
});
</script>

<style scoped>
  #booking-form ion-input, ion-select, ion-textarea {
    margin-top: 10px;
    margin-bottom: 10px;
  }
  .hidden-input {
    opacity: 0;
    height: 0.01px;
    margin-top: -20px;
    position: absolute;
  }
  ion-row ion-col {
    padding: 0;
  }
</style>