<template>
  <ion-page>
    <ion-header>
      <ion-grid fixed>
        <ion-toolbar>
          <ion-title>Recently Browsed</ion-title>
          <ion-buttons slot="start">
            <ion-back-button mode="ios" default-href="/profile"></ion-back-button>
          </ion-buttons>
        </ion-toolbar>

        <ion-toolbar>
          <ion-segment color="primary" mode="ios" v-model="targetList" scrollable>
            <ion-segment-button value="professions">
              <ion-label><p>Professions</p></ion-label>
            </ion-segment-button>
            <ion-segment-button value="segments">
              <ion-label><p>Segments</p></ion-label>
            </ion-segment-button>
            <ion-segment-button value="sectors">
              <ion-label><p>Sectors</p></ion-label>
            </ion-segment-button>
          </ion-segment>
        </ion-toolbar>
      </ion-grid>
    </ion-header>

    <ion-content>
      <ion-grid fixed>
        <div class="spin" v-if="loading">
          <ion-spinner></ion-spinner>
        </div>
        <div v-else>
          <ion-list v-show="targetList == 'professions'">
            <ion-item v-for="(profession, idx) in professions" :key="profession.id" @click="openProfessionModal(profession.id)" detail button>
              <ion-label class="ion-text-wrap">
                <p>{{ idx+1 }}. {{ profession.name }}</p>
              </ion-label>
            </ion-item>
          </ion-list>
          <ion-list v-show="targetList == 'segments'">
            <ion-item v-for="(segment, idx) in segments" :key="segment.id" @click="openSectorModal(segment.sectorId, segment.id)" detail button>
              <ion-label class="ion-text-wrap">
                <p>{{ idx+1 }}. {{ segment.name }}</p>
              </ion-label>
            </ion-item>
          </ion-list>
          <ion-list v-show="targetList == 'sectors'">
            <ion-item v-for="(sector, idx) in sectors" :key="sector.id" @click="openSectorModal(sector.id, '')" detail button>
              <ion-label class="ion-text-wrap">
                <p>{{ idx+1 }}. {{ sector.name }}</p>
              </ion-label>
            </ion-item>
          </ion-list>
        </div>
      </ion-grid>
    </ion-content>
  </ion-page>
</template>

<script lang="ts">
// Vue reactivity
import { computed, ref } from "vue";

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonPage, IonList, IonItem, IonLabel,
        IonGrid, IonRow, IonCol, IonSpinner, IonButtons, IonBackButton, IonSegment, IonSegmentButton, } from "@ionic/vue";
import ProfessionModal from '@/components/pss/ProfessionModal.vue';
import SectorModal from '@/components/pss/SectorModal.vue';

import { useI18n } from 'vue-i18n';
import { useStore } from '@/store';
import { utils } from '@/composables/utils';
import { Profession, Sector, Segment } from '@/types';

export default {
  name: "UserBrowsedListPage",
  components: {
    IonHeader, IonToolbar, IonTitle, IonContent, IonPage, IonList, IonItem, IonLabel,
    IonGrid, IonRow, IonCol, IonSpinner, IonButtons, IonBackButton, IonSegment, IonSegmentButton,
  },
  setup() {
    // methods or filters
    const { t } = useI18n();
    const store = useStore();
    const { openModal } = utils();
    const targetList = ref('professions');

    // 1. declare state variables (ref to make them reactive)
    const loading = computed(() => store.state.loadingPortalData);
    const professions = computed<Profession>(() => store.getters.userRecentBrowsedProfessions);
    const sectors = computed<Sector>(() => store.getters.userRecentBrowsedSectors);
    const segments = computed<Segment>(() => store.getters.userRecentBrowsedSegments);

    const openProfessionModal = async (professionId: any) => await openModal(ProfessionModal, { professionId });
    const openSectorModal = async (sectorId: any, segmentId = '') => (await openModal(SectorModal, { sectorId, segmentId }));

    // 3. return variables & methods to be used in template HTML
    return {
      // variables
      loading,
      professions, sectors, segments,
      targetList,

      // methods
      t,
      openProfessionModal, openSectorModal
    };
  },
};
</script>

<style scoped>
</style>