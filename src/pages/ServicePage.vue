<template>
  <ion-page>
    <!-- Header -->
    <ion-header v-if="isAB4User && (isAB3Service || isAB4Service)">
      <ion-grid class="ion-no-padding" fixed>
        <ion-toolbar>
          <ion-buttons slot="start">
            <ion-back-button default-href="/home"></ion-back-button>
          </ion-buttons>
          <ion-title>
            <ion-label class="ion-text-wrap">
              <h2 v-if="loading">...</h2>
              <h2 v-else><b>{{ getServiceName() }}</b></h2>
            </ion-label>
          </ion-title>
        </ion-toolbar>
      </ion-grid>
    </ion-header>

    <!-- Loading Data -->
    <div class="spin" v-if="loading">
      <ion-spinner></ion-spinner>
    </div>

    <ion-content v-else>
      <!-- Service: AB3 -->
      <ion-grid fixed v-if="isAB4User && isAB3Service">
        <ion-card>
          <ion-item lines="full">
            <ion-label><h2><b>Scope of services</b></h2></ion-label>
          </ion-item>
          <ion-item lines="full">
            <ion-label class="ion-text-wrap"><p>1 lecture for F3 students and their parents</p></ion-label>
          </ion-item>
          <ion-item lines="full">
            <ion-label class="ion-text-wrap"><p>7 months of AchieveBot usage offered to all participating students</p></ion-label>
          </ion-item>
          <ion-item lines="full">
            <ion-label class="ion-text-wrap"><p>Review meeting after report submission</p></ion-label>
          </ion-item>
        </ion-card>
      </ion-grid>

      <!-- Service: AB4 -->
      <ion-grid fixed v-else-if="isAB4User && isAB4Service">
        <ion-card>
          <ion-item lines="full">
            <ion-label><h2><b>Scope of services</b></h2></ion-label>
            <ion-select fill="outline" interface="popover" slot="end" v-if="userSchoolAB4Intakes.length > 0"
                        style="min-width: 130px" v-model="selectedIntakeId">
              <ion-select-option v-for="intake in userSchoolAB4Intakes" :key="intake.id" :value="intake.id">
                {{ intake.academicYear }}
              </ion-select-option>
            </ion-select>
          </ion-item>
          <ion-item lines="full">
            <ion-label class="ion-text-wrap"><p>2 lessons for F4 students (Duration and number of students as specified below)</p></ion-label>
          </ion-item>
          <ion-item lines="full">
            <ion-label class="ion-text-wrap"><p>7 months of AchieveBot usage offered to all participating students</p></ion-label>
          </ion-item>
          <ion-item lines="full">
            <ion-label class="ion-text-wrap"><p>Review meeting after report submission</p></ion-label>
          </ion-item>
        </ion-card>
        <ion-card class="no-border-card">
          <ion-item lines="none">
            <ion-label><h2><b>Proposed arrangement</b></h2></ion-label>
          </ion-item>
          <ion-item lines="none">
            <ion-label class="ion-text-wrap"><p>Lesson 1</p></ion-label>
          </ion-item>
        </ion-card>
        <!--[80+ min] -->
        <ABEventsDetails targetAnchorEvId="abs-session1" :ab4IntakeId="selectedIntakeId" :suggestedDuration="80"></ABEventsDetails>

        <div class="divider"></div>

        <!--[40+ min]-->
        <ion-card class="no-border-card">
          <ion-item lines="none">
            <ion-label class="ion-text-wrap"><p>Lesson 2</p></ion-label>
          </ion-item>
        </ion-card>
        <ABEventsDetails targetAnchorEvId="abs-session2" :ab4IntakeId="selectedIntakeId" :suggestedDuration="40"></ABEventsDetails>
        <!--<p class="ion-text-center"><b>[1 hr] Review meeting after report submission</b></p>-->
      </ion-grid>

      <!--
        Other Services (e.g. Work / Mock JUPAS)
        -->
      <service-details :serviceId="serviceId" :userId="userId" :isSurvey="isSurvey()" v-else></service-details>
    </ion-content>
  </ion-page>
</template>

<script lang="ts">
// Vue reactivity
import { computed, ref, watch } from 'vue';

// icons
import { arrowBack, } from 'ionicons/icons';

// components
import { IonPage, IonGrid, IonHeader, IonFooter, IonToolbar, IonTitle, IonContent, IonSpinner,
        IonButtons, IonButton, IonIcon, IonLabel, IonBackButton, IonCard, IonItem, IonSelect, IonSelectOption,
        onIonViewDidEnter, onIonViewWillLeave, } from '@ionic/vue';
import ABEventsDetails from "@/components/secondary/events/ABEventsDetails.vue";

// API services
import { useStore } from '@/store';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

// types
import { Service, Session, } from '@/types';
import ABSService from '@/services/ABSService';
import AuthService from '@/services/AuthService';

// Firebase
import { customTokenSignin } from '@/auth';

export default {
  name: 'ServicePage',
  components: { IonPage, IonGrid, IonHeader, IonFooter, IonToolbar, IonTitle, IonContent, IonSpinner,
                IonButtons, IonButton, IonIcon, IonLabel, IonBackButton, IonCard, IonItem, IonSelect, IonSelectOption,
                ABEventsDetails, },
  setup() {
    const store = useStore();
    const { t } = useI18n();
    const route = useRoute();
    const router = useRouter();
    const { id: serviceId, userId } = route.params;

    // 1. declare state variables (ref to make them reactive)
    const loading = computed(() => store.state.loadingData || store.state.loadingUser);
    const user = computed(() => store.state.user);
    const isAB4User = computed(() => store.getters.isAB4User);
    const userSchoolAB4Intakes = computed(() => store.getters.userSchoolAB4Intakes);
    const selectedIntakeId = ref(userSchoolAB4Intakes.value[0]?.id);
    const service = computed<Service>(() => store.getters.getServiceById(serviceId));

    let accessTime, duration = 0, counterInterval;
    onIonViewDidEnter(() => {
      // Initial Access
      ABSService.insertPageAccessRecord(`ServicePage | ${serviceId}`, new Date(), null, userId);

      // Record Access & Leave Time
      accessTime = new Date();
      counterInterval = setInterval(() => (duration++), 1000);
    });
    onIonViewWillLeave(() => {
      if (accessTime && duration >= 3) {
        const { id, name } = service.value || {};
        ABSService.insertPageAccessRecord(`ServicePage | ${name} | ${id}`, accessTime, new Date(accessTime.getTime() + duration*1000), userId);
        accessTime = undefined;
        duration = 0; // reset;
        clearInterval(counterInterval);
      }
    });

    watch(user, async (currUser) => {
      /*
      if (currUser.id == null) {
        store.commit('setLoadingUser', true);
        const existingFirebaseUser = await AuthService.checkPhoneExists(userId, null, true); // userId is the phone
        if (existingFirebaseUser && existingFirebaseUser.s == 200 && existingFirebaseUser.t) {
          await customTokenSignin(existingFirebaseUser.t);
        } else {
          store.commit('setLoadingUser', false);
          if (!userId) {
            router.replace(`/login?redirectPath=${route.fullPath}`); // redirect to login page
          }
        }
      } else if (currUser.phone != userId) {
        store.dispatch('checkOnetimeGetUserInfo', userId);
      }
        */
    })

    watch(userSchoolAB4Intakes, () => {
      selectedIntakeId.value = userSchoolAB4Intakes.value[0]?.id;
    })
    
    // 3. return variables & methods to be used in template HTML
    return {
      // icons
      arrowBack,

      // variables
      loading,
      service, serviceId, userId,
      user, isAB4User,
      userSchoolAB4Intakes, selectedIntakeId,

      // methods
      t,
      isSurvey: () => (route.path.includes("survey")),
      getServiceName: () => {
        const { schoolId } = user.value;
        const serviceName = service.value.name || '';
        return schoolId ? serviceName.replace('[School]', `@ ${schoolId.toString().toUpperCase()}`) : serviceName;
      },
      
      // AB3 or AB4
      isAB3Service: computed(() => (serviceId == 's8179c188')),
      isAB4Service: computed(() => (serviceId == 's9215dbaf')),
    }
  },
}
</script>

<style scoped>
</style>