<template>
  <ion-page>
    <!-- Page Header -->
    <page-header :showBackButton="true" :hideSchoolLogo="true" :hideUserProfileToolbar="true"></page-header>
    
    <!-- Page Content -->
    <ion-content :fullscreen="true">
      <ion-grid class="ion-no-padding ion-text-center" fixed>
        <ion-card>
          <ion-row>
            <!-- Loading User Data -->
            <ion-col size="12" class="ion-text-center ion-margin-top" v-if="loading">
              <ion-spinner></ion-spinner>
            </ion-col>
            <ion-col size-xs="12" size-md="4" v-if="!loading && user.id">
              <img class="user-image" :src="getAppSheetFileLink('user', user.profilePic)" />
              <ion-button class="ion-margin-top" color="light" size="small" @click="updateProfilePic()">
                <ion-icon slot="start" :icon="camera"></ion-icon>
                {{ t('UserProfilePage.changeProfilePhoto') }}
              </ion-button>
            </ion-col>
            <ion-col>
              <div v-if="!loading">
                <ion-card-header v-if="user.id">
                  <ion-card-title v-if="user.schoolId">
                    <b>
                      <span v-show="user.class">{{ user.class }}{{ formatStudentNumber(user.studentNumber) }} | </span>
                      {{ user.schoolId.toUpperCase() }}
                    </b>
                  </ion-card-title>
                  <ion-card-title v-show="user.fullName">
                    {{ user.fullName }} <span v-if="user.preferredName">({{ user.preferredName }})</span>
                  </ion-card-title>
                  <ion-card-subtitle>{{ user.email }}</ion-card-subtitle>
                  <ion-card-subtitle>{{ user.phone }}</ion-card-subtitle>
                </ion-card-header>

                <ion-card-header v-else-if="user.schoolId">
                  <ion-card-title v-show="user.schoolId">{{ user.schoolId.toUpperCase() }}</ion-card-title>
                </ion-card-header>

                <ion-chip outline color="primary" v-if="user.secondarySchoolStudent?.group == 'ucircle'">
                  UCircle
                  <ion-icon color="warning" :icon="star"></ion-icon>
                </ion-chip>
              </div>

              <ion-card-content>
                <div v-if="user.id">
                  <ion-button class="ion-margin-bottom" expand="block" @click="openEditUserProfileModal()">
                    <ion-icon slot="start" :icon="createOutline"></ion-icon>
                    {{ t('UserProfilePage.editPersonalInfo') }}
                  </ion-button>
                  <!--
                  <ion-button expand="block" @click="presentChangePasswordPrompt()">
                    <ion-icon slot="start" :icon="keyOutline"></ion-icon>
                    {{ t('UserProfilePage.changePassword') }}
                  </ion-button>
                  -->
                </div>
                <ion-list>
                  <div v-if="user.id || user.schoolId">
                    <div v-if="user.isAdmin">
                      <!-- Admin Switch Role -->
                      <ion-item fill="outline">
                        <ion-label>Role</ion-label>
                        <ion-select label="Role" interface="popover" v-model="user.appRole" @ionDismiss="updateUserAppRole()">
                          <ion-select-option value="teacher">Teacher</ion-select-option>
                          <ion-select-option value="secondary-school-student">Secondary Student</ion-select-option>
                          <ion-select-option value="university-student">University Student</ion-select-option>
                          <ion-select-option value="university-client">University Staff / Client</ion-select-option>
                        </ion-select>
                      </ion-item>

                      <div v-show="user.appRole != 'university-student'">
                        <!-- Admin Switch School -->
                        <ion-item fill="outline" @click="openSchoolSelectModal()" button>
                          <ion-input label="School" class="ion-text-right" type="text" v-model="user.schoolId" required></ion-input>
                        </ion-item>

                        <!-- Is AB4 School? (poster / sessions) -->
                        <ion-item fill="outline" lines="none">
                          <ion-toggle @ionChange="toggleUserIsAB4School($event.detail.checked)" :checked="isAB4User">
                            <p>AB4 School?</p>
                          </ion-toggle>
                        </ion-item>
                      </div>

                      <!--<div v-show="user.appRole == 'university-student'">-->
                      <div>
                        <ion-item fill="outline">
                          <ion-label>jobEX</ion-label>
                          <ion-select interface="popover" v-model="user.programId" @ionChange="updateUserProgram()">
                            <ion-select-option v-for="jobEX in jobEXes" :key="jobEX['jobEX Name']" :value="jobEX.relatedProgramIds[0]">
                              {{ jobEX['jobEX Name'] }}
                            </ion-select-option>
                          </ion-select>
                        </ion-item>
                      </div>

                      <div v-show="user.isUniversityClient">
                        <ion-item fill="outline">
                          <ion-label>Client</ion-label>
                          <ion-select interface="popover" v-model="user.clientId" @ionChange="updateUserClient()">
                            <ion-select-option v-for="client in allClients.filter(c => c.type == 'University')" :key="client.id" :value="client.id">
                              {{ client.fullName }}
                            </ion-select-option>
                          </ion-select>
                        </ion-item>
                      </div>
                    </div>

                    <ion-item lines="none" router-link="/liked" button detail>
                      <ion-icon slot="start" :icon="heart"></ion-icon>
                      <ion-label>Saved</ion-label>
                    </ion-item>
                    <ion-item lines="none" router-link="/browsed" button detail>
                      <ion-icon slot="start" :icon="eyeOutline"></ion-icon>
                      <ion-label>Recently Browsed</ion-label>
                    </ion-item>
                  </div>
                  <ion-item lines="none">
                    <ion-icon slot="start" :icon="globe"></ion-icon>
                    <ion-select :label="t('UserProfilePage.language')" interface="popover" v-model="locale" @ionDismiss="updateUserLocale()"> 
                      <ion-select-option value="zh">中文</ion-select-option>
                      <ion-select-option value="en">English</ion-select-option>
                    </ion-select>
                  </ion-item>
                  <ion-item lines="full">
                    <ion-icon slot="start" :icon="moon"></ion-icon>
                    <ion-toggle @ionChange="toggleDarkTheme($event.detail.checked)" :checked="user.darkTheme == true">
                      <p>{{ t('UserProfilePage.darkTheme') }}</p>
                    </ion-toggle>
                  </ion-item>
                </ion-list>
                <ion-button fill="clear" size="small" router-link="/terms-and-conditions">
                  <ion-label>{{ t('UserProfilePage.termsAndConditions') }}</ion-label>
                </ion-button>
                <ion-button fill="clear" size="small" router-link="/feedback">
                  <ion-label>{{ t('UserProfilePage.feedback') }}</ion-label>
                </ion-button>
              </ion-card-content>
            </ion-col>
          </ion-row>
        </ion-card>
        <!--<ion-button color="medium" size="small" class="logout-btn" @click="doLogout()" v-if="user.id || user.schoolId">-->
        <ion-button color="medium" size="small" class="logout-btn" @click="doLogout()">
          <ion-icon slot="end" :icon="logOutOutline"></ion-icon>
          {{ t('UserProfilePage.logout') }}
        </ion-button>
        <ion-row class="ion-justify-content-center ion-margin-top">
          <ion-note>Ver. {{ versionCode }}</ion-note>
        </ion-row>
      </ion-grid>
    </ion-content>
  </ion-page>
</template>

<script lang="ts">
// Vue
import { computed, ref, watch } from 'vue';
import { Camera, CameraResultType, CameraSource } from "@capacitor/camera";

// icons
import { logOutOutline, createOutline, keyOutline, camera, globe, moon, add,
          heart, heartOutline, eyeOutline, logoWhatsapp, star, } from 'ionicons/icons';

// components
import { IonPage, IonContent, IonGrid, IonRow, IonCol,
        IonSpinner, IonList, IonListHeader, IonItem, IonLabel,
        IonIcon, IonBackButton, IonButton, IonButtons, IonNote, IonImg,
        IonCard, IonCardContent, IonCardHeader, IonCardSubtitle, IonCardTitle, IonInput,
        IonToggle, IonSelect, IonSelectOption, IonBadge, IonChip, IonFabButton,
        alertController, toastController, modalController, loadingController, } from '@ionic/vue';
import UserProfileFormModal from '@/components/modals/UserProfileFormModal.vue';
import SchoolSelectModal from '@/components/secondary/SchoolSelectModal.vue';

// API services
import AuthService from '@/services/AuthService';
import UserService from '@/services/UserService';

// utils or methods
import moment from 'moment';
import { useI18n } from 'vue-i18n';
import { useStore } from '@/store';
import { useRouter } from 'vue-router';

import config from '@/config';
import { utils } from '@/composables/utils';
import PortalService from '@/services/PortalService';

export default {
  name: 'UserProfile',
  components: { IonPage, IonContent, IonGrid, IonRow, IonCol,
                IonSpinner, IonList, IonListHeader, IonItem, IonLabel,
                IonIcon, IonBackButton, IonButton, IonButtons, IonNote, IonImg,
                IonCard, IonCardContent, IonCardHeader, IonCardSubtitle, IonCardTitle, IonInput,
                IonToggle, IonSelect, IonSelectOption, IonBadge, IonChip, IonFabButton,
                alertController, toastController, modalController, loadingController, },
  setup() {
    const store = useStore();
    const router = useRouter();
    const { formatStudentNumber, sleep, getAppSheetFileLink, } = utils();
    
    // 1. declare state variables (ref to make them reactive)
    const loading = computed(() => store.state.loadingUser);
    const user = computed(() => store.state.user);
    const schools = computed(() => store.state.schools);
    const isAB4User = computed(() => store.getters.isAB4User);

    // jobEX / university
    const jobEXes = computed(() => store.state.allJobEXes);
    const allClients = computed(() => store.state.allClients);
    
    // methods or filters
    const { t, locale } = useI18n();

    const doLogout = async () => {
      const alert = await alertController.create({
        header: t('UserProfilePage.logout'),
        message: t('UserProfilePage.confirmLogout'),
        buttons: [
          {
            text: t('cancel'),
            role: 'cancel',
            cssClass: 'secondary',
          }, {
            text: t('UserProfilePage.logout'),
            handler: () => {
              if (store.state.onetimeAccessInfo) {
                localStorage.removeItem("sessionAccessToken");
                store.commit('receiveOnetimeAccessInfo', null);
                store.dispatch('resetUserData');
                //router.replace('/home');
              }
              AuthService.doLogout();
              router.replace('/login'); // redirect to login page
            }
          }
        ]
      });
      return alert.present();
    }

    const presentToast = async(msg: string, duration = 5000) => {
      const toast = await toastController.create({
        message: msg,
        duration,
        position: 'top',
      });
      toast.present();
    }
    const presentChangePasswordPrompt = async () => {
      const alert = await alertController.create({
        header: t('UserProfilePage.changePassword'),
        inputs: [
          {
            name: 'oldPassword',
            type: 'password',
            placeholder: t('UserProfilePage.oldPassword')
          },
          {
            name: 'newPassword',
            type: 'password',
            placeholder: t('UserProfilePage.newPassword')
          },
          {
            name: 'newPasswordConfirm',
            type: 'password',
            placeholder: t('UserProfilePage.newPasswordConfirm')
          },
        ],
        buttons: [
          {
            text: t('cancel'),
            role: 'cancel',
            cssClass: 'secondary',
          }, {
            text: t('confirm'),
            handler: (value) => {
              if (value.oldPassword && value.newPassword && value.newPasswordConfirm) {
                if (value.newPassword != value.newPasswordConfirm) {
                  presentToast(t('inconsistentNewPasswordMsg'));
                } else {
                  AuthService.updatePassword(value.oldPassword, value.newPassword).then(res => {
                    if (res == 'wrong-password') {
                      presentToast(t('wrongOldPassword'));
                    } else {
                      presentToast(t('successUpdatePassword'), 3000);
                      alert.dismiss();
                    }
                  });
                }
              } else {
                presentToast(t('enterAllFields'));
              }
              return false;
            }
          }
        ]
      });
      await alert.present();
    }
    
    // update user information
    const openEditUserProfileModal = async () => {
      const modal = await modalController.create({
        component: UserProfileFormModal,
      });
      return modal.present();
    }

    // Update user profile picture (Capacitor Camera)
    const updateProfilePic = async () => {
      const cameraPhoto = await Camera.getPhoto({
        resultType: CameraResultType.DataUrl,
        source: CameraSource.Prompt,
        quality: 60,
        width: 1500,
      });
      const loading = await loadingController.create({});
      await loading.present();
      const fileName = `${moment().format('YYYYMMDDHHmmss')}-${user.value.id}.${cameraPhoto.format}`;
      const res = await UserService.updateLoggedInUserProfilePic(cameraPhoto.dataUrl, fileName, user.value.profilePic);
      loading.dismiss();
      presentToast(t("UserProfilePage.successChangeProfilePhoto"));
      store.commit('updateUser', { profilePic: res.profilePic });
    }

    const toggleDarkTheme = async (enableDarkTheme: boolean) => {
      document.body.classList.toggle('dark', enableDarkTheme);
      if (user.value.id) { // only for logged in users
        const currDark: any = user.value.darkTheme;
        if ((currDark == true && !enableDarkTheme) || ((currDark == false || currDark == '') && enableDarkTheme)) {
          const loading = await loadingController.create({});
          await loading.present();
          UserService.updateUserDarkThemeSetting(enableDarkTheme);
          document.body.classList.toggle('dark', enableDarkTheme);
          store.commit('updateUser', { darkTheme: enableDarkTheme });
          await sleep(1);
          loading.dismiss();
        }
      }
    }

    const updateUserSchool = async () => {
      const loading = await loadingController.create({});
      await loading.present();
      const { schoolId } = user.value;
      const latestSchoolIntake = store.state.absIntakes.find(a => a.schoolId == schoolId);
      const updatingObj = { schoolId, absIntakeId: latestSchoolIntake ? latestSchoolIntake.id : "" };
      UserService.updateUser(updatingObj);
      store.commit('updateUser', updatingObj);
      store.dispatch('getStep1Questions'); // mainly for AB4 (assume Portal Data already fetched)
      store.dispatch('getSchoolUsers', schoolId);
      loading.dismiss();
    }

    let schoolSelectModalOpened = false;

    // 3. return variables & methods to be used in template HTML
    return {
      // icons
      logOutOutline, createOutline, keyOutline, camera, globe, moon, add,
      heart, heartOutline, eyeOutline, logoWhatsapp, star,

      // variables
      locale,
      loading, user, schools,
      versionCode: config.versionCode,

      // methods
      formatStudentNumber, getAppSheetFileLink,
      t, toggleDarkTheme, doLogout, presentChangePasswordPrompt, openEditUserProfileModal,
      updateProfilePic, updateUserSchool,
      updateUserLocale: async () => {
        if (user.value.id) {
          if (user.value.preferredLang != locale.value) {
            UserService.updateUserAppLanguage(locale.value);
            store.commit('updateUser', { preferredLang: locale.value });
          }
        }
      },

      openSchoolSelectModal: async () => {
        if (!schoolSelectModalOpened) {
          schoolSelectModalOpened = true;
          const modal = await modalController.create({
            component: SchoolSelectModal,
            componentProps: { schools: schools.value, showAllSchools: true }
          });
          modal.onDidDismiss().then(({ data }) => {
            if (data && data.selectedSchool) {
              user.value.schoolId = data.schoolId || data.selectedSchool;
              updateUserSchool();
            }
            schoolSelectModalOpened = false;
          });
          return modal.present();
        }
      },
      onClickWhatsAppButton: () => {
        store.dispatch('setUserJoinedWAGroup');
      },

      // Is / is not AB4 school
      isAB4User,
      toggleUserIsAB4School: (isAB4School: boolean) => {
        if (isAB4School) {
          const { schoolId } = user.value;
          const latestSchoolIntake = store.state.absIntakes.find(a => a.schoolId == schoolId) ||
                                      store.state.absIntakes.find(a => a.sessions.length > 0);
          store.commit('updateUser', {
            absIntakeId: latestSchoolIntake ? latestSchoolIntake.id : "",
            isAB4User: true,
          }); // AB4 School view
        } else {
          store.commit('updateUser', { absIntakeId: "", isAB4User: false, }); // Non-AB4 School view
        }
      },

      // Switch between student & teachers
      updateUserAppRole: () => {
        let baseParams = { teacher: null, isSecondaryStudent: false, isUniversityStudent: false, isUniversityClient: false };
        if (user.value.appRole == 'teacher') {
          store.commit('updateUser', {
            ...baseParams,
            teacher: store.state.teacher,
          }); // Teacher view
          store.dispatch('getSchoolUsers', user.value.schoolId);
        } else {
          switch (user.value.appRole) {
            case 'secondary-school-student':
              baseParams = { ...baseParams, isSecondaryStudent: true }; // Student view
              break;
            case 'university-student':
              baseParams = { ...baseParams, isUniversityStudent: true };
              //store.dispatch('getProgramUsers', user.value.programId);
              break;
            case 'university-client':
              baseParams = { ...baseParams, isUniversityClient: true };
              store.dispatch('getProgramUsers', user.value.programId); // TBC: can browse list of university students
              break;
          }
          store.commit('updateUser', baseParams);
        }
        localStorage.setItem(config.sessionAppRoleField, user.value.appRole);
      },

      // jobEX / University
      jobEXes,
      updateUserProgram: async () => {
        const loading = await loadingController.create({});
        await loading.present();
        const { programId } = user.value;
        store.dispatch('getStep1Questions', programId);
        store.dispatch('getUniProgramData', programId);
        store.dispatch('getProgramUsers', user.value.programId);
        UserService.updateUser({ programId });
        store.commit('updateUser', { programId });
        loading.dismiss();
      },
      allClients,
      updateUserClient: async () => {
        const loading = await loadingController.create({});
        await loading.present();
        const { clientId, programId: oldProgramId, } = user.value;
        const client: any = allClients.value.find(c => c.id == clientId) || {};
        const jobEXObj = jobEXes.value.find(jobEX => jobEX['jobEX Name'] == client.jobEXName);
        const programId = jobEXObj?.relatedProgramIds[0]; // updated program ID
        if (programId) {
          store.dispatch('getStep1Questions', programId);
          store.dispatch('getUniProgramData', programId);
          store.dispatch('getProgramUsers', programId);
        }
        UserService.updateUser({ clientId, programId: programId || oldProgramId });
        store.commit('updateUser', { clientId, programId: programId || oldProgramId });
        loading.dismiss();
      }
    }
  },
}
</script>

<style scoped>
  .user-image {
    margin: auto;
    display: block;
    max-height: 200px;
  }
  .location-card {
    margin: 16px 0;
  }
</style>