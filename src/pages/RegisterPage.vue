<template>
  <ion-page>
    <ion-header style="background: rgb(0, 80, 125); box-shadow: none">
      <ion-grid class="ion-no-padding" fixed>
        <ion-toolbar style="--background: transparent; --min-height: 100px">
          <ion-buttons slot="start">
            <ion-button fill="clear" style="color: #fff" router-link="/">
              <ion-icon size="large" slot="icon-only" :icon="arrowBack"></ion-icon>
            </ion-button>
          </ion-buttons>

          <!-- FDMT Logo -->
          <ion-title slot="end" class="ion-no-padding" style="flex: initial">
            <logo-img :useWhiteLogo="true" style="width: 180px"></logo-img>
          </ion-title>
        </ion-toolbar>
      </ion-grid>
    </ion-header>

    <ion-content>
      <ion-grid fixed>
        <div class="spin" v-show="loading">
          <ion-spinner></ion-spinner>
        </div>

        <div v-show="!loading">
          <!-- Show info of featured service (e.g. HealthcareWork) -->
          <div v-if="featuredService && featuredService.id">

            <!-- Video Google Slide -->
            <iframe :src="featuredService?.embeddedVideoSlideLink" frameborder="0" style="width: 100%; height: calc(min(100vw, 1000px) * 0.75)"
                    allowfullscreen="true" mozallowfullscreen="true" webkitallowfullscreen="true" v-if="featuredService?.embeddedVideoSlideLink"></iframe>
            
            <!-- Video -->
            <iframe class="responsive-embed" style="width: 100%; height: 380px" :src="featuredService?.embeddedYTLink" frameborder="0" allowfullscreen
                    v-else-if="featuredService?.embeddedYTLink"></iframe>

            <img style="width: 100%" :src="getProxyImgLink(featuredService?.posterLink)" v-else />

            <!-- Workshop description -->
            <ion-card v-if="featuredService.description">
              <ion-card-content>
                <div style="white-space: pre-line; color: var(--ion-color-dark)" v-html="getLinkifiedText(featuredService?.description)"></div>
                <div v-if="prefilledServiceId">
                  <br />
                  <div style="white-space: pre-line; color: var(--ion-color-dark)">
                    If you are interested in this program, please submit the below registration form, and we will follow up with you via WhatsApp. Thank you.
                  </div>
                </div>
              </ion-card-content>
            </ion-card>
          </div>

          <ion-list>
            <!--
              Role Selection (Teachers / Students)
            -->
            <ion-select style="margin-bottom: 10px" fill="outline" label="You are a*" label-placement="floating" interface="popover" v-model="application.roles">
              <ion-select-option value='secondary-school-student'>Secondary Student</ion-select-option>
              <ion-select-option value='teacher'>Secondary School Teacher / Principal</ion-select-option>
              <ion-select-option value='university-student'>University Student</ion-select-option>
              <ion-select-option value="university-client">University Staff</ion-select-option>
            </ion-select>

            <div v-if="isTeacher">
              <iframe class="slide-embed" style="width: 100%; height: 560px" src="https://docs.google.com/presentation/d/1EBxEhh9y_J7L_NzCIoItdvePsP4fQMUUVg-rT9EW9To/embed?start=true&loop=true&delayms=1000"
                      frameborder="0" allowfullscreen="true" mozallowfullscreen="true" webkitallowfullscreen="true"></iframe>
            </div>

            <!--
              University programme
              -->
            <div class="ion-margin-bottom" v-show="isUniversityStudent">
              <ion-select fill="outline" label="What's your programme?*" label-placement="stacked" interface="popover" v-model="application.programId">
                          <!--:disabled="application.prefilledProgramId">-->
                <ion-select-option v-for="programme in jobEXProgrammes" :key="programme.name" :value="programme.id">
                  {{ programme.name }}
                </ion-select-option>
              </ion-select>
            </div>
          </ion-list>
        </div>


        <!--
          jobEX Info (poster / video)
        <div v-show="application.programId">
          <iframe class="responsive-embed" width="100%" height="350px"
            :src="selectedJobEX()['Embedded Video Link']" frameborder="0" allowfullscreen
            v-show="selectedJobEX()['Embedded Video Link']"></iframe>

          <div class="ion-text-center">
            <img :src="getProxyImgLink(selectedJobEX().posterLink)" v-if="selectedJobEX().posterLink" style="width: 100%" />
          </div>

          <ion-grid v-show="!selectedJobEX().posterLink">
            <ion-row>
              <ion-col>
                <b>
                  <span v-show="selectedJobEX()['Embedded Video Link']">As explained in this 3-min video, </span>
                  {{ selectedJobEX()['Department'] }} jobEX can help you:
                </b>
                <ol style="padding-left: 1.2em; margin: 5px 0 0 0">
                  <li>Recognize 30+ career prospects open to your major and set an aspiring career goal</li>
                  <li>Work out the entrance path based on your ability & a differentiating pitch with your major & background</li>
                  <li>Make your CV impressive to employers in 30 secs regardless of GPA</li>
                  <li>Meet employers of the target profession</li>
                </ol>
              </ion-col>
            </ion-row>
            <ion-row>
              <ion-col>Don't miss the chance to join! Register below to:<br>
                <ol style="padding-left: 1.2em; margin: 5px 0 0 0">
                  <li>Use our Career Atlas App (to access 200+ career prospects, 1,000+ CV examples, etc)</li>
                  <li>Join the following</li>
                </ol>
              </ion-col>
            </ion-row>
            <ion-row>
              <ion-col size="12"><b>Orientation session:</b> Reduce rejection by 90%</ion-col>
              <ion-col size="12"><b>Workshop (career prospect):</b> Major-related professions & sectors - Make your CV headline attractive</ion-col>
              <ion-col size="12"><b>Workshop (CV):</b> Make your CV impressive in 30 sec - Personalize your pitch</ion-col>
              <ion-col size="12"><b>Self-scheduled individual sessions:</b> Review of CV, employer pitch, cover letter, etc.</ion-col>
            </ion-row>
          </ion-grid>
        </div>
        -->

        <!--
          Main Registration Form
        -->
        <form @submit.prevent="doRegister()" style="padding-bottom: 150px"
              v-show="!loading && application.roles && (isUniversityStudent ? application.programId : true)">

          <!-- Prefilled Work Event Poster -->
          <div style="margin-bottom: 5px" v-if="featuredEvent">
            <!--<img :src="getProxyImgLink(featuredEvent?.posterLink)" @click="openImageModal(featuredEvent?.posterLink)" />-->

            <!-- Video Google Slide -->
            <iframe :src="featuredEventService?.embeddedVideoSlideLink" frameborder="0" style="width: 100%; height: calc(min(100vw, 1000px) * 0.75)"
                    allowfullscreen="true" mozallowfullscreen="true" webkitallowfullscreen="true" v-if="featuredEventService?.embeddedVideoSlideLink"></iframe>

            <!-- Video -->
            <iframe class="responsive-embed" style="width: 100%; height: 380px" :src="featuredEvent?.videoLink" frameborder="0" allowfullscreen
                    v-else-if="featuredEvent?.videoLink"></iframe>

            <!-- Workshop info -->
            <ion-accordion-group v-if="featuredEventService?.description" value="details">
              <ion-accordion value="details">
                <ion-item style="--border-width: 0px !important" slot="header" color="fdmtred">
                  <ion-label class="ion-text-wrap">
                    <p>All details (including school circular)</p>
                  </ion-label>
                </ion-item>
                <ion-card style="margin: 1.5px" slot="content">
                  <ion-card-content>
                    <div v-if="featuredEventService?.description">
                      <div style="white-space: pre-line; color: var(--ion-color-dark)" v-html="getLinkifiedText(featuredEventService?.description)"></div>
                    </div>
                  </ion-card-content>
                </ion-card>
              </ion-accordion>
            </ion-accordion-group>
          </div>

          <!--
            Personal Information
          -->
          <section-header title="Personal Information" v-if="!isTeacher"></section-header>

          <ion-list>
            <div v-if="isTeacher">
              <!-- School (Only for teachers) -->
              <ion-input fill="outline" :label="t('school')" label-placement="floating" type="text"
                          v-model="application.schoolName" :clearInput="true" @click="openSchoolSelectModal()" required></ion-input>

              <!-- Role in School (Only for teachers) -->
              <ion-input fill="outline" label="Your position(s) in the school" label-placement="floating" type="text"
                          v-model="application.roleInSchool" :clearInput="true" @click="openRoleInSchoolSelectModal()" required></ion-input>

              <!-- Role in Classes -->
              <!-- label="Which class(es) are you teaching / the class teacher for?"-->
              <ion-textarea fill="outline" label="Your role(s) in class(es)" label-placement="floating" type="text"
                            :value="application.rolesInClasses.map(ric => `${ric.role} (${ric.classes})`).join('\n')"
                            @click="openRolesInClassesModal()"></ion-textarea>
            </div>

            <!-- Full Name (English) -->
            <ion-input fill="outline" ref="fullNameInput" :label="`${t('fullName')}*`" label-placement="floating" type="text"
                      v-model="application.fullName" enterkeyhint="next" @keyup.enter="focusInputField(preferredNameInput)" required></ion-input>

            <!-- Preferred Name -->
            <ion-input fill="outline" ref="preferredNameInput" :label="`${t('preferredName')}`" label-placement="floating" type="text"
                        v-model="application.preferredName" enterkeyhint="next" @keyup.enter="focusInputField(chineseNameInput)"
                        v-if="!prefilledServiceId"></ion-input>

            <!-- Chinese Name (Only for students) -->
            <ion-input fill="outline" ref="chineseNameInput" :label="`${t('chineseName')}`" label-placement="floating" type="text"
                        v-model="application.chineseName" enterkeyhint="next" v-if="isSecondaryStudent"></ion-input>

            <!-- Gender -->
            <ion-select fill="outline" label="Gender*" label-placement="floating" interface="popover" v-model="application.gender"
                        v-if="!prefilledServiceId">
              <ion-select-option value="male">Male</ion-select-option>
              <ion-select-option value="female">Female</ion-select-option>
            </ion-select>

            <!-- Mobile Number -->
            <ion-input fill="outline" ref="phoneInput" :label="`${t('phone')}*`" label-placement="floating" inputmode="numeric"
                      v-model="application.phone" maxlength="8" @keyup.enter="focusInputField(emailInput)" required
                      helper-text="A HK mobile number is needed for accessing FDMT AchieveBot"
                      :disabled="user?.id && application.phone?.toString().length == 8"></ion-input>

            <div v-if="isUniversityClient">
              <!-- University Email Address -->
              <ion-input fill="outline" ref="emailInput" label="University email address*" label-placement="floating" inputmode="email"
                        type="email" v-model="application.uniEmail" enterkeyhint="next" required></ion-input>

              <!-- Institution -->
              <ion-input fill="outline" label="University / Institution*" label-placement="floating"
                        type="text" v-model="application.institution" enterkeyhint="next" required></ion-input>

              <!-- Position -->
              <ion-input fill="outline" label="Your position*" label-placement="floating"
                        type="text" v-model="application.position" enterkeyhint="next" required></ion-input>
            </div>

            <!-- Email Address -->
            <ion-input style="margin-bottom: 0" fill="outline" ref="emailInput" :label="`${isUniversityStudent ? 'Gmail address' : t('schoolEmail')}*`" label-placement="floating"
                      inputmode="email" type="email" v-model="application.email" enterkeyhint="next" :required="application.roles != 'teacher'"
                      :helper-text="isUniversityStudent ? 'A gmail account is needed for CV resource sharing' : ''"
                      v-else-if="!prefilledServiceId"></ion-input>
          </ion-list>

          <!--
            Study Information
          -->
          <div class="ion-margin-top">
            <section-header v-if="isSecondaryStudent || isUniversityStudent" title="Study Information"></section-header>
          </div>

          <!--
            Secondary School Students
          -->
          <ion-list v-if="isSecondaryStudent">
            <!-- High School -->
            <ion-input fill="outline" :label="`${t('school')}*`" label-placement="floating" type="text"
                        v-model="application.schoolName" :clearInput="true" @click="openSchoolSelectModal()" required></ion-input>

            <!-- Class -->
            <ion-input fill="outline" :label="`${t('class')}*`" label-placement="floating" type="text" v-model="application.class"
                      enterkeyhint="next" autocapitalize="characters" placeholder="e.g. 4C, 5A, 5F, 6L, etc."
                      @keyup.enter="focusInputField(studentNumberInput)" required></ion-input>

            <!-- Student Number (學號) -->
            <ion-input fill="outline" ref="studentNumberInput" :label="`${t('studentNumber')}*`" label-placement="floating" type="number"
                      v-model="application.studentNumber" enterkeyhint="next" placeholder="班號/學號" required></ion-input>

            <!-- Rank In Form -->
            <div v-if="!user?.id">
              <ion-select style="margin-bottom: 0" fill="outline" label="What was your rank in your form on the last exam?*" label-placement="floating"
                          interface="popover" v-model="application.rankInForm" required>
                <ion-select-option v-for="rankRange in OPTIONS.rankInForm" :key="rankRange" :value="rankRange">
                  {{ rankRange }}
                </ion-select-option>
              </ion-select>
              <input type="text" v-model="application.rankInForm" required style="opacity: 0; height: 0.01px" />

              <!-- Studying Curriculum -->
              <ion-select fill="outline" label="Studying curriculum" label-placement="floating" interface="popover" v-model="application.studyingCurriculum">
                <ion-select-option v-for="curriculum in OPTIONS.curriculums" :key="curriculum" :value="curriculum">
                  {{ curriculum }}
                </ion-select-option>
              </ion-select>

              <!-- Year HKDSE -->
              <div>
                <ion-select style="margin-bottom: 0" fill="outline" label="In which year will you graduate / take the HKDSE?*" label-placement="floating"
                            interface="popover" v-model="application.yearDSE">
                  <ion-select-option v-for="year in OPTIONS.yearDSE" :key="year" :value="year">
                    {{ year }} ({{ getSecStudentForm(year) }})
                  </ion-select-option>
                </ion-select>
                <input type="text" v-model="application.yearDSE" required style="opacity: 0; height: 0.1px" />
              </div>

              <!-- Studying Electives -->
              <ion-select fill="outline" label="Which elective subject(s) are your studying?*" label-placement="floating"
                          interface="popover" v-model="application.studyingElectives" multiple
                          v-if="application.yearDSE && Number(application.yearDSE) <= getF4YearDSE()">
                <ion-select-option v-for="elective in application.studyingCurriculum == 'IGCSE' ? OPTIONS.electivesIGCSE : OPTIONS.electives"
                                  :key="elective" :value="elective">
                  {{ elective }}
                </ion-select-option>
              </ion-select>

              <!-- Interested Disciplines 
              20241011: Not quite needed because of AchieveJUPAS (also to speed up reg process for AB3)
              <ion-input fill="outline" label="Which discipline(s) are you interested in?" label-placement="stacked"
                          placeholder="Please select up to 5 disciplines" type="text" v-model="application.interestedDisciplineNames"
                          @click="openDisciplineSelectModal()"></ion-input>
                          -->

              <!-- Referrer Name (for regapply) -->
              <div v-if="application.showReferrerInput">
                <ion-input fill="outline" label="Referrer's name (if applicable)" label-placement="floating" v-model="application.referrerName"></ion-input>
              </div>
          </div>

          </ion-list>

          <!--
            University Students
          -->
          <ion-list v-if="isUniversityStudent">

            <!-- University Email Address -->
            <ion-input fill="outline" ref="uniEmailInput" label="University email address*" label-placement="floating" inputmode="email"
                      type="email" v-model="application.uniEmail" enterkeyhint="next" required></ion-input>

            <!-- University Student ID -->
            <ion-input fill="outline" ref="uniStudentIdInput" label="University student ID*" label-placement="floating"
                      type="text" v-model="application.studentId" enterkeyhint="next" required></ion-input>

            <!-- Student Status / Nationality -->
            <ion-select fill="outline" label="Your student status*" label-placement="floating" interface="popover" v-model="application.studentStatus">
              <ion-select-option v-for="status in OPTIONS.studentStatus" :key="status" :value="status">
                {{ status }}
              </ion-select-option>
            </ion-select>

            <!-- Year of study -->
            <ion-select fill="outline" label="Current year of study*" label-placement="floating" interface="popover" v-model="application.yearOfStudy">
              <ion-select-option v-for="year in OPTIONS.yearOfStudy" :key="year" :value="year">
                {{ year }}
              </ion-select-option>
            </ion-select>

            <!-- Student group / stream -->
            <div v-if="selectedJobEX()['Student Groups']">
              <ion-select fill="outline" label="Your studying stream*" label-placement="floating" interface="popover" v-model="application.studyingStream">
                <ion-select-option v-for="stream in selectedJobEX()['Student Groups'].split(' , ')" :key="stream" :value="stream">
                  {{ stream }}
                </ion-select-option>
                <ion-select-option value="N/A">
                  N/A
                </ion-select-option>
              </ion-select>
            </div>

            <!-- Graduation Year -->
            <ion-select fill="outline" label="Graduation year*" label-placement="floating" interface="popover" v-model="application.gradYear">
              <ion-select-option v-for="year in OPTIONS.gradYear" :key="year" :value="year">
                {{ year }}
              </ion-select-option>
            </ion-select>

            <!-- How to Know -->
            <ion-select fill="outline" label="How do you know jobEX?*" label-placement="floating" interface="popover" v-model="application.howToKnow">
              <ion-select-option v-for="channel in OPTIONS.howToKnow" :key="channel" :value="channel">
                {{ channel }}
              </ion-select-option>
            </ion-select>

            <!-- Referrer -->
            <ion-input fill="outline" label="Referrer's mobile number (if applicable)" label-placement="floating" type="text" v-model="application.referrer"
                        helper-text="Both you and your referrer will get 1 more week of FDMT Career Atlass access"></ion-input>
          </ion-list>


          <!--<div v-if="isSecondaryStudent && application.yearDSE && Number(application.yearDSE) <= getF5YearDSE()">-->
          <div v-if="routeStudentGroup == 'ucircle' && isSecondaryStudent">
            <div class="ion-margin-top"><section-header title="For UCircle applicants only"></section-header></div>

            <!-- Referrer information -->
            <div class="ion-margin-bottom">
              <p><b>Who's your nominator for UCircle?</b></p>
              <ion-item lines="none" v-for="opt in ['Your teacher', 'Your UCircler classmate', 'Myself', 'I will not join UCircle.']" :key="opt">
                <input type="radio" slot="start" v-model="application.referrerRole" name="referrerRole" :value="opt"
                        @click="application.referrerRole = (application.referrerRole == opt ? '' : application.referrerRole)" />
                <ion-label class="ion-text-wrap"><p>{{ opt }}</p></ion-label>
              </ion-item>
            </div>

            <div v-if="isUCircleApplicant()">
              <!-- Nominator Info -->
              <div v-if="application.referrerRole != 'Myself'">
                <ion-input fill="outline" label="Referrer's name" label-placement="floating" v-model="application.referrerName" required></ion-input>

                <ion-input fill="outline" label="Referrer's HK mobile number" label-placement="floating" v-model="application.referrerPhone"
                            helper-text="8 digits without space in between" inputmode="numeric" maxlength="8" required
                            v-if="application.referrerRole.includes('UCircler')"></ion-input>
              </div>

              <!-- Question Answers -->
              <ion-textarea v-model="application.q1Ans" label="Q1. What's your most significant public speaking experience or experience of speaking on video?" label-placement="floating" fill="outline" :auto-grow="true" required>
              </ion-textarea>
              <ion-textarea v-model="application.q2Ans" label="Q2. What's your most significant leadership experience to date?" label-placement="floating" fill="outline" :auto-grow="true" required>
              </ion-textarea>
              <ion-textarea v-model="application.q3Ans" label="Q3. What are your hobbies and interests?" label-placement="floating" fill="outline" :auto-grow="true">
              </ion-textarea>

              <!-- Is prefect? -->
              <p class="question"><b>Are you a prefect in your school?</b></p>
              <ion-item lines="none" v-for="opt in ['Yes', 'No']" :key="opt">
                <input type="radio" slot="start" v-model="application.isPrefect" name="isPrefect" :value="opt == 'Yes' ? true : false" />
                <ion-label class="ion-text-wrap"><p>{{ opt }}</p></ion-label>
              </ion-item>

              <!-- How do you know UCircle? -->
              <p class="question"><b>How did you become aware of UCircle?*</b></p>
              <ion-item lines="none" v-for="opt in ['Teachers', 'Friends / Classmates', 'UCircle YouTube video', 'Others']" :key="opt">
                <input type="radio" slot="start" v-model="application.howToKnow" name="howToKnowUC" :value="opt" required />
                <ion-label class="ion-text-wrap"><p>{{ opt }}</p></ion-label>
              </ion-item>
            </div>
          </div>

          <!--
            UCircle Events
          -->
          <div v-if="isUCircleApplicant()">
            <div class="ion-margin-top"><section-header title="Enjoy privilege (you can do this later)"></section-header></div>
            <div class="ion-padding-horizontal">
              <p>You are now eligible to below exculsive events. Please enroll if you are interested.</p>
            </div>

            <ion-list>
              <ion-item lines="none" color="light">
                <ion-label class="ion-text-wrap"><h2><b>UCircle Events</b></h2></ion-label>
              </ion-item>
              <ion-item lines="full" v-for="ev in upcomingUCircleEvents" :key="ev.id">
                <ion-checkbox
                  slot="start"
                  style="margin-inline-start: 8px"
                  @update:modelValue="onCheckWorkEvent($event, ev)"
                  :modelValue="selectedEvents.find(e => e.id == ev.id) != null"
                  :legacy="true"
                ></ion-checkbox>

                <ion-label>
                  <h3>{{ ev.name }}</h3>
                  <p>{{ ev.formattedDateTime }}</p>
                </ion-label>
              </ion-item>
            </ion-list>
          </div>

          <!--
            Applying Events (for F.5 students only)
            20250418: hide this for now (to prevent wrong applications)
          <div v-show="isSecondaryStudent && Object.keys(filteredGroupedUpcomingEvents()).length > 0 &&
                        application.yearDSE && Number(application.yearDSE) <= getF5YearDSE() && !isUCircleApplicant()">
            <div class="ion-margin-top"><section-header title="Choose Events (you can do this later)"></section-header></div>
            <div class="ion-padding-horizontal">
              <p>Please select to enroll in the event(s) that you are interested in.</p>
            </div>

            <ion-list v-for="serviceId in Object.keys(filteredGroupedUpcomingEvents()).sort().reverse()" :key="serviceId">
              <ion-item lines="none" color="light">
                <ion-label class="ion-text-wrap">
                  <h2><b>{{ filteredGroupedUpcomingEvents()[serviceId][0]?.serviceName || serviceId }}</b></h2>
                </ion-label>
                <ion-button @click="openServiceModal(serviceId, filteredGroupedUpcomingEvents()[serviceId][0])" slot="end" fill="clear" size="small" class="no-text-transform">
                  Details
                </ion-button>
              </ion-item>
              <ion-item lines="full" v-for="ev in filteredGroupedUpcomingEvents()[serviceId]" :key="ev.id">
                <ion-icon v-if="prefilledEventId && ev.id == prefilledEventId" slot="start" :icon="checkmark"></ion-icon>
                <ion-checkbox v-else slot="start" style="margin-inline-start: 8px" :legacy="true"
                  @update:modelValue="onCheckWorkEvent($event, ev)"
                  :modelValue="selectedEvents.find(e => e.id == ev.id) != null"
                ></ion-checkbox>

                <ion-label>
                  <h3>{{ ev.name }}</h3>
                  <p>{{ ev.formattedDateTime }}</p>
                </ion-label>
              </ion-item>
            </ion-list>
          </div>
          -->

          <!--
            Applying Events (for Teacher only)
          -->
          <div v-show="isTeacher && upcomingTeacherEvents.length > 0 && (isPrincipal || selectedEvents.length > 0)">
            <div class="ion-margin-top"><section-header title="Choose Events"></section-header></div>

            <ion-item lines="full" v-for="ev in upcomingTeacherEvents" :key="ev.id">
              <!--<ion-icon v-if="prefilledEventId && ev.id == prefilledEventId" slot="start" :icon="checkmark"></ion-icon>-->
              <ion-checkbox
                slot="start"
                style="margin-inline-start: 8px"
                @update:modelValue="onCheckWorkEvent($event, ev)"
                :modelValue="selectedEvents.find(e => e.id == ev.id) != null"
              ></ion-checkbox>

              <ion-label class="ion-text-wrap">
                <h3>{{ ev.name }}</h3>
                <p>{{ ev.formattedDateTime }} {{ ev.venue ? `at ${ev.venue}`: "" }}</p>
              </ion-label>
            </ion-item>
          </div>

          <!--
            Applying Events (for University Students only)
          -->
          <div v-show="isUniversityStudent && selectedJobEXIntakeEvents().length > 0">
            <div class="ion-margin-top"><section-header title="Choose Events"></section-header></div>

            <ion-item lines="full" v-for="ev in selectedJobEXIntakeEvents()" :key="ev.id" v-show="ev.remainingSeats > 0 || ev.remainingSeats == ''">
              <ion-checkbox
                slot="start"
                style="margin-inline-start: 8px"
                @update:modelValue="onCheckWorkEvent($event, ev)"
                :modelValue="selectedEvents.find(e => e.id == ev.id) != null"
              ></ion-checkbox>

              <ion-label class="ion-text-wrap">
                <!--<h3>{{ ev.name }}</h3>-->
                <h2>{{ selectedJobEX()["jobEX Name"] }}: <b>{{ ev.formattedDateTime }} {{ ev.mode }}</b></h2>
                <h2 v-if="ev.remainingSeats != ''"><b>{{ Math.max(0, ev.remainingSeats) }}</b> seat{{ev.remainingSeats > 1 ? 's' : ''}} left</h2>
                <!--<p>{{ ev.mode }}<span v-if="ev.mode != 'Online'"> (exact venue to be disclosed upon successful registration)</span></p>-->
              </ion-label>
            </ion-item>
          </div>

          <ion-card>
            <ion-card-content v-html="settings.regFormTnc"></ion-card-content>
          </ion-card>

          <ion-button type="submit" shape="round" expand="block" :disabled="registeringAccount">
            {{ t('RegisterPage.register') }}
          </ion-button>

          <div ref="recaptchaDiv"><div id="grecaptcha-div"></div></div>
        </form>
      </ion-grid>
    </ion-content>
  </ion-page>
</template>

<script lang="ts">
// Vue core
import { computed, onMounted, reactive, ref, watch } from 'vue';

// icons
import { navigateOutline, personCircle, arrowBack, close, checkmark, options, } from 'ionicons/icons';

// components
import { IonPage, IonHeader, IonToolbar, IonTitle, IonContent,
        IonGrid, IonCol, IonRow, IonButtons, IonButton, IonBackButton,
        IonCard, IonCardTitle, IonCardHeader, IonCardContent, IonNote,
        IonItem, IonLabel, IonInput, IonTextarea, IonDatetime, IonAccordionGroup, IonAccordion,
        IonSelect, IonSelectOption, IonIcon, IonSpinner, IonReorderGroup, IonReorder, IonList, IonCheckbox,
        loadingController, alertController, onIonViewDidEnter, onIonViewWillLeave, modalController, } from '@ionic/vue';
import SchoolSelectModal from '@/components/secondary/SchoolSelectModal.vue';
import ABDisciplineSelectModal from '@/components/pss/ABDisciplineSelectModal.vue';
import RoleInSchoolSelectModal from '@/components/teacher/RoleInSchoolSelectModal.vue';
import RolesInClassesModal from '@/components/teacher/RolesInClassesModal.vue';

// firebase
import firebase from 'firebase/compat/app';
import { auth, credentialSignIn, phoneSignIn, customTokenSignin, } from '@/auth';
//import { cfaSignInPhone, cfaSignInPhoneOnCodeSent } from 'capacitor-firebase-auth'; // for fixing iOS reCAPTCHA bug
import { FirebaseAuthentication } from '@capacitor-firebase/authentication';


// utils
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';
import { useRoute, useRouter } from 'vue-router';
import config from '@/config';

// Services
import AuthService from '@/services/AuthService';
import UserService from '@/services/UserService';
import TeacherService from '@/services/TeacherService';

// types
import { Discipline, School, Service, Session, User } from '@/types';

export default {
  name: 'RegisterPage',
  components: { IonPage, IonHeader, IonToolbar, IonTitle, IonContent,
                IonGrid, IonCol, IonRow, IonButtons, IonButton, IonBackButton,
                IonCard, IonCardTitle, IonCardHeader, IonCardContent, IonNote,
                IonItem, IonLabel, IonInput, IonTextarea, IonDatetime, IonAccordionGroup, IonAccordion,
                IonSelect, IonSelectOption, IonIcon, IonSpinner, IonReorderGroup, IonReorder, IonList, IonCheckbox, },
  setup() {
    const { presentPrompt, uniqueId, getF4YearDSE, getF5YearDSE, getProxyImgLink, getLinkifiedText,
            openModal, openImageModal, openServiceModal, presentToast, getSecStudentForm, } = utils();
    const store = useStore();
    const route = useRoute();
    const router = useRouter();
    const settings = computed(() => store.state.settings);
    const loading = computed(() => store.state.loadingData);
    const schools = computed<School[]>(() => store.state.schools);
    const user = computed<User>(() => store.state.user); // logged in user
    const groupedUpcomingEvents = computed(() => store.getters.groupedUpcomingWorkEvents);
    const upcomingUCircleEvents = computed(() => store.getters.getUpcomingEventsByGroup('UCircle'));
    const upcomingTeacherEvents = computed(() => store.getters.getUpcomingEventsByGroup('Teacher Event'));
    const selectedEvents = ref<Session[]>([]);

    // Prefilled links / params
    const { schoolId: routeSchoolId, eventId: routeEventId, programId: routeProgramId, group: routeStudentGroup, ucirclerPhone, serviceId: prefilledServiceId } = route.params;
    const { schoolId: querySchoolId, e: queryEventId, phone: prefilledPhone, role: prefilledRole, p: queryProgramId, noVerifyPhone } = route.query;
    const prefilledProgramId = routeProgramId || queryProgramId; // university program
    const prefilledSchoolId = routeSchoolId || querySchoolId; // secondary school
    let prefilledEventId = routeEventId || queryEventId;
    const featuredEvent = ref<Session>();
    const featuredEventService = ref<Service>();
    const featuredService = computed<Service>(() => store.getters.getServiceById(routeStudentGroup == 'ucircle' ? 'ucircle' : prefilledServiceId)); // e.g. HealthcareWork

    const registeringAccount = ref(false);
    const recaptchaDiv = ref<any>(null);
    const fullNameInput = ref(null);
    const preferredNameInput = ref(null);
    const chineseNameInput = ref(null);
    const phoneInput = ref(null);
    const emailInput = ref(null);
    const pswInput = ref(null);
    const confirmPswInput = ref(null);
    const studentNumberInput = ref(null);

    const getPrefilledRole = () => {
      if (route.path.startsWith("/regevent")) return "";
      if (route.path.startsWith("/t") || prefilledServiceId) return "teacher";
      if (route.path.startsWith("/u") || prefilledProgramId) return "university-student";
      if (prefilledRole) return prefilledRole;
      //if (prefilledSchoolId || route.path.startsWith("/s")) return "secondary-school-student";
      if (route.path.startsWith("/s")) return "secondary-school-student";
      if (route.path.startsWith("/regapply")) return "secondary-school-student";
      if (route.path.startsWith("/c")) return "university-client";
      return "";
    }
    const application = reactive({
      schoolName: "",
      schoolId: prefilledSchoolId?.toString().replace("{{s}}", ""),
      fullName: "",
      chineseName: "",
      preferredName: "",
      gender: "",
      phone: (ucirclerPhone || prefilledPhone)?.toString().replace("{{p}}", "") ,
      email: "",

      // Secondary Student
      absIntakeId: "",
      class: "",
      studentNumber: "",
      studyingCurriculum: "HKDSE",
      studyingElectives: [],
      yearDSE: "",
      rankInForm: "",
      interestedDisciplines: [],
      interestedDisciplineIds: [],
      interestedDisciplineNames: "",
      userDisciplines: [],

      // Teacher
      roleInSchool: [],
      rolesInClasses: [],
      prefilledServiceId,
      prefilledEventId, // for records

      // University Student
      programId: prefilledProgramId || "", // jobEX program (e.g. MM jobEX)
      prefilledProgramId,
      uniEmail: "",
      studentId: "",
      studentStatus: "", // nationality
      yearOfStudy: "", // Year 1 / 2 / 3 / 4
      studyingStream: "", // Specialism
      gradYear: "",
      howToKnow: "",
      referrer: "",

      // University client
      institution: "", // text input
      position: "", // text input

      // General
      roles: getPrefilledRole(),
      isPhoneVerified: true,

      // For UCircle
      //wantToApplyUCircle: routeStudentGroup == 'ucircle' ? "Yes" : "",
      group: routeStudentGroup || "",
      q1Ans: "",
      q2Ans: "",
      q3Ans: "",
      isPrefect: null,
      referrerRole: routeStudentGroup == 'ucircle' ? "" : "I will not join UCircle.",
      referrerName: "",
      referrerPhone: "",

      // TBD
      password: "",
      confirmPsw: "",

      showReferrerInput: route.path.startsWith("/regapply"),
    });
    const isUCircleApplicant = () => (application.referrerRole && application.referrerRole != 'I will not join UCircle.');

    const syncSchoolDetails = (schoolId) => {
      const school =  schools.value.find(s => s.id == schoolId);
      if (school) {
        application.schoolName = `[${school.nameShort}] ${school.name}`;
        application.absIntakeId = school.absIntakeId;
      }
    }
    const prefillRegFormDetails = (res: User) => {
      watch(schools, () => {
        syncSchoolDetails(res.schoolId);
      })
      syncSchoolDetails(res.schoolId);
      application.schoolId = res.schoolId;
      application.fullName = res.fullName || "";
      application.chineseName = res.chineseName || "";
      application.preferredName = res.preferredName || "";
      application.gender = res.gender || "";
      application.phone = res.phone || "";
      application.email = res.email || "";
      application.class = res.class || "";
      application.studentNumber = res.studentNumber || "";
      application.studyingCurriculum = res.studyingCurriculum || "";
      const electives: any = Array.isArray(res.studyingElectives) ? res.studyingElectives : (res.studyingElectives?.split(" , ") || []).filter(e => e);
      application.studyingElectives = electives;
      application.yearDSE = res.yearDSE || "";
      application.rankInForm = res.rankInForm || "";
    }
    if (ucirclerPhone) {
      UserService.getUserByPhone(ucirclerPhone).then((res: User) => {
        if (res) prefillRegFormDetails(res);
      });
    }

    const isSecondaryStudent = computed(() => (application.roles == "secondary-school-student"));
    const isTeacher = computed(() => (application.roles == "teacher"));
    const isPrincipal = computed(() => (application.roleInSchool.some(r => ["Principal", "Vice Principal"].includes(r))));
    const isUniversityStudent = computed(() => (application.roles == "university-student"));
    const isUniversityClient = computed(() => (application.roles == "university-client"));

    const window: any = {
      recaptchaVerifier: undefined,
      confirmationResult: undefined,
      verificationId: undefined, // for iOS
    };
    const resetRecaptchaVerifier = () => {
      if (window.recaptchaVerifier) window.recaptchaVerifier.clear();
      if (recaptchaDiv.value) recaptchaDiv.value.innerHTML = '<div id="grecaptcha-div"></div>';
      window.recaptchaVerifier = new firebase.auth.RecaptchaVerifier('grecaptcha-div', {
        'size': 'invisible',
        'callback': (res: any) => {
          // reCAPTCHA solved, allow signInWithPhoneNumber.
        }
      });
    }

    // University Students
    const allJobEXes = computed(() => store.getters.activeJobEXes);
    const jobEXProgrammes = computed(() => store.getters.jobEXProgrammes);
    const selectedJobEX = () => (allJobEXes.value.find((jobEX: any) => jobEX.relatedProgramIds.includes(application.programId)) || {});
    const selectedJobEXIntakeEvents = () => {
      const relatedIntakeId = selectedJobEX()['intakes'] ? selectedJobEX()['intakes'][0]?.id : null;
      return relatedIntakeId ? store.state.sessions.filter(ev => {
        return ev.group == "jobEX" && ev.jobEXIntakeId == relatedIntakeId && (new Date(ev.startTimeStr) > new Date(+new Date() - ********) && ev.anchorEventId != "jobex-consultation-session")
      }) : [];
    }

    // methods
    const { t } = useI18n();
    const { presentAlert, presentVerifyCodeInputAlert, focusInputField, iPhoneNativeApp, } = utils();

    /**
     * Registration Functions
     */
    const setupUserAccount = (userId, existingFirebaseUser: any = null, verificationCode = "") => {
      if (application.roles == "teacher") application.yearDSE = "";
      application.class = (application.class || "").toUpperCase(); // make class uppercase
      store.commit('updateUser', { id: userId, ...application });
      store.commit('receiveUserFormResponses', []); // always empty array for new users

      const filteredEvents = selectedEvents.value.filter(ev => {
        if (application.roles == "teacher" && ev.id == prefilledEventId) {
          if (ev.group != "Teacher Event") return false; // not allow teachers applying students' events
        }
        return isUniversityStudent.value || ev.targetDSEYears.length == 0 || ev.targetDSEYears.includes(application.yearDSE);
      });
      const userEventResponses = filteredEvents.map(ev => ({
        id: `r${uniqueId()}`,
        sessionId: ev.id,
        eventName: ev.displayName,
        eventDateTime: ev.formattedDateTime,
        response: 'Yes',
        confirmed: 'Yes',
      }));
      store.commit('receiveUserEventResponses', userEventResponses);

      // for showing acknowledgment after registration
      if (prefilledEventId) {
        const ackAppliedEvent = filteredEvents.find(ev => ev.id == prefilledEventId);
        store.commit('receiveAckAppliedEvent', ackAppliedEvent);
      }
      if (prefilledServiceId) {
        store.commit('receiveAckInterestedService', featuredService);
      }

      const payload = {
        uid: userId,
        ...application,
        userEventResponses,
        existingFirebaseUser,
        intakeId: selectedJobEX()['intakes'] ? selectedJobEX()['intakes'][0].id : "", // jobEX student
        jobEXName: selectedJobEX()['id'], // jobEX student
        myCredentialsSections: config.cvSections, // jobEX student
      }
      UserService.createNewUser(payload).then(newUser => {
        if (verificationCode == 'skip') newUser.userInWaGroup = true; // users cannot join WhatsApp group with iPad
        if (Array.isArray(newUser.teacher)) newUser.teacher = newUser.teacher[0] || newUser.teacher; // should be object directly
        store.commit('receiveUser', { id: userId, ...newUser });
        store.dispatch('getPortalData', application.programId);

        // Register for specific service; store the preference for follow-up
        if (prefilledServiceId && newUser.teacher) {
          const teacherResponse = {
            intakeYear: featuredService.value?.currIntakeYear,
            eventName: "",
            type: 'service',
            response: "I am interested in this service. Please provide me with more details via WhatsApp.",
          };
          TeacherService.upsertTeacherResponse(null, prefilledServiceId, teacherResponse, newUser.phone, newUser.schoolId, newUser.id).then(responseObj => {
            store.commit('upsertTeacherResponse', { ...responseObj, updatedAt: new Date().toString() });
          });
        }

        // Check if WhatsApp group link is available
        const checkUserInterval = setInterval(() => {
          if (store.state.user.waGroupLink) {
            clearInterval(checkUserInterval);
          } else {
            UserService.getLoggedInUser().then(res => {
              store.commit('receiveUser', res);
              //if (res.sessionResponses) store.commit('receiveUserEventResponses', res.sessionResponses);
              clearInterval(checkUserInterval);
            });
          }
        }, 20000);

        // Get user WhatsApp group (according to row number)
        if (!isUniversityClient.value) { // skip if university client
          UserService.getNewUserWAGroup(newUser.row).then(res => {
            const { id: waGroupId, inviteLink: waGroupLink } = res;
            if (waGroupId) {
              store.commit('updateUser', { waGroupId, waGroupLink });
            }
          });
        }
      });

      if (user.value.id) { // Already logged in
        //router.replace('/profile');
        loadingController.dismiss();
      }
      if (isUCircleApplicant()) presentToast("You have submitted your application for UCircle successfully!", 5000, 'middle');
    }
    const verifyCode = async (verificationCode: any, existingFirebaseUser: firebase.UserInfo) => {
      const loading = await loadingController.create({ message: t('RegisterPage.registeringAccount') });
      await loading.present();
      try {
        let res: any = {};
        if (verificationCode == 'skip') { // if users log in with iPad
          alertController.dismiss();

          application.isPhoneVerified = false;
          res = await AuthService.createFirebaseAuthUser(application.phone);
          await customTokenSignin(res.token);
        } else {
          if (iPhoneNativeApp()) {
            const phoneCredential = firebase.auth.PhoneAuthProvider.credential(window.verificationId, verificationCode);
            res = await credentialSignIn(phoneCredential);
          } else {
            res = await window.confirmationResult.confirm(verificationCode);
          }
        }
        setupUserAccount(res.user?.uid, !!existingFirebaseUser, verificationCode);

        loading.dismiss();
        alertController.dismiss();

        registeringAccount.value = false;
      } catch (e: any) {
        registeringAccount.value = false;
        loading.dismiss();
        if (e.code == "auth/invalid-verification-code") { // wrong verification code
          presentAlert(t('RegisterPage.invalidVerificationCode'))
        } else {
          presentAlert(e.message);
        }
      }
    }
    const presentCodeInputAlert = async (phone: any, existingFirebaseUser: firebase.UserInfo) => {
      await presentVerifyCodeInputAlert(phone, (verificationCode: any) => {
        verifyCode(verificationCode, existingFirebaseUser);
      }, true);
      //}, !existingFirebaseUser);
    }
    const doRegister = async () => {
      
      presentPrompt(t('confirmSubmit'), async () => {
        //application.group = (application.wantToApplyUCircle == 'Yes' ? "ucircle" : "");
        application.group = (isUCircleApplicant() ? "ucircle" : "");
        const { phone, } = application;
        if (phone) {
          if (/^\d{8}$/.test(phone) === false) return presentAlert(t('RegisterPage.enterValidHKMobileNumber'));
          const loading = await loadingController.create({ duration: 30000 });
          await loading.present();

          if (user.value.id) { // Already logged in (no need phone verification)
            setupUserAccount(user.value.id, true, "");
          }
          else { // Phone verification required
            //const timeoutHandler = setTimeout(resetRecaptchaVerifier, 30000);
            registeringAccount.value = true;
            try {
              // 1. Check if phone already exists in our database
              const existingFirebaseUser = await AuthService.checkPhoneExists(phone);

              // 2. Verify the phone number (SMS code)
              const verifyingPhone = `+852${phone}`;
              if (noVerifyPhone == '1') verifyCode('skip', existingFirebaseUser); // skip verifiying number
              else {
                /*if (iPhoneNativeApp()) { // native iOS APP
                  const res: any = await FirebaseAuthentication.signInWithPhoneNumber({ phoneNumber: verifyingPhone });
                  loading.dismiss();
                  window.verificationId = res.verificationId;
                  presentCodeInputAlert(phone, existingFirebaseUser);
                } else { // Web App*/
                  if (settings.value.smsVerification == "FALSE") { // https://docs.google.com/spreadsheets/d/1MRnZtfwR6THzVaRB38Es0RGWhYloPdYJB-n7iC6qQSg/edit?gid=*********#gid=*********
                    verifyCode('skip', existingFirebaseUser); // for mass registration
                  } else {
                    const appVerifier = window.recaptchaVerifier;
                    const confirmationResult = await phoneSignIn(verifyingPhone, appVerifier);
                    window.confirmationResult = confirmationResult;
                    presentCodeInputAlert(phone, existingFirebaseUser);
                  //}
                }
              }
              registeringAccount.value = false;
            } catch (e: any) {
              registeringAccount.value = false;
              loadingController.dismiss();
              //clearTimeout(timeoutHandler);
              resetRecaptchaVerifier();
              if (e.code == "auth/invalid-phone-number") {
                presentAlert(t('RegisterPage.enterValidHKMobileNumber')); // invalid phone number
              } else {
                presentAlert(e.message);
              }
            }
          }
        } else {
          presentAlert(t('RegisterPage.enterPhoneAndPassword'));
        }
      })
    }
    onIonViewDidEnter(() => {
      setTimeout(resetRecaptchaVerifier, 200);
      if (user.value.id) prefillRegFormDetails(user.value); // student already logged in
    });
    onIonViewWillLeave(() => {
      //if (window.recaptchaVerifier) window.recaptchaVerifier.clear();
      if (recaptchaDiv.value) {
        recaptchaDiv.value.innerHTML = '<div id="grecaptcha-div"></div>';
      }
    })

    // Logged in user (ucircle form)
    watch(user, (curr) => {
      if (curr && curr.id) prefillRegFormDetails(curr); // student already logged in
    })

    // Prefill School
    if (prefilledSchoolId) {
      watch(schools, (curr) => {
        syncSchoolDetails(prefilledSchoolId);
      })
    }
    // Prefill Work Event
    if (prefilledEventId) {
      if (prefilledEventId.toString().toLowerCase() == 'econwork') prefilledEventId = "e7b060c3";
      if (prefilledEventId.toString().toLowerCase() == 'businesswork') prefilledEventId = "e071b0aa";

      watch(groupedUpcomingEvents, (curr) => {
        for (const key in curr) {
          const events: any = curr[key];
          for (const ev of events) {
            if (ev.id == prefilledEventId) {
              selectedEvents.value.push(ev);
              featuredEvent.value = ev;
              console.log(ev.serviceId);
              featuredEventService.value = store.getters.getServiceById(ev.serviceId);
              console.log('Featured Service', featuredEventService.value);
              application.yearDSE = (ev.targetDSEYears[0] || getF5YearDSE()).toString();
              break;
            }
          }
        }
      })

      watch(upcomingTeacherEvents, (curr) => {
        for (const ev of curr){
          if (ev.id == prefilledEventId){
            selectedEvents.value.push(ev);
          }
        }
      })
    }

    let selectModalOpened = false;

    return {
      // icons
      navigateOutline, personCircle, arrowBack, close, checkmark,

      // variables
      settings, loading,
      OPTIONS: config.formOptions,
      user, // logged in user
      application, registeringAccount,
      recaptchaDiv,
      phoneInput, emailInput, pswInput, confirmPswInput,
      fullNameInput, preferredNameInput, chineseNameInput,
      groupedUpcomingEvents, selectedEvents,
      studentNumberInput,
      prefilledEventId,

      isSecondaryStudent, isTeacher, isPrincipal,
      isUniversityStudent, isUniversityClient,

      // methods
      getProxyImgLink,
      openRoleInSchoolSelectModal : async () => {
        if (!selectModalOpened) {
          selectModalOpened = true;
          const modal = await modalController.create({
            component: (RoleInSchoolSelectModal as any),
            componentProps: { selectedRolesInSchool: application.roleInSchool },
          });
          modal.onDidDismiss().then(({ data }) => {
            if (data && data.workingSelectedValues) {
              application.roleInSchool = data.workingSelectedValues.value;
            }
            selectModalOpened = false;
          });
          return modal.present();
        }
      },
      openRolesInClassesModal: async () => {
        if (!selectModalOpened) {
          selectModalOpened = true;
          const modal = await modalController.create({
            component: (RolesInClassesModal as any),
            componentProps: { prefilledRolesInClasses: application.rolesInClasses },
          });
          modal.onDidDismiss().then(({ data }) => {
            if (data && data.rolesInClasses) {
              application.rolesInClasses = data.rolesInClasses;
            }
            selectModalOpened = false;
          });
          return modal.present();
        }
      },

      t, focusInputField, doRegister,
      featuredEvent, featuredEventService, openImageModal, openServiceModal,
      getF4YearDSE, getF5YearDSE, getSecStudentForm,
      onCheckWorkEvent: (checked: any, workEvent: Session) => {
        if (checked) {
          if (selectedEvents.value.find(e => e.id == workEvent.id) == null) {
            selectedEvents.value.unshift(workEvent);
          }
        }
        else {
          const idx = selectedEvents.value.findIndex(p => p.id == workEvent.id);
          if (idx !== -1) selectedEvents.value.splice(idx, 1);
        }
      },

      openDisciplineSelectModal: async () =>  {
        if (!selectModalOpened) {
          selectModalOpened = true;
          const modal = await modalController.create({
            component: ABDisciplineSelectModal,
            componentProps: {
              prefilledDisciplines: application.interestedDisciplines.slice(),
              oldUserDisciplines: application.userDisciplines?.slice() || [],
              fromRegForm: true,
            }
          });
          modal.onDidDismiss().then(({ data }) => {
            if (data && data.chosen) {
              application.interestedDisciplines = data.chosen;
              application.interestedDisciplineIds = data.chosen.map(d => d.id);
              application.interestedDisciplineNames = data.chosen.map(d => d.name).join(" , ");
              application.userDisciplines = data.userDisciplines;
            }
            selectModalOpened = false;
          });
          return modal.present();
        }
      },
      openSchoolSelectModal: async () => {
        if (!selectModalOpened) {
          selectModalOpened = true;
          const modal = await modalController.create({
            component: SchoolSelectModal,
            componentProps: { schools: schools.value }
          });
          modal.onDidDismiss().then(({ data }) => {
            if (data && data.selectedSchool) {
              application.schoolName = data.selectedSchool;
              application.schoolId = data.schoolId || data.selectedSchool;
              const school =  schools.value.find(s => s.id == application.schoolId);
              if (school) {
                application.absIntakeId = school.absIntakeId;
              }
            }
            selectModalOpened = false;
          });
          return modal.present();
        }
      },
      filteredGroupedUpcomingEvents: () => {
        const showOnlyFeaturedEvent = featuredEvent.value?.name.toLowerCase().includes("band a");
        const res = {};
        for (const key in groupedUpcomingEvents.value) {
          const events: Session[] = groupedUpcomingEvents.value[key];
          for (const ev of events) {
            if (showOnlyFeaturedEvent && ev.id != featuredEvent.value?.id) {
              continue; // special handling for Band A workshop (not show others)
            }
            if (ev.targetDSEYears.length == 0 || ev.targetDSEYears.includes(application.yearDSE)) {
              (res[key] = res[key] || []).push(ev);
            }
          }
        }
        return res;
      },


      // University Students
      allJobEXes, jobEXProgrammes,
      selectedJobEX, selectedJobEXIntakeEvents,

      // UCircle
      isUCircleApplicant,
      upcomingUCircleEvents,
      routeStudentGroup,

      // Teacher
      upcomingTeacherEvents,
      featuredService, prefilledServiceId, getLinkifiedText,
    }
  },
}
</script>

<style scoped>
  ion-grid {
    height: 100%;
  }
  ion-content {
    --padding-bottom: 100px;
  }
  ion-input, ion-select, ion-textarea {
    margin-bottom: 20px;
  }
</style>
