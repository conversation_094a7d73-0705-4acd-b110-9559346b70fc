<template>
  <ion-page>
    <ion-header>
      <ion-grid fixed>
        <ion-toolbar>
          <ion-buttons slot="start">
            <ion-button router-link="/home"><ion-icon slot="icon-only" :icon="arrowBack"></ion-icon></ion-button>
          </ion-buttons>
          <ion-title>
            <ion-label>
              <h2>{{ loading ? '...' : (profession ? profession.name : currProfessionName) }}</h2>
              <p>{{ profession.nameChinese }}</p>
            </ion-label>
          </ion-title>
        </ion-toolbar>
      </ion-grid>
    </ion-header>
    <ion-content :fullscreen="true">

      <!-- Loading Data -->
      <div class="spin" v-if="profession == null">
        <ion-spinner></ion-spinner>
      </div>

      <!-- Data loaded -->
      <ion-grid fixed v-else>
        <profession-details :profession="profession"></profession-details>
      </ion-grid>
    </ion-content>

    <ion-footer v-if="user.id">
      <ion-toolbar class="ion-text-center">
        <ion-button class="no-text-transform" @click="addToUserTargetProfession(profession)"
                    v-if="!user.step1OrderedProfessionIds?.split(' , ').includes(profession.id)">
          <ion-icon slot="start" :icon="add"></ion-icon>
          Add to my target profession
        </ion-button>

        <ion-button class="no-text-transform" color="success" disabled v-else>
          Added to my target profession
          <ion-icon slot="end" :icon="checkmark"></ion-icon>
        </ion-button>
      </ion-toolbar>
    </ion-footer>
  </ion-page>
</template>

<script lang="ts">
// Vue reactivity
import { computed, watch } from 'vue';

// icons
import { informationCircleOutline, close, add, checkmark, arrowBack, } from 'ionicons/icons';

// components
import { IonPage, IonGrid, IonHeader, IonFooter, IonToolbar, IonTitle, IonContent, IonSpinner,
        IonButtons, IonButton, IonIcon,
        onIonViewDidEnter,
        onIonViewWillLeave, } from '@ionic/vue';
import ProfessionDetails from '@/components/pss/ProfessionDetails.vue';

// API services
import { useStore } from '@/store';
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useRoute } from 'vue-router';

// types
import { Profession } from '@/types';
import ABSService from '@/services/ABSService';
import UserService from '@/services/UserService';

export default {
  name: 'ProfessionPage',
  components: { IonPage, IonGrid, IonHeader, IonFooter, IonToolbar, IonTitle, IonContent, IonSpinner,
                IonButtons, IonButton, IonIcon,
                ProfessionDetails, },
  setup() {
    const store = useStore();
    const { t } = useI18n();
    const route = useRoute();
    const { name: currProfessionName } = route.params;

    // 1. declare state variables (ref to make them reactive)
    const loading = computed(() => store.state.loadingPortalData);
    const profession = computed<Profession>(() => store.getters.getProfessionByName(currProfessionName));
    const user = computed(() => store.state.user);

    // methods or filters
    const { formatDate } = utils();

    let accessTime, duration = 0, counterInterval;
    onIonViewDidEnter(() => {
      // Initial Access
      ABSService.insertPageAccessRecord(`ProfessionPage | ${currProfessionName}`, new Date(), null);

      // Record Access & Leave Time
      accessTime = new Date();
      counterInterval = setInterval(() => (duration++), 1000);
    });
    onIonViewWillLeave(() => {
      if (accessTime && duration >= 3) {
        const { id, name } = profession.value || {};
        ABSService.insertPageAccessRecord(`ProfessionPage | ${name} | ${id}`, accessTime, new Date(accessTime.getTime() + duration*1000));
        accessTime = undefined;
        duration = 0; // reset;
        clearInterval(counterInterval);
      }
    });

    watch(user, () => {
      UserService.addUserBrowsedProfession(profession.value.id); // add to browse history
      store.commit('addUserBrowsedProfession', profession.value.id);
    });
    
    // 3. return variables & methods to be used in template HTML
    return {
      // icons
      informationCircleOutline, close, add, checkmark, arrowBack,

      // variables
      currProfessionName, loading, profession,
      user,

      // methods
      t, formatDate,
      addToUserTargetProfession: (profession: any) => {
        const { lastSelectedProfessions } = user.value;

        // Add to target profession list
        const newSelectedProfessions = [profession, ...(lastSelectedProfessions || [])];
        const newStep1OrderedProfessionIds = newSelectedProfessions.map(p => p.id).join(" , ");

        // Update DB
        ABSService.saveUserChoices({
          step1OrderedProfessionIds: newStep1OrderedProfessionIds
        });

        // Update Store
        store.commit('updateUser', {
          step1OrderedProfessionIds: newStep1OrderedProfessionIds,
          lastSelectedProfessions: newSelectedProfessions.slice(),
        });
      }
    }
  },
}
</script>

<style scoped>
</style>