<template>
  <ion-page>
    <ion-header>
      <ion-grid fixed>
        <ion-toolbar>
          <ion-title class="ion-text-center">
            {{ loading ? '...' : (sector ? sector.name : currSectorName) }}
          </ion-title>
        </ion-toolbar>
      </ion-grid>
    </ion-header>
    <ion-content :fullscreen="true">
      <ion-grid fixed>
        <!-- Loading Data -->
        <div class="spin" v-if="loading">
          <ion-spinner></ion-spinner>
        </div>

        <!-- Data loaded -->
        <sector-details :sector="sector" :segmentId="targetSegmentId" v-else></sector-details>
      </ion-grid>
    </ion-content>
  </ion-page>
</template>

<script lang="ts">
// Vue reactivity
import { computed } from 'vue';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonItem, IonLabel, IonIcon, IonGrid, IonPage, IonSpinner, } from '@ionic/vue';
import SectorDetails from '@/components/pss/SectorDetails.vue';

// API services
import { useStore } from '@/store';
import { useRoute } from 'vue-router';

export default {
  name: "SectorPage",
  components: { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonItem, IonLabel, IonIcon, IonGrid, IonPage, IonSpinner,
                SectorDetails, },
  setup() {
    const store = useStore();
    const route = useRoute();

    const currSectorName = route.params.name;
    const currSegmentName = route.params.segmentName || route.query.segmentName || '';
    console.log(currSegmentName);

    // 1. declare state variables (ref to make them reactive)
    const loading = computed(() => store.state.loadingPortalData);

    // methods or filters
    const sector = computed(() => currSectorName ? store.getters.getSectorByName(currSectorName) : store.getters.getSectorBySegmentName(currSegmentName));
    const targetSegmentId = computed(() => store.getters.getSegmentIdByName(currSegmentName));


    // 3. return variables & methods to be used in template HTML
    return {
      // icons

      // variables
      loading, sector, currSectorName, targetSegmentId,

      // methods
    }
  },
}
</script>

<style scoped>
</style>