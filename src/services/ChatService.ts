import firebase from "firebase/compat/app";
import 'firebase/compat/functions';

export default {
  async getUserChatbotPrompts(type = null, professionId = null) {
    return (await firebase.app().functions('asia-east2').httpsCallable('chat-getUserChatbotPrompts')({ type, professionId, })).data;
  },
  async insertUserChatbotPrompts(prompts) {
    return (await firebase.app().functions('asia-east2').httpsCallable('chat-insertUserChatbotPrompts')({ prompts })).data;
  },
}