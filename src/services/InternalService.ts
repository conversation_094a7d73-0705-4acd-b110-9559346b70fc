/**
 * Mainly for the tab 'FDMT Staff'
 */
import firebase from "firebase/compat/app";
import 'firebase/compat/functions';
import { convertKeysToCamelCase, unpackJSONHData, uploadImage } from '@/services/utils';

export default {
  async queryUsers(schoolIds: any = null, roles = 'secondary-school-student') {
    const packedData = (await firebase.app().functions('asia-east2').httpsCallable('internal-queryUsers')({ schoolIds, roles })).data;
    return unpackJSONHData(packedData, false);
  },

  async sendWhatsAppMsgToUsers(users: any[], msg: string, photoFile: any) {
    let imgLink = "";
    if (photoFile) imgLink = await uploadImage(photoFile, `sendWhatsAppMsgToUsers`);
    console.log(imgLink);
    return (await firebase.app().functions('asia-east2').httpsCallable('internal-sendWhatsAppMsgToUsers')({ users, msg, imgLink })).data;
  },

  async getJUPASConditionSets() {
    return (await firebase.app().functions('asia-east2').httpsCallable('internal-getJUPASConditionSets')()).data;
  },

  async addJUPASConditionSet(name: string, conditions: any[]) {
    return (await firebase.app().functions('asia-east2').httpsCallable('internal-addJUPASConditionSet')({ name, conditions })).data;
  },

  async updateJUPASConditionSet(id: any, name: string, conditions: any[]) {
    return (await firebase.app().functions('asia-east2').httpsCallable('internal-updateJUPASConditionSet')({ id, name, conditions })).data;
  },

  async deleteJUPASConditionSet(id: any) {
    return (await firebase.app().functions('asia-east2').httpsCallable('internal-deleteJUPASConditionSet')({ id })).data;
  },
}