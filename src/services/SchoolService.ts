import firebase from "firebase/compat/app";
import 'firebase/compat/functions';

export default {
  async updateSchool(schoolId, data = {}) {
    return (await firebase.app().functions('asia-east2').httpsCallable('school-updateSchool')({ schoolId, data })).data;
  },

  async getSchools() {
    return (await firebase.app().functions('asia-east2').httpsCallable('school-getSchools')()).data;
  },

  async getSchoolById(schoolId: any) {
    return (await firebase.app().functions('asia-east2').httpsCallable('school-getSchoolById')({ schoolId })).data[0];
  },
}