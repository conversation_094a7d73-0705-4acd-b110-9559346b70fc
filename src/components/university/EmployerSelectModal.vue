<template>
  <ion-header>
    <ion-toolbar>
      <ion-title style="padding-right: 0">
        <ion-label class="ion-text-wrap">
          <h2 style="font-size: 20px;">
            Entry employer
          </h2>
        </ion-label>
      </ion-title>
      <ion-buttons slot="start">
        <ion-button slot="icon-only" @click="closeModal()"><ion-icon :icon="arrowBack"></ion-icon></ion-button>
      </ion-buttons>
    </ion-toolbar>

    <!-- Toggle choose new / show selected -->
    <ion-toolbar style="--min-height: 36px">
      <ion-segment mode="ios" v-model="selectedMode" scrollable>
        <ion-segment-button :value="1">
          <ion-label class="ion-text-wrap">
            <p><b>1. Segment(s)</b></p>
          </ion-label>
        </ion-segment-button>
        <ion-segment-button :value="2" :disabled="employerOptions.length == 0">
          <ion-label class="ion-text-wrap">
            <p><b>2. Employer(s)</b></p>
          </ion-label>
        </ion-segment-button>
        <ion-segment-button :value="3" :disabled="chosenEmployers.length == 0">
          <ion-label class="ion-text-wrap">
            <p><b>3. Prioritize ({{ chosenEmployers.length }})</b></p>
            <p>(Drag & move)</p>
          </ion-label>
        </ion-segment-button>
      </ion-segment>
    </ion-toolbar>

    <!-- Searchbar Input -->
    <ion-toolbar v-show="selectedMode == 2">
      <ion-searchbar style="padding-top: 15px" mode="ios" v-model="searchKeyword" :placeholder="t('search')"
                    @ionFocus="isSearching = true" @ionBlur="isSearching = false" @keyup.enter="(e) => e.target.blur()"></ion-searchbar>
    </ion-toolbar>
  </ion-header>
  
  <ion-content :fullscreen="true">
    <!--
      Choose Segments
      -->
    <div v-show="selectedMode == 1">
      <ion-list>
        <ion-item v-for="segment in segmentOptions" :key="segment.id">
          <ion-checkbox
            justify="start"
            labelPlacement="end"
            @update:modelValue="onCheckSegment($event, segment)"
            :modelValue="chosenSegments.find(s => s.id == segment.id) != null"
          >{{ segment.name }}</ion-checkbox>

          <ion-button @click="openSegmentModal(segment)" color="medium" slot="end" fill="clear" size="small"
                      style="margin-inline-end: 16px; text-transform: none">
            More
          </ion-button>
        </ion-item>
      </ion-list>
    </div>

    <!--
      Choose / Update Employers
      -->
    <div v-show="selectedMode == 2">
      
      <!-- List of options -->
      <ion-list>
        <ion-item v-for="employer in employerOptions" :key="employer.id"
                  v-show="!searchKeyword || employer.name.toLowerCase().includes(searchKeyword.toLowerCase().trim())">
          <ion-checkbox
            justify="start"
            labelPlacement="end"
            @update:modelValue="onCheckEmployer($event, employer)"
            :modelValue="chosenEmployers.find(s => s.id == employer.id) != null"
          >
            <ion-label class="ion-text-wrap">
              {{ employer.name }}
            </ion-label>
          </ion-checkbox>

          <ion-button :href="employer.company_url" target="_blank" color="medium" slot="end" fill="clear" size="small"
                      style="margin-inline-end: 16px; text-transform: none" v-show="employer.company_url">
            More
          </ion-button>
        </ion-item>
      </ion-list>
    </div>

    <!--
      Selected Employers
      -->
    <div v-show="selectedMode == 3">
      <ion-reorder-group @ionItemReorder="doReorder($event, chosenEmployers)" :disabled="false">
        <ion-item lines="full" v-for="(segment, idx) in chosenEmployers" :key="idx" style="--min-height: 55px;">
          <ion-reorder mode="ios" slot="start"></ion-reorder>
          <ion-label class="ion-text-wrap">
            {{ idx+1}}. {{ segment.name }}
          </ion-label>
          <ion-buttons slot="end">
            <ion-button @click.stop="chosenEmployers.splice(idx, 1)">
              <ion-icon slot="icon-only" :icon="trashOutline"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-item>
      </ion-reorder-group>

      <!--
        Ideal candidate reasons / claims
      -->
      <ion-card v-show="chosenEmployers.length > 0">
        <ion-card-content>
          <ion-row>
            <ion-col class="ion-margin-bottom">
            <p>I am an ideal candidate for them because...</p>
            </ion-col>
          </ion-row>

          <ion-textarea class="ion-margin-bottom" label="Claim 1" label-placement="floating" fill="outline" :auto-grow="true"
                        v-model="data.claim1" placeholder="Reason for hiring me / Claim 1"></ion-textarea>

          <ion-textarea class="ion-margin-bottom" label="Claim 2" label-placement="floating" fill="outline" :auto-grow="true"
                        v-model="data.claim2" placeholder="Reason for hiring me / Claim 2"></ion-textarea>

          <ion-textarea label="Claim 3" label-placement="floating" fill="outline" :auto-grow="true"
                        v-model="data.claim3" placeholder="Reason for hiring me / Claim 3"></ion-textarea>
        </ion-card-content>
      </ion-card>
    </div>

  </ion-content>

  <ion-footer>
    <ion-toolbar>
      <ion-row>
        <ion-col size="2" v-show="selectedMode > 1">
          <ion-button color="dark" fill="clear" @click="selectedMode--">
            <ion-icon slot="icon-only" :icon="arrowBack"></ion-icon>
          </ion-button>
        </ion-col>
        <ion-col v-show="selectedMode < 3">
          <ion-button expand="block" @click="onClickNext()" :disabled="isNextDisabled()">
            Next
            <ion-icon slot="end" :icon="arrowForward"></ion-icon>
          </ion-button>
        </ion-col>
        <ion-col v-show="selectedMode >= 3">
          <ion-button color="success" expand="block" @click="confirmSelect()">
            Done
            <ion-icon slot="end" :icon="checkmark"></ion-icon>
          </ion-button>
        </ion-col>
      </ion-row>
    </ion-toolbar>
  </ion-footer>
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, onMounted, reactive, watch, } from 'vue';

// icons
import { add, close, checkmark, arrowUp,  arrowForward, arrowBack, checkbox, trashOutline, } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
        IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonReorderGroup, IonReorder,
        IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption, IonCheckbox,
        IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid,
        IonTextarea, } from '@ionic/vue';
import SectorModal from '@/components/pss/SectorModal.vue';

// composables
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';

// services
import PortalService from '@/services/PortalService';

// types
import { Segment } from '@/types';

export default defineComponent({
  name: 'EmployerSelectModal',
  props: ["prefilledEmployers", "prefilledClaim1", "prefilledClaim2", "prefilledClaim3",
          "prefilledSegments", "segmentOptions"],
  components: { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
                IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonReorderGroup, IonReorder,
                IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption, IonCheckbox,
                IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid,
                IonTextarea, },
  setup(props) {
    // methods or filters
    const store = useStore();
    const { closeModal, doReorder, openModal, } = utils();
    const { t } = useI18n();

    const selectedMode = ref(1);
    const searchKeyword = ref("");
    const isSearching = ref(false);
    
    const employerOptions = ref([]);
    const chosenSegments = ref<Segment[]>(props.prefilledSegments || []);
    const chosenEmployers = ref<any[]>(props.prefilledEmployers || []);
    const data = reactive({
      claim1: props.prefilledClaim1 || "",
      claim2: props.prefilledClaim2 || "",
      claim3: props.prefilledClaim3 || "",
    })

    const confirmSelect = async () => {
      await closeModal({ selectedEmployers: chosenEmployers.value, ...data }); // return selected discipline & order here
    };

    const refreshRelatedEmployers = async (targetSegments: Segment[]) => {
      let relatedEmployers: any = [];
      if (targetSegments.length > 0) {
        const res =  await PortalService.getEmployersBySegmentIds(targetSegments.map((s: any) => s.id));
        for (const employerArr of Object.values(res['local'])) {
          relatedEmployers = relatedEmployers.concat(employerArr);
        }
        for (const employerArr of Object.values(res['mnc'])) {
          relatedEmployers = relatedEmployers.concat(employerArr);
        }
      }
      // filter out selected employers that are not in the new list
      const relatedEmployerIds = relatedEmployers.map((e: any) => e.id);
      chosenEmployers.value = chosenEmployers.value.filter((e: any) => relatedEmployerIds.includes(e.id));
      employerOptions.value = relatedEmployers;
    }

    watch(selectedMode, (currMode) => {
      if (currMode == 2) {
        refreshRelatedEmployers(chosenSegments.value);
      }
    })

    // 3. return variables & methods to be used in template HTML
    return {
      // icons
      add, close, checkmark, arrowUp, arrowForward, arrowBack, checkbox, trashOutline,

      // variables
      chosenSegments, chosenEmployers, employerOptions,
      selectedMode, searchKeyword, isSearching,
      data,

      // methods
      t, confirmSelect,
      closeModal,
      doReorder,

      onClickNext: () => {
        if (selectedMode.value == 1) {
          refreshRelatedEmployers(chosenSegments.value);
        }
        selectedMode.value++;
      },

      isNextDisabled: () => {
        if (selectedMode.value == 1) return chosenSegments.value.length == 0;
        return false;
      },

      // Segments
      openSegmentModal: async (segment: any) => (await openModal(SectorModal, { sectorId: segment.sectorId, segmentId: segment.id })),
      onCheckSegment: (checked: any, chosen: any) => {
        if (checked) {
          if (chosenSegments.value.find(d => d.id == chosen.id) == null) {
            chosenSegments.value.push(chosen);
          }
        }
        else {
          const idx = chosenSegments.value.findIndex(d => d.id == chosen.id);
          if (idx !== -1) chosenSegments.value.splice(idx, 1);
        }
      },

      // Employers
      onCheckEmployer: (checked: any, chosen: any) => {
        if (checked) {
          if (chosenEmployers.value.find(d => d.id == chosen.id) == null) {
            chosenEmployers.value.push(chosen);
          }
        }
        else {
          const idx = chosenEmployers.value.findIndex(d => d.id == chosen.id);
          if (idx !== -1) chosenEmployers.value.splice(idx, 1);
        }
      },
    }
  }
});
</script>

<style scoped>
  
  
</style>
