<template>
  <ion-header>
    <ion-toolbar>
      <!-- Back buttons (back / close modal) -->
      <ion-buttons slot="start">
        <ion-button slot="icon-only" @click="confirmSelect(true)">
          <ion-icon :icon="close"></ion-icon>
        </ion-button>
      </ion-buttons>

      <ion-title style="padding-right: 0">
        <ion-label class="ion-text-wrap">
          <p v-if="isSecSchoolStudents">Target 1-2 professions</p>
          <p v-else>Target 1-2 professions & 2-3 employer segments for each</p>
        </ion-label>
      </ion-title>
    </ion-toolbar>
  </ion-header>
  
  <ion-content :fullscreen="true">
    <ion-reorder-group @ionItemReorder="doReorder($event, selectedProfessions)" :disabled="false">
      <div v-for="(item, idx) in selectedProfessions" :key="item.id" :value="item.id">
        <!--
          Selected Professions
        -->
        <ion-item>
          <ion-reorder slot="start" style="margin-inline-end: 8px"></ion-reorder>
          <ion-label class="ion-text-wrap" @click="openProfessionModal(item.id)">
            <h2><b>{{ idx+1 }}. {{ item.name }}</b></h2>
          </ion-label>

          <ion-buttons>
            <ion-button @click.stop="onDeleteChosenProfession(idx, item)">
              <ion-icon size="small" slot="icon-only" :icon="trashOutline"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-item>

        <!--
          Selected Segments under the profession
        -->
        <ion-list style="padding: 0 0 0 40px; border-bottom: 1px solid var(--ion-color-dark)" v-show="!isSecSchoolStudents && idx <= 1">
          <ion-accordion-group>
            <ion-accordion>
              <ion-item lines="none" button @click="openUniSegmentDeckModal(item)" slot="header">
                <!--<ion-label class="ion-text-wrap" style="flex: none">-->
                <ion-label class="ion-text-wrap"><p>working in</p></ion-label>
                <ion-buttons slot="end" style="height: 24px">
                  <ion-button>
                    <ion-icon size="small" slot="icon-only" :icon="refresh"></ion-icon>
                  </ion-button>
                </ion-buttons>
              </ion-item>

              <div slot="content">
                <ion-reorder-group @ionItemReorder="doReorder($event, item.selectedSegments)" :disabled="false">
                  <ion-item v-for="(s, idx) in item.selectedSegments" :key="s.id" button>
                    <ion-reorder slot="start"></ion-reorder>
                    <ion-label class="ion-text-wrap" @click.stop="openSegmentModal(s)">
                      <p><b>{{ s.name }}</b></p>
                    </ion-label>
                    <ion-buttons>
                      <ion-button @click.stop="onDeleteChosenSegment(item, idx, s)">
                        <ion-icon size="small" slot="icon-only" :icon="trashOutline"></ion-icon>
                      </ion-button>
                    </ion-buttons>
                  </ion-item>
                </ion-reorder-group>
              </div>
            </ion-accordion>
          </ion-accordion-group>
        </ion-list>
      </div>
    </ion-reorder-group>

    <ion-row class="ion-margin-top ion-justify-content-center">
      <ion-button color="primary" class="add-item-btn" @click="openUniProfessionDeckModal()">
        <ion-icon slot="start" :icon="add"></ion-icon>
        Professions
      </ion-button>
    </ion-row>
  </ion-content>
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, watch, reactive, onMounted, onUnmounted, onBeforeUnmount } from 'vue';

// icons
import { add, close, checkmark, arrowUp, arrowForward, arrowBack, trashOutline,
        thumbsUpOutline, thumbsDownOutline, thumbsUp, thumbsDown, heart, heartOutline,
        chevronBack, chevronForward, repeat, search, createOutline, pencil, refresh, } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonReorder, IonReorderGroup,
        IonList, IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonTextarea, IonFab, IonFabButton,
        IonAccordion, IonAccordionGroup,
        isPlatform, modalController, } from '@ionic/vue';
import ProfessionModal from '@/components/pss/ProfessionModal.vue';
import UniProfessionDeckModal from '@/components/university/UniProfessionDeckModal.vue';
import SectorModal from '@/components/pss/SectorModal.vue';

// composables / services
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';
import { Profession, ProfessionTab, Segment, UserProfession, UserSegment } from '@/types';

export default defineComponent({
  name: 'UniProfessionResultPageModal',
  props: ["prefilledProfessions", "oldUserProfessions", "oldUserSegments", "isYear1", "isSecSchoolStudents"],
  components: { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonReorder, IonReorderGroup,
                IonList, IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonTextarea, IonFab, IonFabButton,
                IonAccordion, IonAccordionGroup, },
  setup(props) {
    // methods or filters
    const store = useStore();
    const { openModal, closeModal, doReorder, syncChosenItems, linkNestedChosenItems, processUserItems, } = utils();
    const { t } = useI18n();

    const userSegments = ref<UserSegment[]>(props.oldUserSegments || []);
    const allSegments = computed<Segment[]>(() => store.state.allSegments);

    const selectedProfessions = ref<Profession[]>(props.prefilledProfessions || []);
    const userProfessions = ref<UserProfession[]>(props.oldUserProfessions || []);
    const allProfessions = ref<Profession[]>(store.state.allProfessions);

    const confirmSelect = async (noLoading = false) => {
      for (const prof of selectedProfessions.value) {
        if (prof.selectedSegments) {
          userSegments.value = processUserItems(prof.selectedSegments, userSegments.value, [], 'segmentId', store.state.user.id)
        }
      }
      await closeModal({
        "selectedProfessions": selectedProfessions.value, noLoading,
        "userProfessions": userProfessions.value,
        "userSegments": userSegments.value,
      }); // return selected items & order here
    };
    const openProfessionModal = async (professionId: any) => await openModal(ProfessionModal, { professionId, useBackButton: true });

    onMounted(() => {
      // Prefill with previously written reasons
      syncChosenItems('professionId', selectedProfessions, userProfessions, allProfessions.value);

      // Custom: link selected segments to professions
      linkNestedChosenItems(userSegments, selectedProfessions, 'professionId', allSegments, 'segmentId', 'selectedSegments');
    })

    // return variables & methods to be used in template HTML
    return {
      // icons
      add, close, checkmark, arrowUp, arrowForward, arrowBack, trashOutline,
      thumbsUpOutline, thumbsDownOutline, thumbsUp, thumbsDown, heart, heartOutline,
      createOutline, pencil, refresh,

      // variables
      selectedProfessions, userProfessions,

      // methods
      t,
      doReorder,
      confirmSelect, closeModal,
      
      openProfessionModal,
      openSegmentModal: async (segment: any) => (await openModal(SectorModal, { sectorId: segment.sectorId, segmentId: segment.id })),

      openUniProfessionDeckModal: async () => {
        const modal = await modalController.create({
          component: UniProfessionDeckModal,
          componentProps: {
            prefilledProfessions: selectedProfessions.value.slice(),
            oldUserProfessions: userProfessions.value.slice() || [],
            isSecSchoolStudents: props.isSecSchoolStudents,
          }
        });
        modal.onDidDismiss().then(({ data }) => {
          if (data && data.selectedProfessions) {
            // fill default choice of related segments
            for (const prof of data.selectedProfessions) {
              if (!prof.selectedSegments || prof.selectedSegments?.length == 0) {
                const prevSelected = userSegments.value.filter(us => us.professionId == prof.id && us.reaction == 'like');
                const relatedSegments = prevSelected.length > 0 ? prevSelected.map(us => allSegments.value.find(s => s.id == us.segmentId)).filter(us => !!us)
                                                                : store.getters.getRelatedSegmentsByProfessionId(prof.id);
                userSegments.value = [
                  ...userSegments.value.filter(us => us.professionId != prof.id),
                  ...relatedSegments.map(s => ({
                    professionId: prof.id,
                    segmentId: s.id,
                    reason: "",
                    action: "",
                    reaction: 'like',
                    createdAt: new Date(),
                    appState: {},
                  })),
                ];
                prof.selectedSegments = relatedSegments;
              }
            }
            selectedProfessions.value = data.selectedProfessions;
            userProfessions.value = data.userProfessions;
          }
        });
        return modal.present();
      },
      openUniSegmentDeckModal: async (profession) => {
        // "Reset" the choices to default related segments
        const relatedSegments = store.getters.getRelatedSegmentsByProfessionId(profession.id);
        const currSelectedSegments = profession.selectedSegments.slice();
        profession.selectedSegments = [
          ...currSelectedSegments.filter(cs => relatedSegments.find(s => s.id == cs.id)),
          ...relatedSegments.filter(s => !currSelectedSegments.find(cs => cs.id == s.id)),
        ]
        userSegments.value = [
          ...userSegments.value.filter(us => us.professionId != profession.id),
          ...profession.selectedSegments.map(s => ({
            professionId: profession.id,
            segmentId: s.id,
            reason: "",
            action: "",
            reaction: 'like',
            createdAt: new Date(),
            appState: {},
          })),
        ];
        /*
        const modal = await modalController.create({
          component: UniSegmentDeckModal,
          componentProps: {
            professionId: profession.id,
            professionName: profession.name,
            prefilledSegments: profession.selectedSegments,
            oldUserSegments: userSegments.value.filter(us => us.professionId == profession.id),
          }
        });
        modal.onDidDismiss().then(({ data }) => {
          if (data && data.selectedSegments) {
            profession.selectedSegments = data.selectedSegments;
            userSegments.value = [
              ...userSegments.value.filter(us => us.professionId != profession.id),
              ...data.userSegments, // new user segments after segment deck
            ]
          }
        });
        return modal.present();
        */
      },
      onDeleteChosenSegment: (profession, idx, segment) => {
        profession.selectedSegments.splice(idx, 1);
        const relatedUserSegment = userSegments.value.find(us => us.segmentId == segment.id && us.professionId == profession.id);
        if (relatedUserSegment) relatedUserSegment.reaction = '';
      },
      onDeleteChosenProfession: (idx, profession) => {
        selectedProfessions.value.splice(idx, 1);
        const relatedUserProfession = userProfessions.value.find(us => us.professionId == profession.id);
        if (relatedUserProfession) relatedUserProfession.reaction = '';
      },

      // Filter groups & filters
      chevronBack, chevronForward, repeat, search,
    }
  },
});
</script>

<style scoped>
  ion-item ion-buttons {
    min-width: 32px !important;
    height: 32px !important;
  }
  ion-item ion-buttons ion-button::part(native) {
    padding-inline-start: 0;
    padding-inline-end: 0;
  }
</style>