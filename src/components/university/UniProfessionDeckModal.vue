<template>
  <ion-page>
    <ion-header>
      <ion-grid class="ion-no-padding" fixed>
        <!-- Searchbar Input -->
        <ion-toolbar v-show="isSearching">
          <ion-searchbar id="keyword-searchbar" style="padding-bottom: 1px" mode="md" v-model="searchKeyword" :placeholder="t('search')"
                        @ionFocus="isSearching = true" @ionBlur="onSearchKeywordUpdated()" @ionCancel="isSearching = false; numOfVisibleItems = 20"
                        @keyup.enter="(e) => e.target.blur()" show-cancel-button="always"></ion-searchbar>
        </ion-toolbar>
      </ion-grid>
    </ion-header>
    
    <ion-content>
      <div class="spin" v-if="loadingData">
        <ion-spinner></ion-spinner>
      </div>
      <ion-grid fixed v-else>
        <!--
          Profession list
        -->
        <div v-if="isSearching">
          <div v-show="recommendedProfessions().length > 0">
            <ion-item lines="none">
              <ion-label class="ion-text-wrap"><p><b>Select</b></p></ion-label>
            </ion-item>
            <ion-item lines="full" v-for="profession in recommendedProfessions().slice(0, numOfVisibleItems)" :key="profession.id" style="--min-height: 55px">
              <ion-checkbox
                justify="start"
                labelPlacement="end"
                @update:modelValue="onCheckProfession($event, profession)"
                :modelValue="selectedItems.find(p => p.id == profession.id) != null"
              >
                <ion-label>
                  <ion-text class="ion-text-wrap">
                    <p>{{ profession.name }}</p>
                  </ion-text>
                  <p v-if="profession.relatedSegments && profession.relatedSegments.length > 0">
                    In: {{ profession.relatedSegments.map(s => s.name).join(", ") }}
                  </p>
                </ion-label>
              </ion-checkbox>

              <ion-button @click="openProfessionModal(profession.id)" color="medium" slot="end" fill="clear" size="small" style="margin-inline-end: 16px; text-transform: none">
                More
              </ion-button>
            </ion-item>

            <ion-infinite-scroll @ionInfinite="loadData($event)" threshold="100px" id="infinite-scroll">
              <ion-infinite-scroll-content loading-spinner="bubbles" loading-text="Loading..."></ion-infinite-scroll-content>
            </ion-infinite-scroll>
          </div>
          <div class="ion-text-center" v-show="recommendedProfessions().length == 0">
            <p>No professions available</p>
          </div>
        </div>

        <div v-else>
          <!--
            GPT Mode (Strength -> Profession)
            -->
          <div v-if="selectedFilterGroup == 'gpt'">
            <FDMTRiceCardSlide v-show="gpt.currStep == 1 && botSuggestedProfessions.length == 0"></FDMTRiceCardSlide>

            <div style="height: 100%" v-show="botSuggestedProfessions.length > 0 || gpt.currStep == 2">
              <div class="spin" v-if="gpt.waitingGPTResp">
                <ion-spinner></ion-spinner>
              </div>

              <!-- Explore Professions (result) -->
              <swiper
                  :navigation="true"
                  class="card-slides"
                  id="card-swiper-slides"
                  :effect="'cards'"
                  :grabCursor="true"
                  :modules="modules"
                  v-else
              >
                <!-- Main Slides -->
                <swiper-slide class="card-slide" v-for="profession in botSuggestedProfessions" :key="profession.id"
                              :class="{ 'highlighted-border': isSelected(profession) }" :data-profession-id="profession.id">
                  <!-- Profession Image (AI-generated) -->
                  <div class="top-badge ion-text-center" v-if="profession.imgLink && profession.imgLink != 'N/A'" style="top: 0; width: 100%; height: 100%">
                    <img style="max-width: 100%; object-fit: cover; height: 100%; filter: brightness(60%)" :src="profession.imgLink" />
                  </div>

                  <!-- Profession Name & Details -->
                  <ion-row>
                    <ion-col size="12" style="text-shadow: 3px 3px 15px black">
                      <h1>{{ profession.name }}<br /><small>{{ profession.nameChinese }}</small></h1>
                    </ion-col>
                  </ion-row>

                  <!-- Bot explanation -->
                  <ion-card class="bottom-badge" style="margin-top: 0; border-radius: 16px" v-show="currViewingProfession?.explanation">
                    <ion-card-content style="padding: 0">
                      <ion-item lines="full">
                        <ion-label class="ion-text-wrap">
                          <span class="trimmed-gpt-explanation" v-html="parseMsg(currViewingProfession?.explanation)"></span>
                        </ion-label>

                        <ion-button slot="end" size="small" color="light" class="ion-no-margin no-text-transform"
                                    @click="openProfessionModal(profession.id, parseMsg(currViewingProfession?.explanation))">
                          More
                        </ion-button>
                      </ion-item>
                    </ion-card-content>
                  </ion-card>

                  <ion-fab slot="fixed" vertical="top" horizontal="end">
                    <ion-fab-button v-on="{ [isMobileWeb() ? 'touchstart' : 'click']: () => { onThumbsUp(profession, false) } }"
                                    style="--background: transparent" :color="profession.imgLink ? 'light' : undefined">
                      <ion-icon size="large" :icon="isSelected(profession) ? thumbsUp : thumbsUpOutline"></ion-icon>
                    </ion-fab-button>
                  </ion-fab>
                </swiper-slide>
              </swiper>
            </div>
          </div>

          <!--
            Choose Professions (Cards)
            -->
          <div style="height: 100%" :class="{ 'valign': !isSearching }" v-else>
            <swiper
                :navigation="!isMobileWeb()"
                class="card-slides"
                id="card-swiper-slides"
                :effect="'cards'"
                :grabCursor="true"
                :modules="modules"
                v-if="!delayLoading && recommendedProfessions().length > 0"
            >
              <!-- Go Back to Previous -->
              <swiper-slide class="card-slide" style="background-color: var(--ion-color-light)" v-if="(startIdx+1)-10 > 0">
                <ion-row>
                  <ion-col size="12">
                    <ion-button size="large" color="primary" class="no-text-transform" fill="outline"
                                v-on="{ [isMobileWeb() ? 'touchstart' : 'click']: () => { onClickPrevBtn() } }">
                      <ion-icon slot="end" :icon="arrowBack"></ion-icon>
                      Previous
                    </ion-button>
                  </ion-col>
                </ion-row>
              </swiper-slide>

              <!-- Main Slides -->
              <swiper-slide class="card-slide" v-for="item in recommendedProfessions().slice(startIdx, startIdx+10)" :key="item.id"
                            :class="{ 'highlighted-border': isSelected(item) }" :data-item-id="item.id">

                <div class="top-badge" v-if="['like','dislike'].includes(selectedFilterGroup) && getAppStateText(item.id)">
                  <ion-chip class="small-chip">{{ getAppStateText(item.id) }}</ion-chip>
                </div>
        
                <!-- Profession Image (AI-generated) -->
                <div class="top-badge ion-text-center" v-if="item.imgLink && item.imgLink != 'N/A'" style="top: 0; width: 100%; height: 100%">
                  <img style="max-width: 100%; object-fit: cover; height: 100%; filter: brightness(60%)" :src="item.imgLink" />
                </div>

                <!-- Profession Name & Details -->
                <ion-row style="text-shadow: 3px 3px 3px black">
                  <ion-col size="12">
                    <h1>{{ item.name }}<br /><small>{{ item.nameChinese }}</small></h1>
                  </ion-col>
                  <ion-col size="12">
                    <ion-button size="small" color="light" class="no-text-transform" @click="openProfessionModal(item.id)">
                      Details
                    </ion-button>
                    <!--<ion-button size="small" color="light" class="no-text-transform" @click="openListSegmentModal(item)">
                      Segments
                    </ion-button>-->
                  </ion-col>
                </ion-row>
                
                <ion-fab slot="fixed" vertical="top" horizontal="end">
                  <ion-fab-button v-on="{ [isMobileWeb() ? 'touchstart' : 'click']: () => { onThumbsUp(item) } }"
                                  style="--background: transparent" :color="item.imgLink ? 'light' : undefined">
                    <ion-icon size="large" :icon="isSelected(item) ? thumbsUp : thumbsUpOutline"></ion-icon>
                  </ion-fab-button>
                </ion-fab>
                <!--<ion-fab slot="fixed" vertical="bottom" horizontal="start">
                  <ion-fab-button v-on="{ [isMobileWeb() ? 'touchstart' : 'click']: () => { onThumbsDown(item) } }"
                                  style="--background: transparent" :color="item.imgLink ? 'light' : undefined">
                    <ion-icon size="large" :icon="isDisliked(item) ? thumbsDown : thumbsDownOutline"></ion-icon>
                  </ion-fab-button>
                </ion-fab>-->
              </swiper-slide>

              <!-- More (Next) -->
              <swiper-slide class="card-slide" style="background-color: var(--ion-color-light)" v-if="startIdx+10 < recommendedProfessions().length">
                <ion-row>
                  <ion-col size="12">
                    <ion-button size="large" color="primary" class="no-text-transform" fill="outline"
                                v-on="{ [isMobileWeb() ? 'touchstart' : 'click']: () => { onClickMoreBtn() } }">
                      More
                      <ion-icon slot="end" :icon="arrowForward"></ion-icon>
                    </ion-button>
                  </ion-col>
                </ion-row>
              </swiper-slide>
            </swiper>
          </div>
        </div>
      </ion-grid>
    </ion-content>

    <ion-footer v-show="!loadingData && !isSearching">
      <ion-grid class="ion-no-padding" fixed>
        <ion-toolbar style="--min-height: 24px">
          <!-- Back Button -->
          <ion-buttons slot="start">
            <ion-button class="nav-category-btn" @click="confirmSelect(true)">
              <ion-icon size="small" slot="icon-only" :icon="arrowBack"></ion-icon>
            </ion-button>
          </ion-buttons>

          <!-- Filter Groups -->
          <ion-segment class="filter-group-segment" mode="ios" v-model="selectedFilterGroup" scrollable>
            <ion-segment-button value="gpt">
              <ion-label class="ion-text-wrap">AI</ion-label>
            </ion-segment-button>

            <!-- Questions Tabs -->
            <!--<ion-segment-button v-for="q in questions" :key="q.id" :value="q.id" v-show="userRelatedJobEX['Show question tabs next to AI?'] == 'Y'">-->
            <ion-segment-button v-for="q in questions.slice(0, 1)" :key="q.id" :value="q.id">
              <!--<ion-label class="ion-text-wrap">{{ q.group }}</ion-label>-->
              <ion-label class="ion-text-wrap">Suggested</ion-label>
            </ion-segment-button>

            <!-- Suggested Tab 
            <ion-segment-button value="suggested">
              <ion-label class="ion-text-wrap">Suggested</ion-label>
            </ion-segment-button>-->

            <ion-segment-button value="alumni" v-if="getAlumniProfessions().length > 0">
              <ion-ripple-effect></ion-ripple-effect>
              <!--<ion-label class="ion-text-wrap">Alumni corner</ion-label>-->
              <ion-label class="ion-text-wrap">Alumni</ion-label>
            </ion-segment-button>
            <ion-segment-button value="like" v-if="userItems.some(up => up.reaction == 'like')">
              <ion-icon size="small" :icon="thumbsUpOutline"></ion-icon>
            </ion-segment-button>
            <ion-segment-button value="search">
              <ion-ripple-effect></ion-ripple-effect>
              <ion-icon size="small" :icon="search"></ion-icon>
              <!--<ion-label class="ion-text-wrap">Professions</ion-label>-->
            </ion-segment-button>

            <!-- Sectors -->
            <ion-segment-button value="sectors">
              <ion-ripple-effect></ion-ripple-effect>
              <ion-label class="ion-text-wrap">Sectors</ion-label>
            </ion-segment-button>

            <!-- [Internal] for video taking -->
            <ion-segment-button value="video-taking" v-if="user.isAdmin">
              <ion-ripple-effect></ion-ripple-effect>
              <ion-icon size="small" :icon="ellipsisVertical"></ion-icon>
            </ion-segment-button>
          </ion-segment>
        </ion-toolbar>

        <!-- Materials by Type -->
        <ion-toolbar class="material-div">
          <!-- Question Chips (filters) -->
          <ion-row v-show="selectedFilterGroup != 'gpt'">
            <div v-for="q of questions" :key="q.id">
              <ion-chip v-for="tab in q.options" :key="tab.id" :class="{ 'active-tag': selectedOption.id == tab.id }"
                        @click="selectedOption = tab" v-show="selectedFilterGroup == q.id.toString()">
                <ion-label>{{ tab.text }}</ion-label>
              </ion-chip>
            </div>
          </ion-row>

          <!-- AI -->
          <ion-row v-show="selectedFilterGroup == 'gpt'">
            <!-- Disclaimer -->
            <ion-item lines="none">
              <ion-label class="ion-text-wrap ion-no-margin">
                <p style="line-height: 1"><small>Disclaimer: The data presented is for reference purposes only. We do not guarantee their correctness, completeness, or timeliness.</small></p>
              </ion-label>
            </ion-item>

            <!-- User input (descriptions of strength) -->
            <div style="padding: 8px; width: 100%">
              <ion-textarea placeholder="By writing and/or tags, describe your strengths, passions, and habits as specifically as possible"
                            fill="outline" v-model="gpt.userInputText" :rows="3" :auto-grow="true"></ion-textarea>
            </div>

            <!-- Tag group -->
            <ion-toolbar style="--min-height: 28px">
              <ion-segment class="filter-group-segment" mode="ios" v-model="gpt.selectedTagGroup">
                <ion-segment-button value="">
                  <ion-label>All</ion-label>
                </ion-segment-button>
                <ion-segment-button value="courses">
                  <ion-label>Courses</ion-label>
                </ion-segment-button>
                <!--<ion-segment-button value="suggested">
                  <ion-label>Suggested</ion-label>
                </ion-segment-button>-->
                <ion-segment-button value="general">
                  <ion-label>General</ion-label>
                </ion-segment-button>
              </ion-segment>
            </ion-toolbar>

            <div style="padding-top: 8px">
              <!-- Suggested tags (major-specific) -->
              <div v-for="q of questions" :key="q.id">
                <ion-chip v-for="tag in q.options" :key="tag.id" @click="toggleSelectedTag(tag.id, tag.text)"
                          :class="{ 'active-tag': gpt.tagId == tag.id }" v-show="!gpt.selectedTagGroup || gpt.selectedTagGroup == 'suggested'">
                  <ion-label>{{ tag.text }}</ion-label>
                </ion-chip>
              </div>

              <!-- General tags -->
              <ion-chip v-for="(tag, i) in passionTags" :key="i" @click="toggleSelectedTag(tag.id, tag.text)"
                        :class="{ 'active-tag': gpt.tagId == tag.id }" v-show="!gpt.selectedTagGroup || gpt.selectedTagGroup == 'general'">
                <ion-label>{{ tag.text }}</ion-label>
              </ion-chip>

              <!-- Course tags -->
              <ion-chip v-for="(tag, i) in getCourseOptions()" :key="i" @click="toggleSelectedTag(tag.id, tag.text)"
                        :class="{ 'active-tag': gpt.tagId == tag.id }" v-show="!gpt.selectedTagGroup || gpt.selectedTagGroup == 'courses'">
                <ion-label>{{ tag.text }}</ion-label>
              </ion-chip>

              <!-- Other tag -->
              <ion-chip @click="toggleSelectedTag(tagObjOther.id, tagObjOther.text)" :class="{ 'active-tag': gpt.tagId == tagObjOther.id }">
                <ion-label>{{ tagObjOther.text }}</ion-label>
              </ion-chip>
            </div>
          </ion-row>
        </ion-toolbar>

        <!-- Button for triggering ChatGPT responses -->
        <ion-toolbar v-show="selectedFilterGroup == 'gpt'">
          <ion-row>
            <ion-col>
              <ion-button expand="block" @click="gpt.currStep = 2; getRecommendedProfessionsFromGPT()"
                          :disabled="(!gpt.tagId && !gpt.userInputText) || gpt.isInCooldownTime" class="no-text-transform">
                {{ gpt.isInCooldownTime ? `Please wait (${gpt.cooldownSecLeft}s)` : `GO` }}
                <ion-icon slot="end" :icon="arrowForward"></ion-icon>
              </ion-button>
            </ion-col>
          </ion-row>
        </ion-toolbar>
      </ion-grid>
    </ion-footer>
  </ion-page>
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, watch, reactive, onMounted, onUnmounted, onBeforeUnmount } from 'vue';

// icons
import { add, close, checkmark, arrowUp, arrowForward, arrowBack, trashOutline,
        thumbsUpOutline, thumbsDownOutline, thumbsUp, thumbsDown, heart, heartOutline,
        chevronBack, chevronForward, repeat, search, createOutline, pencil, ellipsisVertical, } from 'ionicons/icons';

// components
import { IonPage, IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonGrid, IonRow, IonCol, IonSpinner,
        IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonSearchbar, IonSegment, IonSegmentButton, IonList,
        IonAvatar, IonCard, IonCardContent, IonTextarea,
        IonChip, IonText, IonCheckbox, IonRippleEffect, IonFab, IonFabButton, IonInfiniteScroll, IonInfiniteScrollContent,
        IonicSlides, isPlatform, modalController, } from '@ionic/vue';
import ProfessionModal from '@/components/pss/ProfessionModal.vue';
import ListSegmentModal from '@/components/university/ListSegmentModal.vue';
import ListSectorModal from '@/components/university/ListSectorModal.vue';
import ChatbotModal from '@/components/modals/ChatbotModal.vue';

// Swiper
import 'swiper/swiper.min.css';
import 'swiper/modules/effect-cards/effect-cards.min.css';
import 'swiper/modules/navigation/navigation.min.css';
import '@ionic/vue/css/ionic-swiper.css';
import { Swiper, SwiperSlide } from 'swiper/vue/swiper-vue';
import { EffectCards, Navigation, } from 'swiper';

// composables
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { utilsGPT } from '@/composables/utilsGPT';
import { useStore } from '@/store';
import { useRoute, useRouter } from 'vue-router';

// types
import { Profession, ProfessionTab, Step1Option, Step1Question, UserClaim, UserProfession, } from '@/types';

// services
import ABSService from '@/services/ABSService';

// lib
import config from '@/config';

export default defineComponent({
  name: 'UniProfessionDeckModal',
  props: ["prefilledProfessions", "oldUserProfessions", "isYear1", "isSecSchoolStudents", "isPage"],
  components: { IonPage, IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonGrid, IonRow, IonCol, IonSpinner,
                IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonSearchbar, IonSegment, IonSegmentButton, IonList,
                IonAvatar, IonCard, IonCardContent, IonTextarea,
                IonChip, IonText, IonCheckbox, IonRippleEffect, IonFab, IonFabButton, IonInfiniteScroll, IonInfiniteScrollContent,
                Swiper, SwiperSlide },
  setup(props) {
    // methods or filters
    const store = useStore();
    const { t } = useI18n();
    const router = useRouter();
    const { openModal, closeModal, doReorder, infiniteScrollLoadData,
          processUserItems, isItemSelected, isItemDisliked, isMobileWeb,
          onThumbsUpItem, onThumbsDownItem, onClickMoreBtn, onClickPrevBtn, animateToFirstCard,
          recordCurrSlideReaction, resetActiveSlide, syncChosenItems, resetFilters, focusKeywordSearchbar,
          presentAlert, } = utils();

    const mode = ref("default"); // default / gpt (AI-assisted)
    const user = computed(() => store.state.user);
    const userRelatedJobEX = computed(() => store.getters.userRelatedJobEX);
    const loadingData = computed(() => store.state.loadingProgram);
    const programWithProfessions = computed(() => store.state.currProgram);
    const questions = computed<Step1Question[]>(() => store.getters.getUserRelatedStep1Questions(props.isYear1));

    const passionTags = computed<Step1Option[]>(() => store.getters.getStep1OptionsByQuestionId("q-192282d2"));
    const step1Questions = computed(() => store.getters.getUserRelatedStep1Questions(props.isYear1, true));
    const step1OptionIds = computed(() => store.getters.getUserRelatedStep1OptionIds(props.isYear1));

    const selectedItems = ref<Profession[]>(props.prefilledProfessions || []);
    const userItems = ref<UserProfession[]>(props.oldUserProfessions || []);
    const tmpNewUserItems = ref<UserProfession[]>([]); // for storing slideChange user professions
    const allProfessions = ref<Profession[]>(store.getters.shuffledProfessions(false));
    //const allProfessions = ref<Profession[]>(store.state.allProfessions);
    const allProfessionTabs = computed<ProfessionTab[]>(() => store.state.professionTabs);
    const LEV_DOMAIN_TAB_ID = 298;
    const SUP_FUNC_TAB_ID = 299;

    const selectedFilterGroup = ref('');
    const selectedOption = ref<any>("all");

    const searchKeyword = ref('');
    const isSearching = ref(false);
    const delayLoading = ref(true);

    /**
     * GPT (strength-based recommendations)
     */
    const { tagObjOther, parseMsg, fetchGPTResponse, parseGPTResponse, whatsAppSendGPTResults, upsertUserItems, getStrengthDescription, initGPTObj, } = utilsGPT();
    const gpt = reactive({
      selectedTagGroup: "",
      userInputText: "", // user input prompt
      tagId: "",
      tagText: "",
      currStep: 1,
      waitingGPTResp: false,
      isInCooldownTime: false, // prevent users sending requests consecutively
      cooldownSecLeft: 0,
    });
    const botSuggestedProfessions = ref<Profession[]>([]);
    const currViewingProfession = ref<Profession>();
    const getRecommendedProfessionsFromGPT = async () => {
      try {
        botSuggestedProfessions.value = []; // reset
        currViewingProfession.value = undefined; // reset
        const MAX_PROFESSIONS = 10, clientId = userRelatedJobEX.value["Client"];
        let overrideBotName = config.separatePoeBotClientIds.includes(clientId) ? clientId : ""; // separate Poe bots
        if (userRelatedJobEX.value.relatedProgramIds.includes('226')) overrideBotName = 'LU_MIBF'; // TMP: use MIBF professions

        let prompt = `Key strength: ${getStrengthDescription(gpt)}`;
        prompt += `\n\nAbove is the information about a Hong Kong university student (${userRelatedJobEX.value.relatedProgramNames[0]}).`
        prompt += ` Please suggest at most ${MAX_PROFESSIONS} unique professions that are most relevant to the student based on his/her strength. `;
        prompt += `The professions must ONLY be those related to the client "${clientId}".`;
        prompt += ` For each profession, please explain in detail how the student's key strength is applied in most related daily tasks in English to convince the student that the profession is suitable. Be as specific & concrete as possible.`;
        prompt += `\n\nYour response MUST be formatted in JSON with only an array of JavaScript objects (at most ${MAX_PROFESSIONS} objects), each object must contain exactly 2 fields: "id", "explanation"\n`;
        prompt += `Example response: [{"id":"499","explanation":"XXX"}]`;

        // Send request (TBC: separate bot URL for university? May not be needed given the override bot)
        const data = await fetchGPTResponse(gpt, "https://ab-chatgpt-api.fdmt.hk/slp", prompt, user.value.id, overrideBotName);
        if (!data) throw new Error("No data");

        // Parse data (bot responses)
        await parseGPTResponse(gpt, data, currViewingProfession, botSuggestedProfessions, allProfessions, '#card-swiper-slides', 'professionId');

        // Insert to DB for records first
        upsertUserItems(user.value.id, botSuggestedProfessions, userItems, 'professionId');

        // Send out the results to student's WhatsApp group for records
        whatsAppSendGPTResults(user.value.phone, user.value.waGroupId, getStrengthDescription(gpt), botSuggestedProfessions, 'profession');
      } catch (e) {
        console.error(e);
        presentAlert("ChatGPT did not response. Please try again");
        gpt.currStep = 1;
      } finally {
        gpt.waitingGPTResp = false;
      }
    }

    const numOfVisibleItems = ref(20);
    const loadData = (ev: any) => { infiniteScrollLoadData(ev, numOfVisibleItems, store.state.allProfessions) }

    const getAppState = () => ({
      selectedFilterGroup: selectedFilterGroup.value,
      selectedOption: selectedOption.value,
      searchKeyword: searchKeyword.value,
    });
    const confirmSelect = async (noLoading = false) => {
      if (props.isPage) router.replace('/home');
      else {
        await closeModal({
          "selectedProfessions": selectedItems.value, noLoading,
          "userProfessions": processUserItems(selectedItems.value, userItems.value, tmpNewUserItems.value, 'professionId', store.state.user.id),
        }); // return selected items & order here
      }
    };
    const openProfessionModal = async (professionId: any, gptExplanationHTML = "") => {
      // Check suppliers serve the sector (apply domain knowledge in generic professions)
      const overrideSegments = [];
      const relatedSectors = [SUP_FUNC_TAB_ID].includes(selectedOption.value.id) ? programWithProfessions.value['related_sectors'] : [];
      await openModal(ProfessionModal, { gptExplanationHTML, professionId, useBackButton: true, relatedSectors, overrideSegments, expandAlumniSection: selectedFilterGroup.value == 'alumni' });
    };

    // Helper functions for card slides
    const recordActiveSlideReaction = () => {
      recordCurrSlideReaction('professionId', userItems, tmpNewUserItems, getAppState());
    }
    const isSelected = (item: any) => (isItemSelected(item, selectedItems));
    const isDisliked = (item: any) => (isItemDisliked(item, 'professionId', userItems));

    const startIdx = ref(0); // for card slides

    // Event Cycle
    onMounted(() => {
      selectedFilterGroup.value = questions.value[0]?.id;

      // Prefill with previously written reasons
      syncChosenItems('professionId', selectedItems, userItems, allProfessions.value);

      setTimeout(() => {
        delayLoading.value = false;
        setTimeout(() => {
          const slides: any = document.querySelector('#card-swiper-slides');
          if (slides) {
            slides.swiper.on('slideChange', recordActiveSlideReaction);
            recordActiveSlideReaction(); // initial slide
          }
        }, 200);
      }, 200);

      // AI: Prefill previously input text & selected tag
      initGPTObj(gpt, user.value.claims);
    })
    watch(selectedOption, () => {
      resetActiveSlide(startIdx, delayLoading, 'professionId', userItems, tmpNewUserItems, getAppState());
    })
    watch(selectedFilterGroup, (currGroup) => {
      if (currGroup) {
        if (['like', 'dislike'].includes(currGroup)) {
          selectedOption.value = currGroup;
        }
        else if (currGroup == 'random') {
          resetFilters(selectedFilterGroup, selectedOption);
          allProfessions.value = store.getters.shuffledProfessions(false);
          animateToFirstCard();
        }
        else if (currGroup == 'search') {
          //selectedOption.value = 'all';
          resetFilters(selectedFilterGroup, selectedOption);
          focusKeywordSearchbar(isSearching);
        }
        else if (currGroup == 'sectors') {
          resetFilters(selectedFilterGroup, selectedOption);
          openModal(ListSectorModal, { majorRelatedSectors: programWithProfessions.value['related_sectors'] });
        }
        else if (currGroup == "video-taking") { // Video taking (demo professions)
          selectedOption.value = store.getters.getStep1OptionById("o-fdda21f6");
        }
        else {
          const q = questions.value.find(q => q.id == currGroup) || { options: [] };
          selectedOption.value = q.options[0] || {};
        }
      }
    })
    watch(loadingData, () => {
      allProfessions.value = store.getters.shuffledProfessions(false);
    })
    watch(questions, () => {
      setTimeout(() => {
        selectedFilterGroup.value = questions.value[0]?.id;
      }, 500);
    })
  
    const getAlumniProfessions = () => {
      return (programWithProfessions.value.alumniContactRelations || []).filter(cr => cr.entityType == 'profession').map(cr => {
        return allProfessions.value.find(p => p.id == cr.entityId) || {};
      });
    }

    // return variables & methods to be used in template HTML
    return {
      // icons
      add, close, checkmark, arrowUp, arrowForward, arrowBack, trashOutline,
      thumbsUpOutline, thumbsDownOutline, thumbsUp, thumbsDown, heart, heartOutline,
      createOutline, pencil, ellipsisVertical,

      // variables
      user, userRelatedJobEX,
      questions,
      selectedOption,
      selectedItems, userItems, tmpNewUserItems,
      searchKeyword, isSearching,
      delayLoading,

      // methods
      t, isMobileWeb,
      doReorder,
      confirmSelect, closeModal, openProfessionModal,
      onSearchKeywordUpdated: () => {
        ABSService.insertProfessionSearchKeywordRecord(searchKeyword.value);
      },
      recommendedProfessions: () => {
        // Alumni Professions
        if (selectedFilterGroup.value == "alumni") return getAlumniProfessions();

        // Others
        const selectedOpt = selectedOption.value;
        if (searchKeyword.value) {
          const filteredProfessions = searchKeyword.value == '' ? allProfessions.value : allProfessions.value.filter(p => {
            return p.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) || (p.nameChinese && p.nameChinese.includes(searchKeyword.value));
          });
          return filteredProfessions;
        }
        if (['like', 'dislike'].includes(selectedOpt)) {
          return userItems.value.filter(up => up.reaction == selectedOpt).map(up => {
            return allProfessions.value.find(p => p.id == up.professionId); 
          });
        }
        let filteredProfessions: any = allProfessions.value; // Default shuffle all professions (every time enter)
        if (selectedOpt && selectedOpt.id) {
          // Step 1 Question & Option Mapping
          const { tabIds, professionIds } = selectedOpt;
          let tabProfessionIds = [];
          for (const tid of tabIds) {
            const pTab = allProfessionTabs.value.find(pt => pt.id == tid);
            if (pTab) tabProfessionIds = tabProfessionIds.concat(pTab.relatedProfessionIds);
          }
          const relatedProfessionIds = professionIds.length > 0 ? professionIds : tabProfessionIds; // override
          filteredProfessions = [
            ...relatedProfessionIds.map(id => (allProfessions.value.find((p: Profession) => p.id == id.toString()))),
          ].filter(p => p != null);
        }
        else {
          // sort by order in profession tabs
          const sortOrders = {};
          let countTab = 0;
          for (const q of questions.value) {
            for (const opt of q.options) {
              const { tabIds, professionIds } = opt;
              let tabProfessionIds = [];
              for (const tid of tabIds) {
                const pTab = allProfessionTabs.value.find(pt => pt.id == tid);
                if (pTab) tabProfessionIds = tabProfessionIds.concat(pTab.relatedProfessionIds);
              }
              const relatedProfessionIds = professionIds.length > 0 ? professionIds : tabProfessionIds; // override
              for (let i = 0; i < relatedProfessionIds.length; i++) {
                const pid = relatedProfessionIds[i];
                sortOrders[pid] = sortOrders[pid] || Infinity;
                sortOrders[pid] = Math.min(sortOrders[pid], i) + countTab;
              }
              countTab++;
            }
          }
          filteredProfessions.sort((a, b) => {
            const orderA = a.id in sortOrders ? sortOrders[a.id] : Infinity;
            const orderB = b.id in sortOrders ? sortOrders[b.id] : Infinity;
            return orderA-orderB;
          });
        }
        filteredProfessions = [
          ...filteredProfessions.filter(d => !isDisliked(d)),
          ...filteredProfessions.filter(d => isDisliked(d)),
        ];
        if (props.isYear1 || selectedOpt?.professionTabId == LEV_DOMAIN_TAB_ID) { // Leverage domain knowledge (generic professions)
          return filteredProfessions;
        }
        if (selectedFilterGroup.value == "video-taking") return filteredProfessions; // no need filter related professions

        //return filteredProfessions;
        const relatedProfessionIds = programWithProfessions.value.allRelatedProfessions.map(p => p.id);
        return relatedProfessionIds.map(id => filteredProfessions.find(p => p.id == id)).filter(p => !!p); // maintain the order
        //return filteredProfessions.filter(p => relatedProfessionIds.includes(p.id));
      },

      onThumbsUp: (item: any, flipToNextSlide = true) => (onThumbsUpItem(item, 'professionId', selectedItems, userItems, tmpNewUserItems, getAppState(), flipToNextSlide, true)),
      onThumbsDown: (item: any) => (onThumbsDownItem(item, 'professionId', selectedItems, userItems, tmpNewUserItems, getAppState())),
      isSelected, isDisliked,

      // swiper
      modules: [EffectCards, IonicSlides, Navigation],
      startIdx,
      onClickMoreBtn: () => { onClickMoreBtn(startIdx) },
      onClickPrevBtn: () => { onClickPrevBtn(startIdx) },

      // Filter groups & filters
      chevronBack, chevronForward, repeat, search,
      selectedFilterGroup,

      getAppStateText: (itemId: any) => {
        const up = userItems.value.find(up => (up.professionId == itemId));
        const { selectedOption, searchKeyword } = up?.appState || {};
        return searchKeyword || (selectedOption ? selectedOption.text : null);
      },
      
      onCheckProfession: (checked: any, profession: Profession) => {
        if (checked) {
          if (selectedItems.value.find(p => p.id == profession.id) == null) {
            selectedItems.value.unshift(profession);
          }
        }
        else {
          const idx = selectedItems.value.findIndex(p => p.id == profession.id);
          if (idx !== -1) selectedItems.value.splice(idx, 1);
        }
      },

      // Infinite scroll
      numOfVisibleItems, loadData,
      
      // Other
      loadingData,
      openListSegmentModal: async (profession) => (openModal(ListSegmentModal, { profession })),

      // Alumni corner
      getAlumniProfessions,

      // GPT
      passionTags, step1Questions, step1OptionIds,
      getCourseOptions: () => (step1Questions.value['Course'] || []),

      gpt, botSuggestedProfessions, currViewingProfession,
      getRecommendedProfessionsFromGPT, parseMsg,
      tagObjOther, toggleSelectedTag: (tagId, tagText) => { gpt.tagId = tagId; gpt.tagText = tagText },

      openChatbotModal: async (profession: any) => {
        const { name, nameChinese, explanation } = profession;
        const professionName = `${name} ${nameChinese}`;
        const botExplanation = `${professionName}\n${explanation}`;
        const prefilledPrompt = `Please elaborate more about how my key strength is applied in ${professionName}: ${getStrengthDescription(gpt)}`;
        return await openModal(ChatbotModal, { isFromAB4: true, professionName, botExplanation, professionId: profession.id, prefilledPrompt });
      },
    }
  },
});
</script>

<style scoped>
  ion-textarea {
    --placeholder-font-weight: bold;
  }
</style>