<template>
  <div class="spin" v-if="loadingData">
    <ion-spinner></ion-spinner>
  </div>

  <div v-else>
    <!-- Static Slide (jobEX intro)
    <img style="width: 100%" :src="getProxyImgLink('https://docs.google.com/presentation/d/1DFgKAqN_hui9CRkstFSNvePModyVoK8VYIx9Y2waLmY/export/jpeg?id=1PcPhzYb77IrgcrfWrPNKf3VUhrsW3ENq4D3xuKZvfaw&pageid=g2890d557c39_1_71')" />
    -->

    <!-- Featured Professions & Career Story Video -->
    <div v-if="userRelatedJobEX.homePageProfessionIds || userRelatedJobEX['Embedded Video Link']"
        style="margin-bottom: -6px">
      <p class="ion-text-center" v-if="loadingPortalData">
        <ion-spinner></ion-spinner>
      </p>
      <ion-row v-else>
        <ion-col size="12" v-if="userRelatedJobEX['Embedded Video Link']">
          <iframe class="responsive-embed" width="100%" height="350px" frameborder="0" allowfullscreen
                  :src="userRelatedJobEX['Embedded Video Link']" v-if="userRelatedJobEX['Embedded Video Link'].startsWith('http')"></iframe>
          <ion-card class="ion-text-center" color="primary" style="border-radius: 16px" v-else>
            <ion-card-header>
              <ion-icon :icon="play" style="font-size: 60px; margin: 0 auto"></ion-icon>
              <ion-card-title>{{ userRelatedJobEX['Embedded Video Link'] }}</ion-card-title>
              <ion-card-subtitle>Video coming soon</ion-card-subtitle>
            </ion-card-header>
          </ion-card>
        </ion-col>
        <ion-col v-if="userRelatedJobEX.homePageProfessionIds">
          <div class="horizontal-scroll">
            <ion-chip v-for="id in userRelatedJobEX.homePageProfessionIds" :key="id"
                      @click="openProfessionModal(id)">{{ userRelatedJobEX.homePageProfessionPrefix }}{{ getProfessionNameById(id) }}</ion-chip>
          </div>
        </ion-col>
      </ion-row>
    </div>

    <!-- Yr 1 only -->
    <div v-if="userRelatedJobEX['Show Yr 1 Academic advising?'] == 'Y'">
      <home-action-header title="Yr 1"></home-action-header>
      <ion-card style="margin-top: 0">
        <ion-card-content style="padding: 0">
          <home-action-item title="Talk (coming soon)" extraNote1="Apply" extraNote2="Roll call..."
                            :isLesson="true" :isDisabled="true"></home-action-item>
          <home-action-item title="Explore career prospects" @click="openYr1ExploreProfessionModal()"></home-action-item>
        </ion-card-content>
      </ion-card>
    </div>

    <!-- Yr 2-4 -->
    <home-action-header :title="(user.yearOfStudy || '').startsWith('Year') ? 'Yr 1 - 4' : 'Resources'"></home-action-header>
    <ion-card style="margin-top: 0">
      <ion-card-content style="padding: 0">
        <!-- L2 Workshop (career prospect & CV) -->
        <!--<home-action-item title="Lecture - Educated job seeking" extraNote1="Apply" extraNote2="Roll call..."
                          :isLesson="true" :isDone="!!user.l2" @click="openJobEXEventsModal('workshop2', user.l2)"></home-action-item>-->
        <!-- Main workshop2 -->
        <ion-accordion-group value="jobEX workshop">
          <ion-accordion style="--min-height: 24px" value="jobEX workshop">
            <ion-item class="ion-no-padding" lines="full" slot="header">
              <ion-icon slot="start" :icon="null"></ion-icon>
              <ion-label><span><b>Lecture - Educated job seeking</b></span></ion-label>
            </ion-item>
            <ion-list class="ion-no-padding" slot="content">
              <event-card v-for="ev in filteredJobEXIntakeSessions('workshop2')" :key="ev.id" :ev="ev" :user="user" v-show="ev.formattedDateTime && isShowEvent(ev)"
                          :showStudentFormsOnly="true" :useAccordionView="true"></event-card>
            </ion-list>
          </ion-accordion>
        </ion-accordion-group>

        <!-- Explore Professions -->
        <home-action-item title="400+ professions | 300+ employer segments" :isDone="jobEXIsSelectedProfessions()"
                          @click="jobEXOpenProfessionSelectModal()" :noIndent="true"></home-action-item>

        <!-- CV -->
        <home-action-item title="AI-powered AchieveCV & AchieveCL" :isDone="user.isCvReady" @click="openCVModal()" :noIndent="true"></home-action-item>

        <!-- L4 Consultation -->
        <!--<home-action-item title="Online consultation" extraNote1="Apply" extraNote2="Roll call..." :isLesson="true" :isDone="!!user.consultation"
                          @click="openJobEXEventsModal(!user.isAdmin && !user.isCvReady ? undefined : 'jobex-consultation-session')"></home-action-item>-->
        <home-action-item title="Online consultation" :isLesson="true" :isDone="!!user.consultation" @click="openBookingModal('bi82ce0b45')"></home-action-item>

        <!-- Guest lecture (employers) -->
        <home-action-item title="Guest lecture" extraNote1="Apply" extraNote2="Roll call..."
                          :isLesson="true" :isDone="!!user.l3" @click="openJobEXEventsModal('cc31fb5c1', user.l3)"
                          v-if="userRelatedJobEX['Show guest lecture?'] == 'Y'"></home-action-item>

        <!-- Job Application
        <home-action-item title="Job" @click="openEmployerJobModal()"></home-action-item>-->

        <!-- L5 e-outing (20240506: TMP hide it for now)
        <home-action-item title="e-outing" extraNote1="Apply" extraNote2="Roll call..." :isLesson="true"
                          @click="openJobEXEventsModal(!user.isAdmin && !user.isCvReady ? undefined : 'jobex-eouting')"></home-action-item>
        -->
      </ion-card-content>
    </ion-card>

    <!--
      Jobs / Internship
      v-if="userRelatedJobEX['Show guest lecture?'] == 'Y'"
      -->
    <home-action-header title="Job opportunities"></home-action-header>

    <ion-card style="margin-top: 0">
      <ion-card-content style="padding: 0">
        <home-action-item title="Bluebird" :noIndent="true" @click="openServiceModal('bluebird')"></home-action-item>
        <home-action-item title="Term-time internship" :noIndent="true"></home-action-item>
        <home-action-item title="Summer internship" :noIndent="true"></home-action-item>
        <home-action-item title="Graduation jobs" :noIndent="true"></home-action-item>
      </ion-card-content>
    </ion-card>

    <!--
      Share this resource with classmates
      -->
    <ion-card class="class-bar" v-show="user.isAdmin || user.uniEmail">
      <ion-card-content style="padding: 0">
        <ion-accordion-group>
          <ion-accordion style="--min-height: 24px">
            <ion-item color="primary" lines="full" slot="header">
              <ion-label style="margin: 4px 8px">
                <h2>Share this tool with my classmates</h2>
              </ion-label>
            </ion-item>

            <ion-list class="ion-text-center" slot="content">
              <p><img style="width: 300px" :src="getQRCodeUrl(`https://ab.fdmt.hk/u/${user.programId}`)" /></p>
              <p>ab.fdmt.hk/u/{{user.programId}}</p>
              <ion-button color="success" size="small" @click="copyText(`https://ab.fdmt.hk/u/${user.programId}`)">
                Copy
              </ion-button>
            </ion-list>
          </ion-accordion>
        </ion-accordion-group>
      </ion-card-content>
    </ion-card>
  </div>
</template>

<script lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue';

// icons
import { personCircle, compass, arrowForward, navigate, settingsOutline, checkmark, arrowBack,
        add, close, checkmarkCircle, alertCircle, pencil, peopleOutline, qrCodeOutline,
        createOutline, calendarOutline, calendarClearOutline, play, } from 'ionicons/icons';

// components
import { IonPage, IonToolbar, IonContent, IonModal, IonHeader, IonTitle,
        IonCard, IonCardTitle, IonCardSubtitle, IonCardHeader, IonCardContent,
        IonGrid, IonCol, IonRow, IonSpinner, IonNote, IonButtons, IonButton, IonIcon,
        IonBadge, IonChip, IonList, IonItem, IonLabel, IonAccordion, IonAccordionGroup,
        loadingController, modalController, toastController, } from '@ionic/vue';
import ProfessionModal from '@/components/pss/ProfessionModal.vue';

// University
import UniProfessionResultPageModal from '@/components/university/UniProfessionResultPageModal.vue';
import UniProfessionDeckModal from '@/components/university/UniProfessionDeckModal.vue';
import UniEmployerSelectModal from '@/components/university/EmployerSelectModal.vue';
import JobEXEventsModal from '@/components/university/JobEXEventsModal.vue';
import CVModal from '@/components/university/CVModal.vue';
import CLModal from '@/components/university/CLModal.vue';
import EmployerJobModal from '@/components/university/EmployerJobModal.vue';

// types
import { Session, Profession, } from '@/types';

// composables
import { useI18n } from 'vue-i18n';
import { useStore } from '@/store';
import { utils } from '@/composables/utils';

// services
import ABSService from '@/services/ABSService';
import JobEXService from '@/services/JobEXService';

export default {
  name: 'HomePage',
  components: { IonPage, IonToolbar, IonContent, IonModal, IonHeader, IonTitle,
                IonCard, IonCardTitle, IonCardSubtitle, IonCardHeader, IonCardContent,
                IonGrid, IonCol, IonRow, IonSpinner, IonNote, IonButtons, IonButton, IonIcon,
                IonBadge, IonChip, IonList, IonItem, IonLabel, IonAccordion, IonAccordionGroup, },
  setup() {
    // methods
    const { t } = useI18n();
    const store = useStore();
    const { openImageModal, openModal, getProxyImgLink, getQRCodeUrl, copyText, getIntakeYearOfDate, getCurrentIntakeYear,
            openBookingModal, openServiceModal, } = utils();

    // state variables
    const loadingData = computed(() => store.state.loadingUser || store.state.loadingData);
    const loadingPortalData = computed(() => store.state.loadingUser || store.state.loadingData || store.state.loadingPortalData);
    const user = computed(() => store.state.user);

    // --------------- University / jobEX --------------- //
    const userRelatedJobEX = computed(() => store.getters.userRelatedJobEX);
    const userJobEXIntakeSessions = computed<Session[]>(() => store.getters.userJobEXIntakeSessions());
    const userCareerPlan = computed(() => store.state.userCareerPlan); // saved on DB

    const careerPlan = reactive({
      orderedProfessions: [] as Profession[], // sorted by students (filtered professions)
      targetSegments: [],
      filteredSegments: [],
      targetEmployers: [],

      claim1: "",
      claim2: "",
      claim3: "",
    });
    const refreshTargetSegments = async () => {
      // Update list of selected segments (usually after changing professions)
      careerPlan.filteredSegments = store.getters.getRelatedSegmentsByProfessionId(careerPlan.orderedProfessions[0]?.id);
      const filteredSegmentIds = careerPlan.filteredSegments.map((s: any) => s.id);
      careerPlan.targetSegments = careerPlan.targetSegments.filter((s: any) => filteredSegmentIds.includes(s.id));
    }

    const loadSavedCareerPlan = (plan: any) => {
      if (plan && plan.orderedProfessions) {
        careerPlan.orderedProfessions = plan.orderedProfessions;
        careerPlan.targetSegments = plan.targetSegments;
        careerPlan.targetEmployers = plan.targetEmployers;
        careerPlan.claim1 = plan.claim1;
        careerPlan.claim2 = plan.claim2;
        careerPlan.claim3 = plan.claim3;

        refreshTargetSegments();
      }
    }
    const saveCareerPlan = async (noLoading = false) => {
      const loading = await loadingController.create({});
      if (!noLoading) await loading.present();
      JobEXService.saveUserCareerPlan(careerPlan);
      store.commit('updateUserCareerPlan', careerPlan);
      if (!noLoading) {
        loading.dismiss();
        const toast = await toastController.create({
          message: t('successSave'),
          duration: 3000,
          position: 'top',
        });
        toast.present();
      }
    }
    const jobEXOpenProfessionSelectModal = async () =>  {
      const modal = await modalController.create({
        component: UniProfessionResultPageModal,
        componentProps: {
          prefilledProfessions: careerPlan.orderedProfessions.slice(),
          oldUserProfessions: user.value.userProfessions?.slice() || [],
          oldUserSegments: user.value.userSegments?.slice() || [],
        },
        backdropDismiss: false,
      });
      modal.onDidDismiss().then(({ data }) => {
        if (data && data.selectedProfessions) {
          careerPlan.orderedProfessions = data.selectedProfessions;
          //if (!user.value.isAdmin) saveCareerPlan(data.noLoading); // save to DB
          saveCareerPlan(data.noLoading); // save to DB

          ABSService.upsertUserProfessions(data.userProfessions);
          ABSService.upsertUserSegments(data.userSegments);
          store.commit('updateUser', { userProfessions: data.userProfessions, userSegments: data.userSegments });
        }
      });
      return modal.present();
    }

    const jobEXOpenEmployerSelectModal = async () =>  {
      const modal = await modalController.create({
        component: UniEmployerSelectModal,
        componentProps: {
          prefilledSegments: careerPlan.targetSegments.slice(),
          segmentOptions: careerPlan.filteredSegments.slice(),
          prefilledEmployers: careerPlan.targetEmployers.slice(),
          prefilledClaim1: careerPlan.claim1,
          prefilledClaim2: careerPlan.claim2,
          prefilledClaim3: careerPlan.claim3,
        }
      });
      modal.onDidDismiss().then(({ data }) => {
        if (data && data.selectedEmployers) {
          careerPlan.targetSegments = data.selectedSegments;
          careerPlan.targetEmployers = data.selectedEmployers;
          careerPlan.claim1 = data.claim1;
          careerPlan.claim2 = data.claim2;
          careerPlan.claim3 = data.claim3;
          if (!user.value.isAdmin) saveCareerPlan(); // save to DB
        }
      });
      return modal.present();
    }

    onMounted(() => {
      loadSavedCareerPlan(userCareerPlan.value);
    });
    watch(userCareerPlan, (currUserCareerPlan: any) => { // triggered only when direct access to this page
      loadSavedCareerPlan(currUserCareerPlan);
    });

    return {
      // icons
      personCircle, compass, arrowForward, navigate, settingsOutline, checkmark, arrowBack,
      add, close, checkmarkCircle, alertCircle, pencil, peopleOutline, qrCodeOutline,
      createOutline, calendarOutline, calendarClearOutline, play,

      // variables
      loadingData, loadingPortalData,
      user,

      // methods
      t, getProxyImgLink, openImageModal, getQRCodeUrl, copyText,

      isUserAttendedLesson: (lessonId) => (user.value.sessionResponses?.find(r => (r.lessonId == lessonId && r.attended == 'Yes'))),
      isShowEvent: (ev: Session) => {
        const evStart = new Date(ev.startTimeStr);
        return (evStart > new Date(+new Date() - 5*86400000) && evStart < new Date(+new Date() + 30*86400000)) || ev.userResponse?.confirmed == "Yes"
      },

      // --------------- University / jobEX --------------- //
      userRelatedJobEX,
      careerPlan, saveCareerPlan,
      jobEXOpenProfessionSelectModal,
      jobEXOpenEmployerSelectModal,
      getProfessionNameById: (id: any) => (store.state.allProfessions.find(p => p.id == id)?.name),
      filteredJobEXIntakeSessions: (targetAnchorEvId) => {
        return userJobEXIntakeSessions.value.filter(s => s.anchorEventId == targetAnchorEvId);
      },
      isUserUniYear1: () => {
        const { yearOfStudy, createdAt } = user.value;
        return yearOfStudy == 'Year 1' && getIntakeYearOfDate(createdAt) == getCurrentIntakeYear();
      },
      openYr1ExploreProfessionModal: async () => (await openModal(UniProfessionDeckModal, { isYear1: true })),
      openJobEXEventsModal: async (targetAnchorEvId, attendedLessonAt = null) => {
        return openModal(JobEXEventsModal, {
          attendedLesson: !!attendedLessonAt,
          anchorEventId: targetAnchorEvId,
        });
      },
      jobEXIsSelectedProfessions: () => {
        const { userProfessions } = user.value;
        const profs = (userProfessions || []).filter(up => up.reaction == 'like').sort((a,b) => (Number(a.order)-Number(b.order)));
        return profs.length >= 2 && profs.slice(0, 2).every(up => (user.value.userSegments?.some(us => (us.reaction == 'like' && us.professionId == up.professionId))));
      },
      jobEXIsSelectedEmployers: () => (careerPlan.targetEmployers.length > 0),
      openProfessionModal: async (professionId: any, expandAlumniSection = false) => await openModal(ProfessionModal, { professionId, useBackButton: true, expandAlumniSection }),
      openCVModal: async () => (await openModal(CVModal, {})),
      openCLModal: async () => (await openModal(CLModal, {})),
      openEmployerJobModal: async () => await openModal(EmployerJobModal, {}), // Employer / JD for jobEX guest lectures
      openBookingModal,
      openServiceModal,
    }
  },
}
</script>

<style scoped>
  ion-icon[slot="start"] {
    margin: 2px;
  }
  ion-card {
    margin-inline: 1px;
  }
  ion-item::part(native) {
    padding-inline-start: 0;
  }
</style>