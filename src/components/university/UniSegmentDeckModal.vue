<template>
  <ion-header>
    <ion-toolbar>
      <!-- Back buttons (back / close modal) -->
      <ion-buttons slot="start">
        <ion-button slot="icon-only" @click="confirmSelect(true)">
          <ion-icon :icon="arrowBack"></ion-icon>
        </ion-button>
      </ion-buttons>
      <ion-icon slot="start" style="font-size: 20px" :icon="thumbsUpOutline"></ion-icon>

      <ion-title style="padding-left: 16px">
        <ion-label class="ion-text-wrap">
          <h2><b>{{ professionName }}</b></h2>
          <p>working in</p>
        </ion-label>
      </ion-title>
    </ion-toolbar>

    <!-- Searchbar Input -->
    <ion-toolbar class="top-left-fixed" v-show="isSearching">
      <ion-buttons slot="start">
        <ion-button slot="icon-only" @click="confirmSelect(true)">
          <ion-icon :icon="arrowBack"></ion-icon>
        </ion-button>
      </ion-buttons>

      <ion-searchbar id="keyword-searchbar" style="padding-bottom: 1px" mode="md" v-model="searchKeyword" :placeholder="t('search')"
                    @keyup.enter="(e) => e.target.blur()"></ion-searchbar>
      <!--<ion-searchbar id="keyword-searchbar" style="padding-bottom: 1px" mode="md" v-model="searchKeyword" :placeholder="t('search')"
                    @ionFocus="isSearching = true" @ionCancel="isSearching = false"
                    @keyup.enter="(e) => e.target.blur()" show-cancel-button="always"></ion-searchbar>-->
    </ion-toolbar>
  </ion-header>
  
  <ion-content :fullscreen="true">
    <div v-if="isSearching">
      <div v-show="recommendedItems().length > 0">
        <ion-item lines="none">
          <ion-label class="ion-text-wrap">
            <p><b>Select</b></p>
          </ion-label>
        </ion-item>
        <ion-item lines="full" v-for="s in recommendedItems().slice(0, numOfVisibleItems)" :key="s.id" style="--min-height: 36px">
          <ion-checkbox
            justify="start"
            labelPlacement="end"
            @update:modelValue="onCheckSegment($event, s)"
            :modelValue="selectedItems.find(x => x.id == s.id) != null"
          >
            <ion-label>
              <ion-text class="ion-text-wrap" :color="isRelated(s) ? 'primary' : undefined">
                <p>{{ s.name }}</p>
              </ion-text>
            </ion-label>
          </ion-checkbox>
          <ion-button @click="openSegmentModal(s)" color="medium" slot="end" fill="clear" size="small" style="margin-inline-end: 16px; text-transform: none">
            Details
          </ion-button>
        </ion-item>
        
        <ion-infinite-scroll
          @ionInfinite="loadData($event)" 
          threshold="100px" 
          id="infinite-scroll"
        >
          <ion-infinite-scroll-content
            loading-spinner="bubbles"
            loading-text="Loading...">
          </ion-infinite-scroll-content>
        </ion-infinite-scroll>
      </div>
      <div class="ion-text-center" v-show="recommendedItems().length == 0">
        <p>No segments available</p>
      </div>
    </div>

    <!--
      Choose Items (Cards)
      -->
    <div style="height: 100%" v-else>
      <swiper
          :navigation="!isMobileWeb()"
          class="card-slides"
          id="card-swiper-slides"
          :effect="'cards'"
          :grabCursor="true"
          :modules="modules"
          v-if="!delayLoading && recommendedItems().length > 0"
      >
        <!-- Go Back to Previous -->
        <swiper-slide class="card-slide" style="background-color: var(--ion-color-light)" v-if="(startIdx+1)-10 > 0">
          <ion-row>
            <ion-col size="12">
              <ion-button size="large" color="primary" class="no-text-transform" fill="outline"
                          v-on="{ [isMobileWeb() ? 'touchstart' : 'click']: () => { onClickPrevBtn() } }">
                <ion-icon slot="end" :icon="arrowBack"></ion-icon>
                Previous
              </ion-button>
            </ion-col>
          </ion-row>
        </swiper-slide>

        <!-- Main Slides -->
        <swiper-slide class="card-slide" v-for="item in recommendedItems().slice(startIdx, startIdx+10)" :key="item.id"
                      :class="{ 'highlighted-border': isSelected(item) }" :data-segment-id="item.id">

          <div class="top-badge" v-if="['like','dislike'].includes(selectedFilterGroup) && getAppStateText(item.id)">
            <ion-chip class="small-chip">{{ getAppStateText(item.id) }}</ion-chip>
          </div>

          <ion-row>
            <ion-col size="12"><h1>{{ item.name }}</h1></ion-col>
            <ion-col size="12">
              <ion-button size="small" color="light" class="no-text-transform" @click="openSegmentModal(item)">
                Details
              </ion-button>
            </ion-col>
          </ion-row>
          
          <ion-fab slot="fixed" vertical="top" horizontal="end">
            <ion-fab-button style="--background: transparent" v-on="{ [isMobileWeb() ? 'touchstart' : 'click']: () => { onThumbsUp(item) } }">
              <ion-icon size="large" :icon="isSelected(item) ? thumbsUp : thumbsUpOutline"></ion-icon>
            </ion-fab-button>
          </ion-fab>

        </swiper-slide>

        <!-- More (Next) -->
        <swiper-slide class="card-slide" style="background-color: var(--ion-color-light)" v-if="startIdx+10 < recommendedItems().length">
          <ion-row>
            <ion-col size="12">
              <ion-button size="large" color="primary" class="no-text-transform" fill="outline"
                          v-on="{ [isMobileWeb() ? 'touchstart' : 'click']: () => { onClickMoreBtn() } }">
                More
                <ion-icon slot="end" :icon="arrowForward"></ion-icon>
              </ion-button>
            </ion-col>
          </ion-row>
        </swiper-slide>
      </swiper>
    </div>
  </ion-content>

  <ion-footer v-show="!isSearching">
    <!-- Filter Groups -->
    <ion-toolbar style="--min-height: 24px">
      <ion-buttons slot="start">
        <ion-button class="nav-category-btn" @click="navigateMaterialCategories('prev')">
          <ion-icon size="small" slot="icon-only" :icon="chevronBack"></ion-icon>
        </ion-button>
      </ion-buttons>
      <ion-segment class="filter-group-segment" mode="ios" v-model="selectedFilterGroup" scrollable>
        <!-- Questions -->
        <ion-segment-button value="thisProfession" v-if="relatedSegments.length > 0">
          <ion-label>Related segments</ion-label>
        </ion-segment-button>
        <ion-segment-button value="relatedSectors">
          <ion-label>Other segments (may not employ this profession)</ion-label>
        </ion-segment-button>
        <!--
        <ion-segment-button value="allRelatedSectors" v-if="relatedSegments.length > 0">
          <ion-label>Other segments</ion-label>
        </ion-segment-button>
        -->
        <ion-segment-button value="like" v-if="userItems.some(up => up.reaction == 'like')">
          <ion-icon size="small" :icon="thumbsUpOutline"></ion-icon>
        </ion-segment-button>
        <!--
        <ion-segment-button value="dislike" v-if="userItems.some(up => up.reaction == 'dislike')">
          <ion-icon size="small" :icon="thumbsDownOutline"></ion-icon>
        </ion-segment-button>
        <ion-segment-button value="random">
          <ion-ripple-effect></ion-ripple-effect>
          <ion-icon size="small" :icon="repeat"></ion-icon>
        </ion-segment-button>
        -->
        <ion-segment-button value="search">
          <ion-ripple-effect></ion-ripple-effect>
          <ion-icon size="small" :icon="search"></ion-icon>
        </ion-segment-button>
      </ion-segment>
      <ion-buttons slot="end">
        <ion-button class="nav-category-btn" @click="navigateMaterialCategories('next')">
          <ion-icon size="small" slot="icon-only" :icon="chevronForward"></ion-icon>
        </ion-button>
      </ion-buttons>
    </ion-toolbar>

    <!-- Question Chips (filters) -->
    <ion-toolbar class="material-div">
      <!-- Major employers for this profession -->
      <ion-row v-show="selectedFilterGroup == 'thisProfession'">
        <ion-chip :class="{ 'active-tag': selectedFilterGroup == 'thisProfession' }">
          <ion-label>For this profession</ion-label>
        </ion-chip>
      </ion-row>

      <!-- Program related sectors -->
      <ion-row v-show="selectedFilterGroup == 'relatedSectors'">
        <ion-chip v-for="s in programWithProfessions['related_sectors']" :key="s.id" @click="selectedOption = s.id"
                  :class="{ 'active-tag': selectedOption == s.id }">
          <ion-label>{{ s.name }}</ion-label>
        </ion-chip>
      </ion-row>
      
      <!-- All related sectors -->
      <ion-row v-show="selectedFilterGroup == 'allRelatedSectors'">
        <ion-chip v-for="s in getAllRelatedSectors()" :key="s.id" @click="selectedOption = s.id" :class="{ 'active-tag': selectedOption == s.id }">
          <ion-label>{{ s.name }}</ion-label>
        </ion-chip>
      </ion-row>
    </ion-toolbar>
  </ion-footer>
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, watch, reactive, onMounted, onUnmounted, onBeforeUnmount } from 'vue';

// icons
import { add, close, checkmark, arrowUp, arrowForward, arrowBack, trashOutline,
        thumbsUpOutline, thumbsDownOutline, thumbsUp, thumbsDown, heart, heartOutline,
        chevronBack, chevronForward, repeat, search, } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonGrid, IonRow, IonCol,
        IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonSearchbar, IonSegment, IonSegmentButton, IonList,
        IonChip, IonText, IonCheckbox, IonRippleEffect, IonFab, IonFabButton, IonInfiniteScroll, IonInfiniteScrollContent,
        IonicSlides, isPlatform, modalController, } from '@ionic/vue';
import ProfessionModal from '@/components/pss/ProfessionModal.vue';
import SectorModal from '@/components/pss/SectorModal.vue';

// Swiper
import 'swiper/swiper.min.css';
import 'swiper/modules/effect-cards/effect-cards.min.css';
import 'swiper/modules/navigation/navigation.min.css';
import '@ionic/vue/css/ionic-swiper.css';
import { Swiper, SwiperSlide } from 'swiper/vue/swiper-vue';
import { EffectCards, Navigation, } from 'swiper';

// composables / services
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';
import { Sector, Segment, UserSegment } from '@/types';

export default defineComponent({
  name: 'UniSegmentDeckModal',
  props: ["prefilledSegments", "oldUserSegments", "professionId", "professionName"],
  components: { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonGrid, IonRow, IonCol,
                IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonSearchbar, IonSegment, IonSegmentButton, IonList,
                IonChip, IonText, IonCheckbox, IonRippleEffect, IonFab, IonFabButton, IonInfiniteScroll, IonInfiniteScrollContent,
                Swiper, SwiperSlide },
  setup(props) {
    // methods or filters
    const store = useStore();
    const { openModal, closeModal, doReorder, navigateMaterialCategories, infiniteScrollLoadData,
            processUserItems, setUserItemReaction, isItemSelected, isItemDisliked, isMobileWeb,
            onThumbsUpItem, onThumbsDownItem, onClickMoreBtn, onClickPrevBtn, animateToFirstCard,
            recordCurrSlideReaction, resetActiveSlide, syncChosenItems, resetFilters, focusKeywordSearchbar, } = utils();
    const { t } = useI18n();

    const relatedSegments = computed(() => store.getters.getRelatedSegmentsByProfessionId(props.professionId));
    const allSegments = computed<Segment[]>(() => store.state.allSegments);
    const allSectors = computed<Sector[]>(() => store.state.allSectors);
    const programWithProfessions = computed(() => store.state.currProgram);

    const prefilledItems = ref<any>([]);
    const selectedItems = ref<Segment[]>(props.prefilledSegments || []);
    const userItems = ref<UserSegment[]>(props.oldUserSegments || []);
    const tmpNewUserItems = ref<UserSegment[]>([]); // for storing slideChange user professions

    const selectedFilterGroup = ref('thisProfession');
    const selectedOption = ref<any>("all");
    //const selectedFilterGroup = ref('search');
    //const selectedOption = ref<any>("search");
    //const isSearching = ref(true);

    const searchKeyword = ref('');
    const isSearching = ref(false);
    const delayLoading = ref(true);

    const numOfVisibleItems = ref(20);
    const loadData = (ev: any) => { infiniteScrollLoadData(ev, numOfVisibleItems, allSegments.value) }

    const openProfessionModal = async (professionId: any) => await openModal(ProfessionModal, { professionId, useBackButton: true });
    const openSegmentModal = async (segment: any) => (await openModal(SectorModal, { sectorId: segment.sectorId, segmentId: segment.id }));

    // Helper functions for card slides
    const getAppState = () => ({
      selectedFilterGroup: selectedFilterGroup.value,
      selectedOption: selectedOption.value,
      searchKeyword: searchKeyword.value,
    });
    const confirmSelect = async (noLoading = false) => {
      const newUserItems = processUserItems(selectedItems.value, userItems.value, tmpNewUserItems.value, 'segmentId', store.state.user.id);
      await closeModal({
        "selectedSegments": selectedItems.value, noLoading,
        "userSegments": newUserItems.map(ui => ({ ...ui, professionId: props.professionId })), // link to related profession
      });
    };
    const setReaction = (segmentId: any, reaction: any, skipIfExists = false) => {
      setUserItemReaction(
        segmentId, reaction, 'segmentId', userItems, tmpNewUserItems, getAppState(), skipIfExists
      );
    }
    const recordActiveSlideReaction = () => {
      recordCurrSlideReaction('segmentId', userItems, tmpNewUserItems, getAppState());
    }
    const isSelected = (item: any) => (isItemSelected(item, selectedItems));
    const isDisliked = (item: any) => (isItemDisliked(item, 'segmentId', userItems));
    const isRelated = (item: any) => (relatedSegments.value.find(s => s.id == item.id)); // related to the profession
    const isPrefilled = (item: any) => (prefilledItems.value.find(s => s.id == item.id)); // prefilled segments

    const startIdx = ref(0); // for card slides

    const getAllRelatedSectors = () => {
      const sectorIds = [
        ...relatedSegments.value.map(s => s.sectorId),
        ...programWithProfessions.value['related_sectors'].map(s => s.id)
      ];
      return [...new Set(sectorIds)].map(id => allSectors.value.find(s => s.id == id));
    }

    // Record Access & Leave Time
    onMounted(() => {
      if (props.prefilledSegments) {
        prefilledItems.value = props.prefilledSegments?.slice();
      }
      if (relatedSegments.value.length == 0) {
        selectedFilterGroup.value = 'relatedSectors';
      }

      // Prefill with previously written reasons
      syncChosenItems('segmentId', selectedItems, userItems, allSegments.value);

      setTimeout(() => {
        delayLoading.value = false;
        setTimeout(() => {
          const slides: any = document.querySelector('#card-swiper-slides');
          if (slides) {
            slides.swiper.on('slideChange', recordActiveSlideReaction);
            recordActiveSlideReaction(); // initial slide
          }
        }, 200);
      }, 200);
    })
    watch(selectedOption, () => {
      resetActiveSlide(startIdx, delayLoading, 'segmentId', userItems, tmpNewUserItems, getAppState());
    })
    watch(selectedFilterGroup, (currGroup) => {
      if (['like', 'dislike'].includes(currGroup)) {
        selectedOption.value = currGroup;
      }
      else if (currGroup == 'search') {
        resetFilters(selectedFilterGroup, selectedOption);
        focusKeywordSearchbar(isSearching);
      }
      else if (currGroup == 'relatedSectors') {
        selectedOption.value = programWithProfessions.value['related_sectors'][0]?.id;
      }
      else if (currGroup == 'allRelatedSectors') {
        selectedOption.value = getAllRelatedSectors()[0]?.id;
      }
      else {
        selectedOption.value = currGroup;
      }
    })

    // return variables & methods to be used in template HTML
    return {
      // icons
      add, close, checkmark, arrowUp, arrowForward, arrowBack, trashOutline,
      thumbsUpOutline, thumbsDownOutline, thumbsUp, thumbsDown, heart, heartOutline,

      // variables
      selectedOption,
      selectedItems, userItems, tmpNewUserItems,
      searchKeyword, isSearching,
      delayLoading,

      // methods
      t, isMobileWeb,
      doReorder,
      confirmSelect, closeModal,
      openProfessionModal, openSegmentModal,
      recommendedItems: () => {
        const selectedOpt = selectedOption.value;
        if (searchKeyword.value) {
          const filteredItems = searchKeyword.value == '' ? allSegments.value : allSegments.value.filter(p => {
            return p.name.toLowerCase().includes(searchKeyword.value.toLowerCase());
          });
          return filteredItems;
        }
        if (['like', 'dislike'].includes(selectedOpt)) {
          return userItems.value.filter(up => up.reaction == selectedOpt).map(up => {
            return allSegments.value.find(p => p.id == up.segmentId); 
          });
        }
        let filteredItems: any = allSegments.value;
        switch (selectedFilterGroup.value) {
          case 'thisProfession':
            filteredItems = relatedSegments.value;
            break;
          case 'relatedSectors':
            filteredItems = filteredItems.filter(s => s.sectorId == selectedOption.value);
            break;
        }
        
        return [
          ...filteredItems.filter(d => isPrefilled(d)),
          ...filteredItems.filter(d => isRelated(d) && !isPrefilled(d)),
          ...filteredItems.filter(d => !isRelated(d) && !isPrefilled(d)),
          //...filteredItems.filter(d => isDisliked(d)),
        ];
      },

      onThumbsUp: (item: any) => (onThumbsUpItem(item, 'segmentId', selectedItems, userItems, tmpNewUserItems, getAppState())),
      onThumbsDown: (item: any) => (onThumbsDownItem(item, 'segmentId', selectedItems, userItems, tmpNewUserItems, getAppState())),
      setReaction,
      isRelated, isSelected, isDisliked,

      // swiper
      modules: [EffectCards, IonicSlides, Navigation],
      startIdx,
      onClickMoreBtn: () => { onClickMoreBtn(startIdx) },
      onClickPrevBtn: () => { onClickPrevBtn(startIdx) },

      // Filter groups & filters
      chevronBack, chevronForward, repeat, search,
      selectedFilterGroup, navigateMaterialCategories,

      getAppStateText: (itemId: any) => {
        const up = userItems.value.find(up => (up.professionId == itemId));
        const { selectedOption, searchKeyword } = up?.appState || {};
        return searchKeyword || (selectedOption ? selectedOption.text : null);
      },

      // data
      allSectors,
      programWithProfessions,
      getAllRelatedSectors,
      relatedSegments,

      // List segments
      loadData, numOfVisibleItems,
      onCheckSegment: (checked: any, segment: Segment) => {
        if (checked) {
          if (selectedItems.value.find(x => x.id == segment.id) == null) {
            selectedItems.value.unshift(segment);
          }
        }
        else {
          const idx = selectedItems.value.findIndex(x => x.id == segment.id);
          if (idx !== -1) selectedItems.value.splice(idx, 1);
          const us = userItems.value.find(ui => ui.segmentId == segment.id);
          if (us) us.reaction = "";
        }
      },
    }
  },
});
</script>

<style scoped>
</style>