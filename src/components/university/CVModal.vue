<template>
  <ion-header>
    <ion-toolbar style="--min-height: 36px">
      <ion-buttons slot="start">
        <ion-button slot="icon-only" @click="closeModal()"><ion-icon :icon="arrowBack"></ion-icon></ion-button>
      </ion-buttons>
      <ion-segment color="primary" mode="ios" v-model="section" scrollable>
        <!--
        <ion-segment-button @click="openCLModal()">
          <ion-label class="ion-text-wrap"><p>Cover letter</p></ion-label>
        </ion-segment-button>
        -->
        <ion-segment-button :value="1">
          <ion-label class="ion-text-wrap"><p>CV | Cover letter</p></ion-label>
        </ion-segment-button>
      </ion-segment>
    </ion-toolbar>
  </ion-header>
  
  <ion-content :fullscreen="true">
    <!-- Loading Data -->
    <div class="spin" v-if="loading">
      <ion-spinner></ion-spinner>
    </div>

    <div v-else>
      <div v-show="section == 1">
        <ion-list>
          <ion-item @click="openChatbotModal()" button detail="">
            <ion-label>
              <h2><b>1. Generate CV bullet points / CL claims</b></h2>
            </ion-label>
          </ion-item>

          <!-- Transform headlines -->
          <ion-accordion-group>
            <ion-accordion style="--min-height: 24px">
              <ion-item slot="header">
                <ion-label>
                  <h2><b>2. Transform CV headlines</b></h2>
                </ion-label>
              </ion-item>
              <div slot="content">
                <img style="width: 100%" :src="getProxyImgLink('https://docs.google.com/presentation/d/1PcPhzYb77IrgcrfWrPNKf3VUhrsW3ENq4D3xuKZvfaw/export/jpeg?id=1PcPhzYb77IrgcrfWrPNKf3VUhrsW3ENq4D3xuKZvfaw&pageid=g24552ba4bf2_0_30')" />
              </div>
            </ion-accordion>
          </ion-accordion-group>

          <!-- Finish (previous: Tailor sections) -->
          <ion-item :href="user['profileDoc'] || 'https://docs.google.com/document/d/1k7worydNb-ntGs9gySuWC9-p35tl163mpMuR5gX_ZIA/edit'"
                    target="_blank" button detail>
            <ion-label>
              <h2><b>3. Finish</b></h2>
              <p><small>CV and Cover Letter templates shared with your Gmail: {{ user.email }}</small></p>
            </ion-label>
          </ion-item>

          <!-- Check before submit -->
          <ion-accordion-group>
            <ion-accordion style="--min-height: 24px">
              <ion-item slot="header">
                <ion-label>
                  <h2><b>4. Check before submit</b></h2>
                </ion-label>
              </ion-item>
              <div slot="content" class="ion-text-center" style="overflow-y: scroll">
                <!-- https://docs.google.com/presentation/d/e/2PACX-1vRPIoEUK064odWPUCaPCkJVf1Zycy2M8ff6WBng_lVRzWunYeC52UOGUUVqcj8V0cAqgQliBky21tqC/pub?start=false&loop=false&delayms=3000 -->
                <iframe src="https://docs.google.com/presentation/d/e/2PACX-1vRPIoEUK064odWPUCaPCkJVf1Zycy2M8ff6WBng_lVRzWunYeC52UOGUUVqcj8V0cAqgQliBky21tqC/embed?start=false&loop=false&delayms=3000&rm=full"
                        frameborder="0" width="100%" height="450px" allowfullscreen="true" mozallowfullscreen="true" webkitallowfullscreen="true"></iframe>
              </div>
            </ion-accordion>
          </ion-accordion-group>

          <!-- Book a time for consultation -->
          <ion-item @click="openBookingModal('bi82ce0b45')" button detail>
            <ion-label>
                <h2><b>5. Book an online CV consultation</b></h2>
            </ion-label>
          </ion-item>
        </ion-list>
      </div>

      <ion-modal :keep-contents-mounted="true" :is-open="section == 3">
        <ion-header>
          <ion-toolbar>
            <ion-buttons slot="start">
              <ion-button slot="icon-only" @click="section = 1">
                <ion-icon :icon="arrowBack"></ion-icon>
              </ion-button>
            </ion-buttons>
            <ion-title>
              <ion-label><h2><b>Draft bullet points</b></h2></ion-label>
            </ion-title>
          </ion-toolbar>
          <ion-toolbar>
            <ion-searchbar style="padding-top: 15px" mode="ios" v-model="searchKeyword" :placeholder="t('search')"
                          @keyup.enter="(e) => e.target.blur()"></ion-searchbar>
          </ion-toolbar>
        </ion-header>
        <ion-list>
          <ion-item v-for="p in professionsWithCv" :key="p.id" button detail @click="openCVExamplesModal(p)"
                    v-show="!searchKeyword || p.name.toLowerCase().includes(searchKeyword.toLowerCase().trim())">
            <ion-label>
              <!--{{ p.name }}&nbsp;&nbsp;<small>({{ p.numOfCvBestExamples }})</small>-->
              {{ p.name }}
            </ion-label>
          </ion-item>
        </ion-list>
      </ion-modal>
    </div>

  </ion-content>
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, watch, onMounted, } from 'vue';

// icons
import { add, close, checkmark, arrowUp, arrowForward, arrowBack, trashOutline, caretDown, createOutline, openOutline, } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
        IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonReorderGroup, IonReorder,
        IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption,
        IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCheckbox,
        IonInfiniteScroll, IonInfiniteScrollContent, IonSpinner, IonModal,
        modalController, } from '@ionic/vue';
import CVExamplesModal from "@/components/university/CVExamplesModal.vue";
import CLModal from '@/components/university/CLModal.vue';
import ChatbotModal from '@/components/modals/ChatbotModal.vue';

// composables
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';

// services
import PortalService from '@/services/PortalService';

export default defineComponent({
  name: 'CVModal',
  props: [],
  components: { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
                IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonReorderGroup, IonReorder,
                IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption,
                IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCheckbox,
                IonInfiniteScroll, IonInfiniteScrollContent, IonSpinner, IonModal, },
  setup(props) {
    // methods or filters
    const store = useStore();

    const { openModal, closeModal, presentToast, getProxyImgLink, openBookingModal, } = utils();
    const { t } = useI18n();

    const loading = computed(() => store.state.loadingData || store.state.loadingPortalData || store.state.loadingProgram);
    const loadingExamples = ref(false);
    const user = computed(() => store.state.user);
    const professionsWithCv = computed(() => store.getters.professionsWithCv);
    const searchKeyword = ref("");
    const selectedProfession = ref(null);
    const cvBestExamples = ref([]);
    const selectedBulletPoints = ref([]);

    const section = ref(1);
    const lastSection = 4;

    // 3. return variables & methods to be used in template HTML
    return {
      // icons
      add, close, checkmark, arrowUp, arrowForward, arrowBack, trashOutline, caretDown, createOutline, openOutline,

      // variables
      loading, loadingExamples, user,
      section, lastSection,
      searchKeyword, professionsWithCv, selectedProfession,
      cvBestExamples, selectedBulletPoints,

      // methods
      t,
      closeModal, getProxyImgLink,

      openCLModal: async () => {
        await openModal(CLModal, {});
        section.value = 1;
      },
      openCVExamplesModal: async (p) => (await openModal(CVExamplesModal, { chosenProfession: p })),
      openChatbotModal: async () => (await openModal(ChatbotModal, {})),

      // for online consultation
      openBookingModal,
    }
  }
});
</script>

<style scoped>
</style>