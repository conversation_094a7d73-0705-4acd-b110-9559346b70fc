<template>
  <ion-header>
    <ion-toolbar>
      <ion-buttons slot="start">
        <ion-button slot="icon-only" @click="closeModal()"><ion-icon :icon="close"></ion-icon></ion-button>
      </ion-buttons>

      <ion-title>
        <ion-label class="ion-text-wrap">
          <p>CV examples for {{ chosenProfession.name }}</p>
        </ion-label>  
      </ion-title>

    </ion-toolbar>
  </ion-header>
  
  <ion-content color="light">
    <!-- Loading Data -->
    <div class="ion-text-center" v-if="loadingExamples">
      <ion-spinner></ion-spinner>
    </div>

    <ion-accordion-group :multiple="true" v-else>
      <ion-accordion v-for="example in cvBestExamples" :key="example.headline" :value="example.headline">

        <ion-item color="medium" slot="header">
          <ion-label class="ion-text-wrap">
            <b>{{ example.headline }}</b>
          </ion-label>
        </ion-item>

        <ion-list class="suggestion-list" slot="content">
          <!--<ion-item v-for="point in example.bulletPoints" :key="point" @click="useCvExample(example.headline, point)" button>-->
          <ion-item lines="full" v-for="(point, i) in example.bulletPoints" :key="i">
            <!--<input type="checkbox" slot="start" v-model="selectedBulletPoints" :value="point" />-->
            <ion-label class="ion-text-wrap">
              <p>{{ point }}</p>
            </ion-label>
            
            <ion-button fill="outline" slot="end" size="small" @click="copyText(point)" style="margin-inline-start: 16px">
              Copy
            </ion-button>
          </ion-item>
        </ion-list>

      </ion-accordion>
    </ion-accordion-group>
  </ion-content>
</template>

<script lang="ts">
// vue
import { computed } from '@vue/reactivity';
import { defineComponent, onMounted, ref,  } from 'vue';

// icons
import { add, close } from 'ionicons/icons';

// components
import {
  IonInput,
  IonIcon,
  IonFooter,
  IonButton,
  IonButtons,
  IonCheckbox,
  IonContent,
  IonHeader,
  IonItem, IonLabel,
  IonList,
  IonTitle,
  IonSearchbar,
  IonToolbar,
  IonAccordion, IonAccordionGroup, IonSpinner,
  modalController
} from '@ionic/vue';
import config from '@/config';

// composables
import { useStore } from '@/store';
import { utils } from '@/composables/utils';
import { useI18n } from 'vue-i18n';
import PortalService from '@/services/PortalService';

export default defineComponent({
  name: 'CVExamplesModal',
  props: ["chosenProfession"],
  components: {
    IonInput,
    IonIcon,
    IonFooter,
    IonButton,
    IonButtons,
    IonCheckbox,
    IonContent,
    IonHeader,
    IonItem, IonLabel,
    IonList,
    IonTitle,
    IonSearchbar,
    IonToolbar,
    IonAccordion, IonAccordionGroup, IonSpinner,
  },
  setup(props) {
    const { closeModal, presentToast, copyText, } = utils();
    const { t } = useI18n();

    const store = useStore();

    const cvBestExamples = ref([]);
    const loadingExamples = ref(false);

    onMounted(() => {
      loadingExamples.value = true;

      PortalService.getCvCl(props.chosenProfession?.id).then(res => {
        const { curriculum_vitae: cv, cover_letter: cl } = res;
        const headlines = cv.match(/<em>([\S\s]*?)<\/em>/g);
        const bulletPointGroups = cv.match(/<ul>([\S\s]*?)<\/ul>/g);
        const bestExamples: any = [];
        if (headlines && bulletPointGroups) {
          for (let i = 0; i < headlines.length; i++) {
            const bulletPoints = bulletPointGroups[i].match(/<li>([\S\s]*?)<\/li>/g);
            bestExamples.push({
              headline: headlines[i].replace(/<[^>]*>?/gm, '').replace(/&#42;/g, "*"),
              bulletPoints: bulletPoints.map((pt) => pt.replace(/<[^>]*>?/gm, '')),
            })
          }
        }
        cvBestExamples.value = bestExamples;
        loadingExamples.value = false;
      });
    })

    return {
      // icon
      add, close,

      // variables
      loadingExamples, cvBestExamples,

      // methods
      t, closeModal,

      copyText,
    };
  },
});
</script>

<style scoped>
  ion-item {
    --min-height: 36px !important;
  }
</style>