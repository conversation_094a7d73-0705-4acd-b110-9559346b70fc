<template>
  <ion-header>
    <ion-toolbar>
      <ion-title>
        <ion-label class="ion-text-wrap">
          <h2>{{ formTitle }}</h2>
        </ion-label>
      </ion-title>
      <ion-buttons slot="start">
        <ion-button slot="icon-only" @click="closeModal()"><ion-icon :icon="arrowBack"></ion-icon></ion-button>
      </ion-buttons>
    </ion-toolbar>
  </ion-header>
  <ion-content :fullscreen="true">
    <ion-grid>
      <form @submit.prevent="submitFormResponse()">

        <ion-card v-for="(question, index) in questions || []" :key="question.id">
          <ion-card-content>
            <p class="question"><b>{{ index+1 }}. {{ question.title }}{{ question.isRequired ? "*" : "" }}</b></p>

            <!-- Linear Scale -->
            <div v-if="question.type == 'linear-scale'">
              <h3>{{ question.lsUpperScaleLabel }}</h3>
              <ion-item lines="none" class="answer-option-item" v-for="opt in OPTIONS.linearScale" :key="opt">
                <input type="radio" slot="start" v-model="formResponse[question.id]" :name="question.id" :value="opt" :required="question.isRequired" />
                <ion-label class="ion-text-wrap"><p>{{ opt }}</p></ion-label>
              </ion-item>
              <h3>{{ question.lsLowerScaleLabel }}</h3>
            </div>

            <!-- Multiple Choices -->
            <div v-else-if="question.type == 'multiple-choice'">
              <ion-item lines="none" class="answer-option-item" v-for="opt in question.options" :key="opt">
                <input type="radio" slot="start" v-model="formResponse[question.id]" :name="question.id" :value="opt" :required="question.isRequired" />
                <ion-label class="ion-text-wrap"><p>{{ opt }}</p></ion-label>
              </ion-item>
            </div>
              
            <!-- Single / Multiple Select -->
            <ion-select fill="outline" :interface="question.type == 'multi-select' ? null : 'popover'"
                        placeholder="Please select" :multiple="question.type == 'multi-select'" v-model="formResponse[question.id]" v-else-if="['single-select', 'multi-select'].includes(question.type)">
              <ion-select-option v-for="opt in question.options" :key="opt" :value="opt">
                {{ opt }}
              </ion-select-option>
            </ion-select>

            <!-- Paragraph (Long Answer) -->
            <div v-else-if="question.type == 'paragraph'">
              <ion-item fill="outline">
                <ion-textarea v-model="formResponse[question.id]" :required="question.isRequired"></ion-textarea>
              </ion-item>
            </div>

            <!-- Default: Short Answer -->
            <div v-else>
              <ion-item fill="outline">
                <ion-input type="text" v-model="formResponse[question.id]" :required="question.isRequired"></ion-input>
              </ion-item>
            </div>
          </ion-card-content>
        </ion-card>

        <ion-button class="ion-margin-vertical" type="submit" expand="block" :disabled="!someAnswersUpdated()">
          {{ t('submit') }}
        </ion-button>
      </form>
    </ion-grid>
  </ion-content>
</template>

<script lang="ts">
// Vue reactivity
import { reactive, defineComponent, computed, ref, onMounted } from 'vue';

// icons
import { mail, close, arrowBack, } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonSpinner, IonItem, IonLabel, IonChip, IonIcon,
        IonThumbnail, IonAvatar, IonButtons, IonButton, IonInput, IonTextarea,
        IonGrid, IonCol, IonRow, IonSelect, IonSelectOption, IonCard, IonCardContent,
        modalController, loadingController, toastController } from '@ionic/vue';

// API services
import UserService from '@/services/UserService';

// Utils
import { utils } from '@/composables/utils';
import { useI18n } from 'vue-i18n';
import { useStore } from '@/store';
import config from '@/config';

import { FormQuestion } from '@/types';
import ABSService from '@/services/ABSService';
import EventService from '@/services/EventService';

export default defineComponent({
  name: 'FormQuestionModal',
  props: ["formTitle", "formQuestionIds", "sessionId", "taskId", "oldResponses"],
  components: { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter,
                IonSpinner, IonItem, IonLabel, IonChip, IonIcon,
                IonThumbnail, IonAvatar, IonButtons, IonButton, IonInput, IonTextarea,
                IonGrid, IonCol, IonRow, IonSelect, IonSelectOption, IonCard, IonCardContent, },
  setup(props) {
    // 1. declare state variables (ref to make them reactive)
    const store = useStore();
    const user = computed(() => store.state.user);
    const questions = ref<FormQuestion[]>();
    const OPTIONS = {
      linearScale: ["5", "4", "3", "2", "1"],
    }

    const formResponse = reactive({});

    // methods or filters
    const { t } = useI18n();
    const { formatDate, presentToast, presentAlert } = utils();
    const closeModal = async (updatedUser: any = null) => {
      await modalController.dismiss({ updatedUser });
    };

    onMounted(() => {
      const { formQuestionIds, oldResponses } = props;

      // Get form questions related to the task
      questions.value = formQuestionIds.map(qid => (store.state.formQuestions.find(q => q.id == qid))).filter(q => !!q);

      // Prefill previous responses
      for (const r of oldResponses) {
        formResponse[r.questionId] = r.answer;
      }
    })

    // 3. return variables & methods to be used in template HTML
    return {
      // icons
      mail, close, arrowBack,

      // variables
      questions, formResponse,
      OPTIONS,

      // methods
      t, formatDate, closeModal,
      submitFormResponse: async () => {
        const loading = await loadingController.create({ });
        await loading.present();
        const { sessionId, taskId } = props;
        const responses = Object.keys(formResponse).map(questionId => ({
          questionId,
          questionTitle: questions.value?.find(q => q.id == questionId)?.title || "",
          answer: formResponse[questionId],
        }));
        EventService.upsertFormResponses(sessionId, taskId, responses); // pass session & event task IDs as well
        store.commit('upsertUserFormResponses', responses.map(resp => ({
          ...resp,
          sessionId,
          taskId,
        })));
        loading.dismiss();
        presentToast('Your responses have been saved.');
        closeModal();
      },

      someAnswersUpdated: () => {
        const { oldResponses } = props;
        if (oldResponses.length == 0) return Object.keys(formResponse).length > 0; // new submission
        for (const questionId in formResponse) {
          const latestAns = formResponse[questionId];
          const oldResponse = oldResponses.find(r => r.questionId == questionId) || {};
          if (oldResponse.answer != latestAns) {
            return true; // some answers updated
          }
        }
        return false;
      }
    }
  }
});
</script>

<style scoped>
  .question {
    font-size: 14px;
    font-weight: 400;
    margin-bottom: 10px;
  }
  .answer-option-item {
    left: -10px;
    --min-height: 28px;
  }
  .answer-option-item ion-label {
    margin: 5px 0 !important;
  }
  .answer-option-item p {
    font-weight: 400;
    color: var(--ion-color-dark);
  }
</style>