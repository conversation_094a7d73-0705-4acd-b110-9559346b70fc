<template>
  <ion-header>
    <ion-toolbar>
      <ion-title size="small" v-if="caption">
        <div style="white-space: pre-wrap">
          {{ caption }}
        </div>
      </ion-title>
      <ion-buttons slot="start">
        <ion-button slot="icon-only" @click="closeModal()"><ion-icon :icon="close"></ion-icon></ion-button>
      </ion-buttons>
    </ion-toolbar>
  </ion-header>
  <ion-content :fullscreen="true">
    <div v-if="noZoomInOut">
      <img class="main-img" :src="imageLink" @click="onClickImg()" />
    </div>

    <swiper :modules="modules" :zoom="true" v-else>
      <swiper-slide>
        <!--<div class="wrapper valign" :style="{ background: `linear-gradient(rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.7)), url(${imageLink})` }">-->
        <div class="wrapper">
          <div class="swiper-zoom-container">
            <img class="main-img" :src="imageLink" @click="onClickImg()" />
          </div>
        </div>
      </swiper-slide>
    </swiper>
  </ion-content>
</template>

<script lang="ts">
// Vue reactivity
import { defineComponent } from 'vue';

// Swiper
import 'swiper/swiper.min.css';
import '@ionic/vue/css/ionic-swiper.css';
import { Swiper, SwiperSlide } from 'swiper/vue/swiper-vue';
import { Zoom } from 'swiper';

// icons
import { close, arrowBack } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonButtons, IonButton, IonIcon,
         modalController } from '@ionic/vue';

// composables
import { utils } from '@/composables/utils';
        
export default defineComponent({
  name: 'ImageModal',
  props: ["imageLink", "caption", "onClickImgLink", "noZoomInOut"],
  components: { IonHeader, IonToolbar, IonTitle, IonContent, IonButtons, IonButton, IonIcon,
                Swiper, SwiperSlide, },
  setup(props) {
    const { openBrowser, } = utils();
    const closeModal = async () => { await modalController.dismiss() };
    
    // 3. return variables & methods to be used in template HTML
    return {
      // icons
      close, arrowBack,
      
      // variables
      modules: [Zoom],
      
      // methods
      closeModal,
      onClickImg: () => {
        if (props.onClickImgLink) {
          openBrowser(props.onClickImgLink);
        }
      }
    }
  }
});
</script>

<style scoped>
  .main-img {
    width: 100%;
  }
  .wrapper {
    /*height: 95vh;*/
    height: 100vh;
    overflow-y: scroll;
  }
  .valign {
    display: flex;
    justify-content: normal;
    align-items: center;
    vertical-align: middle;
  }
  ion-slides {
    height: 100vh;
  }
</style>