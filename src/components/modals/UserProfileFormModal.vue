<template>
  <ion-header v-if="!hideHeader">
    <ion-grid fixed>
      <ion-toolbar>
        <ion-buttons slot="start" v-if="!isPage">
          <ion-button slot="icon-only" @click="closeModal()"><ion-icon :icon="arrowBack"></ion-icon></ion-button>
        </ion-buttons>
        <ion-title v-if="fromAchieveJUPAS">Edit Electives & Personal Information</ion-title>
        <ion-title v-else>{{ t('UserProfilePage.editPersonalInfo') }}</ion-title>
      </ion-toolbar>
    </ion-grid>
  </ion-header>
  <ion-content :fullscreen="true">
    <ion-grid fixed>
      <form @submit.prevent="updateUser()">
        <!-- Studying Electives (for AchieveJUPAS) -->
        <div class="ion-margin-bottom" v-if="!targetUser && (user.teacher || fromAchieveJUPAS)">
          <ion-item class="ion-margin-top" fill="outline">
            <ion-label position="stacked">
              Studying electives (for AchieveJUPAS)
            </ion-label>
            <ion-select v-model="newUser.studyingElectives" multiple>
              <ion-select-option v-for="elective in OPTIONS.electives.sort((a) => (newUser.studyingElectives?.includes(a) ? -1 : 0))" :key="elective" :value="elective">
                {{ elective }}
              </ion-select-option>
            </ion-select>
          </ion-item>
        </div>

        <!-- Personal Info -->
        <ion-row>
          <ion-col size="12">
            <ion-item fill="outline">
              <ion-label position="stacked">{{ t('fullName') }}</ion-label>
              <ion-input v-model="newUser.fullName" required></ion-input>
            </ion-item>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col size="6">
            <ion-item fill="outline">
              <ion-label position="stacked">{{ t('preferredName') }}</ion-label>
              <ion-input v-model="newUser.preferredName"></ion-input>
            </ion-item>
          </ion-col>
          <ion-col size="6">
            <ion-item fill="outline">
              <ion-label position="stacked">{{ t('chineseName') }}</ion-label>
              <ion-input v-model="newUser.chineseName"></ion-input>
            </ion-item>
          </ion-col>
        </ion-row>

        <ion-row>
          <ion-col size="12">
            <ion-item fill="outline">
              <ion-label position="stacked">{{ t('UserProfilePage.emailAddress') }}</ion-label>
              <ion-input inputmode="email" type="email" v-model="newUser.email" required></ion-input>
            </ion-item>
          </ion-col>
        </ion-row>

        <!--
          Below fields for students only
        -->
        <div v-if="targetUser ? targetUser?.roles.includes('secondary-school-student') : (user.isAdmin || user.isSecondaryStudent)">
          <ion-row>
            <ion-col size="4">
              <ion-item fill="outline">
                <ion-label position="stacked">{{ t('class') }}</ion-label>
                <ion-input v-model="newUser.class"></ion-input>
              </ion-item>
            </ion-col>
            <ion-col size="8">
              <ion-item fill="outline">
                <ion-label position="stacked">{{ t('studentNumber') }}</ion-label>
                <ion-input v-model="newUser.studentNumber"></ion-input>
              </ion-item>
            </ion-col>
          </ion-row>

          <ion-row>
            <ion-col size="12">
              <!-- Rank In Form -->
              <ion-item class="ion-margin-top" fill="outline">
                <ion-label position="stacked">
                  What was your rank in your <b>form</b> on the last exam?*
                </ion-label>
                <ion-select interface="popover" v-model="newUser.rankInForm" required>
                  <ion-select-option v-for="rankRange in OPTIONS.rankInForm" :key="rankRange" :value="rankRange">
                    {{ rankRange }}
                  </ion-select-option>
                </ion-select>
              </ion-item>

              <!-- Studying Electives -->
              <ion-item class="ion-margin-top" fill="outline" v-if="!fromAchieveJUPAS">
                <ion-label position="stacked">
                  Which elective subject(s) are your studying?*
                </ion-label>
                <ion-select v-model="newUser.studyingElectives" multiple>
                  <ion-select-option v-for="elective in OPTIONS.electives.sort((a) => (newUser.studyingElectives?.includes(a) ? -1 : 0))" :key="elective" :value="elective">
                    {{ elective }}
                  </ion-select-option>
                </ion-select>
              </ion-item>

              <!-- Year HKDSE -->
              <ion-item class="ion-margin-top" fill="outline">
                <ion-label position="stacked">
                  In which year will you take the HKDSE?*
                </ion-label>
                <ion-select interface="popover" v-model="newUser.yearDSE">
                  <ion-select-option v-for="year in OPTIONS.yearDSE" :key="year" :value="year">
                    {{ year }}
                  </ion-select-option>
                </ion-select>
              </ion-item>
            </ion-col>
          </ion-row>
        </div>

        <ion-row>
          <ion-col size="12">
            <ion-item>
              <ion-label position="stacked">{{ t('UserProfilePage.phone') }}</ion-label>
              <ion-input maxlength="8" inputmode="numeric" v-model="newUser.phone" :disabled="!targetUser"></ion-input>
            </ion-item>
          </ion-col>
        </ion-row>

        <ion-button class="ion-margin-vertical" type="submit" expand="block">
          {{ t('UserProfilePage.save') }}
        </ion-button>
      </form>
    </ion-grid>
  </ion-content>
</template>

<script lang="ts">
// Vue reactivity
import { reactive, defineComponent, computed, onMounted, watch, } from 'vue';

// icons
import { mail, close, arrowBack, } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonSpinner, IonItem, IonLabel, IonChip, IonIcon,
        IonThumbnail, IonAvatar, IonButtons, IonButton, IonInput, IonTextarea, modalController, loadingController,
        IonGrid, IonCol, IonRow, IonSelect, IonSelectOption,
        toastController } from '@ionic/vue';

// API services
import UserService from '@/services/UserService';

// Utils
import { utils } from '@/composables/utils';
import { useI18n } from 'vue-i18n';
import { useStore } from '@/store';
import { useRouter } from 'vue-router';
import config from '@/config';

export default defineComponent({
  name: 'UserProfileFormModal',
  props: ["hideHeader", "isPage", "targetUser", "targetUserId", "fromAchieveJUPAS"],
  components: { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter,
                IonSpinner, IonItem, IonLabel, IonChip, IonIcon,
                IonThumbnail, IonAvatar, IonButtons, IonButton, IonInput, IonTextarea,
                IonGrid, IonCol, IonRow, IonSelect, IonSelectOption, },
  setup(props) {
    // 1. declare state variables (ref to make them reactive)
    const store = useStore();
    const router = useRouter();
    const user = computed(() => store.state.user);
    const newUser = reactive<any>({
      id: user.value.id,
      fullName: user.value.fullName,
      preferredName: user.value.preferredName,
      email: user.value.email,
      chineseName: user.value.chineseName,
      class: user.value.class,
      studentNumber: user.value.studentNumber,
      studyingElectives: user.value.studyingElectives,
      yearDSE: user.value.yearDSE,
      rankInForm: user.value.rankInForm,
      phone: user.value.phone,
    });

    // methods or filters
    const { t } = useI18n();
    const { formatDate, presentToast, presentAlert } = utils();
    const closeModal = async (updatedUser: any = null) => {
      if (props.isPage) {
        router.replace('/home');
      } else {
        await modalController.dismiss({ updatedUser });
      }
    };
    const updateUser = async () => {
      const { targetUser } = props;
      const loading = await loadingController.create({});
      await loading.present();
      try {
        await UserService.updateUser(newUser, targetUser || user.value);
        if (targetUser) store.commit('upsertSchoolStudents', [newUser]);
        else store.commit('updateUser', newUser);
        presentToast(t('successUpdatePersonalInfo'), 2000);
        closeModal();
      } catch (e) {
        //presentAlert(t('clashEmailAddress'));
        presentAlert(e.message);
      } finally {
        loading.dismiss();
      }
    }

    onMounted(() => {
      if (props.targetUser) {
        const { targetUser } = props;
        newUser.id = targetUser.id;
        newUser.fullName = targetUser.fullName;
        newUser.preferredName = targetUser.preferredName;
        newUser.email = targetUser.email;
        newUser.phone = targetUser.phone;
        newUser.chineseName = targetUser.chineseName;
        newUser.class = targetUser.class;
        newUser.studentNumber = targetUser.studentNumber;
        newUser.studyingElectives = targetUser.studyingElectives;
        newUser.yearDSE = targetUser.yearDSE;
        newUser.rankInForm = targetUser.rankInForm;
      }
      if (newUser.studyingElectives && !Array.isArray(newUser.studyingElectives)) {
        newUser.studyingElectives = newUser.studyingElectives.split(" , ");
      }
    })

    watch(user, () => {
      if (!props.targetUser) {
        newUser.id = user.value.id;
        newUser.fullName = user.value.fullName;
        newUser.preferredName = user.value.preferredName;
        newUser.email = user.value.email;
        newUser.phone = user.value.phone;
        newUser.chineseName = user.value.chineseName;
        newUser.class = user.value.class;
        newUser.studentNumber = user.value.studentNumber;
        newUser.studyingElectives = user.value.studyingElectives;
        newUser.yearDSE = user.value.yearDSE;
        newUser.rankInForm = user.value.rankInForm;
      }
    })

    // 3. return variables & methods to be used in template HTML
    return {
      // icons
      mail, close, arrowBack,

      // variables
      newUser, user,
      OPTIONS: config.formOptions,

      // methods
      t, formatDate, closeModal, updateUser
    }
  }
});
</script>

<style scoped>
</style>