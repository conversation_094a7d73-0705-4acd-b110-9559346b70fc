<template>
  <ion-header>
    <ion-toolbar>
      <!-- Back buttons (back / close modal) -->
      <ion-buttons slot="start">
        <ion-button slot="icon-only" @click="closeModal()">
          <ion-icon :icon="arrowBack"></ion-icon>
        </ion-button>
      </ion-buttons>

      <!-- (Latest best presenters) -->
      <ion-title class="ion-no-padding" v-show="selectedSection == 'leaderboard'" style="height: 52px">
        <ion-label class="ion-text-wrap">
          <h2>Most voted: <b>{{ sortedStudentsWithVotes(1).map(s => s.fullName).join(" , ") || 'N/A' }}</b> <ion-icon :icon="thumbsUpOutline" style="vertical-align: text-bottom"></ion-icon></h2>
        </ion-label>
      </ion-title>

      <!-- Search students -->
      <ion-searchbar mode="ios" id="keyword-searchbar" style="padding: 0" v-model="searchKeyword" :placeholder="t('search')"
                    @ionFocus="isSearching = true" @ionCancel="isSearching = false" @keyup.enter="(e) => e.target.blur()"
                    :style="{ 'visibility': ['all', 'groups'].includes(selectedSection) ? 'visible' : 'hidden' }"
                    v-show="selectedSection != 'leaderboard'"></ion-searchbar>
    </ion-toolbar>

    <ion-toolbar v-if="!isShowUniStudents">
      <ion-segment mode="ios" v-model="selectedSection" @ionChange="searchKeyword = ''" scrollable>
        <ion-segment-button value="all" v-if="!isStudentView">
          Participants
        </ion-segment-button>
        <ion-segment-button value="video" v-if="user.isAdmin && session.videoLink">
          Outstanding video
        </ion-segment-button>
        <ion-segment-button value="groups" v-if="['work-workshop-2'].includes(session.anchorEventId)">
          <!-- Only available in workshop events -->
          {{ isStudentView ? 'Participants' : 'By groups' }}
        </ion-segment-button>
        <!--<ion-segment-button value="shortlisted" v-show="shortlistedStudents().length > 0">-->
        <ion-segment-button value="shortlisted">
          {{ isStudentView ? 'Voted' : 'Shortlisted' }} ({{ shortlistedStudents().length }})
        </ion-segment-button>
        <!--<ion-segment-button value="leaderboard" v-if="isStudentView">-->
        <ion-segment-button value="leaderboard">
          Best presenters
        </ion-segment-button>
      </ion-segment>

      <ion-item lines="none" v-show="isStudentView && selectedSection == 'groups'">
        <ion-label class="ion-text-wrap">
          <p><i>Click on students' names to vote for them. You may also vote for yourself. (Your current vote{{ getMyCurrentVotes() > 1 ? 's' : '' }}: <b>{{ getMyCurrentVotes() }}</b>)</i></p>
        </ion-label>
      </ion-item>
    </ion-toolbar>

    <!--
      (May be needed later if many groups)
      Chips: Filters (Schools / Profession Groups)
    <ion-toolbar>
      <div style="max-height: 150px; overflow: scroll; padding: 8px">
        <ion-chip :class="{ 'active-tag': selectedFilter == 'all' || searchKeyword }" 
                  @click="selectedFilter = 'all'" :color="selectedFilter == 'all' ? '' : 'medium'" >
          <ion-label>All</ion-label>
        </ion-chip>

        <ion-chip v-for="schoolId in allRelatedSchoolIds()" :key="schoolId" :class="{ 'active-tag': selectedFilter == schoolId }" 
                  @click="selectedFilter = schoolId || ''" :color="selectedFilter == schoolId ? '' : 'medium'" >
          <ion-label>{{ schoolId?.toUpperCase() }}</ion-label>
        </ion-chip>
      </div>
    </ion-toolbar>
    -->
  </ion-header>
  
  <ion-content :fullscreen="true">
    <div class="spin" v-if="loadingData">
      <ion-spinner></ion-spinner>
    </div>
    
    <div v-else>
      <!-- Outstanding video -->
      <div v-show="selectedSection == 'video'">
        <!--<iframe class="responsive-embed" style="width: 100%; height: 380px" src="https://www.youtube.com/embed/6vZEDE1jgJU" frameborder="0" allowfullscreen></iframe>-->
        <iframe class="responsive-embed" style="width: 100%; height: 380px" :src="getYouTubeEmbedUrl(session.videoLink)" frameborder="0" allowfullscreen></iframe>
      </div>

      <!-- Leaderboard (most voted participants) -->
      <div v-show="selectedSection == 'leaderboard'">
        <ion-item lines="full" v-for="(user, idx) in sortedStudentsWithVotes()" :key="idx" button
                  @click="openUserDetailsModal(user)">
          <ion-label class="ion-text-wrap">
            <h3><b>{{ user.rank }}. {{ user.fullName }}{{ user.preferredName ? ` (${user.preferredName})` : '' }}</b> <small>{{ user.schoolId?.toUpperCase() }}</small></h3>
          </ion-label>

          <!-- Student Name & Class -->
          <ion-chip class="small-chip">
            <ion-label>
              <span>{{ user.numOfVotes }} vote{{ user.numOfVotes > 1 ? 's' : '' }}</span>
            </ion-label>
            <ion-icon color="warning" :icon="star"></ion-icon>
          </ion-chip>
        </ion-item>
      </div>

      <!-- List of participants (by schools / profession groups) -->
      <div v-show="['all', 'groups'].includes(selectedSection)">
        <ion-accordion-group :multiple="true" :value="(searchKeyword || isStudentView) ? Object.keys(filteredStudents()) : isShowUniStudents ? ['Participants'] : []">
          <ion-accordion style="--min-height: 24px" v-for="(users, group) in filteredStudents()" :key="group" :value="group">
            <ion-item color="primary" lines="full" slot="header">
              <ion-label>
                <p><b>{{ group }}<span v-if="selectedSection == 'all' && getSchoolName(group)"> ({{ getSchoolName(group) }})</span></b></p>
              </ion-label>
              <ion-badge color="fdmtred">{{ users.length }}</ion-badge>
            </ion-item>
            <div slot="content">
              <ion-item lines="full" v-for="user in users" :key="user.phone" button detail @click="openUserDetailsModal(user)">
                <!-- Shown when grouped by school -->
                <ion-label class="ion-text-wrap" v-if="selectedSection == 'all'">
                  <h3>{{ user.class }}{{ formatStudentNumber(user.studentNumber) }} {{ user.fullName }}{{ user.preferredName ? ` (${user.preferredName})` : '' }}</h3>
                  <p v-if="getUserUserObj(user.id, 'reason')"><small>Remarks: {{ getUserUserObj(user.id, 'reason') }}</small></p>
                </ion-label>

                <!-- Shown when by profession group -->
                <ion-label class="ion-text-wrap" v-else>
                  <h3><b>{{ user.fullName }}{{ user.preferredName ? ` (${user.preferredName})` : '' }}</b> <small>{{ user.schoolId?.toUpperCase() }}</small></h3>
                  <!--<p>{{ user.schoolId?.toUpperCase() }} {{ user.class }}{{ formatStudentNumber(user.studentNumber) }} {{ user.chineseName }}</p>-->
                  <p v-if="getUserUserObj(user.id, 'reason')"><small>Remarks: {{ getUserUserObj(user.id, 'reason') }}</small></p>
                </ion-label>

                <!-- Shorlisted badge -->
                <ion-badge color="fdmtred" v-if="getUserUserObj(user.id, 'grade')">{{ getUserUserObj(user.id, 'grade') }}</ion-badge>&nbsp;

                <!-- Shorlisted badge -->
                <ion-badge v-if="getUserUserObj(user.id, 'reaction') == 'like'">
                  {{ isStudentView ? 'Voted' : 'Shortlisted' }}
                </ion-badge>
              </ion-item>
            </div>
          </ion-accordion>
        </ion-accordion-group>
      </div>
      
      <div v-show="selectedSection == 'shortlisted'">
        <ion-reorder-group @ionItemReorder="reorderShortlistedStudents($event, shortlistedStudents())" :disabled="false">
          <ion-item lines="full" v-for="(user, idx) in shortlistedStudents()" :key="idx" button detail
                    @click="openUserDetailsModal(user)">
            <ion-reorder mode="ios" slot="start" style="margin-inline-end: 8px"></ion-reorder>

            <ion-label class="ion-text-wrap">
              <!--<h3><b>{{ user.schoolId?.toUpperCase() }}</b> {{ user.class }}{{ formatStudentNumber(user.studentNumber) }} {{ user.chineseName }}</h3>
              <p>{{ user.fullName }}{{ user.preferredName ? ` (${user.preferredName})` : '' }}</p>-->
              <h3><b>{{ idx+1 }}. {{ user.fullName }}{{ user.preferredName ? ` (${user.preferredName})` : '' }}</b> <small>{{ user.schoolId?.toUpperCase() }}</small></h3>
              <p v-if="getUserUserObj(user.id, 'reason')"><small>Remarks: {{ getUserUserObj(user.id, 'reason') }}</small></p>
            </ion-label>

            <!-- Grade (A+, A, ...) -->
            <ion-badge color="fdmtred" v-if="getUserUserObj(user.id, 'grade')">{{ getUserUserObj(user.id, 'grade') }}</ion-badge>
          </ion-item>
        </ion-reorder-group>
      </div>
    </div>
  </ion-content>

</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, onMounted, watch, inject, onBeforeUnmount, } from 'vue';

// icons
import { add, close, checkmark, arrowUp,  arrowForward, arrowBack, checkbox, trashOutline,
         thumbsUpOutline, thumbsDownOutline, thumbsUp, thumbsDown, heart, heartOutline,
         star, } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
        IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonReorderGroup, IonReorder, IonProgressBar,
        IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption, IonSpinner,
        IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid, IonRippleEffect,
        IonNote, IonTextarea, IonFab, IonFabButton, IonBadge, IonInfiniteScroll, IonInfiniteScrollContent, IonModal,
        isPlatform, getPlatforms, modalController, } from '@ionic/vue';
import UserDetailsModal from '@/components/modals/UserDetailsModal.vue';

// composables / services
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';
import { School, Session, User, UserProgram } from '@/types';
import EventService from '@/services/EventService';

// Supabase
import { SupabaseClient } from '@supabase/supabase-js'

export default defineComponent({
  name: 'SessionStudentListModal',
  props: ["sessionId", "isStudentView", "isShowUniStudents"],
  components: { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
                IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonReorderGroup, IonReorder, IonProgressBar,
                IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption, IonSpinner,
                IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid, IonRippleEffect,
                IonNote, IonTextarea, IonBadge, IonFab, IonFabButton, IonInfiniteScroll, IonInfiniteScrollContent, IonModal, },
  setup(props) {
    // methods or filters
    const store = useStore();
    const { closeModal, doReorder, openModal, getProxyImgLink, isMobileWeb,
            openImageModal, getAppSheetFileLink, formatStudentNumber, convertKeysToCamelCase,
            getYouTubeEmbedUrl, } = utils();
    const { t } = useI18n();

    const selectedSection = ref(props.isStudentView ? "groups" : "all");
    const selectedFilter = ref("all");
    const searchKeyword = ref("");
    const isSearching = ref(false);
    
    const loadingData = ref(true);
    const allParticipants = ref<User[]>([]);
    const allSchools = computed<School[]>(() => store.state.schools);
    const session = computed<Session>(() => store.getters.getSessionById(props.sessionId));
    const user = computed(() => store.state.user);
    const relatedUserUsers = (reaction: any = null) => (user.value.userUsers || []).filter(uu => uu.sessionId == props.sessionId && (!reaction || uu.reaction == reaction));

    // Get users who applied / attended the session
    const refreshSessionParticipants = async () => {
      const { sessionId, } = props;
      loadingData.value = true;
      const res = await EventService.getSessionParticipants(sessionId);
      allParticipants.value = res;
      loadingData.value = false;
    }

    // Supabase real-time subscriptions (for voting)
    const supabase = inject('supabase') as SupabaseClient;
    let channel;
    const handleChangedUserUsers = (payload) => {
      const obj = convertKeysToCamelCase(payload.new);

      // user who votes
      let userObj = allParticipants.value.find(p => p.id == obj.userId);
      if (userObj) {
        const relatedUserUserIdx = (userObj.userUsers || []).findIndex(uu => uu.targetUserId == obj.targetUserId);
        if (relatedUserUserIdx !== -1) userObj.userUsers?.splice(relatedUserUserIdx, 1, obj);
        else userObj.userUsers?.push(obj);
      }

      // user who gets voted
      userObj = allParticipants.value.find(p => p.id == obj.targetUserId);
      if (userObj) {
        const relatedUserUserIdx = (userObj.votedByUsers || []).findIndex(uu => uu.targetUserId == obj.targetUserId);
        if (relatedUserUserIdx !== -1) userObj.votedByUsers?.splice(relatedUserUserIdx, 1, obj);
        else userObj.votedByUsers?.push(obj);
      }
    }
    const subscribeSupabaseTables = () => {
      if (channel) return; // already subscribed
      channel = supabase.channel('fdmt-best-presenters');

      // Set up project work photo data change event handler
      channel.on('postgres_changes', { event: '*', schema: 'public', table: 'user_users', filter: `session_id=eq.${props.sessionId}` }, handleChangedUserUsers);

      // Subscribe to specified tables
      channel.subscribe(async (status) => {
        console.log(status);
        if (status !== 'SUBSCRIBED') { return }
      });
    }

    // INIT
    onMounted(() => {
      refreshSessionParticipants();
      subscribeSupabaseTables(); // Main: subscribe real-time best presenter votes
    });
    onBeforeUnmount(() => {
      if (channel) channel.unsubscribe(); // leave the channel
    });
    
    // 3. return variables & methods to be used in template HTML
    return {
      // icons
      add, close, checkmark, arrowUp, arrowForward, arrowBack, checkbox, trashOutline,
      thumbsUpOutline, thumbsDownOutline, thumbsUp, thumbsDown, heart, heartOutline, star,

      // variables
      loadingData, allParticipants,
      selectedFilter, selectedSection,
      searchKeyword, isSearching,
      session, user,

      // methods
      t, isMobileWeb,
      getProxyImgLink, openImageModal, getAppSheetFileLink,
      closeModal, doReorder, formatStudentNumber,
      openUserDetailsModal: async (user: any) => {
        const { isStudentView, sessionId } = props;
        return await openModal(UserDetailsModal, { user, sessionId, isStudentView });
      },
      getYouTubeEmbedUrl,

      filteredStudents: () => {
        let filteredParticipants = allParticipants.value;

        if (selectedFilter.value != 'all') {
          filteredParticipants = filteredParticipants.filter(p => p.schoolId?.toLowerCase() == selectedFilter.value.toLowerCase());
        }
        if (searchKeyword.value) {
          filteredParticipants = filteredParticipants.filter(p => {
            const targetText = `${p.fullName?.toLowerCase()} ${p.preferredName?.toLowerCase()} ${(p.schoolId || '').toLowerCase()}`;
            return targetText.includes(searchKeyword.value.toLowerCase().trim());
          });
        }

        const groupedObj = {};
        for (const student of filteredParticipants) {
          let key = student.schoolId?.toUpperCase() || "Other";
          if (props.isShowUniStudents) key = "Participants"
          if (selectedSection.value == 'groups') { // By profession group
            const targetTaskId = "t64d798cb";
            const relatedResp = student.formResponses?.find(resp => resp.sessionId == props.sessionId && resp.taskId == targetTaskId &&
                                                                    resp.questionTitle == "What's your group?");
            key = relatedResp?.answer || "Unassigned";
          }
          groupedObj[key] = groupedObj[key] || [];
          groupedObj[key].push(student);
        }
        return groupedObj;
      },

      // Shortlisted / graded students
      getUserUserObj: (targetUserId, targetKey = "") => {
        const obj = relatedUserUsers().find(uu => uu.targetUserId == targetUserId);
        return targetKey ? (obj || {})[targetKey] : obj;
      },
      shortlistedStudents: () => {
        return relatedUserUsers('like').map(uu => (allParticipants.value.find(p => p.id == uu.targetUserId) || {}));
      },
      reorderShortlistedStudents: (event: CustomEvent, targetArr: any) => {
        doReorder(event, targetArr); // for completing the event only
        const updatedUserUsers: any = [];
        for (const uu of user.value.userUsers || []) {
          if (uu.sessionId != props.sessionId) continue;
          uu.order = targetArr.findIndex(u => u.id == uu.targetUserId)+1;
          updatedUserUsers.push(uu);
        }
        (user.value.userUsers || []).sort((a: any, b: any) => (a.order-b.order)); // not update store for performance consideration
        EventService.upsertUserUsers(updatedUserUsers); // update DB
      },
      sortedStudentsWithVotes: (targetRank: any = null) => {
        // user.votedByUsers: only count votes from session participants & reaction = 'like'
        const sortedParticipants = allParticipants.value.filter(p => {
          p.numOfVotes = (p.votedByUsers || []).filter(uu => uu.reaction == 'like' && allParticipants.value.find(p => p.id == uu.userId)).length;
          return p.numOfVotes > 0;
        }).sort((a,b) => b.numOfVotes - a.numOfVotes);

        // Assign ranks to participants
        let currentRank = 1;
        for (let i = 0; i < sortedParticipants.length; i++) {
          if (i > 0 && sortedParticipants[i].numOfVotes < sortedParticipants[i - 1].numOfVotes) {
            currentRank = i + 1;
          }
          sortedParticipants[i].rank = currentRank;
        }

        return targetRank == null ? sortedParticipants : sortedParticipants.filter(p => p.rank == targetRank);
      },
      getMyCurrentVotes: () => {
        const me = allParticipants.value.find(p => p.id == user.value.id);
        return me?.numOfVotes || 0;
      },

      // School accordion groups
      allRelatedSchoolIds: () => {
        return [...new Set(allParticipants.value.map(s => s.schoolId))].filter(c => !!c).map((c) => c?.toUpperCase()).sort();
      },
      getSchoolName: (schoolId) => {
        const schoolObj = allSchools.value.find(sch => sch.id == schoolId?.toLowerCase());
        if (!schoolId || !schoolObj) return null;
        return schoolObj.name;
      },
    }
  }
});
</script>

<style scoped>
  ion-toolbar, ion-item {
    --min-height: 32px;
  }
</style>
