<template>
  <ion-header>
    <ion-toolbar>
      <!-- Back buttons (back / close modal) -->
      <ion-buttons slot="start">
        <ion-button slot="icon-only" @click="closeModal()">
          <ion-icon :icon="arrowBack"></ion-icon>
        </ion-button>
      </ion-buttons>

      <!-- Title -->
      <ion-title style="padding-left: 16px">
        <ion-label class="ion-text-wrap" v-if="isStudentView">
          <h2>Details</h2>
          <!--<h2><b>{{ user.fullName }}{{ user.preferredName ? ` (${user.preferredName})` : '' }}</b> <small>{{ user.schoolId?.toUpperCase() }}</small></h2>-->
        </ion-label>
        <ion-label class="ion-text-wrap" v-else>
          <h2><span v-show="user.class">{{ user.class }}{{ formatStudentNumber(user.studentNumber) }}</span> {{ user.fullName }} <span v-if="user.chineseName">({{ user.chineseName }})</span></h2>
          <p><b>{{ user.schoolId?.toUpperCase() }} ({{ getSchoolObj('name') }})</b></p>
        </ion-label>
      </ion-title>

      <!-- WhatsApp Group Icon -->
      <ion-buttons slot="end" v-if="!isStudentView">
        <ion-button :color="user.userInWaGroup ? 'success' : 'danger'" slot="icon-only" target="_blank" :href="user.waGroupLink">
          <ion-icon :icon="logoWhatsapp"></ion-icon>
        </ion-button>
      </ion-buttons>
    </ion-toolbar>
  </ion-header>
  
  <ion-content :fullscreen="true">
    <ion-grid fixed>
      <!--
        Student Basic Info
        -->
      <ion-card>
        <ion-row>
          <!-- Student Image & Name -->
          <ion-col size="5" class="ion-text-center">
            <img :src="getAppSheetFileLink('user', user.profilePic)" />
            <div v-if="!isStudentView"><ion-chip class="small-chip">{{ user.preferredName || user.fullName }}</ion-chip></div>
          </ion-col>
          
          <!-- Extra Info of Student -->
          <ion-col size="7">
            <!-- Student view (minimal info) -->
            <div v-if="isStudentView">
              <ion-item>
                <ion-label class="ion-text-wrap">Full name: <b>{{ user.fullName }}</b></ion-label>
              </ion-item>
              <ion-item v-if="user.preferredName">
                <ion-label class="ion-text-wrap">Preferred name: <b>{{ user.preferredName }}</b></ion-label>
              </ion-item>
              <ion-item v-if="user.chineseName">
                <ion-label class="ion-text-wrap">Chinese name: <b>{{ user.chineseName }}</b></ion-label>
              </ion-item>
              <ion-item>
                <ion-label class="ion-text-wrap">School: <b>{{ user.schoolId?.toUpperCase() }}</b> ({{ getSchoolObj('name') }})</ion-label>
              </ion-item>
            </div>

            <!-- Professor view (more detailed info) -->
            <div v-else>
              <ion-item>
                <ion-label class="ion-text-wrap">School district: <b>{{ getSchoolObj('district') }}</b></ion-label>
              </ion-item>
              <ion-item>
                <ion-label class="ion-text-wrap">School banding: <b>{{ getSchoolObj('band') }}</b></ion-label>
              </ion-item>
              <ion-item v-if="user.roles == 'secondary-school-student'">
                <ion-label class="ion-text-wrap">Rank in form: <b>{{ user.rankInForm }}</b></ion-label>
              </ion-item>
              <ion-item>
                <ion-label class="ion-text-wrap">{{ Array.isArray(user.studyingElectives) ? user.studyingElectives.join(" , ") : user.studyingElectives }}</ion-label>
              </ion-item>

              <div class="ion-padding-start" v-if="user.roles == 'secondary-school-student'">
                <ion-badge>{{ user.yearDSE }} DSE</ion-badge>&nbsp;
                <ion-badge v-if="user.secondarySchoolStudent?.group == 'ucircle'">
                  UCircle
                </ion-badge>
              </div>
            </div>

            <div v-if="user.roles == 'teacher'">
              <!-- School Roles -->
              <ion-item><ion-label class="ion-text-wrap">School roles: <b>{{ user.teacher?.schoolRoles }}</b></ion-label></ion-item>

              <!-- Class Roles -->
              <ion-item>
                <ion-label class="ion-text-wrap">
                  <small v-html="(user.teacher?.classRoles || []).filter(cr => cr.status != 'removed').map(cr => (`<b>${cr.role} (${cr.classes})</b>`)).join(' & ')"></small>
                </ion-label>
              </ion-item>

              <!-- Email -->
              <ion-item><ion-label class="ion-text-wrap">Email: <b>{{ user.email }}</b></ion-label></ion-item>

              <!-- Phone -->
              <ion-item><ion-label class="ion-text-wrap">Phone: <b>{{ user.phone }}</b></ion-label></ion-item>
            </div>
          </ion-col>
        </ion-row>
      </ion-card>

      <!-- First Selected Disciplines -->
      <ion-card v-if="user.firstSelectedDisciplineNames && !isStudentView">
        <ion-item color="primary"><ion-label><b>Interested disciplines</b></ion-label></ion-item>
        <div class="horizontal-scroll">
          <ion-chip class="small-chip" v-for="d in user.firstSelectedDisciplineNames.split(' , ')" :key="d">
            {{ d }}
          </ion-chip>
        </div>
      </ion-card>


      <!-- Mock JUPAS results (TBC: show / hide to professors due to information policies) -->
      <div v-if="currUser.teacher || currUser.isAdmin">
        <ion-card v-if="user.userPrograms && user.userPrograms.length > 0">
          <ion-item color="primary" button detail @click="openAchieveJUPASResultPageModal(user)">
            <ion-label><b>AchieveJUPAS</b></ion-label>
          </ion-item>
          <div style="max-height: 200px; overflow-y: scroll">
            <ion-item v-for="(up, idx) in user.userPrograms" :key="idx">
              <ion-label class="ion-text-wrap"><b>{{ getBandLabel(up.order-1) }}.</b> {{ getProgramObj(up.programId, 'displayName') }}</ion-label>
            </ion-item>
          </div>
        </ion-card>
      </div>

      <!-- Target Professions -->
      <ion-card v-if="user.userProfessions && user.userProfessions.length > 0 && !isStudentView">
        <ion-item color="primary"><ion-label><b>Target professions</b></ion-label></ion-item>

        <div style="max-height: 200px; overflow-y: scroll">
          <ion-item v-for="(up, idx) in user.userProfessions" :key="idx" @click="openProfessionModal(up.professionId)" button detail>
            <ion-label class="ion-text-wrap">{{ idx+1 }}. {{ getProfessionName(up.professionId) }}</ion-label>
          </ion-item>
        </div>
      </ion-card>
    </ion-grid>
  </ion-content>

  <!--
    For professors / teachers to give comments to students
  -->
  <ion-footer v-if="user.roles != 'teacher' && !hideCommentSection">
    <ion-toolbar>
      <!-- Like / dislike the student (shortlisted) -->
      <ion-item color="fdmtred" lines="full">
        <ion-label class="ion-text-wrap">
          <p v-if="isStudentView">Vote for this student?</p>
          <p v-else>Is this student shortlisted?</p>
        </ion-label>
        <ion-chip style="--color: #fff; --background: var(--ion-color-primary); border-color: #999" @click="updateUserReaction('dislike')" :outline="userUser.reaction != 'dislike'">
          <ion-label>No</ion-label>
        </ion-chip>
        <ion-chip style="--color: #fff; --background: var(--ion-color-primary); border-color: #999" @click="updateUserReaction('like')" :outline="userUser.reaction != 'like'">
          <ion-text>Yes</ion-text>
        </ion-chip>
      </ion-item>

      <!-- Grading -->
      <ion-item color="fdmtred" lines="full" v-if="!isStudentView">
        <ion-select label="Your grading" placeholder="Please select" style="min-height: 32px"
                    v-model="userUser.grade" @ionChange="updateUserGrade(userUser.grade)">
          <!--<ion-select-option v-for="grade in ['A+','A','A-','B+','B','B-','C+','C','C-']" :key="grade" :value="grade">{{ grade }}</ion-select-option>-->
          <ion-select-option v-for="grade in ['Excellent','Very good','Good','Fair','Below average']" :key="grade" :value="grade">{{ grade }}</ion-select-option>
        </ion-select>
      </ion-item>

      <!-- Comments / remarks -->
      <ion-textarea v-model="userUser.reason" label-placement="floating" label="Any comments / remarks?"
                    style="margin-top: 5px" fill="outline" :rows="5"></ion-textarea>
    </ion-toolbar>
  </ion-footer>
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, onMounted, watch, onBeforeUnmount, reactive, } from 'vue';

// icons
import { add, close, checkmark, arrowUp,  arrowForward, arrowBack, checkbox, trashOutline,
         thumbsUpOutline, thumbsDownOutline, thumbsUp, thumbsDown, heart, heartOutline, star,
         logoWhatsapp, chevronBack, chevronForward, repeat, search, } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
        IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonReorderGroup, IonReorder, IonProgressBar,
        IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption, IonSpinner,
        IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid, IonRippleEffect,
        IonNote, IonTextarea, IonFab, IonFabButton, IonBadge, IonInfiniteScroll, IonInfiniteScrollContent, IonModal, } from '@ionic/vue';
import ProfessionModal from '@/components/pss/ProfessionModal.vue';
import AchieveJUPASChartsModal from '@/components/achievejupas/AchieveJUPASChartsModal.vue';
import AchieveJUPASResultPageModal from '@/components/achievejupas/AchieveJUPASResultPageModal.vue';

// composables / services
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';
import { Profession, Program, School, UserUser } from '@/types';
import EventService from '@/services/EventService';

export default defineComponent({
  name: 'UserDetailsModal',
  props: ["user", "sessionId", "isStudentView", "hideCommentSection"],
  components: { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
                IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonReorderGroup, IonReorder, IonProgressBar,
                IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption, IonSpinner,
                IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid, IonRippleEffect,
                IonNote, IonTextarea, IonBadge, IonFab, IonFabButton, IonInfiniteScroll, IonInfiniteScrollContent, IonModal, },
  setup(props) {
    // methods or filters
    const store = useStore();
    const { closeModal, doReorder, openModal, getProxyImgLink, isMobileWeb,
            openImageModal, getAppSheetFileLink, formatStudentNumber,
            getBandLabel, getBandClass, } = utils();
    const { t } = useI18n();

    const currUser = computed(() => store.state.user);
    const settings = computed(() => store.state.settings);
    const allSchools = computed<School[]>(() => store.state.schools);
    const allPrograms = ref<Program[]>(store.state.allPrograms);
    const allProfessions = ref<Profession[]>(store.state.allProfessions);
    const oldUserUser = ref<UserUser>();

    // Comments & reaction to target user
    const userUser = reactive({
      sessionId: props.sessionId,
      targetUserId: props.user.id,
      reaction: "",
      reason: "",
      grade: "",
      createdAt: new Date(),
    })

    onMounted(() => {
      const { user, sessionId } = props;
      const relatedUserUser = (currUser.value.userUsers || []).find(uu => uu.targetUserId == user.id && uu.sessionId == sessionId);
      if (relatedUserUser) {
        oldUserUser.value = relatedUserUser;
        userUser.reaction = relatedUserUser.reaction || "";
        userUser.reason = relatedUserUser.reason || "";
        userUser.grade = relatedUserUser.grade || "";
        userUser.createdAt = relatedUserUser.createdAt;
      }
    })

    // Sync to DB if filled comments for users before leaving modal
    onBeforeUnmount(() => {
      const { reaction, reason, grade, } = userUser;
      const { reaction: oldReaction, reason: oldReason , grade: oldGrade,} = oldUserUser.value || {};

      if ((reaction || reason || grade) && (reaction != oldReaction || reason != oldReason || grade != oldGrade)) {
        const { id: userId, userUsers } = currUser.value;
        const newUserUser = { ...userUser, userId, };
        EventService.upsertUserUsers([newUserUser]); // update DB

        // Update store (for later retrieval)
        const { user, sessionId } = props;
        const relatedUserUserIdx = (userUsers || []).findIndex(uu => uu.targetUserId == user.id && uu.sessionId == sessionId);
        if (relatedUserUserIdx !== -1) userUsers?.splice(relatedUserUserIdx, 1, newUserUser);
        else userUsers?.push(newUserUser);
        store.commit('updateUser', { userUsers });
      }
    })

    // 3. return variables & methods to be used in template HTML
    return {
      // icons
      add, close, checkmark, arrowUp, arrowForward, arrowBack, checkbox, trashOutline,
      thumbsUpOutline, thumbsDownOutline, thumbsUp, thumbsDown, heart, heartOutline, star,
      logoWhatsapp,

      // variables
      settings, userUser,
      currUser,

      // methods
      t, isMobileWeb, closeModal, doReorder,
      getProxyImgLink, openImageModal, getAppSheetFileLink,
      formatStudentNumber, getBandLabel, getBandClass, 
      
      getSchoolObj: (targetKey) => {
        const { schoolId } = props.user;
        const schoolObj = allSchools.value.find(sch => sch.id == schoolId);
        if (!schoolId || !schoolObj) return null;
        return targetKey ? schoolObj[targetKey] : schoolObj;
      },

      getProgramObj: (programId, targetKey) => {
        const p = allPrograms.value.find(p => p.id == programId) || {};
        return targetKey ? p[targetKey] : p;
      },
      getProfessionName: (professionId) => {
        const p = allProfessions.value.find(p => p.id == professionId);
        return `${p?.name} ${p?.nameChinese}`;
      },
      openProfessionModal: async (professionId) => (await openModal(ProfessionModal, { professionId })),

      // User comments (for professors)
      updateUserReaction: (reaction) => {
        userUser.reaction = reaction;
        EventService.upsertUserUsers([{ ...userUser, userId: currUser.value.id }]); // update DB
      },
      updateUserGrade: (grade) => {
        userUser.grade = grade;
        EventService.upsertUserUsers([{ ...userUser, userId: currUser.value.id }]); // update DB
      },

      // Mock JUPAS statistics
      openAchieveJUPASChartsModal: async (userPrograms) => (await openModal(AchieveJUPASChartsModal, { userPrograms })),
      openAchieveJUPASResultPageModal: async (user) => (await openModal(AchieveJUPASResultPageModal, { targetUser: user })),
    }
  }
});
</script>

<style scoped>
</style>
