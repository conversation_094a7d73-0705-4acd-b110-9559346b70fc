<template>
  <ion-header>
    <ion-toolbar>
      <ion-buttons slot="start">
        <ion-button slot="icon-only" @click="closeModal()"><ion-icon :icon="arrowBack"></ion-icon></ion-button>
      </ion-buttons>
      <ion-title style="padding-right: 0">
        <ion-label class="ion-text-wrap">
          <p>{{ school.nameShort }}</p>
        </ion-label>
      </ion-title>
    </ion-toolbar>
  </ion-header>
    
  <ion-content :fullscreen="true">
  </ion-content>

  <ion-footer>
    <ion-toolbar>
      <ion-button expand="full" color="success">
        Update
      </ion-button>
    </ion-toolbar>
  </ion-footer>
</template>
  
<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, watch, reactive } from 'vue';

// icons
import { add, close, checkmark, arrowUp,  arrowForward, arrowBack, trashOutline, } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
        IonItem, IonLabel, IonIcon, IonButtons, IonButton,
        IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption,
        IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid, IonCheckbox,
        IonRadioGroup, IonRadio, IonInfiniteScroll, IonInfiniteScrollContent, IonReorder, IonReorderGroup,
        loadingController, modalController, } from '@ionic/vue';

// composables / services
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';

// types
import { User, Session, Discipline } from '@/types';

export default defineComponent({
name: 'SchoolFormModal',
props: ["school"],
components: { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
                IonItem, IonLabel, IonIcon, IonButtons, IonButton,
                IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption,
                IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid, IonCheckbox,
                IonRadioGroup, IonRadio, IonInfiniteScroll, IonInfiniteScrollContent, IonReorder, IonReorderGroup, },
setup(props) {
    // methods or filters
    const store = useStore();
    const { t } = useI18n();

    const closeModal = async () => (await modalController.dismiss({}));


    // return variables & methods to be used in template HTML
    return {
        // icons
        add, close, checkmark, arrowUp, arrowForward, arrowBack,
        trashOutline,

        // methods
        t,
        closeModal,
    }
},
});
</script>

<style scoped>
</style>