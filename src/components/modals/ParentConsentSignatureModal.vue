<!-- Self/Parent Consent Signature Modal -->
<template>
  <ion-header v-if="!hideHeader">
    <ion-grid fixed>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-button slot="icon-only" @click="goBack()">
            <ion-icon :icon="arrowBack"></ion-icon>
          </ion-button>
        </ion-buttons>

        <ion-title>
          <ion-label v-if="submittedConsent">
            {{ isSelfConsent ? 'Consent Submitted' : 'Parent Consent Obtained' }}
          </ion-label>
          <ion-label v-else>
            {{ isSelfConsent ? 'Consent Form' : 'Parent/Guardian Consent' }}
          </ion-label>
        </ion-title>

        <ion-buttons slot="end" v-if="submittedConsent">
          <ion-button slot="icon-only">
            <ion-icon slot="end" color="success" :icon="checkmarkCircle"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-grid>
  </ion-header>

  <ion-content :class="{ 'ion-padding': !hideHeader }" :style="{ '--padding-bottom': hideHeader ? '0' : '200px' }">
    <ion-grid fixed>
      <!-- Video -->
      <div v-if="consentVideoId && targetVideo">
        <a :href="targetVideo?.link" target="_blank">{{ targetVideo?.title || 'Video' }}</a>
        <iframe class="responsive-embed" style="width: 100%; height: 380px" :src="getYouTubeEmbedUrl(targetVideo?.link)" frameborder="0" allowfullscreen></iframe>
      </div>

      <!-- Shooting -->
      <div v-if="targetEvent && !hideHeader">
        <b>{{ targetEvent.name }}</b><br />
        on <b>{{ targetEvent.formattedDateTime }}</b>
      </div>

      <!-- Consent Submitted -->
      <div v-if="submittedConsent" class="submitted-consent ion-padding">
        <p>
          {{ isSelfConsent ? 'Your consent' : 'Parent/guardian consent' }} was submitted on {{ formatDate(submittedConsent.createdAt) }}
        </p>

        <div class="signature-preview ion-margin-top">
          <p class="signature-label">{{ isSelfConsent ? 'Your signature' : 'Parent/Guardian Signature' }}:</p>
          <img 
            :src="submittedConsent.signatureImg || submittedConsent.signatureDataUrl" 
            class="signature-image"
          />
        </div>
      </div>

      <!-- Signature Area -->
      <div v-show="!submittedConsent">
        <!-- Consent Text -->
        <div class="consent-text">
          <p v-html="responseByTarget"></p>
        </div>

        <!-- Signature Pad -->
        <div class="signature-container">
          <!--
            Child info field (if not logged in)
          -->
          <div v-if="!user?.id">
            <ion-input label-placement="stacked" fill="solid" :label="isSelfConsent ? 'Full Name*' : 'Child\'s Full Name*'" v-model="formData.childFullName" type="text" :placeholder="isSelfConsent ? 'Enter your full name' : 'Enter child\'s full name'" required></ion-input>
            <ion-input label-placement="stacked" fill="solid" :label="isSelfConsent ? 'Phone Number*' : 'Child\'s Phone Number*'" v-model="formData.childPhoneNumber" type="text" :placeholder="isSelfConsent ? 'Enter your phone number' : 'Enter child\'s phone number'" required></ion-input>
          </div>

          <p class="signature-label">{{ isSelfConsent ? 'Your Signature' : 'Parent/Guardian Signature' }}:</p>
          <canvas id="signaturePad" class="signature-pad"></canvas>
          <p v-if="!hideHeader">By signing in the box, you agree to our Terms and Conditions (<a href="https://www.fdmt.hk/legal" target="_blank">fdmt.hk/legal</a>)</p>

          <!-- Action Buttons -->
          <div class="button-container">
            <ion-button color="danger" fill="outline" @click="clearSignature" :disabled="!hasSignature">Clear</ion-button>
            <ion-button color="success" @click="saveSignature" :disabled="!hasSignature">Submit</ion-button>
          </div>
        </div>
      </div>
    </ion-grid>
  </ion-content>
</template>

<script setup lang="ts">
// vue
import { defineEmits, defineProps, defineComponent, ref, reactive, onMounted, watch, computed, onBeforeUnmount } from 'vue';

// icons
import { arrowBack, checkmarkCircle, } from 'ionicons/icons';

// components
import { IonPage, IonGrid, IonHeader, IonToolbar, IonTitle, IonContent, IonButton, IonButtons, IonLabel, IonIcon,
          IonInput, modalController } from '@ionic/vue';

// services
import AchieveJUPASService from '@/services/AchieveJUPASService';
import CommonService from '@/services/CommonService';

// lib / composables
import SignaturePad from 'signature_pad';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';
import { useRoute, useRouter } from 'vue-router';

// types
import { User } from '@/types';

const props = defineProps(['isPage', 'target', 'videoId', 'targetEvent', 'hideHeader']);
const emit = defineEmits(['submitSignature']);
const router = useRouter();
const route = useRoute();
const isSelfConsent = computed(() => route.path?.startsWith('/consent'));
const { presentToast, presentPrompt, uniqueId, formatDate, closeModal, getYouTubeEmbedUrl, } = utils();
const goBack = async () => {
  if (props.isPage) router.replace('/home');
  else await modalController.dismiss({});
};
const consentTarget = route.params.target || props.target || 'achievejupas_parent_consent';
const consentVideoId = route.params.videoId || props.videoId;

// store
const store = useStore();
const user = computed<User>(() => store.state.user);
const targetVideo = ref<any>(null);
const formData = reactive({
  childFullName: '',
  childPhoneNumber: '',
});

// signature pad
const signaturePadElement = ref<HTMLCanvasElement | null>(null);
const signaturePad = ref<SignaturePad | null>(null);
const hasSignature = ref(false);

const initSignaturePad = () => {
  const canvas: any = document.querySelector("#signaturePad");

  signaturePad.value = new SignaturePad(canvas, {
    backgroundColor: 'rgb(255, 255, 255)',
    penColor: 'rgb(0, 0, 0)',
  });
  
  signaturePad.value.addEventListener('endStroke', () => {
    hasSignature.value = !signaturePad.value?.isEmpty();
  });
};

const resizeCanvas = () => {
  const canvas: any = document.querySelector("#signaturePad");
  const ratio = Math.max(window.devicePixelRatio || 1, 1);
  canvas.width = canvas.offsetWidth * ratio;
  canvas.height = canvas.offsetHeight * ratio;
  canvas.getContext('2d')?.scale(ratio, ratio);
  if (signaturePad.value) signaturePad.value.clear();
};

onMounted(() => {
  initSignaturePad();
  setTimeout(() => {
    window.addEventListener('resize', resizeCanvas);
    resizeCanvas();
  }, 300);

  if (consentVideoId) {
    CommonService.getVideoById(consentVideoId).then((res) => {
      console.log(res);
      targetVideo.value = res;
    });
  }
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', resizeCanvas);
  if (signaturePad.value) {
    signaturePad.value.off();
  }
});

const clearSignature = () => {
  if (signaturePad.value) {
    signaturePad.value.clear();
    hasSignature.value = false;
  }
};

const responseByTarget = computed(() => {
  console.log(isSelfConsent.value)
  // Define the dynamic phrases based on whether the user is consenting for themselves or a child/ward.
  const subject = isSelfConsent.value ? 'myself' : 'my child/ward';
  const actionJoin = isSelfConsent.value ? 'joining' : 'my child/ward joining';
  const actionUse = isSelfConsent.value ? 'using' : 'my child/ward using';

  switch (consentTarget) {
    case 'video':
      return `I hereby consent the video involving ${subject} to be published.`;

    case 'bluebird-seed':
      return `I hereby consent to ${actionJoin} Bluebird Seed.`;

    case 'shooting':
      return `I hereby consent to ${actionJoin} this program: <a href="https://fdmt.hk/preprogram" target="_blank">fdmt.hk/preprogram</a>.
<br />Also I read and consent to the letter & Terms and Conditions (<a href="https://fdmt.hk/legal" target="_blank">fdmt.hk/legal</a>)`;
      
    default:
      return `I hereby consent to ${actionUse} AchieveJUPAS.`;
  }
})

const validateChildInfo = () => {
  const { childFullName, childPhoneNumber } = formData;
  if (!user.value?.id) {
    if (!childFullName?.trim()) {
      presentToast('Please enter child\'s full name', 2000);
      return false;
    }
    if (!childPhoneNumber?.trim()) {
      presentToast('Please enter child\'s phone number', 2000);
      return false;
    }
  }
  return true;
};

const saveSignature = async () => {
  if (signaturePad.value?.isEmpty()) {
    presentToast('Please provide your signature', 2000);
    return;
  }
  
  if (!validateChildInfo()) return;
  
  presentPrompt("Submit your consent with signature?", async () => {
    try {
      const signatureDataUrl = signaturePad.value?.toDataURL("image/jpeg", 0.5);
      const sessionInfo = props.targetEvent ? 
        `${props.targetEvent.name} (${props.targetEvent.formattedDateTime})` : "";
      
      const { schoolId, fullName, roles, phone, waGroupId } = user.value;
      const isLoggedIn = !!user.value?.id;
      const userConsentRecordId = `uc${uniqueId()}`;
      
      const payload = {
        id: userConsentRecordId,
        schoolId,
        fullName: isLoggedIn ? fullName : formData.childFullName.trim(),
        target: consentTarget,
        videoId: consentVideoId,
        videoLink: targetVideo.value?.link,
        roles: isLoggedIn ? roles : 'anonymous',
        response: responseByTarget.value,
        phone: isLoggedIn ? phone : formData.childPhoneNumber.trim(),
        waGroupId,
        signatureDataUrl,
        createdAt: new Date(),
        sessionId: props.targetEvent?.id,
        sessionInfo,
      };
      AchieveJUPASService.insertUserConsent(payload);
      user.value.userConsentRecords?.push(payload);
      presentToast('Consent recorded successfully', 2000);

      // Reset the form
      formData.childPhoneNumber = '';
      formData.childFullName = '';
      clearSignature();

      // Close modal
      closeModal({ userConsentRecordId });
      emit('submitSignature', userConsentRecordId);
    } catch (error) {
      console.error('Error saving consent:', error);
        presentToast('Failed to record consent. Please try again', 2000);
    }
  });
};

const submittedConsent = computed(() => {
  return user.value.userConsentRecords?.find(r => {
    return r.target == consentTarget && (!consentVideoId || r.videoId == consentVideoId) && (!props.targetEvent || props.targetEvent?.id == r.sessionId)
  });
});
</script>

<style scoped>
.required {
  color: var(--ion-color-danger);
  margin-left: 2px;
}

ion-item {
  --padding-start: 0;
  --inner-padding-end: 0;
  --background: transparent;
}

ion-input {
  margin-top: 8px;
  background: var(--ion-color-light);
  border-radius: 8px;
  --padding-start: 12px !important;
  --padding-end: 12px !important;
}

.consent-text {
  margin-bottom: 10px;
}

.consent-text h2 {
  font-size: 1.5em;
  margin-bottom: 15px;
}

.consent-text p {
  margin-bottom: 10px;
  line-height: 1.4;
}

.consent-text ul {
  padding-left: 20px;
  margin-bottom: 20px;
}

.consent-text li {
  margin-bottom: 8px;
  line-height: 1.3;
}

.signature-container {
  margin: 10px 0;
  border: 1px solid #ccc;
  border-radius: 8px;
  padding: 10px;
}

.signature-label {
  margin-bottom: 10px;
  color: var(--ion-color-medium);
}

.signature-pad {
  width: 100%;
  height: 200px;
  border: 1px solid #eee;
  border-radius: 4px;
  background-color: #fff;
}

.submitted-consent {
  text-align: center;

  h2 {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 16px;
  }

  .signature-preview {
    margin-top: 24px;
    border: 1px solid var(--ion-color-light);
    border-radius: 8px;
  }

  .signature-image {
    max-width: 100%;
    height: 200px;
    border: 1px solid #eee;
    border-radius: 4px;
    background: white;
    object-fit: contain;
    padding: 8px;
  }
}

.button-container {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  margin-top: 20px;
}
</style>
