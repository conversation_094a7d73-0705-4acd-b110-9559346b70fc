<template>
  <ion-header>
    <ion-toolbar color="primary" style="--min-height: 24px" v-if="isFromAB4">
      <ion-buttons slot="start">
        <ion-button slot="icon-only" @click="closeModal()"><ion-icon :icon="arrowBack"></ion-icon></ion-button>
      </ion-buttons>
      
      <ion-title>
        <ion-label class="ion-text-wrap">
          <h2>{{ professionName }}</h2>
        </ion-label>
      </ion-title>
    </ion-toolbar>

    <!--
      User Inputs: Guided prompts for AI image generation (expandable)
      -->
    <ion-accordion-group value="input" v-else-if="isImageBot">
      <ion-accordion value="input">
        <ion-item color="primary" lines="full" slot="header">
          <ion-buttons slot="start">
            <ion-button slot="icon-only" @click="closeModal()"><ion-icon :icon="arrowBack"></ion-icon></ion-button>
          </ion-buttons>

          <ion-label class="ion-text-wrap">
            <h2 v-if="imageBotTargetProfession">{{ imageBotTargetProfession.name }}</h2>
            <h2 v-else>AI images</h2>
          </ion-label>

          <!-- Demo Button -->
          <ion-buttons slot="end">
            <ion-button class="no-text-transform" @click.stop="openProfessionModal(professionId)" v-if="professionId">Details</ion-button>
            <ion-button class="no-text-transform" @click.stop="prefillDemoDataForImageBot()">Demo</ion-button>
          </ion-buttons>
        </ion-item>
        
        <div slot="content">
          <ion-toolbar style="padding: 2px">
            <div class="input-grid">
              <!-- TODO: change this input into a dropdown (fixed list of professions) -->
              <!-- 1. Ship Charterer/Broker 船舶承租人/船務經紀
                    2. Ship Inspectors (Maritime)驗船督察（海事）
                    3. Genetic counselor 遺傳諮詢員
                    4. Quality Assurance (Healthcare)質量保證員（醫療保健）
                    5. Epidemiologist 流行病學家
                    6. Regulatory Affairs 法規人員
                    7. Chinese Medicine Practitioner 中醫
                    8. Surveyor 測量師
                    9. Game Designer 遊戲設計師
                    10. Tour Guide (Inbound) 導遊
                    11. Diplomat 外交官
                    12. Consultant (ESG) 顧問（環境、社會和管治）
                    13. Supply Chain Analyst/ Executive供應鏈分析員
                    14. Risk Analyst 風險分析師
                    15. Treasury Officer 財政主任 -->
              <!-- Also professions without prompts / images -->
               <!-- Details page (should show the image) @click="openProfessionModal(profession.id)"-->
              <ion-input class="compact-input" v-model="userInputs.photoOfA" label="Photo of a" label-placement="floating" fill="outline" :clear-input="true"></ion-input>
              <ion-input class="compact-input" v-model="userInputs.wearing" label="Wearing" placeholder="e.g. office suit" label-placement="floating" fill="outline" :clear-input="true"></ion-input>
              <ion-input class="compact-input" v-model="userInputs.holding" label="Holding" placeholder="e.g. iPad" label-placement="floating" fill="outline" :clear-input="true"></ion-input>
              <ion-input class="compact-input" v-model="userInputs.engaging" label="Engaging" placeholder="e.g. with client" label-placement="floating" fill="outline" :clear-input="true"></ion-input>
              <ion-input class="compact-input" v-model="userInputs.gesture" label="Gesture" placeholder="e.g. talking" label-placement="floating" fill="outline" :clear-input="true"></ion-input>
              <ion-input class="compact-input" v-model="userInputs.workingIn" label="Working in" placeholder="e.g. meeting room" label-placement="floating" fill="outline" :clear-input="true"></ion-input>
              <ion-input class="compact-input" v-model="userInputs.facialExp" label="Expression" placeholder="e.g. smile" label-placement="floating" fill="outline" :clear-input="true"></ion-input>
              <ion-input class="compact-input" v-model="userInputs.lookingAt" label="Looking at" placeholder="e.g. camera" label-placement="floating" fill="outline" :clear-input="true"></ion-input>
            </div>
          </ion-toolbar>

          <ion-button size="small" expand="block" class="no-text-transform" @click="sendPromptToBot('image')" :disabled="waitingBotResp">
            Generate Image
            <ion-icon slot="end" :icon="arrowForward"></ion-icon>
          </ion-button>
        </div>
      </ion-accordion>
    </ion-accordion-group>

    <!--
      User Inputs: CV / Work experiences (expandable)
      -->
    <ion-accordion-group value="input" v-else>
      <ion-accordion value="input">
        <ion-item color="primary" lines="full" slot="header">
          <ion-buttons slot="start">
            <ion-button slot="icon-only" @click="closeModal()"><ion-icon :icon="arrowBack"></ion-icon></ion-button>
          </ion-buttons>

          <ion-label class="ion-text-wrap">
            <h2>Generate CV bullet points / CL claims</h2>
          </ion-label>

          <!-- Demo Button (fill with generic CV & JD) -->
          <ion-buttons slot="end" v-if="user.isAdmin">
            <ion-button @click.stop="prefillDemoData()">Demo</ion-button>
          </ion-buttons>
        </ion-item>
        
        <div slot="content">
          <ion-toolbar style="padding-left: 5px; padding-right: 5px">
            <ion-textarea v-model="userInputs.workExps" label="Your CV / work experiences" label-placement="floating" fill="outline"
                          style="margin-top: 10px; margin-bottom: 10px" :rows="6"></ion-textarea>
            <ion-textarea v-model="userInputs.jd" label="JD (Job Description)" label-placement="floating" fill="outline" :rows="6"></ion-textarea>
          </ion-toolbar>

          <ion-row>
            <ion-col size="6">
              <ion-button expand="block" class="no-text-transform" @click="sendCVBulletPointsReq()" :disabled="waitingBotResp">CV bullet points</ion-button>
            </ion-col>
            <ion-col size="6">
              <ion-button expand="block" class="no-text-transform" @click="sendCLClaimsReq()" :disabled="waitingBotResp">CL claims</ion-button>
            </ion-col>
          </ion-row>
        </div>
      </ion-accordion>
    </ion-accordion-group>
  </ion-header>
  
  <ion-content ref="contentEl">
    <ion-grid class="ion-no-padding" fixed>
      
      <!-- Loading data -->
      <div class="spin ion-text-center" v-if="loading">
        <p><img style="width: 100px" :src="require('@/assets/logo.png')" /></p>
        <ion-spinner></ion-spinner>
      </div>

      <ion-list v-else>
        <ion-item lines="none" v-for="msg of messages.slice(messages.length-50, messages.length)" :key="msg.id">
          <ion-card :color="msg.fromUser ? 'tertiary' : 'light'" :slot="msg.fromUser ? 'end' : 'start'"
                    style="padding: 8px; font-size: 16px">
            <template v-if="isImageMessage(msg.content)">
              <img :src="msg.content" style="max-width: 100%; border-radius: 4px; cursor: pointer;" @click="openImageModal(msg.content, getPreviousUserMessage(msg))" />
            </template>
            <div class="message" v-else v-html="parseMsg(msg.content)"></div>
            <div class="ion-text-right" v-if="msg.content != 'The bot encountered an unexpected issue.'">
              <ion-button size="small" class="no-text-transform" color="success" v-if="!msg.fromUser" @click="sendBotMsgToWhatsApp(msg.content)">
                Send to my WhatsApp
                <ion-icon slot="end" :icon="send"></ion-icon>
              </ion-button>
            </div>
          </ion-card>
        </ion-item>
        <ion-item lines="none" v-if="latestBotResp">
          <ion-card :color="'light'" slot="start" style="padding: 8px; font-size: 16px">
            <div class="message" v-html="parseMsg(latestBotResp)"></div>
          </ion-card>
        </ion-item>
        
      </ion-list>

    </ion-grid>
  </ion-content>

  <ion-footer>
    <ion-grid fixed>
      <ion-toolbar>
        <ion-item lines="none" style="padding-top: 5px; --padding-start: 0">
          <ion-textarea fill="outline" v-model="userInputs.custom" shape="round"
                        :disabled="waitingBotResp" @keyup.enter.exact.prevent="sendMsg()" :auto-grow="true"
                        style="max-height: 50vh; overflow: scroll"></ion-textarea>
          <ion-buttons slot="end">
            <ion-button color="primary" @click="sendMsg()" :disabled="!userInputs.custom">
              <ion-icon slot="icon-only" :icon="send"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-item>
      </ion-toolbar>
    </ion-grid>
  </ion-footer>
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, watch, onMounted, reactive, } from 'vue';

// icons
import { add, close, checkmark, arrowUp, arrowForward, arrowBack, trashOutline, caretDown, createOutline, openOutline, send, } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonLabel, IonContent, IonFooter, IonGrid, IonCol, IonRow, IonInput,
        IonList, IonItem, IonButtons, IonButton, IonIcon, IonSpinner, IonCard, IonTextarea, IonAccordionGroup, IonAccordion,
        modalController, loadingController, } from '@ionic/vue';
import ProfessionModal from '@/components/pss/ProfessionModal.vue';

// composables
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';

// services
import ChatService from '@/services/ChatService';

// lib
import markdownit from 'markdown-it'
import { Profession, Segment } from '@/types';
import SLPService from '@/services/SLPService';

export default defineComponent({
  name: 'ChatbotModal',
  props: [
    "isFromAB4", "professionName", "botExplanation", "professionId", "prefilledPrompt",
    "isImageBot", "imageBotTargetProfession",
  ],
  components: { IonHeader, IonToolbar, IonTitle, IonLabel, IonContent, IonFooter, IonGrid, IonCol, IonRow, IonInput,
                IonList, IonItem, IonButtons, IonButton, IonIcon, IonSpinner, IonCard, IonTextarea, IonAccordionGroup, IonAccordion, },
  setup(props) {
    // methods or filters
    const { openModal, openImageModal, closeModal, presentToast, presentAlert, getProxyImgLink, uniqueId, presentPrompt, sleep, } = utils();
    const { t } = useI18n();
    const md = markdownit({ breaks: true });

    // state variables
    const store = useStore();
    const user = computed(() => store.state.user);
    const allSegments = computed<Segment[]>(() => store.state.allSegments);
    const allProfessions = ref<Profession[]>(store.state.allProfessions);
    const contentEl = ref<any>(null);
    const waitingBotResp = ref(false);
    const latestBotResp = ref("");
    const userInputs = reactive({
      custom: "",
      workExps: "",
      jd: "",

      photoOfA: "",
      wearing: "",
      holding: "",
      engaging: "",
      gesture: "",
      workingIn: "",
      facialExp: "",
      lookingAt: "",
      style: "in japanese Anime Art Styles",
      // 一個男／女___，穿着___，拿着___，在___裏，背景有__、_____等，以日系動漫風繪畫
    });
    const messages = ref<any>([]);
    const loading = ref(true);

    // utils
    const scrollToBottom = () => {
      setTimeout(() => {
        contentEl.value?.$el.scrollToBottom();
      }, 100)
    }
    const addNewMsgObj = (msgObj) => {
      if (!msgObj.createdAt) msgObj.createdAt = new Date().valueOf();
      messages.value.push(msgObj);
      scrollToBottom();
    }
    const getReqType = () => {
      const { isFromAB4, isImageBot, } = props;
      if (isFromAB4) return 'ab4';
      if (isImageBot) return 'image';
      return 'cvcl';
    }
    const sendPromptToBot = async (type) => {
      let sendingPrompt = userInputs.custom;
      if (['cv','cl'].includes(type)) {
        sendingPrompt = `--Below is a draft of my working experiences:--\n${userInputs.workExps}`;
        if (type == 'cl') {
          sendingPrompt = `--Below is the job description of my applied job:--\n${userInputs.jd}\n\n${sendingPrompt}`;
        }
      } else if (type == 'image') { // guided prompt for image generation
        sendingPrompt = `Photo of a ${userInputs.photoOfA} worker`;
        if (userInputs.wearing) sendingPrompt += `, wearing ${userInputs.wearing}`;
        if (userInputs.holding) sendingPrompt += `, holding ${userInputs.holding}`;
        if (userInputs.engaging) sendingPrompt += `, engaging ${userInputs.engaging}`;
        if (userInputs.gesture) sendingPrompt += `, ${userInputs.gesture}`;
        if (userInputs.workingIn) sendingPrompt += `, working in ${userInputs.workingIn}`;
        if (userInputs.facialExp) sendingPrompt += `, ${userInputs.facialExp}`;
        if (userInputs.lookingAt) sendingPrompt += `, looking at ${userInputs.lookingAt}`;
      }

      if (props.isImageBot) {
        if (userInputs.style) sendingPrompt += `, ${userInputs.style}`; // in Japanese Anime Art Styles
      }

      // Show user prompt on UI
      const msgId  = `m${uniqueId()}`;
      const newPrompt = { id: msgId, fromUser: true, content: sendingPrompt, type, };
      addNewMsgObj(newPrompt);

      // construct query msgs (with previous conversation as context)
      const queryMsgs = messages.value.filter(m => !m.fromUser).concat(newPrompt).map(m => ({
        "role": m.fromUser ? "user" : "bot",
        "content": m.content,
        "timestamp": (new Date(m.createdAt)).valueOf(),
        "message_id": m.id,
      }))

      // Send request to Chatbot
      latestBotResp.value = "...";
      waitingBotResp.value = true;
      const reqType = getReqType();
      const url = `https://ab-chatgpt-api.fdmt.hk/${reqType}`;
      try {
        const response = await fetch(url, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": "Bearer 617ba408824b9f2c2e9e1b61e2c04761",
          },
          body: JSON.stringify({
            "version":"1.0","type":"query","query": queryMsgs,
            "user_id": user.value.id,"conversation_id": `${reqType}-${user.value.id}`,"message_id":"",
          })
        });

        if (!response.ok) throw new Error(response.statusText);

        // This data is a ReadableStream
        const data = response.body;
        if (!data) return;
        latestBotResp.value = ""; // empty bot response first

        const reader = data.getReader();
        const decoder = new TextDecoder();
        let done = false;

        while (!done) {
          const { value, done: doneReading } = await reader.read();
          done = doneReading;
          const chunkValue = decoder.decode(value);
          //console.log(chunkValue);
          for (const message of chunkValue.split("\n")) {
            const matches = message.match(/\{([^}]+)\}/);
            if (matches) {
              const parsed = JSON.parse(matches[0]);
              if (parsed.text) {
                latestBotResp.value += parsed.text;
                scrollToBottom();
              }
            }
          }
        }
        addNewMsgObj({ id: `b-${msgId}`, content: latestBotResp.value, type }); // bot msg
      } catch (e) {
        presentAlert("Bot did not response. Please try again");
        if (!['cl', 'cv'].includes(type)) userInputs.custom = sendingPrompt;
        latestBotResp.value = "";
      } finally {
        ChatService.insertUserChatbotPrompts([{
          ...newPrompt,
          botResponse: latestBotResp.value,
          professionId: props.professionId,
        }]);
        latestBotResp.value = "";
        waitingBotResp.value = false;
      }
    }

    // Init

    onMounted(() => {
      const { isFromAB4, professionId, botExplanation, prefilledPrompt, } = props;

      if (!isFromAB4) {
        // Prefill JD (target profession & segment)
        const { userProfessions, userSegments } = user.value;
        const likedProfessions = (userProfessions || []).filter(up => up.reaction == 'like').sort((a, b) => Number(a.order)-Number(b.order));
        if (likedProfessions.length > 0) {
          const targetProfession = allProfessions.value.find(p => p.id == likedProfessions[0].professionId);
          if (targetProfession) userInputs.jd = `A ${targetProfession?.name}`;
          const likedSegments = (userSegments || []).filter(us => us.professionId == targetProfession?.id).filter(up => up.reaction == 'like');
          if (likedSegments.length > 0) {
            const targetSegment = allSegments.value.find(s => s.id == likedSegments[0].segmentId);
            if (targetSegment) userInputs.jd = `${userInputs.jd} working in ${targetSegment.name}`;
          }
        }
      }

      // Retrieve old conversations
      ChatService.getUserChatbotPrompts(null, professionId).then(res => {
        loading.value = false;
        for (const row of res) {
          const { id, prompt, botResponse, type, createdAt, } = row;
          messages.value.push({ id: `p${id}`, fromUser: true, content: prompt, type, createdAt, });
          if (botResponse) messages.value.push({ id: `b${id}`, content: botResponse, type, createdAt, });
        }
        scrollToBottom();

        // AB4: trigger "tell me more"
        if (botExplanation) {
          messages.value.push({ id: `b${uniqueId()}`, content: botExplanation, type: 'ab4', createdAt: new Date(), });
          userInputs.custom = prefilledPrompt;
          sendPromptToBot('ab4');
          userInputs.custom = "";
        }
      });

      // Image Bot
      if (props.isImageBot && props.imageBotTargetProfession) {
        const { imagePrompt, name, } = props.imageBotTargetProfession;
        userInputs.photoOfA = name;
        userInputs.wearing = imagePrompt.wearing || "";
        userInputs.holding = imagePrompt.holding || "";
        userInputs.engaging = imagePrompt.engaging || "";
        userInputs.gesture = imagePrompt.gesture || "";
        userInputs.workingIn = imagePrompt["working in"] || "";
        userInputs.facialExp = imagePrompt["Facial expression"] || "";
        userInputs.lookingAt = imagePrompt["Looking at"] || "";
      }
    })

    // 3. return variables & methods to be used in template HTML
    return {
      // icons
      add, close, checkmark, arrowUp, arrowForward, arrowBack, trashOutline, caretDown, createOutline, openOutline,
      send,

      // variables
      loading, user,
      contentEl,
      userInputs, messages,
      waitingBotResp, latestBotResp,

      // methods
      t,
      closeModal, getProxyImgLink,
      openImageModal,
      sendCLClaimsReq: () => {
        sendPromptToBot('cl');
      },
      sendCVBulletPointsReq: () => {
        sendPromptToBot('cv');
      },
      sendMsg: () => {
        if (userInputs.custom && userInputs.custom.trim()) {
          sendPromptToBot('cvcl-custom');
          userInputs.custom = "";
        }
      },
      parseMsg: (msg) => {
        return md.render(msg);
      },
      isImageMessage: (msg) => {
        return msg.match(/^https?:\/\/.*\.(png|jpg|jpeg|gif|webp)$/i);
      },
      getPreviousUserMessage: (currentMsg) => {
        const currentIndex = messages.value.findIndex(m => m.content === currentMsg.content);
        const prevUserMsg = messages.value.slice(0, currentIndex)
          .reverse()
          .find(m => m.fromUser)?.content;
        return prevUserMsg || '';
      },
      sendBotMsgToWhatsApp: async (msg, imgLink = "") => {
        // If this is an image message, find the previous user message
        let messageToSend = msg;
        if (msg.match(/^https?:\/\/.*\.(png|jpg|jpeg|gif|webp)$/i)) {
          // Find the last user message before this image
          const currentIndex = messages.value.findIndex(m => m.content === msg);
          const prevUserMsg = messages.value.slice(0, currentIndex).reverse().find(m => m.fromUser)?.content;
          
          if (prevUserMsg) {
            messageToSend = prevUserMsg;
            imgLink = msg;
          }
        }

        presentPrompt("Send this message to your WhatsApp group for records?", async () => {
          const loading = await loadingController.create({});
          await loading.present();
          SLPService.sendWhatsAppMsg(messageToSend, user.value.phone, user.value.waGroupId, imgLink);
          await sleep(2);
          loading.dismiss();
          presentToast("The message will be sent to your WhatsApp group in minutes.");
        });
      },

      // For Demo only (generic CV & JD)
      prefillDemoData: () => {
        userInputs.workExps = `Marketing Coordinator at TechCorp (2021-2023)
- Managed social media campaigns across 3 platforms
- Coordinated with design team for marketing materials
- Analyzed campaign metrics and prepared monthly reports
- Assisted in organizing 4 major product launch events

Digital Marketing Intern at CreativeAgency (2020)
- Supported email marketing campaigns
- Helped maintain company website content
- Created social media content calendars`

        userInputs.jd = `Position: Senior Marketing Specialist
Company: InnovateDigital

We're seeking a creative and data-driven Senior Marketing Specialist to join our growing team. The ideal candidate will lead our digital marketing initiatives and develop comprehensive marketing strategies.

Key Responsibilities:
- Develop and execute digital marketing campaigns
- Manage social media presence and content strategy
- Analyze marketing metrics and optimize campaign performance
- Coordinate with internal teams for content creation
- Lead product launch marketing activities

Requirements:
- 3+ years of digital marketing experience
- Strong analytical and project management skills
- Experience with social media marketing
- Knowledge of SEO and content marketing`;
      },

      // Demo prompt (image bot)
      prefillDemoDataForImageBot: () => {
        userInputs.photoOfA = userInputs.photoOfA || "Lawyer";
        userInputs.wearing = userInputs.wearing || "Suit";
        userInputs.holding = userInputs.holding || "a pen";
        userInputs.engaging = userInputs.engaging || "with a client";
        userInputs.gesture = userInputs.gesture || "talking to a client";
        userInputs.workingIn = userInputs.workingIn || "in a conference room";
        userInputs.facialExp = userInputs.facialExp || "smile";
        userInputs.lookingAt = userInputs.lookingAt || "the camera";
      },
      sendPromptToBot,
      openProfessionModal: async (professionId) => (await openModal(ProfessionModal, { professionId })),
    }
  }
});
</script>

<style scoped>
.input-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 4px;
  padding: 4px;
}

.compact-input {
  --padding-start: 8px;
  --padding-end: 8px;
  font-size: 14px;
  margin: 0;
  min-height: 42px;
}

.compact-input::part(label) {
  font-size: 12px;
}

.compact-input::part(placeholder) {
  font-size: 12px;
}
</style>