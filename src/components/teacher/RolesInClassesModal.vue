<template>
  <ion-page>
    <ion-header>
      <ion-grid fixed>
        <ion-toolbar v-if="!hideHeader">
          <ion-title>Your role(s) in class(es), if applicable.</ion-title>

          <ion-buttons slot="end">
            <ion-button slot="icon-only" @click="cancelChanges()"><ion-icon :icon="close"></ion-icon></ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-grid>
    </ion-header>

    <ion-content>
      <ion-grid fixed>
        <form @submit.prevent="confirmChanges()">
          <ion-card style="border-left: 8px solid #EF6C00" v-for="(rc, i) in rolesInClasses" :key="i">
            <ion-item lines="none">
              <ion-label>
                <h2>Role {{i+1}}</h2>
              </ion-label>
              <ion-buttons slot="end">
                <ion-button @click="removeRoleInClass(i)">
                  <ion-icon slot="icon-only" :icon="close"></ion-icon>
                </ion-button>
              </ion-buttons>
            </ion-item>
            <ion-row>
              <ion-col size="6">
                <ion-textarea fill="outline" label="You are a" labelPlacement="floating" type="text" v-model="rc.role"
                              @click="openRoleInClassSelectModal(i, rc.role, rc.classes)" required></ion-textarea>
              </ion-col>
              <ion-col size="6">
                <ion-textarea fill="outline" label="in class(es)" labelPlacement="floating" type="text" v-model="rc.classes"
                              @click="openRoleInClassSelectModal(i, rc.role, rc.classes, rc.role ? 2 : 1)" required></ion-textarea>
              </ion-col>
            </ion-row>
          </ion-card>

          <!-- Add More Button -->
          <div class="ion-margin-vertical ion-text-center">
            <ion-button color="secondary" @click="incrementRoleInClass()" shape="round">
              <ion-icon slot="start" :icon="add"></ion-icon>
              Add More
            </ion-button>
          </div>

          <!-- Submit Button -->
          <ion-button class="ion-margin-vertical" type="submit" expand="block">
            {{ t('UserProfilePage.save') }}
          </ion-button>
        </form>
      </ion-grid>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
// vue
import { defineProps, defineComponent, onMounted, ref, computed, watch, } from 'vue';

// icons
import { add, close } from 'ionicons/icons';

// components
import {
  IonPage,
  IonGrid,
  IonInput, IonTextarea,
  IonIcon,
  IonFooter,
  IonButton,
  IonButtons,
  IonContent,
  IonHeader,
  IonItem,
  IonList,
  IonTitle,
  IonSearchbar,
  IonToolbar,
  IonCol, IonRow, IonLabel, IonCard,
  modalController,
  loadingController
} from '@ionic/vue';
import RoleInClassSelectModal from '@/components/teacher/RoleInClassSelectModal.vue';

// composables
import config from '@/config';
import { utils } from '@/composables/utils';
import { useI18n } from 'vue-i18n';
import { useStore } from '@/store';

// services
import TeacherService from '@/services/TeacherService';

// types
import { User } from '@/types';

// props
const props = defineProps(["hideHeader", "prefilledRolesInClasses"]);

// methods
const { t } = useI18n();
const { formatDate, presentToast, presentAlert, presentPrompt, uniqueId } = utils();

// State
const store = useStore();
const user = computed<User>(() => store.state.user);
const activeClassRoles = computed(() => ((user.value.teacher?.classRoles || []).filter(cr => cr.status != 'removed')));
const searchKeyword = ref(""); // TBC: for searching class / subjects?
const rolesInClasses = ref([{
  id: `ric${uniqueId()}`,
  role: "",
  classes: "",
  remarks: "",
}]);

// Prefill selected roles / user class roles
onMounted(() => {
  const { prefilledRolesInClasses } = props;
  if (prefilledRolesInClasses && prefilledRolesInClasses.length > 0) {
    rolesInClasses.value = prefilledRolesInClasses;
  } else {
    rolesInClasses.value = [...(activeClassRoles.value as any)];
  }
})
watch(user, () => {
  if (!props.prefilledRolesInClasses) {
    rolesInClasses.value = [...(activeClassRoles.value as any)];
  }
})

// Methods
const cancelChanges = async () => (await modalController.dismiss({}));
const confirmChanges = async () => {
  if (props.hideHeader) { // from TeacherRolesModal
    const loading = await loadingController.create({});
    await loading.present();

    // Check remove class roles (set status to removed)
    const removedClassRoles: any = activeClassRoles.value.filter(acr => (rolesInClasses.value.find(cr => cr.id == acr.id) == null));
    const updatedClassRoles = rolesInClasses.value.concat(removedClassRoles.map(rcr => {
      rcr.status = 'removed';
      return rcr;
    }));
    TeacherService.updateTeacher(user.value, { classRoles: updatedClassRoles });
    store.commit('upsertTeacherClassRoles', updatedClassRoles);
    presentToast("Saved successfully!");

    loading.dismiss();
  }
  else {
    await modalController.dismiss({ rolesInClasses: rolesInClasses.value })
  }
};

const incrementRoleInClass = () => {
  rolesInClasses.value.push({
    id: `ric${uniqueId()}`,
    role: "",
    classes: "",
    remarks: "",
  })
};
const removeRoleInClass = (idx) => {
  rolesInClasses.value.splice(idx, 1);
};

// Modal
const openRoleInClassSelectModal = async (idx, prefilledRole, classes, initialSection = 1) => {
  const modal = await modalController.create({
    component: RoleInClassSelectModal,
    componentProps: { initialSection, prefilledRole, prefilledClasses: classes ? classes.split(" , ") : [] },
  });
  modal.onDidDismiss().then(({ data }) => {
    if (data && data.selectedRole) {
      const targetObj = rolesInClasses.value[idx];
      targetObj.role = data.selectedRole;
      targetObj.classes = data.selectedClasses.sort().join(" , ");
      if (prefilledRole != data.selectedRole) targetObj.id = `ric${uniqueId()}`; // role change: gen new row to keep records
    }
  });
  return modal.present();
};
</script>

<style scoped>
  ion-input, ion-textarea {
    --color: var(--ion-color-dark);
    font-weight: 500;
  }
</style>