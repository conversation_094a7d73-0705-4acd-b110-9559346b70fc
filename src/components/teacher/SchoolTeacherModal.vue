<template>
  <ion-header>
    <ion-toolbar style="--min-height: 36px">
      <ion-buttons slot="start">
        <ion-button slot="icon-only" @click="closeModal()"><ion-icon :icon="arrowBack"></ion-icon></ion-button>
      </ion-buttons>

      <!-- Search teachers -->
      <ion-searchbar style="padding-bottom: 0; height: 40px" mode="ios" v-model="searchKeyword" show-cancel-button="focus"></ion-searchbar>
    </ion-toolbar>

    <ion-toolbar v-if="service">
      <ion-title>
        <ion-label class="ion-text-wrap">
          <p>Our programs use a Real Name system (實名制), with certificates issued jointly by universities and FDMT.</p>
          <p style="margin-top: 10px">F5 class and subject teachers, besides career teachers, are encouraged to register by scanning the QR code at
          <a style="color: var(--ion-color-dark)" target="_blank" :href="service.slideLink">{{ service.slideLink.replace("https://", "") }}</a> to earn the nomination right and be listed below.</p>
          <p style="margin-top: 10px">Meanwhile, please inform FDMT if any of the following teachers are not school staff.</p>
        </ion-label>
      </ion-title>
    </ion-toolbar>
  </ion-header>

  <ion-content :fullscreen="true">
    <!--
      For principals to verify teacher authenticity from phone numbers
    -->
    <div v-if="showPhones">
      <ion-item lines="full" v-for="user in filteredSchoolTeachers(false)" :key="user.id">
        <ion-label class="ion-text-wrap" style="line-height: 1.15">
          <h3 v-if="user.preferredName && user.preferredName != user.fullName">{{ user.preferredName }} ({{ user.fullName }})</h3>
          <h3 v-else>{{ user.fullName }}</h3>
          <small v-if="user.teacher?.schoolRoles">{{ user.teacher?.schoolRoles }}</small><br />
          <small v-html="(user.teacher?.classRoles || []).filter(cr => cr.status != 'removed').map(cr => (`<b>${cr.role} (${cr.classes})</b>`)).join(' & ')"></small>
          <p>{{ user.phone }}<span v-if="user.email"> | {{ user.email }}</span></p>
        </ion-label>

        <ion-select interface="popover" placeholder="Select role" @ionChange="handleRoleChange($event, user)" :value="getTeacherServiceRole(user)">
          <ion-select-option value="Active">Active</ion-select-option>
          <ion-select-option value="Inactive">No longer in school</ion-select-option>
        </ion-select>
      </ion-item>
    </div>

    <!--
      Verify teachers / teacher list / nominate / assign teachers
      -->
    <div v-else>
      <ion-accordion-group :multiple="true" :value="Object.keys(filteredSchoolTeachers())">
        <ion-accordion style="--min-height: 24px" v-for="(teachers, group) in filteredSchoolTeachers()" :key="group" :value="group">
          <ion-item color="primary" lines="full" slot="header">
            <ion-label><p><b>{{ group }}</b></p></ion-label>
            <ion-badge :color="teachers.length > 0 ? 'success' : 'danger'" v-if="targetTeacherGroups.includes(group)">{{ teachers.length }}</ion-badge>
            <ion-badge v-else>{{ teachers.length }}</ion-badge>
          </ion-item>
          <div slot="content">
            <ion-item lines="full" v-for="user in teachers" :key="user.id">
              <!-- Assign -->
              <ion-label class="ion-text-wrap" style="line-height: 1.15">
                <h3 v-if="user.preferredName && user.preferredName != user.fullName">{{ user.preferredName }} ({{ user.fullName }})</h3>
                <h3 v-else>{{ user.fullName }}</h3>
                <small v-if="user.teacher?.schoolRoles">{{ user.teacher?.schoolRoles }}</small><br />
                <small v-html="(user.teacher?.classRoles || []).filter(cr => cr.status != 'removed').map(cr => (`<b>${cr.role} (${cr.classes})</b>`)).join(' & ')"></small>
                <!--<p>{{ teacher.phone }}<span v-if="teacher.email"> | {{ teacher.email }}</span></p>-->
              </ion-label>

              <ion-select interface="popover" placeholder="Select role" @ionChange="handleRoleChange($event, user)" :value="getTeacherServiceRole(user)">
                <ion-select-option value="Active">Active</ion-select-option>
                <ion-select-option value="Inactive">No longer in school</ion-select-option>
                <!--<ion-select-option value="Nominator">Nominator</ion-select-option>
                <ion-select-option value="Supervisor">Supervisor</ion-select-option>
                <ion-select-option value="Not involved">Not involved</ion-select-option>
                <ion-select-option value="Retired">Retired</ion-select-option>-->
              </ion-select>

              <!-- Nominate Teacher Events -->
              <ion-icon slot="start" :icon="checkmark" v-if="teacherEvent && ['Attended', 'Yes', 'Confirmed'].includes(getTeacherSessionResp(user)) && getTeacherSessionResp(user, 'nominatedBy')"></ion-icon>
              <ion-checkbox
                v-else-if="teacherEvent"
                slot="start"
                @update:modelValue="onCheckNominee($event, user)"
                :modelValue="nominees.find(n => n.id == user.id) != null"
              >
              </ion-checkbox>
              <ion-label class="ion-text-wrap" v-if="teacherEvent">
                <h3>{{ user.fullName }}{{ (user.preferredName && user.preferredName != user.fullName) ? ` (${user.preferredName})` : '' }}</h3>
                <p v-if="user.teacher?.schoolRoles">{{ user.teacher?.schoolRoles }}</p>
                <span v-if="getTeacherSessionResp(user)">
                  <span v-if="getTeacherSessionResp(user, 'nominatedBy')"><ion-badge color="medium">Nominated</ion-badge>&nbsp;</span>
                  <span v-if="['Attended', 'Yes', 'Confirmed'].includes(getTeacherSessionResp(user))"><ion-badge color="primary">Applied</ion-badge>&nbsp;</span>
                  <span v-if="['Attended'].includes(getTeacherSessionResp(user))"><ion-badge color="success">Attended</ion-badge>&nbsp;</span>
                  <span v-if="['Unsubscribed', 'No'].includes(getTeacherSessionResp(user))"><ion-badge color="danger">No</ion-badge></span>
                </span>
              </ion-label>
            </ion-item>
          </div>
        </ion-accordion>
      </ion-accordion-group>
    </div>
  </ion-content>
  <!--
    Footer Nominate Button (Batch Nominate)
  -->
  <ion-footer>
    <ion-toolbar class="ion-text-center" v-show="nominees.length > 0">
      <ion-grid style="padding-top: 0" fixed>
        <ion-button class="no-text-transform" color="success" expand="block" @click="submitNomination(schoolTeachers, nominees)">
          <ion-icon slot="end" :icon="arrowForward"></ion-icon>
          <ion-label>
            <h2>Nominate {{ nominees.length }} teacher{{ nominees.length > 1 ? 's' : '' }}</h2>
          </ion-label>
        </ion-button>
      </ion-grid>
    </ion-toolbar>
  </ion-footer>
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, watch, reactive, onMounted } from 'vue';

// icons
import { add, close, checkmark, arrowUp,  arrowForward, arrowBack, trashOutline, sendOutline, helpCircleOutline,
        createOutline, openOutline, addCircleOutline, chevronForwardOutline, informationCircleOutline } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
        IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonBadge,
        IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption,
        IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid, IonCheckbox,
        IonInfiniteScroll, IonInfiniteScrollContent, IonReorder, IonReorderGroup,
        IonFab, IonFabButton,
        loadingController, modalController, alertController, } from '@ionic/vue';

// composables / services
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';
import RegistrationQRCodeModal from '@/components/modals/RegistrationQRCodeModal.vue'

// services
import TeacherService from '@/services/TeacherService';

// types
import { User } from '@/types';
import config from '@/config';

export default defineComponent({
  name: 'SchoolTeacherModal',
  props: ["service", "teacherEvent", "showPhones"],
  components: { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
                IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonBadge,
                IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption,
                IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid, IonCheckbox,
                IonInfiniteScroll, IonInfiniteScrollContent, IonReorder, IonReorderGroup,
                IonFab, IonFabButton, },
  setup(props) {
    // methods or filters
    const store = useStore();
    const { openModal, presentPrompt, presentToast, getQRCodeUrl, sendLinkToMyWhatsApp, } = utils();
    const { t } = useI18n();

    const user = computed(() => store.state.user);
    const nominees = ref<User[]>([]);
    const schoolTeachers = computed<User[]>(() => store.state.schoolTeachers);
    const searchKeyword = ref("");
    const selectedFilter = ref("all");

    const closeModal = async () => (await modalController.dismiss({}));

    // Teacher Events
    const getTeacherSessionResp = (s: User, targetKey = 'response') => {
      if (props.teacherEvent) {
        return (s.sessionResponses?.find(r => r.sessionId == props.teacherEvent.id) || {})[targetKey];
      }
      return null;
    }

    const onCheckNominee = (checked: any, student: any) => {
      const idx = nominees.value.findIndex(n => n.id == student.id);
      if (checked && idx == -1) nominees.value.push(student);
      else if (!checked && idx != -1) nominees.value.splice(idx, 1);
    }

    // return variables & methods to be used in template HTML
    return {
      // icons
      add, close, checkmark, arrowUp, arrowForward, arrowBack,
      trashOutline, sendOutline, helpCircleOutline, createOutline, openOutline,addCircleOutline,
      chevronForwardOutline, informationCircleOutline,

      // variables
      user,
      schoolTeachers, nominees,
      searchKeyword,
      selectedFilter,
      targetTeacherGroups: config.targetTeacherGroups,

      // methods
      t, getQRCodeUrl, sendLinkToMyWhatsApp,
      closeModal, getTeacherSessionResp, onCheckNominee,

      openRegQRCodeModal: async (event) => {
        return await openModal(RegistrationQRCodeModal, { event, targetTo: 'teacher', serviceId: props.service?.id, })
      },

      // Verifying Students
      updateUserVerificationResult: (result: any, user: User) => {
        if (user.verificationResult == result) result = null; // toggle

        TeacherService.updateUserVerificationResult(result, user.id); // update DB

        user.verificationResult = result; // update store
      },

      // Show list of teachers based on search keyword & filters
      filteredSchoolTeachers: (groupTeachers = true) => {
        let filteredTeachers: User[] = schoolTeachers.value.filter(t => t.fullName);
        if (!user.value.isAdmin) filteredTeachers = filteredTeachers.filter((s: User) => (!s.roles?.includes('admin'))); // skip admin users
        if (searchKeyword.value) {
          filteredTeachers = filteredTeachers.filter((s: User) => {
            const classRolesStr = (s.teacher?.classRoles || []).filter(cr => cr.status != 'removed').map(cr => (`${cr.role} ${cr.classes}`));
            const searchInWords = `${s.fullName} ${s.chineseName} ${s.preferredName} ${s.phone} ${s.email} ${classRolesStr}`.toLowerCase();
            const cleanedKeyword = searchKeyword.value.toLowerCase();
            return searchInWords.includes(cleanedKeyword);
          });
        }
        filteredTeachers = [
          ...filteredTeachers.filter(t => t.teacher?.employmentStatus == "Active"),
          ...filteredTeachers.filter(t => t.teacher?.employmentStatus != "Active"),
        ];

        if (groupTeachers) {
          const groupedObj = {};
          for (const group of config.targetTeacherGroups) groupedObj[group] = []; // init
          for (const t of filteredTeachers) {
            let key: any = "Other teachers";
            if (t.teacher?.employmentStatus != "Active") key = "No longer in school";
            else if (t.teacher?.schoolRoles?.toLowerCase().split(" , ").includes("principal")) key = "Principal";
            else if (t.teacher?.schoolRoles?.toLowerCase().includes("career")) key = "Career Teacher";
            else {
              const targetRoles = [
                "Class teacher", "Ethics and Religious Studies teacher",
                ...(config.coreSubjects.map(s => `${s} teacher`)),
              ];
              for (const cl of ["5A", "5B", "5C", "5D", "5E", "6A", "6B", "6C", "6D", "6E"]) {
                if (t.teacher?.classRoles?.some(cr => (cr.classes.includes(cl) && targetRoles.includes(cr.role) && cr.status == "active"))) {
                  groupedObj[cl] = groupedObj[cl] || [];
                  groupedObj[cl].push(t); // e.g. ERE teacher may teach multiple classes
                  key = null;
                }
              }
            }
            if (key) {
              groupedObj[key] = groupedObj[key] || [];
              groupedObj[key].push(t);
            }
          }
          return groupedObj;
        }
        return filteredTeachers;
      },

      // Update teacher role for the service
      handleRoleChange: async (ev, targetUser) => {
        const { service } = props;
        const serviceId = service?.id;
        const newRole = ev.detail.value;

        if (serviceId) {
          let relatedTeacherResp = targetUser.teacherResponses.find(tr => tr.serviceId == serviceId);
          if (!relatedTeacherResp) {
            relatedTeacherResp = { serviceId, role: "", intakeYear: service?.currIntakeYear, };
            targetUser.teacherResponses.push(relatedTeacherResp);
          }
          const oldRole = relatedTeacherResp.role || "";
          relatedTeacherResp.role = null;
          relatedTeacherResp.role = oldRole; // prevent select UI changing value
        }

        //presentPrompt("Confirm update?", async () => {
        const loading = await loadingController.create({ });
        await loading.present();

        const newEmploymentStatus = ["Inactive", "Retired"].includes(newRole) ? newRole : "Active";
        await TeacherService.updateTeacher(targetUser, { employmentStatus: newEmploymentStatus });

        // Sync latest employment status of target teacher
        if (targetUser.teacher) targetUser.teacher.employmentStatus = ["Inactive", "Retired"].includes(newRole) ? newRole : "Active";
        store.commit('upsertSchoolTeachers', [targetUser]);

        // Upsert teacher response
        /*relatedTeacherResp.role = newRole;
        relatedTeacherResp.assignedTo = targetUser.id;
        relatedTeacherResp.eventName = service.name;
        const responseObj = await TeacherService.upsertTeacherResponse(null, serviceId, relatedTeacherResp, null, null);
        for (const key in responseObj) relatedTeacherResp[key] = responseObj[key];

        // update store teacher response for current user
        if (targetUser.id == user.value.id) store.commit('upsertTeacherResponse', responseObj);*/

        loading.dismiss();
      },
      getTeacherServiceRole: (user: any) => {
        /*
        if (user.teacher?.employmentStatus == "Active") {
          const serviceId = props.service.id;
          let relatedTeacherResp = user.teacherResponses.find(tr => tr.serviceId == serviceId);
          if (!relatedTeacherResp) {
            relatedTeacherResp = { serviceId, role: "" };
            user.teacherResponses.push(relatedTeacherResp);
          }
          return relatedTeacherResp ? relatedTeacherResp.role : ""; // default is nominator / not related?
        }
        */
        return user.teacher?.employmentStatus;
      },

      submitNomination: (schoolTeachers: any, nominees: User[]) => {
        const nomineeDescriptions = nominees.map(n => `${n.fullName} (${n.teacher?.schoolRoles})`);
        presentPrompt(`Confirm nomination?<br /><br />${nomineeDescriptions.sort().join("<br />")}`, async () => {
          const loading = await loadingController.create({ duration: 60000 });
          await loading.present();

          const { id : sessionId } = props.teacherEvent;

          // Insert nomination record
          const reqNominees = nominees.map(({ id, fullName, phone, teacher }) => {
            return { id, fullName, phone, schoolId: store.state.user.schoolId, schoolRoles: teacher?.schoolRoles }; // minimize the payload
          });
          await TeacherService.insertNominationRecord(reqNominees, [sessionId]);


          // Update corresponding session event response (nominatedBy)
          for (const n of nominees) {
            const sessionResp = n.sessionResponses?.find(resp => resp.sessionId == sessionId);
            if (sessionResp) {
              // update nominator of existing response
              sessionResp.nominatedBy = store.state.user.id;
            } else {
              // insert new session response
              (n.sessionResponses = n.sessionResponses || []).push({
                sessionId: sessionId,
                userId: n.id,
                response: "Yes",
                confirmed: "Yes",
                nominatedBy: store.state.user.id
              });
            }
          }

          // Upsert school teacher
          store.commit('upsertSchoolTeachers', nominees);

          // Present toast msg (ack)
          presentToast(`Thank you for your nomination!`);

          alertController.dismiss();
          loading.dismiss();
        })
      }

    }
  },
});
</script>

<style scoped>
  ion-toolbar {
    --min-height: 48px;
  }
  ion-title {
    font-weight: normal;
    padding-right: 8px;
    padding-top: 5px;
    padding-bottom: 5px;
  }
  ion-title p {
    font-size: 14px;
  }
</style>