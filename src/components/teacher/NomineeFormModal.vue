<template>
  <ion-header>
    <ion-toolbar>
      <ion-title>Add new nominees</ion-title>
      <ion-buttons slot="end">
        <ion-button slot="icon-only" @click="closeModal()"><ion-icon :icon="close"></ion-icon></ion-button>
      </ion-buttons>
    </ion-toolbar>
  </ion-header>
  <ion-content :fullscreen="true">
    <ion-grid>
      <form @submit.prevent="closeModal(nominees)">
        <ion-card style="border-left: 8px solid #EF6C00" v-for="(n, i) in nominees" :key="i">
          <ion-item lines="none">
            <ion-label>
              <h1>Nominee {{i+1}}</h1>
            </ion-label>
            <ion-buttons slot="end">
              <ion-button @click="removeNominee(i)">
                <ion-icon slot="icon-only" :icon="close"></ion-icon>
              </ion-button>
            </ion-buttons>
          </ion-item>
          <ion-row>
            <ion-col size="4">
              <ion-item>
                <ion-input :label="t('class')" labelPlacement="floating" type="text" autocapitalize="characters" v-model="n.class" required></ion-input>
              </ion-item>
            </ion-col>
            <ion-col size="8">
              <ion-item>
                <ion-input :label="t('fullName')" labelPlacement="floating" type="text" v-model="n.fullName" required></ion-input>
              </ion-item>
            </ion-col>
          </ion-row>
          <ion-row>
            <ion-col size="12">
              <ion-item>
                <ion-input :label="t('UserProfilePage.phone')" labelPlacement="floating" inputmode="numeric" v-model="n.phone" maxlength="8" required></ion-input>
              </ion-item>
            </ion-col>
          </ion-row>
        </ion-card>

        <!-- Add More Button -->
        <div class="ion-margin-vertical ion-text-center">
          <ion-button color="secondary" @click="incrementNominees()" shape="round">
            <ion-icon slot="start" :icon="add"></ion-icon>
            Add More
          </ion-button>
        </div>

        <!-- Submit Button -->
        <ion-button class="ion-margin-vertical" type="submit" expand="block">
          {{ t('UserProfilePage.save') }}
        </ion-button>
      </form>
    </ion-grid>
  </ion-content>
</template>

<script lang="ts">
// Vue reactivity
import { reactive, defineComponent, computed, ref } from 'vue';

// icons
import { mail, close, add, arrowBack, } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonSpinner, IonItem, IonLabel, IonChip, IonIcon,
        IonButtons, IonButton, IonInput, IonTextarea, IonGrid, IonCol, IonRow, IonSelect, IonSelectOption, IonCard,
        modalController, loadingController, toastController } from '@ionic/vue';

// Utils
import { utils } from '@/composables/utils';
import { useI18n } from 'vue-i18n';
import { useStore } from '@/store';

// types
import { Nominee } from '@/types';

export default defineComponent({
  name: 'NomineeFormModal',
  props: [],
  components: { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter,
                IonSpinner, IonItem, IonLabel, IonChip, IonIcon,
                IonButtons, IonButton, IonInput, IonTextarea, IonCard,
                IonGrid, IonCol, IonRow, IonSelect, IonSelectOption, },
  setup(props) {
    // methods or filters
    const { t } = useI18n();
    const { formatDate, presentToast, presentAlert, presentPrompt, uniqueId } = utils();

    // 1. declare state variables (ref to make them reactive)
    const nominees = ref<Nominee[]>([{
      id: uniqueId(),
      fullName: "",
      class: "",
      phone: "",
    }]);

    // 2. return variables & methods to be used in template HTML
    return {
      // icons
      mail, close, add,

      // variables
      nominees,

      // methods
      t, formatDate,
      incrementNominees: () => {
        const lastInputClass = nominees.value.slice().reverse().find(n => !!n.class)?.class;
        nominees.value.push({
          id: uniqueId(),
          fullName: "",
          class: lastInputClass || "",
          phone: "",
        })
      },
      removeNominee: (idx) => {
        nominees.value.splice(idx, 1);
      },
      closeModal: async (nominees: any = null) => {
        if (nominees) {
          //const nomineeDescriptions = nominees.map((n, i) => `${i+1}. ${n.class} ${n.fullName} (${n.phone})`);
          //presentPrompt(`Confirm save?<br /><br />${nomineeDescriptions.join("<br />")}`, async () => {
          await modalController.dismiss({ nominees });
          //});
        } else {
          await presentPrompt(t("confirmLeave"), async () => {
            await modalController.dismiss({});
          });
        }
      },
    }
  }
});
</script>