<template>
  <!--
    School Readiness
  -->
  <ion-card class="outer-card ion-no-margin" v-if="user.schoolId">
    <!--<ion-accordion-group>
      <ion-accordion style="--min-height: 24px">
        <ion-item lines="full" slot="header" style="--min-height: 24px">
          <ion-label><b>{{ (checkedSchoolTeacherRoles().progress * 100).toFixed(0) }}%</b> School registration completed</ion-label>
        </ion-item>
        <div class="ion-text-center" slot="content">-->

          <!-- AchieveJUPAS MOU (Accept / Reject) -->
          <ion-accordion-group>
            <ion-accordion style="--min-height: 24px" value="mou">
              <ion-item lines="full" slot="header">
                <ion-spinner v-if="!userSchool.userConsentRecords"></ion-spinner>
                <ion-icon slot="start" :icon="checkmarkCircle" color="success" v-else-if="isMOUAccepted"></ion-icon>
                <ion-icon slot="start" :icon="alertCircle" color="danger" v-else></ion-icon>

                <ion-label class="ion-text-wrap">
                  1. MOU between <b>FDMT Consulting</b> and <b>{{ user.schoolId?.toUpperCase() }}</b> (the School)
                </ion-label>
              </ion-item>
              <div slot="content" class="ion-padding">
                <ion-row>
                  <ion-col size="8">
                    <ion-button class="no-text-transform" expand="block" href="https://fdmt.hk/mou" target="_blank">
                      <ion-icon slot="start" :icon="documentOutline"></ion-icon>
                      Open MOU Document
                    </ion-button>
                  </ion-col>
                  <ion-col size="4">
                    <ion-button class="no-text-transform" fill="clear" expand="block" href="https://docs.google.com/feeds/download/documents/export/Export?id=1Xmk67g9b2cpiaCwkwmVIGbayzvq9xug-dKdHVN5-jbg&exportFormat=pdf" target="_blank">
                      Download
                    </ion-button>
                  </ion-col>
                </ion-row>
                <p>
                  <ion-checkbox
                    label-placement="end"
                    @ionChange="handleMouAcceptance($event)"
                    :checked="isMOUAccepted"
                    :disabled="isMOUAccepted"
                    v-if="isPrincipal || user.isAdmin"
                  >I have read and approve the MOU</ion-checkbox>
                </p>
              </div>
            </ion-accordion>
          </ion-accordion-group>

          <!-- Verify Teachers -->
          <ion-item lines="full" @click="openSchoolTeacherModal()" button detail v-if="isPrincipal || user.isAdmin">
            <ion-icon slot="start" :icon="informationCircleOutline"></ion-icon>
            <ion-label class="ion-text-wrap">
              2. Registered teachers verified
            </ion-label>
          </ion-item>

          <!-- Term 1 briefing (student & teacher) -->
          <!--<ion-item lines="full" target="_blank" button detail @click="openBookTimeModal('achievejupas-b1', 'the semasterly briefing')">-->
          <ion-item lines="full" target="_blank" button detail @click="openBookingModal('bi0fd219e9')">
            <ion-icon slot="start" :icon="checkmarkCircle" color="success" v-if="isSubmittedTeacherResponse('achievejupas-b1') && isSubmittedTeacherResponse('achievejupas-b1t')"></ion-icon>
            <ion-icon slot="start" :icon="alertCircle" color="danger" v-else></ion-icon>
            <ion-label class="ion-text-wrap">3. Sep/Oct briefing (same-day students & teachers)</ion-label>
          </ion-item>

          <!-- Term 2 briefing (student & teacher) -->
          <!--<ion-item lines="full" target="_blank" button detail @click="openBookTimeModal('achievejupas-b2', 'the semesterly briefing')">-->
          <ion-item lines="full" target="_blank" button detail @click="openBookingModal('bi6ef13f36')">
            <ion-icon slot="start" :icon="checkmarkCircle" color="success" v-if="isSubmittedTeacherResponse('achievejupas-b2') && isSubmittedTeacherResponse('achievejupas-b2t')"></ion-icon>
            <ion-icon slot="start" :icon="alertCircle" color="danger" v-else></ion-icon>
            <ion-label class="ion-text-wrap">4. Feb/Mar briefing (same-day students & teachers)</ion-label>
          </ion-item>

          <!-- Predicted grades 
          <ion-item lines="full" target="_blank" button detail>
            <ion-label class="ion-text-wrap">Students' predicted grades</ion-label>
          </ion-item>-->

          <!-- Report -->
          <ion-item lines="full" target="_blank" button detail :href="`https://fdmt.hk/r/${user.schoolId}`">
            <ion-icon slot="start" :icon="informationCircleOutline"></ion-icon>
            <ion-label class="ion-text-wrap">5. Report <small>(your Google account needed for secured sharing)</small></ion-label>
          </ion-item>

          <!-- School Role Registration Status -->
          <ion-accordion-group value="onboard">
            <ion-accordion style="--min-height: 24px" value="onboard">
              <ion-item lines="full" slot="header">
                <ion-label>Onboard</ion-label>
              </ion-item>
              <div slot="content">
                <ion-segment mode="ios" :scrollable="true" v-model="targetTeacherRole" @ionChange="checkOpenTeacherModal()">
                  <ion-segment-button v-for="(roleObj, idx) in checkedSchoolTeacherRoles().roleObjs" :key="idx" layout="icon-start">
                    <ion-icon size="small" color="success" :icon="checkmarkCircle" v-if="roleObj.relatedTeacher"></ion-icon>
                    <ion-icon size="small" color="danger" :icon="alertCircle" v-else></ion-icon>
                    <ion-label>{{ roleObj.role }}</ion-label>
                  </ion-segment-button>
                </ion-segment>
              </div>
            </ion-accordion>
          </ion-accordion-group>

          <!-- Logs (my actions / responses); Later can show responses by schools / classes here -->
          <ion-accordion-group v-if="recentResponses().length > 0">
            <ion-accordion style="--min-height: 24px">
              <ion-item lines="full" slot="header">
                <ion-label>My recent responses/actions</ion-label>
              </ion-item>
              <div slot="content">
                <ion-item v-for="(resp, idx) in recentResponses().slice(0, 10)" :key="idx" lines="full" detail button
                          @click="openServiceModal(resp.serviceId)">
                  <ion-label class="ion-text-wrap">
                    <p>
                      <b v-if="resp.eventName">{{ resp.eventName }}:</b> {{ resp.response }}
                      <br /><small>{{ formatDate(resp.updatedAt) }}</small>
                    </p>
                  </ion-label>
                </ion-item>
              </div>
            </ion-accordion>
          </ion-accordion-group>

          <!-- User school / class roles -->
          <ion-item lines="none" @click="openTeacherRolesModal()" button detail>
            <ion-label class="ion-text-wrap">Your profile</ion-label>
          </ion-item>
        <!--</div>
      </ion-accordion>
    </ion-accordion-group>-->
  </ion-card>

  <!-- For indicating time for AchieveJUPAS briefing -->
  <ion-modal :is-open="bookTimeData.isModalOpened" :backdrop-dismiss="false" :show-backdrop="true">
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-button slot="icon-only" @click="bookTimeData.isModalOpened = false"><ion-icon :icon="arrowBack"></ion-icon></ion-button>
        </ion-buttons>
        <ion-title class="ion-text-left" style="padding-left: 0">
          <ion-label><h2><b>Book a time for {{ bookTimeData.title }}</b></h2></ion-label>
        </ion-title>
      </ion-toolbar>
    </ion-header>
    <ion-content class="ion-padding">
      <teacher-response-questions :serviceId="bookTimeData.serviceId" group="service" :showSubmitBtn="true"
                                @responseSubmitted="bookTimeData.isModalOpened = false"></teacher-response-questions>
    </ion-content>
  </ion-modal>
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, watch, reactive, onMounted } from 'vue';

// icons
import { add, close, arrowBack, checkmarkCircle, alertCircle, ellipsisHorizontal, ellipsisVertical, pencil, documentOutline, school, informationCircleOutline, } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol,
        IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonSpinner, IonList,
        IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonPopover, IonCheckbox,
        IonAccordion, IonAccordionGroup, IonText, IonChip, IonProgressBar, IonSegment, IonSegmentButton,
        IonModal,
        loadingController, modalController, alertController, } from '@ionic/vue';
import SchoolTeacherModal from '@/components/teacher/SchoolTeacherModal.vue';
import ServiceModal from '@/components/modals/ServiceModal.vue';
import StudentModal from '@/components/teacher/SchoolStudentModal.vue';
import TeacherRolesModal from '@/components/teacher/TeacherRolesModal.vue';
import TeacherResponseQuestions from '@/components/teacher/TeacherResponseQuestions.vue';

// composables
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';
import { User } from '@/types';
import config from '@/config';

// services
import AchieveJUPASService from '@/services/AchieveJUPASService';
import SchoolService from '@/services/SchoolService';

export default {
  props: [],
  components: { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol,
    IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonSpinner, IonList,
    IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonPopover, IonCheckbox,
    IonAccordion, IonAccordionGroup, IonText, IonChip, IonProgressBar, IonSegment, IonSegmentButton,
    IonModal,
    TeacherResponseQuestions, },
  setup(props) {
    // methods or filters
    const store = useStore();
    const { t } = useI18n();
    const { openModal, sleep, presentToast, presentPrompt, getQRCodeUrl, sendLinkToMyWhatsApp, formatDate,
            getIntakeYearOfDate, getCurrentIntakeYear, openImageModal, getProxyImgLink, copyText, uniqueId,
            openBookingModal, } = utils();

    const user = computed(() => store.state.user);
    const userSchool = computed(() => store.getters.userRelatedSchool);
    
    // School teachers & principals
    const schoolTeachers = computed<User[]>(() => store.state.schoolTeachers);
    const targetTeacherRole = ref("");

    // AchieveJUPAS briefing
    const bookTimeData = reactive({
      isModalOpened: false,
      serviceId: "",
      title: "AchieveJUPAS briefing",
    });

    onMounted(() => {
      SchoolService.getSchoolById(user.value.schoolId).then(school => {
        store.commit('upsertSchools', [school]);
      });
    })

    // return variables & methods to be used in template HTML
    return {
      // icons
      add, close, arrowBack,
      checkmarkCircle, alertCircle, ellipsisHorizontal, ellipsisVertical, pencil, documentOutline, informationCircleOutline,

      // variables
      user, userSchool,
      isPrincipal: computed(() => (user.value.teacher && user.value.teacher?.schoolRoles?.toLowerCase().includes('principal'))),
      bookTimeData,

      // methods
      t,
      formatDate,
      closeModal: async () => (await modalController.dismiss({})),
      openStudentModal: async (initialSection = 2) => (await openModal(StudentModal, { schoolId: user.value.schoolId, initialSection })),

      openImageModal, getProxyImgLink, copyText,
      openServiceModal: async (serviceId: any, event: any = null) => {
        if (serviceId) {
          return await openModal(ServiceModal, { serviceId, event })
        }
      },
      openSchoolTeacherModal: async () => (await openModal(SchoolTeacherModal, { showPhones: true })),
      recentResponses: () => (user.value.teacherResponses || []),

      // Role(s) in class(es)
      openTeacherRolesModal: async () => (await openModal(TeacherRolesModal, {})),

      // Progress bar (teacher registration status)
      schoolTeachers, targetTeacherRole,
      checkOpenTeacherModal: async () => {
        //const selectedRole = targetTeacherRole.value;
        await openModal(SchoolTeacherModal, {});
        targetTeacherRole.value = "";
      },
      checkedSchoolTeacherRoles: () => {
        const roles = config.targetTeacherGroups; // Principal / Career Teacher / 5A / ...
        const roleObjs: any = []; // [{ role, relatedTeacher }];
        const teachers = schoolTeachers.value.filter(t => t.teacher?.employmentStatus == "Active"); // only take into account active teachers

        for (const role of roles) {
          let relatedTeacher: any = null;
          switch (role) {
            case "Principal":
              relatedTeacher = teachers.find(t => t.teacher?.schoolRoles?.toLowerCase().split(" , ").includes("principal"));
              break;
            case "Career Teacher":
              relatedTeacher = teachers.find(t => t.teacher?.schoolRoles?.toLowerCase().includes("career"));
              break;
            default: // 5A-E
              relatedTeacher = teachers.find(t => t.teacher?.classRoles?.some(cr => {
                const targetRoles = [
                  "Class teacher",
                  "Ethics and Religious Studies teacher",
                  ...(config.coreSubjects.map(s => `${s} teacher`)),
                ];
                return cr.classes.includes(role) && targetRoles.includes(cr.role) && cr.status == "active";
              }));
              break;
          }
          roleObjs.push({ role, relatedTeacher });
        }
        return {
          progress: roleObjs.filter(ro => (ro.relatedTeacher != null)).length / roles.length, // 100% = all teachers registered
          roleObjs,
        }
      },

      // MOU acceptance
      handleMouAcceptance: (event: any) => {
        event.stopPropagation();
        event.target.checked = false;
        presentPrompt("Confirm you have read and accept the MOU?", async () => {
          const loading = await loadingController.create({ duration: 15000 });
          await loading.present();
          const { schoolId, fullName, teacher, roles, phone, waGroupId, } = user.value;
          const payload = {
            id: `uc${uniqueId()}`,
            schoolId,
            fullName,
            target: 'mou',
            roles: teacher?.schoolRoles || roles,
            response: 'I have read and accept the MOU',
            phone, waGroupId,
          };
          //await AchieveJUPASService.insertUserConsent(payload); // make sure record inserted properly
          AchieveJUPASService.insertUserConsent(payload);
          user.value.userConsentRecords?.push(payload);
          event.target.checked = true;
          event.target.disabled = true;
          await sleep(3);
          loading.dismiss();
        });
      },
      isMOUAccepted: computed(() => {
        //return user.value.userConsentRecords?.find(r => r.target == 'mou' && r.schoolId == user.value.schoolId) != null;
        console.log(userSchool.value?.userConsentRecords);
        return userSchool.value?.userConsentRecords?.find(r => r.target == 'mou' && r.roles?.toLowerCase().includes('principal')) != null;
      }),

      // For booking time for Term 1 & 2 briefing sessions
      openBookingModal,
      openBookTimeModal: (serviceId, title) => {
        bookTimeData.serviceId = serviceId;
        bookTimeData.isModalOpened = true;
        bookTimeData.title = title;
      },
      isSubmittedTeacherResponse: (serviceId) => {
        return user.value.teacherResponses?.find(r => r.serviceId == serviceId) != null;
      },
    }
  },
};
</script>

<style scoped>
  ion-item {
    --min-height: 32px;
  }
  ion-item ion-icon[slot="start"] {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    margin-right: 8px !important;
  }
</style>
