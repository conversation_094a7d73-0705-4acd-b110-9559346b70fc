<template>
  <ion-page>
    <!--
      Header
      -->
    <ion-header>
      <ion-grid fixed>
        <ion-toolbar>
          <ion-buttons slot="start">
            <ion-button slot="icon-only" @click="closeModal()"><ion-icon :icon="arrowBack"></ion-icon></ion-button>
          </ion-buttons>
          <ion-title>
            <ion-label class="ion-text-wrap">
              <h2>AB4 customization{{ user.schoolId ? ` [${user.schoolId.toString().toUpperCase()}]` : "" }}</h2>
            </ion-label>
          </ion-title>
          <ion-buttons slot="end">
            <ion-button color="primary" fill="outline" @click="openProfessionSelectModal()" class="no-text-transform">
              Preview
            </ion-button>
          </ion-buttons>

        </ion-toolbar>
      </ion-grid>
    </ion-header>

    <!--
      Main Content
      -->
    <ion-content>
      <div class="spin" v-if="loadingData">
        <ion-spinner></ion-spinner>
      </div>
      <ion-grid fixed v-else>
        <ion-accordion-group>
          <ion-accordion v-for="(q, qIdx) in step1Questions" :key="q.id" style="--min-height: 24px">
            <ion-item lines="full" slot="header" style="--min-height: 24px">
              <ion-label class="ion-text-wrap">
                <p>{{ qIdx+1 }}. <b>[{{ q.group }}]</b> {{ q.title }}</p>
              </ion-label>
            </ion-item>

            <ion-list class="ion-no-padding" slot="content">
              <!-- Add new tag -->
              <ion-item color="primary" @click="addNewTag(q)" button>
                <ion-label class="ion-text-wrap" style="padding-left: 20px">
                  <p>Add new option</p>
                </ion-label>
                <ion-buttons slot="end" style="padding-right: 76px">
                  <ion-button>
                    <ion-icon slot="icon-only" :icon="add"></ion-icon>
                  </ion-button>
                </ion-buttons>
              </ion-item>

              <!-- List of existing tags -->
              <ion-accordion-group>
                <ion-accordion v-for="(opt, idx) in q.options" :key="opt.id">
                  <!-- Tag -->
                  <ion-item color="light" lines="full" slot="header" style="--min-height: 24px">
                    <ion-label class="ion-text-wrap" style="padding-left: 20px">
                      <p>{{ opt.text }}</p>
                    </ion-label>
                    <ion-buttons slot="end">
                      <ion-button @click.stop="updateTag(opt)">
                        <ion-icon size="small" slot="icon-only" :icon="pencil"></ion-icon>
                      </ion-button>
                      <ion-button color="danger" @click.stop="deleteTag(q.options, idx)">
                        <ion-icon size="small" slot="icon-only" :icon="trashOutline"></ion-icon>
                      </ion-button>
                    </ion-buttons>
                  </ion-item>
                  
                  <!-- Related Professions -->
                  <ion-list class="ion-no-padding" slot="content">
                    <ion-item lines="full" v-for="profession in relatedProfessions(opt)" :key="profession.id">
                      <ion-label class="ion-text-wrap" style="padding-left: 40px">
                        <p>{{ profession.name }} {{ profession.nameChi }}</p>
                      </ion-label>
                    </ion-item>
                  </ion-list>
                </ion-accordion>
              </ion-accordion-group>
            </ion-list>
          </ion-accordion>
        </ion-accordion-group>
      </ion-grid>
    </ion-content>
  </ion-page>
</template>

<script lang="ts">
// Vue reactivity
import { computed, defineComponent, ref } from 'vue';

// icons
import { close, arrowBack, pencil, trashOutline, eyeOutline, add, } from 'ionicons/icons';

// components
import { IonPage, IonHeader, IonFooter, IonToolbar, IonTitle, IonContent,
        IonButtons, IonButton, IonIcon, IonCard, IonCardHeader, IonCardContent,
        IonGrid, IonList, IonItem, IonLabel, IonAccordionGroup, IonAccordion, IonSpinner,
        modalController, loadingController, alertController } from '@ionic/vue';
import ABProfessionSelectModal from '@/components/pss/profession/ABProfessionSelectModal.vue';

// types
import { Profession, ProfessionTab, Step1Option } from '@/types';

// composables
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { useStore } from '@/store';
import { utils } from '@/composables/utils';

// service
import ABSService from '@/services/ABSService';

export default defineComponent({
  name: 'AB4CustomizeContentModal',
  props: ["isPage"],
  components: { IonPage, IonHeader, IonFooter, IonToolbar, IonTitle, IonContent,
                IonButtons, IonButton, IonIcon, IonCard, IonCardHeader, IonCardContent,
                IonGrid, IonList, IonItem, IonLabel, IonAccordionGroup, IonAccordion, IonSpinner, },
  setup(props) {
    const { t } = useI18n();
    const router = useRouter();
    const store = useStore();

    const loadingData = computed(() => store.state.loadingPortalData);
    const user = computed(() => store.state.user);
    const step1Questions = computed(() => store.getters.getStep1Questions(false, false));
    const allProfessions = computed(() => store.state.allProfessions);
    const allProfessionTabs = computed<ProfessionTab[]>(() => store.state.professionTabs);

    const { openImageModal, presentPrompt, presentToast, presentAlert, uniqueId, openModal, } = utils();

    const closeModal = async () => {
      if (props.isPage) {
        router.replace('/home');
      } else {
        await modalController.dismiss();
      }
    };

    const promptNewOptText = async (message, prefilledText = "", callback) => {
      const alert = await alertController.create({
        message,
        inputs: [
          {
            name: 'newText',
            type: 'text',
            placeholder: "New text",
            value: prefilledText,
            attributes: { required: true, }
          },
        ],
        buttons: [
          {
            text: t('back'),
            role: 'cancel',
            cssClass: 'secondary',
          }, {
            text: t('submit'),
            handler: (value) => {
              if (value.newText) {
                callback(value.newText);
              } else {
                presentAlert("New text cannot be empty!")
                return false;
              }
            }
          }
        ]
      });
      return alert.present();
    }

    const insertChangeRecord = async (toastMsg, type, optionId, questionId, prevText, newText = null) => {
      const loading = await loadingController.create({});
      await loading.present();
      ABSService.insertStep1ChangeRecord(type, optionId, questionId, prevText, newText);
      presentToast(toastMsg);
      loading.dismiss();
    }

    // 3. return variables & methods to be used in template HTML
    return {
      // icons
      close, arrowBack, pencil, trashOutline, eyeOutline, add,
      
      // variables
      loadingData, step1Questions,
      user,
      
      // methods
      closeModal, openImageModal,

      relatedProfessions: (opt) => {
        const { tabIds, professionIds } = opt;
        let tabProfessionIds = [];
        for (const tid of (tabIds || [])) {
          const pTab = allProfessionTabs.value.find(pt => pt.id == tid);
          if (pTab) tabProfessionIds = tabProfessionIds.concat(pTab.relatedProfessionIds);
        }
        const relatedProfessionIds = professionIds?.length > 0 ? professionIds : tabProfessionIds; // override
        const filteredProfessions = [
          ...(relatedProfessionIds || []).map(id => (allProfessions.value.find((p: Profession) => p.id == id.toString()))),
        ].filter(p => p != null);
        return filteredProfessions;
      },

      // Step 1 Change Records
      deleteTag: (options, idx) => {
        const opt: Step1Option = options[idx];
        presentPrompt(`Are you sure to delete '${opt.text}'?`, () => {
          options.splice(idx, 1);
          insertChangeRecord("Item deleted successfully.", "Delete", opt.id, opt.questionId, opt.text);
        })
      },
      updateTag: async (opt) => {
        await promptNewOptText(`Updating '${opt.text}'`, opt.text, async (newText) => {
          opt.text = newText;
          insertChangeRecord("Item updated successfully!", "Edit", opt.id, opt.questionId, opt.text, newText);
        })
      },
      addNewTag: async (question) => {
        await promptNewOptText(`Adding new item`, "", async (newText) => {
          const { id: questionId } = question;
          const newOptId = `to-${uniqueId()}`;
          question.options.unshift({ id: newOptId, questionId, text: newText });
          insertChangeRecord("Item added successfully!", "Add Tag", newOptId, questionId, "", newText);
        })
      },

      // Preview
      openProfessionSelectModal: async () =>  {
        return await openModal(ABProfessionSelectModal, { isPreview: true }, 'tall-modal');
      },
    }
  }
});
</script>

<style scoped>
  ion-item ion-buttons {
    min-width: 32px !important;
    height: 32px !important;
    margin-left: 4px;
  }
  ion-item ion-buttons ion-button {
    width: 36px !important;
  }
  ion-item ion-buttons ion-button::part(native) {
    padding-inline-start: 0;
    padding-inline-end: 0;
  }
</style>