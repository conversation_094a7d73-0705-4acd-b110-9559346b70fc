<template>
  <ion-header>
    <ion-toolbar>
      <ion-buttons slot="start" v-show="initialSection != 2">
        <ion-button slot="icon-only" @click="section == 1 ? cancelChanges() : section = 1">
          <ion-icon :icon="arrowBack"></ion-icon>
        </ion-button>
      </ion-buttons>

      <ion-title>
        <ion-label class="ion-text-wrap"><h2>{{ getTitle() }}</h2></ion-label>
      </ion-title>
    </ion-toolbar>

    <!-- Custom Role -->
    <ion-toolbar v-show="section == 1">
      <ion-item lines="none" fill="outline">
        <ion-button slot="start" size="normal" @click="onSelectRole(customRole)" v-show="customRole">
          <ion-icon slot="start" :icon="add"></ion-icon>
          Add
        </ion-button>
        <ion-input type="text" v-model="customRole" placeholder="Type here if your role is not listed"></ion-input>
      </ion-item>
    </ion-toolbar>

    <!-- Custom Class -->
    <ion-toolbar v-show="section == 2">
      <ion-item lines="none" fill="outline">
        <ion-button slot="start" size="normal" @click="onAddNewClass()" v-show="customClass">
          <ion-icon slot="start" :icon="add"></ion-icon>
          Add
        </ion-button>
        <ion-input type="text" v-model="customClass" placeholder="Type here if your class is not listed" autocapitalize="characters"></ion-input>
      </ion-item>
    </ion-toolbar>
  </ion-header>

  <ion-content>
    <!--
      1. Select role first
      -->
    <ion-list v-show="section == 1">
      <ion-item v-for="role in rolesInClass()" :key="role" button detail @click="onSelectRole(role)">
        <ion-label>
          {{ role }}
        </ion-label>
      </ion-item>
    </ion-list>

    <!--
      2. Select class that is in charge of / teaching
      -->
    <ion-list v-show="section == 2">
      <ion-item v-for="cl in allClasses()" :key="cl" button>
        <ion-checkbox label-placement="end" justify="start" :value="cl" :checked="isChecked(cl)" @ionChange="checkboxChange($event)">
          {{ cl }}
        </ion-checkbox>
      </ion-item>
    </ion-list>
  </ion-content>

  <ion-footer v-show="section == 2 && selectedClasses.length > 0">
    <ion-toolbar>
      <ion-button color="success" expand="block" @click="confirmChanges(selectedRole, selectedClasses)">
        Confirm
      </ion-button>
    </ion-toolbar>
  </ion-footer>
</template>

<script setup lang="ts">
// vue
import { computed } from '@vue/reactivity';
import { defineComponent, ref, defineProps, } from 'vue';

// icons
import { add, close, arrowBack, arrowForward, } from 'ionicons/icons';

// components
import {
  IonHeader, IonToolbar, IonTitle, IonContent, IonButton, IonButtons,
  IonList, IonItem, IonLabel, IonInput, IonTextarea, IonIcon, IonCheckbox, IonFooter,
  modalController
} from '@ionic/vue';

import config from '@/config';
import { useStore } from '@/store';

const props = defineProps(["initialSection", "prefilledRole", "prefilledClasses"]);

const section = ref(props.initialSection || 1); // role -> classes

const customRole = ref("");
const selectedRole = ref(props.prefilledRole || "");

const customClass = ref("");
const selectedClasses = ref<any>(props.prefilledClasses?.slice() || []);
const extraClasses = ref<any>(props.prefilledClasses?.slice() || []);

// store variables
const store = useStore();
const allElectives = computed(() => store.state.allElectives);

const getTitle = () => {
  if (section.value == 1) return `Select your role`;
  return `You are a ${selectedRole.value} in class(es):`;
};
const cancelChanges = async () => (await modalController.dismiss({}));
const confirmChanges = async (selectedRole, selectedClasses) => (await modalController.dismiss({ selectedRole, selectedClasses, }));

// Roles
const rolesInClass = () => {
  const baseRoles = [props.prefilledRole, 'Class teacher', ...(config.coreSubjects.map(s => `${s} teacher`))];
  const roles = baseRoles.concat(allElectives.value.map(e => (`${e.shortName || e.name} teacher`)));
  return [...new Set(roles.filter(r => r))];
};
const onSelectRole = (role) => {
  if (role) {
    selectedRole.value = role.trim();
    section.value = 2; // select class(es)
  }
};

// Classes
const allClasses = () => {
  const basicClasses: any = [];
  for (let form = 1; form <= 6; form++) {
    for (const label of ['A', 'B', 'C', 'D', 'E', 'F']) {
      basicClasses.push(`${form}${label}`);
    }
  }
  return [...new Set(extraClasses.value.concat(basicClasses))];
};

const onAddNewClass = () => {
    if (customClass.value) {
      const newClass = customClass.value.trim().toUpperCase();
      selectedClasses.value.unshift(newClass);
      customClass.value = '';
      extraClasses.value.unshift(newClass); // put to front
    }
};
const isChecked = (value) => (selectedClasses.value.find((item) => item === value) !== undefined);
const checkboxChange = (ev) => {
  const { checked, value } = ev.detail;
  if (checked){
    selectedClasses.value.push(value);
  } else {
    selectedClasses.value = selectedClasses.value.filter(cl => cl != value);
  }
};
</script>

<style scoped>
  ion-item {
    --min-height: 36px !important;
  }
</style>