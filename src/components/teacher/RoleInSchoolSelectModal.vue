<template>
  <ion-page>
    <ion-header>
      <ion-grid fixed>
        <ion-toolbar v-if="!hideHeader">
          <ion-title>Select your position(s) in the school</ion-title>

          <ion-buttons slot="end">
            <ion-button slot="icon-only" @click="closeModal()"><ion-icon :icon="close"></ion-icon></ion-button>
          </ion-buttons>
        </ion-toolbar>

        <ion-toolbar>
          <ion-item lines="none" fill="outline">
            <ion-button slot="start" size="normal" @click="onAddNewRole()" v-show="searchKeyword">
              <ion-icon slot="start" :icon="add"></ion-icon>
              Add
            </ion-button>

            <ion-input type="text" v-model="searchKeyword" placeholder="Type here if your position is not listed"></ion-input>
          </ion-item>
        </ion-toolbar>
      </ion-grid>
    </ion-header>

    <ion-content>
      <ion-grid fixed>
        <ion-card>
          <ion-list style="max-height: 65vh; overflow-y: scroll">
            <ion-item v-for="role in rolesInSchool" :key="role" button>
              <ion-checkbox label-placement="end" justify="start" :value="role" :checked="isChecked(role)" @ionChange="checkboxChange($event)">
                {{ role }}
              </ion-checkbox>
            </ion-item>
          </ion-list>
        </ion-card>

        <ion-button color="success" expand="block" @click="confirmChanges()">
          Confirm
        </ion-button>
      </ion-grid>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
// vue
import { defineProps, defineComponent, ref, computed, watch, } from 'vue';

// icons
import { add, close } from 'ionicons/icons';

// components
import {
  IonPage,
  IonGrid,
  IonCard,
  IonInput,
  IonIcon,
  IonFooter,
  IonButton,
  IonButtons,
  IonCheckbox,
  IonContent,
  IonHeader,
  IonItem,
  IonList,
  IonTitle,
  IonSearchbar,
  IonToolbar,
  modalController,
  loadingController
} from '@ionic/vue';

import config from '@/config';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';

import TeacherService from '@/services/TeacherService';

const props = defineProps(["hideHeader", "selectedRolesInSchool"]);

const { closeModal, presentToast } = utils();
const store = useStore();
const user = computed(() => store.state.user);
const userSchoolRoles = computed(() => ((user.value.teacher?.schoolRoles || "").split(" , ").filter(r => r)));

const searchKeyword = ref("");

const workingSelectedValues = ref([...(props.selectedRolesInSchool || userSchoolRoles.value)]);
const rolesInSchool = ref([...new Set(workingSelectedValues.value.concat(config.formOptions.rolesInSchool))]);

const isChecked = (value: string) => {
  return workingSelectedValues.value.find((item) => item === value) !== undefined;
};

const confirmChanges = async () => {
  if (props.hideHeader) { // from TeacherRolesModal
    const updateSchoolRoles = workingSelectedValues.value.join(" , ");

    const loading = await loadingController.create({});
    await loading.present();

    // Check remove class roles (set status to removed)
    TeacherService.updateTeacher(user.value, { schoolRoles: updateSchoolRoles });
    store.commit('updateTeacher', { schoolRoles: updateSchoolRoles });
    presentToast("Saved successfully!");

    loading.dismiss();
  }
  else {
    await modalController.dismiss({ workingSelectedValues })
  }
};
const checkboxChange = (ev) => {
  const { checked, value } = ev.detail;
  if (checked){
    workingSelectedValues.value.push(value)
  } else {
    workingSelectedValues.value = workingSelectedValues.value.filter(role => role!= value)
  }
};
const onAddNewRole = () => {
  if (searchKeyword.value) {
    workingSelectedValues.value.unshift(searchKeyword.value.trim());
    searchKeyword.value = '';
    rolesInSchool.value = [...new Set(workingSelectedValues.value.concat(config.formOptions.rolesInSchool))];
  }
};

// Watch (direct page access)
watch(user, () => {
  if (!props.selectedRolesInSchool) workingSelectedValues.value = [...(userSchoolRoles.value)];
})
</script>

<style scoped>
  ion-item {
    --min-height: 36px !important;
  }
</style>