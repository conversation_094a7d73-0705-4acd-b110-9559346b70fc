<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start" v-if="!hideHeader">
          <ion-button slot="icon-only" @click="closeModal()"><ion-icon :icon="arrowBack"></ion-icon></ion-button>
        </ion-buttons>

        <!-- Search Students -->
        <ion-searchbar style="padding-bottom: 0; height: 40px;" mode="ios" v-model="searchKeyword" show-cancel-button="focus"></ion-searchbar>
      </ion-toolbar>

      <ion-toolbar v-if="!hideHeader">
        <!--
        <ion-title style="padding-right: 0" v-if="nominatingEvent">
          <ion-label class="ion-text-wrap">
            <p>Nominating for {{ nominatingEvent.name }}</p>
            <p><i><small>{{ nominatingEvent.date }} [{{ nominatingEvent.mode == 'Online' ? 'Online' : `at ${nominatingEvent.venue || 'Campus'}` }}]</small></i></p>
          </ion-label>
        </ion-title>
        <ion-title style="padding-right: 0" v-else-if="service">
          <ion-label class="ion-text-wrap">
            <p>{{ service.name }}</p>
          </ion-label>
        </ion-title>
        -->
        <ion-title v-if="nominatingEvent || service">
          <ion-label class="ion-text-wrap">
            <p>Our programs use a <b>Real Name system</b> (實名制), with <b>certificates</b> issued jointly by universities and FDMT.</p>
            <p style="margin-top: 10px">After students register (by scanning the QR code at <a target="_blank" :href="service.slideLink">{{ service.slideLink.replace("https://", "") }}</a>), they are listed below for you to:</p>
            <p><ol>
              <li>Verify (and edit here if needed) their mobile and name spelling for certificate issues, etc</li>
              <li>Nominate them by checking the corresponding box</li>
            </ol></p>
            <p>We hope this saves teachers' eForm-creating efforts in pre-nomination data collection.</p>
          </ion-label>
        </ion-title>
        <ion-title style="padding-left: 0" class="ion-text-center" v-else>
          <ion-button size="small" class="no-text-transform" fill="outline" color="success" :href="`https://fdmt.hk/r/${schoolId}`" target="_blank">
            Registered student report
            <ion-icon slot="end" :icon="openOutline"></ion-icon>
          </ion-button>
        </ion-title>
      </ion-toolbar>

      <div v-show="section == 2">
        <!--
          Chips: Filters (Unverified, Grouping by classes)
          -->
        <!--<ion-toolbar v-if="!nominatingEvent">-->
        <ion-toolbar>
          <div style="max-height: 150px; overflow: scroll; padding: 8px">
            <ion-chip :class="{ 'active-tag': selectedFilter == 'all' || searchKeyword }" 
                      @click="selectedFilter = 'all'" :color="selectedFilter == 'all' ? '' : 'medium'" >
              <ion-label>All</ion-label>
            </ion-chip>

            <ion-chip :class="{ 'active-tag': selectedFilter == 'unverified' }" 
                      @click="selectedFilter = 'unverified'" :color="selectedFilter == 'unverified' ? '' : 'medium'"
                      v-if="nominatingEvent == null">
              <ion-label>Unverified</ion-label>
            </ion-chip>

            <span v-else>
              <!--
              <ion-chip :class="{ 'active-tag': selectedFilter == 'selected' }" 
                        @click="selectedFilter = 'selected'" :color="selectedFilter == 'selected' ? '' : 'medium'"
                        v-show="nominees.length > 0">
                <ion-label>Selected ({{nominees.length}})</ion-label>
              </ion-chip>
              -->
              <ion-chip :class="{ 'active-tag': selectedFilter == 'applied' }" 
                        @click="selectedFilter = 'applied'" :color="selectedFilter == 'applied' ? '' : 'medium'">
                <ion-label>Applied</ion-label>
              </ion-chip>
              <!--
              <ion-chip :class="{ 'active-tag': selectedFilter == 'attended' }" 
                        @click="selectedFilter = 'attended'" :color="selectedFilter == 'attended' ? '' : 'medium'">
                <ion-label>Attended</ion-label>
              </ion-chip>
              <ion-chip :class="{ 'active-tag': selectedFilter == 'nominated' }" 
                        @click="selectedFilter = 'nominated'" :color="selectedFilter == 'nominated' ? '' : 'medium'">
                <ion-label>Nominated</ion-label>
              </ion-chip>-->
            </span>

            <ion-chip v-for="studentClass in allClasses(schoolStudents)" :key="studentClass" :class="{ 'active-tag': selectedFilter == studentClass }" 
                      @click="selectedFilter = studentClass || ''" :color="selectedFilter == studentClass ? '' : 'medium'" >
              <ion-label>{{ studentClass }}</ion-label>
            </ion-chip>
          </div>

          <div class="ion-text-center" v-if="!nominatingEvent">
            Are they students at your school?
          </div>

          <ion-buttons slot="end" v-show="nominees.length > 0">
            <ion-button size="small" fill="solid" class="no-text-transform" color="danger" @click="submitNomination('No')"
                        v-if="nominees.every(n => (['Yes', 'Confirmed'].includes(getStudentSessionResp(n))))">
              <ion-icon slot="start" :icon="close"></ion-icon>
              <ion-label>
                <h3>Reject</h3>
              </ion-label>
            </ion-button>
            <ion-button size="small" fill="solid" class="no-text-transform" color="success" @click="submitNomination()">
              <ion-label>
                <h3>Nominate</h3>
              </ion-label>
              <ion-icon slot="end" :icon="arrowForward"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-toolbar>

    <!--<ion-item color="light" @click="openNomineeFormModal(schoolStudents)" button detail v-if="nominatingEvent">
          <ion-icon slot="start" :icon="helpCircleOutline" style="margin: 0 12px 0 0;"></ion-icon>
          <ion-label><h2>My students are not listed</h2></ion-label>
        </ion-item>-->
      </div>
<!--
      <ion-item lines="full" v-if="nominatingEvent?.anchorEventId == 'work-workshop-nomination'">
        <ion-text :color="getTotalNumNominees() >= maxWorkshopNominees ? 'success' : 'medium'">
          <span>{{ getTotalNumNominees() }} nominated</span>
        </ion-text>
        <ion-buttons slot="end">
          <ion-button fill="solid" class="no-text-transform" color="success" @click="submitNomination()" v-show="nominees.length > 0">
            <ion-label>
              <h2>Nominate</h2>
            </ion-label>
            <ion-icon slot="end" :icon="arrowForward"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-item>
      -->
    </ion-header>
    
    <ion-content>
      <div v-show="section == 1">
        <div class="spin ion-text-center">
          <p><img width="200" :src="getQRCodeUrl(`https://ab.fdmt.hk/s/${user.schoolId}`)" /></p>
          <p>ab.fdmt.hk/s/{{ user.schoolId }}</p>
          <ion-button size="small" color="success" @click="sendLinkToMyWhatsApp(`https://ab.fdmt.hk/s/${user.schoolId}`, user.phone, user.waGroupId)">
            Send to my WhatsApp
          </ion-button>
        </div>
      </div>

      <!--
        Verify students / student list / nominate students
        -->
      <ion-list v-show="section == 2">
        <!--
        <ion-item lines="full" button detail target="_blank" :href="service.slideLink">
          <ion-icon slot="start" :icon="informationCircleOutline" style="margin-inline-end: 16px"></ion-icon>
          <ion-label class="ion-text-wrap">
            <p><b>Your students will appear here for your verification and nomination after they register with the QR code at {{ service.slideLink }}</b></p>
          </ion-label>
        </ion-item>
        -->

        <div v-if="nominatingEvent">
          <ion-item lines="full" v-for="student in filteredSchoolStudents().slice(0, numOfVisibleItems)" :key="student.phone">
            <!-- Nominate Status / Checkbox Button -->
            <ion-icon slot="start" :icon="checkmark" style="margin-inline-end: 16px"
                      v-if="['Yes', 'Confirmed', 'Attended'].includes(getStudentSessionResp(student)) && getStudentSessionResp(student, 'nominatedBy')"></ion-icon>
            <ion-checkbox
              v-else
              slot="start"
              @update:modelValue="onCheckNominee($event, student)"
              :modelValue="nominees.find(n => n.id == student.id) != null"
              style="margin-inline-end: 16px"
              :disabled="isDisableNomination(student)"
            >
            <!--:disabled="service.status == 'Inactive' || nominees.find(n => n.id == student.id) == null && getTotalNumNominees() >= maxWorkshopNominees"-->
            </ion-checkbox>

            <!-- Student Info -->
            <ion-label class="ion-text-wrap">
              <h3>{{ student.class }}{{ formatStudentNumber(student.studentNumber) }} {{ student.fullName }} {{ student.chineseName }}{{ student.preferredName ? ` (${student.preferredName})` : '' }}</h3>
              <p>DSE {{student.yearDSE}} | {{ student.phone }}<span v-if="student.email"> | {{ student.email }}</span></p>
              <span v-if="student.isNewStudent"><ion-badge color="secondary">New</ion-badge>&nbsp;</span>

              <!-- Attended before (e.g. BBAWork workshop 2024 Jun) -->
              <span v-if="isStudentAttendedBefore(student)"><ion-badge color="success">Attended before</ion-badge>&nbsp;</span>

              <!-- Session nomination / apply / attend status -->
              <span v-if="getStudentSessionResp(student)">
                <span v-if="getStudentSessionResp(student, 'nominatedBy')">
                  <ion-badge color="danger" v-if="getStudentSessionResp(student) == 'No'">Rejected</ion-badge>
                  <ion-badge color="primary" v-else>Nominated</ion-badge>&nbsp;
                </span>
                <span v-if="['Attended', 'Yes', 'Confirmed'].includes(getStudentSessionResp(student))"><ion-badge color="primary">Applied</ion-badge>&nbsp;</span>
                <span v-if="['Attended'].includes(getStudentSessionResp(student))"><ion-badge color="success">Attended</ion-badge>&nbsp;</span>
                <span v-if="['Unsubscribed'].includes(getStudentSessionResp(student))"><ion-badge color="danger">Unsubscribed</ion-badge></span>
                <span v-if="['No'].includes(getStudentSessionResp(student))"><ion-badge color="danger">Withdrawn</ion-badge></span>
              </span>

              <!-- Interview status -->
              <span v-if="nominatingEvent.needInterview && ['Attended', 'Yes', 'Confirmed'].includes(getStudentSessionResp(student))">
                <span v-if="getInterviewSessionResp(student)">
                  <ion-badge color="success" v-if="getInterviewSessionResp(student, 'interviewStatus').includes('Accepted') ">Interview passed</ion-badge>&nbsp;
                  <ion-badge color="danger" v-if="getInterviewSessionResp(student, 'interviewStatus').includes('Rejected') ">Interview failed</ion-badge>&nbsp;
                  <ion-badge color="tertiary" v-else>{{ getInterviewSessionResp(student, 'statusText') }}</ion-badge>
                </span>
                <span v-else>
                  <ion-badge color="medium">Interview to be confirmed</ion-badge>
                </span>
              </span>
            </ion-label>

            <!-- Time preferences 
            <ion-select interface="popover" v-model="student.timePreference" v-if="service.nature == 'Workshop' && nominees.find(n => n.id == student.id) != null">
              <ion-select-option value="Morning session">Morning session</ion-select-option>
              <ion-select-option value="Afternoon session">Afternoon session</ion-select-option>
              <ion-select-option value="No preference">No preference</ion-select-option>
            </ion-select>-->

            <!-- Edit student data -->
            <ion-buttons slot="end">
              <ion-button size="small" @click="openUserProfileFormModal(student)">
                <ion-icon slot="icon-only" :icon="pencil"></ion-icon>
              </ion-button>
            </ion-buttons>
            <!--<div><small>Incorrect info?</small></div>-->
            
          </ion-item>
        </div>

        <div v-else>
          <ion-item lines="full" v-for="student in filteredSchoolStudents().slice(0, numOfVisibleItems)" :key="student.phone">
            <ion-label class="ion-text-wrap">
              <h3>{{ student.class }}{{ formatStudentNumber(student.studentNumber) }} {{ student.chineseName }}</h3>
              <p>{{ student.fullName }}{{ student.preferredName ? ` (${student.preferredName})` : '' }}</p>
              <p>{{ student.phone }}<span v-if="student.email"> | {{ student.email }}</span></p>
            </ion-label>

            <!-- Verification Buttons -->
            <ion-chip @click="updateUserVerificationResult('Yes', student)" :outline="student.verificationResult != 'Yes'" v-if="nominatingEvent == null">
              <ion-label>Yes</ion-label>
            </ion-chip>
            <ion-chip @click="updateUserVerificationResult('No', student)" :outline="student.verificationResult != 'No'" v-if="nominatingEvent == null">
              <ion-label>No</ion-label>
            </ion-chip>
          </ion-item>
        </div>

        <ion-infinite-scroll
          @ionInfinite="loadData($event)" 
          threshold="100px" 
          id="infinite-scroll"
        >
          <ion-infinite-scroll-content
            loading-spinner="bubbles"
            loading-text="Loading...">
          </ion-infinite-scroll-content>
        </ion-infinite-scroll>
      </ion-list>
    </ion-content>


    <!--
      Footer Nominate Button (Batch Nominate)
    <ion-footer>
      <ion-toolbar class="ion-text-center" v-show="nominees.length > 0">
        <ion-grid style="padding-top: 0" fixed>
          <ion-button class="no-text-transform" color="success" expand="block" @click="submitNomination()">
            <ion-icon slot="end" :icon="arrowForward"></ion-icon>
            <ion-label>
              <h2>Nominate {{ nominees.length }} student{{ nominees.length > 1 ? 's' : '' }}</h2>
            </ion-label>
          </ion-button>
        </ion-grid>
      </ion-toolbar>
    </ion-footer>
    -->
  </ion-page>
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, watch, reactive, onMounted } from 'vue';

// icons
import { add, close, checkmark, arrowUp,  arrowForward, arrowBack, trashOutline, sendOutline, helpCircleOutline,
        createOutline, openOutline, chevronForwardOutline, informationCircleOutline, personAddOutline, pencil, } from 'ionicons/icons';

// components
import { IonPage, IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
        IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonBadge,
        IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption,
        IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid, IonCheckbox,
        IonInfiniteScroll, IonInfiniteScrollContent, IonReorder, IonReorderGroup,
        IonFab, IonFabButton,
        loadingController, modalController, alertController, } from '@ionic/vue';
import NomineeFormModal from '@/components/teacher/NomineeFormModal.vue';
import RegistrationQRCodeModal from '@/components/modals/RegistrationQRCodeModal.vue';
import UserProfileFormModal from '@/components/modals/UserProfileFormModal.vue';

// composables / services
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';

// services
import TeacherService from '@/services/TeacherService';

// types
import { Session, User } from '@/types';

export default defineComponent({
  name: 'StudentModal',
  props: ["nominatingEvent", "schoolId", "service", "initialSection", "hideHeader", "maxYearDSE"],
  components: { IonPage, IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
                IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonBadge,
                IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption,
                IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid, IonCheckbox,
                IonInfiniteScroll, IonInfiniteScrollContent, IonReorder, IonReorderGroup,
                IonFab, IonFabButton, },
  setup(props) {
    // methods or filters
    const store = useStore();
    const { openModal, presentPrompt, presentToast, infiniteScrollLoadData, formatStudentNumber,
            getQRCodeUrl, sendLinkToMyWhatsApp, getCurrentIntakeYear, formatDate, } = utils();
    const { t } = useI18n();

    const user = computed(() => store.state.user);
    //const maxWorkshopNominees = 5; // Only for interactive Work workshop (anchor event ID: "work-workshop-2") 5 for each session (so total is 10)
    //const maxWorkshopNominees = 30; // Only for interactive Work workshop (anchor event ID: "work-workshop-2") 5 for each session (so total is 10)
    //const maxWorkshopNominees = 4; // 20241015: tenative for BBAWork workshop
    const maxWorkshopNominees = 999; // 20241015: tenative for BBAWork workshop
    const nominees = ref<User[]>([]);
    const schoolStudents = computed<User[]>(() => store.state.schoolStudents);
    const allSessions = computed<Session[]>(() => store.state.sessions);
    const searchKeyword = ref("");
    const selectedFilter = ref("all");
    const section = ref(props.initialSection || 2);

    const numOfVisibleItems = ref(20);
    const loadData = (ev: any) => {
      infiniteScrollLoadData(ev, numOfVisibleItems, schoolStudents.value, 100);
    }

    const closeModal = async () => (await modalController.dismiss({}));

    const getStudentSessionResp = (s: User, targetKey = 'response') => {
      if (props.nominatingEvent) {
        return (s.sessionResponses?.find(r => r.sessionId == props.nominatingEvent.id) || {})[targetKey];
      }
      return null;
    }
    const onCheckNominee = (checked: any, student: any) => {
      const idx = nominees.value.findIndex(n => n.id == student.id);
      if (checked && idx == -1) {
        //student.timePreference = (props.service?.nature == "Workshop" ? "No preference" : "");
        student.timePreference = "";
        nominees.value.push(student);
      }
      else if (!checked && idx != -1) nominees.value.splice(idx, 1);
    }
    const getTotalNumNominees = () => {
      const nominatedStudents = schoolStudents.value.filter(student => (['Yes', 'Confirmed', 'Attended'].includes(getStudentSessionResp(student)) && getStudentSessionResp(student, 'nominatedBy')));
      return nominatedStudents.length + nominees.value.length;
    }
    const isStudentAttendedBefore = (student) => {
    const { nominatingEvent } = props;
    return allSessions.value.find(s => {
      return s.id != nominatingEvent.id && s.anchorEventId == nominatingEvent.anchorEventId && s.clientId == nominatingEvent.clientId
            && student.sessionResponses.find(sr => sr.sessionId == s.id && sr.attended == 'Yes');
    }) != null;
    }

    onMounted(() => {
      if (props.nominatingEvent) section.value = 2;
    })

    // return variables & methods to be used in template HTML
    return {
      // icons
      add, close, checkmark, arrowUp, arrowForward, arrowBack,
      trashOutline, sendOutline, helpCircleOutline, createOutline, openOutline,
      chevronForwardOutline, informationCircleOutline, personAddOutline, pencil,

      // variables
      user,
      numOfVisibleItems,
      schoolStudents, nominees,
      searchKeyword,
      selectedFilter,
      section,

      // methods
      t, getQRCodeUrl, sendLinkToMyWhatsApp,
      loadData, closeModal,
      formatStudentNumber,
      allClasses: (schoolStudents: any) => {
        return [...new Set(schoolStudents.map(s => s.class?.replace(/[^a-z0-9]/gi, "").toLowerCase()))]
                .filter(c => !!c).map((c: any) => c?.toUpperCase()).sort();
      },

      openRegQRCodeModal: async (event) => {
        return await openModal(RegistrationQRCodeModal, { event, targetTo: 'student', serviceId: props.service?.id })
      },
      
      // Verifying Students
      updateUserVerificationResult: (result: any, user: User) => {
        if (user.verificationResult == result) result = null; // toggle
        
        TeacherService.updateUserVerificationResult(result, user.id); // update DB

        user.verificationResult = result; // update store
      },

      // Show list of students based on search keyword & filters
      filteredSchoolStudents: () => {
        let filteredStudents: User[] = schoolStudents.value.filter(s => s.yearDSE >= getCurrentIntakeYear());
        if (props.maxYearDSE) {
          filteredStudents = filteredStudents.filter((s: User) => (s.yearDSE && s.yearDSE <= props.maxYearDSE));
        }
        if (searchKeyword.value) {
          filteredStudents = filteredStudents.filter((s: User) => {
            const searchInWords = `${s.fullName} ${s.chineseName} ${s.preferredName} ${s.phone} ${s.email} ${s.class}${s.studentNumber}`.toLowerCase();
            const cleanedKeyword = searchKeyword.value.toLowerCase();
            return searchInWords.includes(cleanedKeyword);
          });
        } else if (selectedFilter.value != 'all') {
          filteredStudents = filteredStudents.filter((s: User) => {
            if (selectedFilter.value == 'unverified') return !s.verificationResult;
            if (selectedFilter.value == 'applied') return ['Attended', 'Yes'].includes(getStudentSessionResp(s));
            if (selectedFilter.value == 'attended') return ['Attended'].includes(getStudentSessionResp(s));
            if (selectedFilter.value == 'nominated') return getStudentSessionResp(s, 'nominatedBy');
            if (selectedFilter.value == 'selected') return nominees.value.find(n => n.id == s.id);
            return s.class == selectedFilter.value;
          });
        }
        const sortedStudents = filteredStudents.slice().sort((a: any, b: any) => {
          const textA = a.class?.toUpperCase(), textB = b.class?.toUpperCase();
          //if (getStudentSessionResp(a, 'nominatedBy') || ['Attended', 'Yes'].includes(getStudentSessionResp(a))) return -1; // show applied students first
          if (textA == "") return 1;
          if (textB == "") return -1;
          return textA < textB ? -1 : (textA > textB ? 1 : (a.studentNumber - b.studentNumber)); // put to back if empty
        });
        const isStudentApplied = (s) => (getStudentSessionResp(s, 'nominatedBy') || ['Attended', 'Yes'].includes(getStudentSessionResp(s)));
        
        if (props.service.status == 'Inactive') {
          return sortedStudents.filter(s => isStudentApplied(s)); // only show applied students
        }
        return [
          ...sortedStudents.filter(s => isStudentApplied(s)), // show applied students first
          ...sortedStudents.filter(s => !isStudentApplied(s)),
          //...sortedStudents.filter(s => s.isNewStudent), // show new students first
          //...sortedStudents.filter(s => !s.isNewStudent)
        ];
      },

      // Nomination
      getStudentSessionResp,
      onCheckNominee,
      deleteNewStudent: (schoolStudents, studentId) => {
        const studentIdx = schoolStudents.findIndex(s => s.id == studentId);
        if (studentIdx != -1) schoolStudents.splice(studentIdx, 1);
        const nomineeIdx = nominees.value.findIndex(n => n.id == studentId);
        if (nomineeIdx != -1) nominees.value.splice(nomineeIdx, 1);
      },
      openNomineeFormModal: async (schoolStudents) => {
        const modal = await modalController.create({
          component: NomineeFormModal,
          componentProps: {}
        });
        modal.onDidDismiss().then(({ data }) => {
          if (data && data.nominees) {
            for (const n of data.nominees) {
              if (n.phone) {
                const existingStud = schoolStudents.find(s => s.phone == n.phone);
                if (existingStud) {
                  // Existing student: already in DB
                  onCheckNominee(true, existingStud);
                } else {
                  // New student: not yet registered
                  n.isNewStudent = true;
                  schoolStudents.push(n);
                  nominees.value.push(n);
                }
              }
            }
          }
        });
        return modal.present();
      },
      submitNomination: (targetResponse: any = 'Yes') => {
        const { id: targetSessionId } = props.nominatingEvent;
        console.log(targetSessionId);
        const nomineeDescriptions = nominees.value.map(n => `${n.class}${formatStudentNumber(n.studentNumber)} ${n.fullName} (${n.phone})`);
        let msgHeader = `Confirm nomination?`;
        if (targetResponse == 'No') msgHeader = `Confirm rejection of student applications?`;
        presentPrompt(`${msgHeader}<br /><br />${nomineeDescriptions.sort().join("<br />")}`, async () => {
          const loading = await loadingController.create({ duration: 60000 });
          await loading.present();

          // Insert nomination record
          const reqNominees = nominees.value.map(({ id, fullName, phone, "class": studentClass, isNewStudent, timePreference, }) => {
            return { id, fullName, phone, "class": studentClass, isNewStudent, schoolId: store.state.user.schoolId, timePreference, }; // minimize the payload
          });
          await TeacherService.insertNominationRecord(reqNominees, [targetSessionId], targetResponse);

          // Update corresponding schoolStudent event response (nominatedBy)
          for (const n of nominees.value) {
            const sessionResp = n.sessionResponses?.find(resp => resp.sessionId == targetSessionId);
            if (sessionResp) {
              // update nominator of existing response
              sessionResp.nominatedBy = store.state.user.id;
              sessionResp.response = targetResponse;
              sessionResp.confirmed = targetResponse;
            } else {
              // insert new session response
              (n.sessionResponses = n.sessionResponses || []).push({
                sessionId: targetSessionId,
                userId: n.id,
                response: targetResponse,
                confirmed: targetResponse,
                nominatedBy: store.state.user.id
              });
            }
          }

          // Upsert school students
          store.commit('upsertSchoolStudents', nominees.value);
          nominees.value = []; // clear handled nominees

          // Present toast msg (ack)
          presentToast(`Thank you for your ${targetResponse == 'Yes' ? 'nomination' : 'response'}.`);

          alertController.dismiss();
          loading.dismiss();
        })
      },

      // Checking for Work workshop (nominee number)
      maxWorkshopNominees, getTotalNumNominees,

      // Edit student info
      openUserProfileFormModal: async (targetUser) => {
        await openModal(UserProfileFormModal, { targetUser });
      },

      // Interview
      getInterviewSessionResp: (student: User, targetKey: any = null) => {
        const { clientId } = props.nominatingEvent; // Interview session is by client
        for (const resp of student.sessionResponses || []) {
          const session: Session = store.getters.getSessionById(resp.sessionId);
          if (resp.response != 'No' && session.anchorEventId == "c98cc077d" && session.clientId == clientId) {
            if (targetKey == 'statusText') {
              if (resp.response == 'Confirmed') return `Confirmed to attend ${formatDate(session.date, 'MMM D')} interview`;
              if (resp.response == 'Attended') return `Attended ${formatDate(session.date, 'MMM D')} interview`;
              return `Invited to ${formatDate(session.date, 'MMM D')} interview`;
            }
            return targetKey ? resp[targetKey] || "" : resp;
          }
        }
        return null;
      },

      // Check whether the student can be Nominated
      isStudentAttendedBefore,
      isDisableNomination: (student) => {
        const { nominatingEvent } = props;
        if (isStudentAttendedBefore(student)) return true; // e.g. BBAWork workshop, not allow duplicate application / nomination
        return new Date() > new Date(nominatingEvent.startTimeStr) || nominees.value.find(n => n.id == student.id) == null && getTotalNumNominees() >= maxWorkshopNominees;
      }
    }
  },
});
</script>

<style scoped>
  ion-toolbar {
    --min-height: 48px;
  }
  ion-title {
    font-weight: normal;
    padding-right: 8px;
    padding-top: 5px;
    padding-bottom: 5px;
  }
  ion-title p {
    font-size: 14px;
  }
</style>