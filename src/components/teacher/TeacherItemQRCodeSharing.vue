<template>
  <!--
    Share with other colleagues
    -->
  <ion-card class="outer-card" style="margin: 0 1px">
    <ion-accordion-group value="Sharing">
      <ion-accordion style="--min-height: 24px" value="Sharing">
        <ion-item lines="none" slot="header" style="--min-height: 24px">
          <ion-label><b>QR code sharing</b></ion-label>
        </ion-item>
        <div class="ion-text-center" slot="content">
          <ion-accordion-group>
            <!-- With colleagues / students -->
            <ion-accordion style="--min-height: 24px">
              <ion-item lines="full" slot="header">
                <ion-label>
                  <p><b>with my colleagues/students</b></p>
                </ion-label>
              </ion-item>

              <ion-list class="ion-text-center" slot="content">
                <p><img style="width: 300px" :src="getQRCodeUrl(`https://ab.fdmt.hk/r/${user.schoolId}`)" /></p>
                <p>
                  ab.fdmt.hk/r/{{user.schoolId}}<br />
                  <ion-button fill="clear" color="success" size="small" @click="copyText(`https://ab.fdmt.hk/r/${user.schoolId}`)">
                    <small>Copy Link</small>
                  </ion-button>
                </p><!-- /r: generic ; /t: prefill teacher -->
                <ion-button size="small" color="success" @click="sendLinkToMyWhatsApp(`https://ab.fdmt.hk/r/${user.schoolId}`, user.phone, user.waGroupId)">
                  Send to my WhatsApp
                </ion-button>
              </ion-list>
            </ion-accordion>

            <!-- With other schools -->
            <ion-accordion style="--min-height: 24px">
              <ion-item lines="full" slot="header">
                <ion-label>
                  <p><b>with other schools</b></p>
                </ion-label>
              </ion-item>

              <ion-list class="ion-text-center" slot="content">
                <p><img style="width: 300px" :src="getQRCodeUrl(`https://ab.fdmt.hk/r`)" /></p>
                <p>
                  ab.fdmt.hk/r<br />
                  <ion-button fill="clear" color="success" size="small" @click="copyText(`https://ab.fdmt.hk/r`)">
                    <small>Copy Link</small>
                  </ion-button>
                </p>
                <ion-button size="small" color="success" @click="sendLinkToMyWhatsApp(`https://ab.fdmt.hk/r`, user.phone, user.waGroupId)">
                  Send to my WhatsApp
                </ion-button>
              </ion-list>
            </ion-accordion>
          </ion-accordion-group>
        </div>
      </ion-accordion>
    </ion-accordion-group>
  </ion-card>
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, watch, reactive } from 'vue';

// icons
import { add, close, arrowBack, checkmarkCircle, alertCircle, ellipsisHorizontal, ellipsisVertical, pencil, } from 'ionicons/icons';

// components
import { IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonList, IonCard, IonAccordion, IonAccordionGroup, IonText, } from '@ionic/vue';

// composables
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';

export default {
  props: [],
  components: {
    IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonList,
    IonCard, IonAccordion, IonAccordionGroup, IonText, },
  setup(props) {
    // methods or filters
    const store = useStore();
    const { t } = useI18n();
    const { getQRCodeUrl, sendLinkToMyWhatsApp, copyText, } = utils();

    const user = computed(() => store.state.user);

    // return variables & methods to be used in template HTML
    return {
      // icons
      add, close, arrowBack,
      checkmarkCircle, alertCircle, ellipsisHorizontal, ellipsisVertical, pencil,

      // variables
      user,

      // methods
      sendLinkToMyWhatsApp,
      getQRCodeUrl,
      copyText,
    }
  },
};
</script>

<style scoped>
  ion-item {
    --min-height: 32px;
  }
</style>
