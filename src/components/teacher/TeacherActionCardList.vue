<template>
  <div class="spin" v-if="loadingData">
    <ion-spinner></ion-spinner>
  </div>

  <div v-else>
    <ion-toolbar class="sticky-toolbar">
      <ion-segment class="view-type-segment" mode="ios" v-model="selectedViews.type" scrollable>
        <ion-segment-button value="Sharing" class="short-segment-btn ion-text-center">
          <ion-icon size="small" :icon="qrCodeOutline"></ion-icon>
        </ion-segment-button>
        <ion-segment-button value="Checklist">
          <ion-label><b>Checklist</b></ion-label>
        </ion-segment-button>
        <ion-segment-button value="AchieveJUPAS">
          <ion-label style="line-height: 1.5" class="no-text-transform"><b>AchieveJUPAS</b><br />2.0 (beta)</ion-label>
        </ion-segment-button>
        <ion-segment-button value="AchieveSLP">
          <ion-label style="line-height: 1.5" class="no-text-transform"><b>AchieveSLP</b><br />events</ion-label>
        </ion-segment-button>
        <ion-segment-button value="school-funded">
          <ion-label style="line-height: 1.5" class="no-text-transform">School-funded</ion-label>
        </ion-segment-button>
      </ion-segment>

      <!-- TMP: Feedback Form (14 Mar) 
      <ion-item v-if="selectedViews.type == 'AchieveJUPAS' &&achievejupasSurveyTask" lines="full" button detail @click.stop="openFormQuestionModal(achievejupasSurveyTask)" :color="isSubmittedTaskForm(achievejupasSurveyTask) ? 'success' : 'danger'">
        <ion-spinner slot="start" v-if="loadingUserFormResponses"></ion-spinner>
        <ion-icon slot="start" v-if="!loadingUserFormResponses" :icon="isSubmittedTaskForm(achievejupasSurveyTask) ? checkmarkCircle : alertCircle"></ion-icon>
        <ion-label>
          <h3><b>{{ achievejupasSurveyTask.title }}</b></h3>
          <p>{{ getTaskResponses(achievejupasSurveyTask.id).length }}/{{ getFilteredFormQuestions(achievejupasSurveyTask).length }} completed</p>
        </ion-label>
      </ion-item>-->

      <div v-if="selectedViews.type == 'AchieveSLP'">
        <!-- TMP: Feedback Form (14 Mar) 
        <div v-if="achieveslpSurveyTask">
          <ion-item lines="full" button detail @click.stop="openFormQuestionModal(achieveslpSurveyTask)" :color="isSubmittedTaskForm(achieveslpSurveyTask) ? 'success' : 'danger'">
            <ion-spinner slot="start" v-if="loadingUserFormResponses"></ion-spinner>
            <ion-icon slot="start" v-if="!loadingUserFormResponses" :icon="isSubmittedTaskForm(achieveslpSurveyTask) ? checkmarkCircle : alertCircle"></ion-icon>
            <ion-label>
              <h3><b>{{ achieveslpSurveyTask.title }}</b></h3>
              <p>{{ getTaskResponses(achieveslpSurveyTask.id).length }}/{{ getFilteredFormQuestions(achieveslpSurveyTask).length }} completed</p>
            </ion-label>
          </ion-item>
        </div>-->

        <!-- Nested type selection -->
        <ion-segment mode="ios" v-model="selectedViews.subType">
          <ion-segment-button value="resources">
            <ion-label>School-based</ion-label>
          </ion-segment-button>
          <ion-segment-button value="nomination">
            <ion-label>Joint-school</ion-label>
          </ion-segment-button>
        </ion-segment>

        <!-- AchieveSLP Tool Preview -->
        <ion-toolbar style="--min-height: 27px">
          <ion-item lines="full" button detail color="fdmtred" @click="openSLPModal(true)">
            <ion-label class="ion-text-wrap ion-text-center">
              <p style="margin: 0; color: #fff">AchieveSLP preview</p>
            </ion-label>
          </ion-item>
        </ion-toolbar>
      </div>
    </ion-toolbar>

    <!-- QR Code Sharing -->
    <div v-if="selectedViews.type == 'Sharing'">
      <TeacherItemQRCodeSharing></TeacherItemQRCodeSharing>
    </div>

    <!--
      Engagement Checklist e.g. School Registration (readiness)
      -->
    <div v-if="selectedViews.type == 'Checklist'">
      <TeacherItemSchoolReadiness></TeacherItemSchoolReadiness>
    </div>

    <!--
      AchieveJUPAS Tool
      -->
    <div v-if="selectedViews.type == 'AchieveJUPAS'" class="achieve-jupas-wrapper">
      <TeacherAchieveJUPASModal :isDemo="true"></TeacherAchieveJUPASModal>
    </div>
    
    <div v-if="selectedViews.type === 'AchieveSLP'">
      <!-- School-based -->
      <div v-if="selectedViews.subType === 'resources'">
        <!-- 30-minute talk -->
        <img src="@/assets/banner_30min.jpg" style="width: 100%; margin-bottom: -3px">
        <action-accordion-group :expandAll="true" :no-header="true">
          <div class="ion-text-center">
            <service-item v-for="service in getServicesByNature('Talk at your school')" :key="service.id" :service="service"></service-item>
          </div>
        </action-accordion-group>

        <!-- 2hr university visit -->
        <img src="@/assets/banner_2hr_visit.jpg" style="width: 100%; margin-bottom: -3px">
        <action-accordion-group :expandAll="true" :no-header="true">
          <div class="ion-text-center">
            <service-item v-for="service in getServicesByNature('Visit')" :key="service.id" :service="service"></service-item>
          </div>
        </action-accordion-group>
      </div>

      <!-- Joint-school mainly 4-hr workshop (for nominations) -->
      <div v-else-if="selectedViews.subType === 'nomination'">
      <!--
        20250206: temporary hide / close it due to no sessions?
        <action-accordion-group :expandAll="true">
          <template v-slot:header>
            <span><b>Video about graduation career</b></span>
          </template>
          <div class="ion-padding-start ion-text-center">
            <service-item v-for="service in getServicesByNature('Video')" :key="service.id" :service="service"></service-item>
          </div>
        </action-accordion-group>-->

        <!-- Banner -->
        <img src="@/assets/banner_nomination.jpg" style="width: 100%; margin-bottom: -3px">

        <action-accordion-group :expandAll="true" :no-header="true">
          <!--<template v-slot:header>
            <span><b>4-hr university workshop: broadcast video in class and nominate</b></span>
          </template>-->
          <div class="ion-text-center">
            <service-item v-for="service in getServicesByNature('Workshop')" :key="service.id" :service="service"></service-item>
          </div>
        </action-accordion-group>
      </div>
    </div>

    <!-- Teacher Development -->
    <!-- <home-action-item v-show="hasTeacherEvents" title="Professional development" extraNote1="Apply" :isLesson="true" @click="openTeacherEventsModal('c0bcf00d0')"
                      :hideIcon="true"></home-action-item> -->

    <div v-if="selectedViews.type == 'school-funded'">
      <!-- Criteria -->
      <ion-item v-if="!isAB4User">
        <ion-label class="ion-text-wrap" style="line-height: 1">
          <i>School-funded services for F4 & F3 only available upon school request after 3+ years of university-funded service</i>
        </ion-label>
      </ion-item>

      <!-- Bluebird Seed -->
      <action-accordion-group :expandAll="true">
        <template v-slot:header>
          <span><b>Bluebird Seed</b></span>
        </template>
        <div v-if="user.schoolId == 'htc'">
          <!-- Video & Regulation -->
          <home-action-item :isLesson="true" title="Videos and regulations" @click="openServiceModal('bluebird-seed', null, { hideApplyBtn: true })"></home-action-item>

          <!-- List of Bluebird Seed (students' project logs & profile (UserDetails.vue)) -->
          <action-accordion-group :expandAll="false" :header-color="'none'" :no-padding="true">
            <template v-slot:header>
              <ion-item lines="full">
                <ion-label><b>5D22 Ng Wing Sum Chloe</b></ion-label>
                <ion-buttons slot="end">
                  <ion-button class="no-text-transform" size="small" @click.stop="openUserDetails()">Profile</ion-button>
                </ion-buttons>
              </ion-item>
            </template>

            <!-- TODO: direct link for nomination? (like Work events) -->
            <ion-item lines="full" style="padding-left: 32px">
              <ion-icon slot="start" :icon="checkmarkCircle" color="success"></ion-icon>
              <ion-label>
                <b>Nominator: Ms. Lillian Ho</b>
              </ion-label>
            </ion-item>
            
            <ion-item lines="full" style="padding-left: 32px">
              <ion-label>
                <b>Co-supervisors: Ms. Lilian Ho (HTC), Brandon Chan (FDMT)</b>
              </ion-label>
            </ion-item>

            <action-accordion-group style="padding-left: 20px" :expandAll="false" :header-color="'none'" :no-padding="true">
              <template v-slot:header>
                <ion-item lines="full">
                  <ion-label><b>Project logs</b></ion-label>
                </ion-item>
              </template>

              <!-- Related Project Logs (technical tasks & expected completion dates) -->
              <ion-item button lines="full" style="padding-left: 32px">
                <ion-label class="ion-text-wrap">
                  <b>AchieveJUPAS UI Updates</b>
                  <p>Show version history of program choices</p>
                  <p>Expected completion: 2025-04-30</p>
                </ion-label>
                <ion-badge color="medium" slot="end">Planned</ion-badge>
              </ion-item>
            </action-accordion-group>
          </action-accordion-group> 
        </div>
      </action-accordion-group>

      <!-- F4: AB4 (school-funded) -->
      <action-accordion-group :expandAll="true">
        <template v-slot:header>
          <!--<span><b>AB4</b></span>-->
          <span><b>Form 4</b></span>
        </template>
        <ion-item lines="full" button detail @click="openAB4CustomizeContentModal()" :disabled="!user.isAdmin && !isAB4User">
          <ion-label class="ion-text-wrap">
            <p>Pre-session customization for your school</p>
          </ion-label>
        </ion-item>
        <ion-item lines="full" button detail @click="openABEventsModal('abs-session1')" :disabled="!isAB4User">
          <ion-label class="ion-text-wrap">
            <p>[80+ min] Lesson 1</p>
          </ion-label>
        </ion-item>
        <ion-item lines="full" button detail @click="openABEventsModal('abs-session2')" :disabled="!isAB4User">
          <ion-label class="ion-text-wrap">
            <p>[40+ min] Lesson 2</p>
          </ion-label>
        </ion-item>
        <ion-item lines="full" button detail disabled>
          <!-- TODO: input student worries -->
          <ion-icon size="small" color="danger" :icon="alertCircle"></ion-icon>
          <ion-label class="ion-text-wrap">Manage student worries</ion-label>
        </ion-item>
      </action-accordion-group>

      <!--
        F3: Subject choices for senior form (school-funded AB3)
        -->
      <action-accordion-group :expandAll="true">
        <template v-slot:header>
          <!--<span><b>AB3</b></span>-->
          <span><b>AchieveElective</b></span>
        </template>
        <ion-item lines="full" button detail @click="openABEventsModal('ab3-session1')" :disabled="!isAB4User">
          <ion-label class="ion-text-wrap">
            <p>[45+ min] Lesson 1</p>
          </ion-label>
        </ion-item>
      </action-accordion-group>
    </div>
  </div>
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, watch, reactive } from 'vue';

// icons
import { add, close, arrowBack, checkmarkCircle, alertCircle, ellipsisHorizontal, ellipsisVertical,
        pencil, qrCodeOutline, } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol,
        IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonSpinner, IonList,
        IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonPopover, IonBadge,
        IonAccordion, IonAccordionGroup, IonText, IonChip, IonProgressBar, IonSegment, IonSegmentButton,
        loadingController, modalController, alertController, } from '@ionic/vue';
import SchoolTeacherModal from '@/components/teacher/SchoolTeacherModal.vue';
import StudentModal from '@/components/teacher/SchoolStudentModal.vue';
import ServiceModal from '@/components/modals/ServiceModal.vue';
import ABEventsModal from '@/components/secondary/events/ABEventsModal.vue';
import AB4CustomizeContentModal from '@/components/teacher/AB4CustomizeContentModal.vue';
import TeacherResponseQuestions from '@/components/teacher/TeacherResponseQuestions.vue';
import TeacherEventsModal from '@/components/teacher/TeacherEventsModal.vue';
import TeacherAchieveJUPASModal from '@/components/achievejupas/TeacherAchieveJUPASModal.vue';
import ServiceItem from '@/components/shared/ServiceItem.vue';
import ServiceAccordionGroup from '@/components/shared/ServiceAccordionGroup.vue';
import ActionAccordionGroup from '@/components/shared/ActionAccordionGroup.vue';
import TeacherItemQRCodeSharing from '@/components/teacher/TeacherItemQRCodeSharing.vue';
import TeacherItemSchoolReadiness from '@/components/teacher/TeacherItemSchoolReadiness.vue';
import FormQuestionModal from '@/components/modals/FormQuestionModal.vue';
import SLPModal from '@/components/secondary/ABSLPModal.vue';
import UserDetailsModal from '@/components/modals/UserDetailsModal.vue';

// composables
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';
import { AnchorEventTask, Service, Session, TeacherResponse, User } from '@/types';
import config from '@/config';

export default {
  props: ["prefilledViewType"],
  components: { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol,
    IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonSpinner, IonList,
    IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonPopover, IonBadge,
    IonAccordion, IonAccordionGroup, IonText, IonChip, IonProgressBar, IonSegment, IonSegmentButton,
    TeacherResponseQuestions, TeacherEventsModal, TeacherAchieveJUPASModal,
    ServiceAccordionGroup, ServiceItem, ActionAccordionGroup, TeacherItemQRCodeSharing, TeacherItemSchoolReadiness, },
  setup(props) {
    // methods or filters
    const store = useStore();
    const { t } = useI18n();
    const { openModal, sleep, presentToast, presentPrompt, getQRCodeUrl, sendLinkToMyWhatsApp, formatDate,
            getIntakeYearOfDate, getCurrentIntakeYear, openImageModal, getProxyImgLink, copyText, } = utils();

    //const loadingData = computed(() => store.state.loadingUser || store.state.loadingData || store.state.loadingSchoolUserData);
    const loadingData = computed(() => store.state.loadingUser || store.state.loadingData);
    const user = computed(() => store.state.user);
    const userRelatedSchool = computed(() => store.getters.userRelatedSchool);
    const isAB4User = computed(() => store.getters.isAB4User);
    const hasTeacherEvents = computed<Session[]>(() => store.getters.getSessionsByAnchorEventId('c0bcf00d0')).value.length > 0;

    // TMP: for 14 Mar 2025 event
    const anchorEventTasks = computed<AnchorEventTask[]>(() => store.state.anchorEventTasks);
    const loadingUserFormResponses = computed(() => store.state.loadingUserFormResponses);
    const userFormResponses = computed(() => store.state.userFormResponses);
    const getFilteredFormQuestions = (task: AnchorEventTask) => (task.formQuestions.filter(q => (q.workServiceIds.length == 0 || q.workServiceIds.includes(props.ev.serviceId))));
    const getTaskResponses = (tid: string) => (userFormResponses.value.filter(r => r.taskId == tid));
    const isSubmittedTaskForm = (task: AnchorEventTask) => (getTaskResponses(task.id).length >= getFilteredFormQuestions(task).length);
  
    // Toggle views
    const targetForm = ref("F5-6");
    const selectedViews = reactive({
      type: props.prefilledViewType || "AchieveJUPAS",
      subType: "resources",
    }); // mainly for switching between AchieveJUPAS & Resources

    // Filter service by school roles (or customized wordings)
    const allSchoolRoles = computed(() => store.state.schoolRoles);
    const userSchoolRoles = () => ((user.value.teacher?.schoolRoles || "").split(" , ").filter(r => r));
    const selectedSchoolRole = ref(userSchoolRoles()[0] || ""); // XXX-related career prospect

    // Services & actions
    const services = computed<Service[]>(() => store.state.services);
    const getFilteredServices = (targetPrincipalOnly = false, allowProposeDatesOnly: any = null) => {
      return services.value.filter(s => {
        if (s.type == "jobEX") return false;
        if (s.id == 'ucircle') return true; // special case: nominate students for UCircle
        if (allowProposeDatesOnly !== null && s.isAllowProposeDates != allowProposeDatesOnly) return false;
        if (s.isForPrincipalOnly != targetPrincipalOnly) return false;
        if (s.status == 'Inactive' || s.status == 'Removed') return false;
        return s.targetSchoolBands.length == 0 || userRelatedSchool.value?.id == 'beacon1' || s.targetSchoolBands.includes(userRelatedSchool.value?.band)
      });
    }
    const openUserDetails = async () => {
      const student = store.state.schoolStudents.find(s => s.phone == '52110402'); // TMP: CN for demo
      return await openModal(UserDetailsModal, { user: student, isStudentView: false, hideCommentSection: true, });
    };

    watch(targetForm, (curr) => {
      switch (curr) {
        case 'F4':
          selectedViews.type = 'AB4';
          break;
        default:
          selectedViews.type = 'AchieveJUPAS';
          break;
      }
    })

    // return variables & methods to be used in template HTML
    return {
      // icons
      add, close, arrowBack,
      checkmarkCircle, alertCircle, ellipsisHorizontal, ellipsisVertical, pencil, qrCodeOutline,

      // variables
      user, isAB4User,
      loadingData,
      services,
      hasTeacherEvents,
      isPrincipal: computed(() => (user.value.teacher && userSchoolRoles().some(r => ["Principal", "Vice Principal"].includes(r)))),
      userSchoolRoles, selectedSchoolRole,
      filteredSchoolRoles: () => (config.formOptions.rolesInSchool.filter(r => !userSchoolRoles().includes(r))),
      getSchoolRoleAlias: (role: string) => (allSchoolRoles.value.find(r => r.title == role)?.alias || role),

      // TMP: 14 Mar events
      anchorEventTasks, loadingUserFormResponses, userFormResponses,
      openFormQuestionModal: async (task: AnchorEventTask) => {
        const { id: taskId, title, } = task;
        const formTitle = `${title}`; // modal title
        const oldResponses = getTaskResponses(taskId);
        const formQuestionIds = getFilteredFormQuestions(task).map(q => q.id);
        return await openModal(FormQuestionModal, { formTitle, formQuestionIds, taskId, oldResponses });
      },
      isSubmittedTaskForm, getTaskResponses, getFilteredFormQuestions,
      achievejupasSurveyTask: computed(() => anchorEventTasks.value.find(t => t.id == 't7ef068da') as AnchorEventTask),
      achieveslpSurveyTask: computed(() => anchorEventTasks.value.find(t => t.id == 't47822ace') as AnchorEventTask),

      // methods
      t,
      formatDate,
      closeModal: async () => (await modalController.dismiss({})),
      openStudentModal: async (initialSection = 2) => (await openModal(StudentModal, { schoolId: user.value.schoolId, initialSection })),
      sendLinkToMyWhatsApp,
      getQRCodeUrl,

      openUserDetails,
      openImageModal, getProxyImgLink, copyText,
      openServiceModal: async (serviceId: any, event: any = null, props: any = {}) => {
        if (serviceId) {
          return await openModal(ServiceModal, { serviceId, event, ...props })
        }
      },
      openABEventsModal: async (targetAnchorEvId) => (await openModal(ABEventsModal, { targetAnchorEvId })),
      openAB4CustomizeContentModal: async () => (await openModal(AB4CustomizeContentModal, { })),
      openTeacherEventsModal: async (anchorEvId) => (await openModal(TeacherEventsModal, { anchorEvId })),
      openSchoolTeacherModal: async () => (await openModal(SchoolTeacherModal, {})),
      openTeacherAchieveJUPASModal: async () => (await openModal(TeacherAchieveJUPASModal, {})),

      // popover
      setPopoverOpen: (service: any, state: boolean, ev = null) => {
        service.popoverEvent = ev;
        service.isPopoverOpened = state;
      },

      // Filter service list based on teacher school banding
      getFilteredServices,
      groupedServices: (targetPrincipalOnly = false, allowProposeDatesOnly: any = null, targetForm: any = null, returnArr = false, scheduledServiceOnly = false) => {
        const obj = {}, filteredServices: any = [];
        const targetServices = getFilteredServices(targetPrincipalOnly, allowProposeDatesOnly);
        for (const service of targetServices) {
          if (targetForm && service.targetForms && service.targetForms.length > 0 && !service.targetForms.includes(targetForm)) {
            continue; // only include services related to the form
          }
          let group = service.nature || 'Others';
          //if (service.id == 'mock-jupas') group = "FDMT unique";
          if (service.id == 'mock-jupas') group = "2. AchieveJUPAS";
          else if (group == 'Talk at your school') group = "3. University lecture at your school";
          //else if (group == 'Talk at your school') group = "[Subject enrichment] 30-min University lecture at your school";
          else if (group == "Workshop") group = "[Post-exam period] 4-hr university workshop";
          //else if (group == "Visit") group = "[Annual outing] 2-hr university visit";
          else if (group == "Visit") group = "1. Work visits";
          else if (group == "20-min Online Briefing") group = "[Class teacher session] 15-min Zoom reminders";
          //if (group == "Workshop") group = "Nominate students to Workshops on university campus";
          //else if (group == "Visit") group = "Lead students to University visits at schools’ preferred time";
          obj[group] = obj[group] || [];
          obj[group].push(service);
          filteredServices.push(service);
        }
        return returnArr ? filteredServices : obj;
      },

      // For view switching between forms & type
      selectedViews, targetForm,

      getServicesByNature: (nature) => {
        const filteredServices = getFilteredServices();
        if (nature == 'Video') return filteredServices.filter(s => s.name.toLowerCase().includes(" preview"));
        return filteredServices.filter(s => s.nature === nature && !s.name.toLowerCase().includes(" preview"));
      },

      // AchieveSLP
      openSLPModal: async (isPreview = false) => {
        const ev = store.getters.getLastAttendedSession;
        return await openModal(SLPModal, { isPreview, ev, serviceId: ev?.serviceId, relatedProgramId: ev?.relatedProgramId, relatedClientId: user.value.clientId }, "", false)
      },
    }
  },
};
</script>

<style scoped>
  ion-item {
    --min-height: 32px;
  }
  ion-icon[slot="start"] {
    margin: 0;
    margin-right: 4px;
  }

  a {
    cursor: pointer;
  }

  ion-segment-button {
    min-width: 50px;
    --padding-start: 3px;
    --padding-end: 3px;
  }

  .achieve-jupas-wrapper {
    height: 100vh;
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  /**
   * F5-6 switcher: AchieveJUPAS / CLP Resources
   */
  .view-type-segment ion-segment-button {
    --indicator-color: var(--ion-color-fdmtred);
    --color-checked: #fff;
    --border-radius: 20px !important;
  }
  .view-type-segment ion-segment-button ion-label {
    line-height: 1.15;
    padding-left: 4px;
    padding-right: 4px;
  }
</style>
