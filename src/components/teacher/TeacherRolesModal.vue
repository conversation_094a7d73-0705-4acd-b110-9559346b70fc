<template>
  <ion-page>
    <ion-header>
      <ion-grid fixed>
        <ion-toolbar>
          <ion-buttons slot="start">
            <ion-button slot="icon-only" @click="goBack()"><ion-icon :icon="arrowBack"></ion-icon></ion-button>
          </ion-buttons>

          <ion-title>Update your information</ion-title>
        </ion-toolbar>

        <ion-toolbar>
          <ion-segment mode="ios" v-model="selectedSection" scrollable>
            <ion-segment-button value="personal">
              Your personal information
            </ion-segment-button>
            <ion-segment-button value="school">
              Your position(s) in the school
            </ion-segment-button>
            <ion-segment-button value="class">
              Your role(s) in class(es)
            </ion-segment-button>
          </ion-segment>
        </ion-toolbar>
      </ion-grid>
    </ion-header>

    <ion-content>
      <div v-show="selectedSection == 'personal'">
        <user-profile-form-modal :hideHeader="true"></user-profile-form-modal>
      </div>
      
      <div v-show="selectedSection == 'school'">
        <role-in-school-select-modal :hideHeader="true"></role-in-school-select-modal>
      </div>

      <div v-show="selectedSection == 'class'">
        <roles-in-classes-modal :hideHeader="true"></roles-in-classes-modal>
      </div>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
// vue
import { defineProps, ref, computed } from 'vue';

// icons
import { add, close, arrowBack, } from 'ionicons/icons';

// components
import { IonPage, IonHeader, IonTitle, IonButtons, IonButton, IonIcon, IonToolbar,
        IonSegment, IonSegmentButton, IonContent, IonGrid, loadingController, } from '@ionic/vue';
import RolesInClassesModal from '@/components/teacher/RolesInClassesModal.vue';
import RoleInSchoolSelectModal from '@/components/teacher/RoleInSchoolSelectModal.vue';
import UserProfileFormModal from '@/components/modals/UserProfileFormModal.vue';

// utils
import config from '@/config';
import { useStore } from '@/store';
import { utils } from '@/composables/utils';
import { useRoute, useRouter } from 'vue-router';

// services
import AuthService from '@/services/AuthService';
import { customTokenSignin } from '@/auth';

// props
const props = defineProps(['isPage']);

const selectedSection = ref("school");
const { closeModal } = utils();
const router = useRouter();
const route = useRoute();
const store = useStore();
const { userId: paramUserId } = route.params;

const checkPrefilledLogin = async (userId: any) => {
  const loading = await loadingController.create({ message: "Loading..." });
  await loading.present();
  try {
    const existingFirebaseUser = await AuthService.checkPhoneExists(null, userId, true);
    if (existingFirebaseUser && existingFirebaseUser.s == 200 && existingFirebaseUser.t) {
      await customTokenSignin(existingFirebaseUser.t);
    } else {
      store.commit('setLoadingUser', false);
      router.replace({ path: '/login', query: { redirectPath: '/update-roles' } });
    }
  } catch (e) {
    console.log(e);
  } finally {
    loading.dismiss();
  }
}
if (paramUserId) checkPrefilledLogin(paramUserId);

const goBack = () => {
  if (props.isPage) router.replace('/home');
  else closeModal();
};
</script>

<style scoped>
  ion-toolbar {
    --min-height: 28px;
  }
</style>