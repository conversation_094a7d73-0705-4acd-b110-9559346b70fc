<template>
  <ion-header>
    <ion-toolbar>
      <!-- Back buttons (back / close modal) -->
      <ion-buttons slot="start">
        <ion-button slot="icon-only" @click="confirmSelect(true)">
          <ion-icon :icon="close"></ion-icon>
        </ion-button>
      </ion-buttons>

      <ion-title style="padding-right: 0">
        <ion-label class="ion-text-wrap">
          <h2>
            <b><span style="vertical-align: middle"><ion-icon :icon="thumbsUpOutline"></ion-icon></span>
            &nbsp;1-4 disciplines</b>
          </h2>
        </ion-label>
      </ion-title>
    </ion-toolbar>
  </ion-header>
  
  <ion-content :fullscreen="true">
    <!--
      [AB4] Selected Disciplines
      -->
    <ion-reorder-group @ionItemReorder="doReorder($event, chosenDisciplines)" :disabled="false">
      <ion-item lines="full" v-for="(discipline, idx) in chosenDisciplines" :key="idx" style="--min-height: 55px;">
        <ion-reorder mode="ios" slot="start" style="margin-inline-end: 0"></ion-reorder>

        <div style="width: 100%">
          <ion-textarea v-model="discipline.reason" :placeholder="`Why?`" label-placement="stacked" fill="solid" :auto-grow="true"
                        :rows="3">
            <div slot="label">
              <ion-text :color="discipline.reason ? 'success' : 'danger'" style="transform: none">{{ idx+1}}. {{ discipline.name }}</ion-text>
            </div>
          </ion-textarea>

          <ion-button fill="outline" color="medium" class="no-text-transform" @click.stop="discipline.reason = settings.templateReason">
            <small>Template</small>
          </ion-button>
          <ion-button fill="outline" color="medium" class="no-text-transform" @click="openProgramModal(discipline)" style="margin-top: 4px; margin-bottom: 4px">
            <small>Programs</small>
          </ion-button>

          <ion-textarea v-model="discipline.action" label-placement="stacked" fill="solid" :auto-grow="true" :rows="2">
            <div slot="label">
              <!--<ion-text :color="discipline.action ? 'success' : 'danger'" style="transform: none">{{ idx+1}}. {{ discipline.name }}</ion-text>-->
              <ion-text :color="discipline.action ? 'success' : 'danger'" style="transform: none">Your actions / preparations</ion-text>
            </div>
          </ion-textarea>

          <ion-button fill="outline" color="medium" class="no-text-transform" @click.stop="discipline.action = settings.templateAction">
            <small>Template</small>
          </ion-button>
        </div>

        <ion-buttons slot="end">
          <ion-button @click.stop="onDeleteChosenDiscipline(idx, discipline)">
            <ion-icon slot="icon-only" :icon="trashOutline"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-item>
    </ion-reorder-group>

    <!-- Add / Update discipline choices (Discipline Deck) -->
    <ion-row class="ion-margin-top ion-justify-content-center">
      <ion-button class="add-item-btn" color="primary" @click="openDisciplineDeckModal()">
        <ion-icon slot="start" :icon="add"></ion-icon>
        Discipline(s)
      </ion-button>
    </ion-row>
  </ion-content>
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, onMounted, watch, reactive, } from 'vue';

// icons
import { add, close, checkmark, arrowUp,  arrowForward, arrowBack, checkbox, trashOutline,
         thumbsUpOutline, thumbsDownOutline, thumbsUp, thumbsDown, heart, heartOutline,
         chevronBack, chevronForward, repeat, search, pencil, } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonTextarea, IonText,
        IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonReorderGroup, IonReorder,
        modalController } from '@ionic/vue';
import ABProgramSelectModal from '@/components/achievejupas/ABProgramSelectModal.vue';
import ABDisciplineSelectModal from '@/components/pss/ABDisciplineSelectModal.vue';

// composables / services
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';
import { Discipline, UserDiscipline } from '@/types';

export default defineComponent({
  name: 'AB4DisciplineResultPageModal',
  props: ["prefilledDisciplines", "targetProfessions", "oldUserDisciplines"],
  components: { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonTextarea, IonText,
                IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonReorderGroup, IonReorder, },
  setup(props, { emit }) {
    // methods or filters
    const { t } = useI18n();
    const store = useStore();
    const { closeModal, doReorder, openModal, processUserItems, syncChosenItems, } = utils();
    
    const user = computed(() => store.state.user);
    const settings = computed(() => store.state.settings);

    const disciplineGroups = computed(() => store.state.allDisciplineGroups);
    const allDisciplines = ref<Discipline[]>(store.getters.shuffledDisciplines);
    const chosenDisciplines = ref<Discipline[]>(props.prefilledDisciplines || []);
    const userDisciplines = ref<UserDiscipline[]>(props.oldUserDisciplines || []);

    const confirmSelect = async (noLoading = false) => {
      await closeModal({
        "chosen": chosenDisciplines.value,
        "userDisciplines": processUserItems(chosenDisciplines.value, userDisciplines.value, [], 'disciplineId', user.value.id),
        noLoading,
      }); // return selected discipline & order here
    };

    // INIT
    onMounted(() => {
      syncChosenItems('disciplineId', chosenDisciplines, userDisciplines, allDisciplines.value);
    })

    // 3. return variables & methods to be used in template HTML
    return {
      // icons
      add, close, checkmark, arrowUp, arrowForward, arrowBack, checkbox, trashOutline,
      thumbsUpOutline, thumbsDownOutline, thumbsUp, thumbsDown, heart, heartOutline, pencil,

      // variables
      disciplineGroups, allDisciplines,
      chosenDisciplines, userDisciplines,
      settings,

      // methods
      t, confirmSelect,
      closeModal,
      doReorder,
      openProgramModal: (specificDiscipline: any) => (openModal(ABProgramSelectModal, { specificDiscipline, readOnly: true })),

      onDeleteChosenDiscipline: (idx, discipline) => {
        chosenDisciplines.value.splice(idx, 1);
        const relatedUserItem = userDisciplines.value.find(us => us.disciplineId == discipline.id);
        if (relatedUserItem) relatedUserItem.reaction = '';
      },

      // Discipline Deck (for choosing disciplines)
      openDisciplineDeckModal: async () => {
        const modal = await modalController.create({
          cssClass: 'tall-modal',
          component: ABDisciplineSelectModal,
          componentProps: {
            prefilledDisciplines: chosenDisciplines.value.slice(),
            oldUserDisciplines: userDisciplines.value.slice() || [],
            targetProfessions: props.targetProfessions,
          }
        });
        modal.onDidDismiss().then(({ data }) => {
          if (data && data.chosen) {
            chosenDisciplines.value = data.chosen;
            userDisciplines.value = data.userDisciplines;
          }
        });
        return modal.present();
      }
    }
  }
});
</script>

<style scoped>
</style>
