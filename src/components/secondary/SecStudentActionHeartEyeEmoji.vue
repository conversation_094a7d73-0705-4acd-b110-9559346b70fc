<template>
  <div>
    <!-- Banner -->
    <img src="@/assets/banner_work.jpeg" style="width: 100%; margin-bottom: -3px">

    <!-- AchieveSLP (Advertisement: need join event first) -->
    <ion-toolbar style="--min-height: 27px">
      <ion-item lines="full" button detail color="primary" @click="openSLPModal(true)">
        <ion-label class="ion-text-wrap ion-text-center">
          <p style="margin: 0; color: #fff">AchieveSLP preview</p>
          <small>unlocked upon successful Work event enrollment</small>
        </ion-label>
      </ion-item>
    </ion-toolbar>

    <ion-card style="margin-top: 0">
      <ion-card-content style="padding: 16px">

        <!-- Exploring Section -->
        <div style="margin-bottom: 20px">
          <p style="margin: 0 0 12px 0; color: #666; font-size: 14px">Exploring</p>
          <div style="display: flex; flex-wrap: wrap; gap: 8px; margin-bottom: 16px">
            <ion-chip @click="selectDiscipline('Bus')" :color="selectedDiscipline === 'Bus' ? 'danger' : 'medium'" style="margin: 0">
              <ion-label>Bus</ion-label>
            </ion-chip>
            <ion-chip @click="selectDiscipline('SocSci')" :color="selectedDiscipline === 'SocSci' ? 'success' : 'medium'" style="margin: 0">
              <ion-label>SocSci</ion-label>
            </ion-chip>
            <ion-chip @click="selectDiscipline('Eng')" :color="selectedDiscipline === 'Eng' ? 'success' : 'medium'" style="margin: 0">
              <ion-label>Eng</ion-label>
            </ion-chip>
            <ion-chip @click="selectDiscipline('Arts')" :color="selectedDiscipline === 'Arts' ? 'success' : 'medium'" style="margin: 0">
              <ion-label>Arts</ion-label>
            </ion-chip>
            <ion-chip @click="selectDiscipline('Sci')" :color="selectedDiscipline === 'Sci' ? 'success' : 'medium'" style="margin: 0">
              <ion-label>Sci</ion-label>
            </ion-chip>
          </div>
          <div style="display: flex; flex-wrap: wrap; gap: 8px">
            <ion-chip @click="selectDiscipline('License (healthcare)')" :color="selectedDiscipline === 'License (healthcare)' ? 'success' : 'medium'" style="margin: 0">
              <ion-label>License (healthcare)</ion-label>
            </ion-chip>
            <ion-chip @click="selectDiscipline('License (others)')" :color="selectedDiscipline === 'License (others)' ? 'success' : 'medium'" style="margin: 0">
              <ion-label>License (others)</ion-label>
            </ion-chip>
            <ion-chip @click="selectDiscipline('Others')" :color="selectedDiscipline === 'Others' ? 'success' : 'medium'" style="margin: 0">
              <ion-label>Others</ion-label>
            </ion-chip>
          </div>
        </div>

        <!-- Click Section -->
        <div style="margin-bottom: 20px">
          <ion-chip @click="toggleClickSection" :color="'danger'" style="margin: 0 0 12px 0">
            <ion-label>Click</ion-label>
          </ion-chip>
          <div v-if="showClickSection" style="display: flex; flex-wrap: wrap; gap: 8px">
            <ion-chip @click="selectApplicationStatus('Applied')" :color="selectedApplicationStatus === 'Applied' ? 'primary' : 'medium'" style="margin: 0">
              <ion-label>1. Applied</ion-label>
            </ion-chip>
            <ion-chip @click="selectApplicationStatus('Nominated')" :color="selectedApplicationStatus === 'Nominated' ? 'primary' : 'medium'" style="margin: 0">
              <ion-label>2. Nominated</ion-label>
            </ion-chip>
            <ion-chip @click="selectApplicationStatus('Confirmed interview')" :color="selectedApplicationStatus === 'Confirmed interview' ? 'primary' : 'medium'" style="margin: 0">
              <ion-label>3. Confirmed interview</ion-label>
            </ion-chip>
            <ion-chip @click="selectApplicationStatus('Interview passed')" :color="selectedApplicationStatus === 'Interview passed' ? 'primary' : 'medium'" style="margin: 0">
              <ion-label>4. Interview passed</ion-label>
            </ion-chip>
          </div>
        </div>

        <!-- Lead Section -->
        <div style="margin-bottom: 20px">
          <ion-chip @click="selectLead" :color="'primary'" style="margin: 0">
            <ion-label>Lead (by invitation)</ion-label>
          </ion-chip>
        </div>

        <!-- Flexible Options -->
        <ion-item button @click="toggleFlexibleOptions" lines="none" style="--padding-start: 0; --inner-padding-end: 0">
          <ion-label>
            <h3>Flexible options (Overseas / Hong Kong)</h3>
          </ion-label>
          <ion-icon :icon="arrowForward" slot="end"></ion-icon>
        </ion-item>

        <!-- HK$300K Scholarship -->
        <ion-item button @click="openScholarshipModal" lines="none" style="--padding-start: 0; --inner-padding-end: 0">
          <ion-label>
            <h3>HK$300K Emerging Global Leaders Scholarship @ PolyU</h3>
          </ion-label>
          <ion-icon :icon="arrowForward" slot="end"></ion-icon>
        </ion-item>

      </ion-card-content>
    </ion-card>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue';

// icons
import { personCircle, compass, arrowForward, navigate, settingsOutline, checkmark, arrowBack,
        add, close, checkmarkCircle, alertCircle, pencil, peopleOutline, qrCodeOutline,
        createOutline, calendarOutline, calendarClearOutline, play, } from 'ionicons/icons';

// components
import { IonPage, IonToolbar, IonContent, IonModal, IonHeader, IonTitle,
        IonCard, IonCardTitle, IonCardSubtitle, IonCardHeader, IonCardContent, IonSegment, IonSegmentButton,
        IonGrid, IonCol, IonRow, IonSpinner, IonNote, IonButtons, IonButton, IonIcon,
        IonBadge, IonChip, IonList, IonItem, IonLabel, IonAccordion, IonAccordionGroup,
        loadingController, modalController, toastController, } from '@ionic/vue';
import ActionAccordionGroup from '@/components/shared/ActionAccordionGroup.vue';
import ParentConsentSignatureModal from '@/components/modals/ParentConsentSignatureModal.vue';

// Secondary School
import SLPModal from '@/components/secondary/ABSLPModal.vue';

// types
import { Session, Discipline, Profession, Program, Step1Option, User, Service, } from '@/types';

// composables
import { useI18n } from 'vue-i18n';
import { useStore } from '@/store';
import { utils } from '@/composables/utils';

// services
import ABSService from '@/services/ABSService';

// methods
const { t } = useI18n();
const store = useStore();
const { openImageModal, openModal, getF3YearDSE, getF4YearDSE, getF5YearDSE, getF6YearDSE, promptRollCallCode,
        getProxyImgLink, getQRCodeUrl, copyText, openServiceModal, } = utils();

// state variables
const loadingData = computed(() => store.state.loadingUser || store.state.loadingData);
const loadingPortalData = computed(() => store.state.loadingUser || store.state.loadingData || store.state.loadingPortalData);
const settings = computed(() => store.state.settings);
const user = computed(() => store.state.user);
const userRelatedSchool = computed(() => store.getters.userRelatedSchool);

// Work Services / Sessions
const workServices = computed<Service[]>(() => store.getters.getServicesByTypes(["Work"]));
const workVisitEvents = computed<Session[]>(() => store.getters.getSessionsByAnchorEventId('work-campus-visit'));
const workWorkshopEvents = computed<Session[]>(() => store.getters.getSessionsByAnchorEventId('work-workshop-2'));

// Interactive UI state
const selectedDiscipline = ref<string>('');
const selectedApplicationStatus = ref<string>('');
const showClickSection = ref<boolean>(false);

// Interactive UI methods
const selectDiscipline = (discipline: string) => {
  selectedDiscipline.value = selectedDiscipline.value === discipline ? '' : discipline;
  console.log('Selected discipline:', selectedDiscipline.value);
};

const selectApplicationStatus = (status: string) => {
  selectedApplicationStatus.value = selectedApplicationStatus.value === status ? '' : status;
  console.log('Selected application status:', selectedApplicationStatus.value);
};

const toggleClickSection = () => {
  showClickSection.value = !showClickSection.value;
};

const selectLead = () => {
  console.log('Lead (by invitation) selected');
};

const toggleFlexibleOptions = () => {
  console.log('Flexible options clicked');
};

const getServices = (targetNature: any = null) => {
  const yearDSE = Number(user.value.yearDSE);
  const targetForms = (yearDSE == getF6YearDSE() ? ['F.6', 'F.5'] : (yearDSE == getF4YearDSE() ? ['F.4'] : ['F.5']));
  const targetServices = (workServices.value || []).filter(s => {
    if (targetNature && s.nature != targetNature) return false; // Workshop / Visit
    if (store.getters.getLastAppliedSessionByServiceId(s.id)) return true; // contain applied session
    if (s.status == 'Inactive' || !s.nature) return false;
    
    // only include services related to the form
    if (targetForms && s.targetForms && s.targetForms.length > 0 && !s.targetForms.some(f => targetForms.includes(f))) return false;
    
    // skip because nominated by teachers (TBC: align layout with teachers?)
    if (['Talk at your school', 'Video'].includes(s.nature)) return false;

    return s.targetSchoolBands.length == 0 || userRelatedSchool.value.id == 'beacon1' ||
            s.targetSchoolBands.includes(userRelatedSchool.value.band);
  }).map(s => {
    s.lastAppliedSession = store.getters.getLastAppliedSessionByServiceId(s.id); // SLP visit / workshop
    return s;
  });
  return targetServices;
};
const getWorkshopServicesByUserLevel = (userLevel) => {
  const services = getServices('Workshop');
  const isShortlisted = (lastAppliedSession) => {
    if (!lastAppliedSession) return false;
    const { interviewStatus, response } = lastAppliedSession.userResponse;
    return interviewStatus?.includes('Accepted') || ['Attended', 'Confirmed'].includes(response);
  }
  switch (userLevel) {
    case 'eligible-work-applicant': 
      return services.filter(s => s.status != 'Inactive' && !isShortlisted(s.lastAppliedSession));
    case 'shortlisted-work-applicant':
      return services.filter(s => isShortlisted(s.lastAppliedSession));
  }
  return services;
}

/**
 * INIT
 */
const isUserAttendedLesson = (lessonId) => (user.value.sessionResponses?.find(r => (r.lessonId == lessonId && r.attended == 'Yes')));

// modals
const openSLPModal = async (isPreview = false) => {
  const ev = store.getters.getLastAttendedSession;
  return await openModal(SLPModal, { isPreview, ev, serviceId: ev?.serviceId, relatedProgramId: ev?.relatedProgramId, relatedClientId: user.value.clientId }, "", false)
};

// Other
const getSessionById = (sessionId: any) => (store.getters.getSessionById(sessionId));
const getServiceName = (service) => {
  const { lastAppliedSession, name } = service;
  return lastAppliedSession ? `${name} (${lastAppliedSession.date} ${lastAppliedSession.startTime})` : name;
};
const getServicesByIds = (ids) => (workServices.value || []).filter(s => ids.includes(s.id)); // TMP: for demo

const openScholarshipModal = () => {
  openServiceModal('lms-scholarship', getSessionById('e4c145b9'), true);
};

// Videos
const openVideoParentConsentModal = (videoId) => {
  return openModal(ParentConsentSignatureModal, { videoId, target: 'video' });
}
</script>

<style scoped>
  ion-icon[slot="start"] {
    margin: 2px;
  }
  ion-card {
    margin-inline: 1px;
  }
  ion-item::part(native) {
    padding-inline-start: 0;
  }
</style>