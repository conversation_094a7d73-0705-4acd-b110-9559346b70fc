<!--
  STEP 1: Choose professions by answering school-based questions
-->
<template>
  <ion-header>
    <ion-toolbar>
      <!-- Back buttons (back / close modal) -->
      <ion-buttons slot="start">
        <ion-button slot="icon-only" @click="confirmSelect(true)">
          <ion-icon :icon="close"></ion-icon>
        </ion-button>
      </ion-buttons>

      <ion-title style="padding-right: 0">
        <ion-label class="ion-text-wrap">
          <h2>
            <b><span style="vertical-align: middle"><ion-icon :icon="thumbsUpOutline"></ion-icon></span>
            &nbsp;10 professions</b>
            <br /><small><i>Prioritize (Drag & move) and shortlist professions</i></small>
          </h2>
        </ion-label>
      </ion-title>
    </ion-toolbar>
  </ion-header>

  <ion-content :fullscreen="true">
    <ion-grid class="ion-no-padding">
      <!--
        [AB4] Selected Professions
        -->
      <ion-reorder-group @ionItemReorder="doProfessionReorder($event, selectedProfessions)" :disabled="false">
        <ion-item lines="full" v-for="(profession, idx) in selectedProfessions" :key="idx" style="--min-height: 55px; --inner-padding-end: 4px">
          <ion-reorder mode="ios" slot="start" style="margin-inline-end: 8px"></ion-reorder>
          <ion-thumbnail slot="end" v-if="idx > 0 && profession.imgLink">
            <img :src="profession.imgLink" />
          </ion-thumbnail>

          <div style="width: 100%" v-if="idx == 0">
            <ion-row>
              <ion-col size="9">
                <span @click.stop="openProfessionModal(profession.id)">{{ idx+1}}. {{ profession.name }}</span>
                <ion-textarea v-model="profession.reason" label-placement="stacked" fill="solid" :auto-grow="true"
                              :rows="3" :value="settings.templateReason" @ionBlur="saveUserProfession()">
                  <div slot="label">
                    <ion-text :color="profession.reason ? 'success' : 'danger'" style="transform: none; font-size: 16px">
                      Why {{ profession.nameChinese }}?
                    </ion-text>
                  </div>
                </ion-textarea>
              </ion-col>
              <ion-col size="2">
                <ion-button fill="outline" class="no-text-transform" @click.stop="profession.reason = settings.templateReason" color="medium">
                  <small>Template</small>
                </ion-button>
                <ion-buttons style="padding-left: 28px">
                  <ion-button size="small" @click.stop="onDeleteChosenProfession(idx, profession)">
                    <ion-icon slot="icon-only" :icon="trashOutline"></ion-icon>
                  </ion-button>
                </ion-buttons>
              </ion-col>
            </ion-row>
            <ion-row>
              <ion-col size="9">
                <ion-textarea v-model="profession.action" label-placement="stacked" fill="solid" :auto-grow="true"
                              :rows="3" :value="settings.templateAction" @ionBlur="saveUserProfession()">
                  <div slot="label">
                    <ion-text :color="profession.action ? 'success' : 'danger'" style="transform: none; font-size: 16px">
                      Actions / Preparations
                    </ion-text>
                  </div>
                </ion-textarea>
              </ion-col>
              <ion-col size="2">
                <ion-button fill="outline" class="no-text-transform" @click.stop="profession.action = settings.templateAction" color="medium">
                  <small>Template</small>
                </ion-button>
              </ion-col>
            </ion-row>
          </div>

          <ion-label class="ion-text-wrap" @click.stop="openProfessionModal(profession.id)" v-else>
            <h2>{{ idx+1}}. {{ profession.name }}</h2>
            <p>{{ profession.nameChinese }}</p>
          </ion-label>

          <ion-buttons slot="end" style="margin: 0; flex-direction: column" v-if="idx != 0">
            <ion-button size="small" @click.stop="onDeleteChosenProfession(idx, profession)">
              <ion-icon slot="icon-only" :icon="trashOutline"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-item>
      </ion-reorder-group>

      <!-- Add profession choices (AI-assisted) -->
      <ion-row class="ion-margin-top ion-justify-content-center">
        <ion-button color="primary" class="add-item-btn" @click="openProfessionDeckModal()">
          <ion-icon slot="start" :icon="add"></ion-icon>
          Professions
        </ion-button>
      </ion-row>
    </ion-grid>
  </ion-content>
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, watch, reactive, onMounted, onUnmounted, onBeforeUnmount } from 'vue';

// icons
import { add, close, checkmark, arrowUp, arrowForward, arrowBack, trashOutline,
        thumbsUpOutline, thumbsDownOutline, thumbsUp, thumbsDown, heart, heartOutline,
        chevronBack, chevronForward, repeat, search, pencil, refresh, } from 'ionicons/icons';

// components
import { IonHeader, IonGrid, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonTextarea, IonText,
        IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonReorderGroup, IonReorder, IonThumbnail,
        modalController } from '@ionic/vue';
import ProfessionModal from '@/components/pss/ProfessionModal.vue';
import ABProfessionSelectModal from '@/components/pss/profession/ABProfessionSelectModal.vue';

// composables
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';

// types
import { Profession, UserProfession } from '@/types';

// services
import ABSService from '@/services/ABSService';

export default defineComponent({
  name: 'AB4ProfessionResultPageModal',
  props: ["prefilledProfessions", "oldUserProfessions"],
  components: { IonHeader, IonGrid, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonTextarea, IonText,
                IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonReorderGroup, IonReorder, IonThumbnail, },
  setup(props) {
    // methods or filters
    const { t } = useI18n();
    const store = useStore();
    const { closeModal, doReorder, openModal, processUserItems, syncChosenItems, } = utils();

    const user = computed(() => store.state.user);
    const settings = computed(() => store.state.settings);

    const selectedProfessions = ref<Profession[]>(props.prefilledProfessions || []);
    const userProfessions = ref<UserProfession[]>(props.oldUserProfessions || []);
    const allProfessions = ref<Profession[]>(store.getters.shuffledProfessions(false, true));

    const confirmSelect = async (noLoading = false) => {
      await closeModal({
        "selectedProfessions": selectedProfessions.value,
        "userProfessions": processUserItems(selectedProfessions.value, userProfessions.value, [], 'professionId', user.value.id),
        noLoading,
      }); // return selected profession & order here
    };
    const openProfessionModal = async (professionId: any) => await openModal(ProfessionModal, { professionId, useBackButton: true });

    // Record Access & Leave Time
    let accessTime, duration = 0, counterInterval;
    onMounted(() => {
      accessTime = new Date();
      counterInterval = setInterval(() => (duration++), 1000);

      // Prefill with previously written reasons
      syncChosenItems('professionId', selectedProfessions, userProfessions, allProfessions.value);
    })
    onBeforeUnmount(() => {
      if (accessTime && duration >= 5) {
        ABSService.insertPageAccessRecord('AB4ProfessionResultPageModal', accessTime, new Date(accessTime.getTime() + duration*1000));
        accessTime = undefined;
        duration = 0; // reset;
        clearInterval(counterInterval);
      }
    })

    // For auto save
    const saveUserProfession = () => {
      const updatedUserProfessions = processUserItems(selectedProfessions.value, userProfessions.value, [], 'professionId', store.state.user.id);
      ABSService.upsertUserProfessions(updatedUserProfessions);
    };

    // return variables & methods to be used in template HTML
    return {
      // icons
      add, close, checkmark, arrowUp, arrowForward, arrowBack, trashOutline,
      thumbsUpOutline, thumbsDownOutline, thumbsUp, thumbsDown, heart, heartOutline,
      pencil, refresh,

      // variables
      user, settings,
      selectedProfessions, userProfessions,

      // methods
      t,
      confirmSelect, closeModal, openProfessionModal,
      doReorder,

      // Auto-save
      saveUserProfession,
      doProfessionReorder: (event: CustomEvent, targetArr: any) => {
        doReorder(event, targetArr);
        saveUserProfession(); // auto save
      },

      onDeleteChosenProfession: (idx, profession) => {
        selectedProfessions.value.splice(idx, 1);
        const relatedUserItem = userProfessions.value.find(us => us.professionId == profession.id);
        if (relatedUserItem) relatedUserItem.reaction = '';
      },

      openProfessionDeckModal: async () => {
        const modal = await modalController.create({
          cssClass: 'tall-modal',
          component: ABProfessionSelectModal,
          componentProps: {
            prefilledProfessions: selectedProfessions.value.slice(),
            oldUserProfessions: userProfessions.value.slice() || [],
          }
        });
        modal.onDidDismiss().then(({ data }) => {
          if (data && data.selectedProfessions) {
            selectedProfessions.value = data.selectedProfessions;
            userProfessions.value = data.userProfessions;
          }
        });
        return modal.present();
      }
    }
  },
});
</script>

<style scoped>
</style>
