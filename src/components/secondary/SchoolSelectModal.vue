<template>
  <ion-header>
    <ion-toolbar style="--min-height: 40px">
      <!-- Back button -->
      <ion-buttons slot="start">
        <ion-button slot="icon-only" @click="closeModal()"><ion-icon :icon="close"></ion-icon></ion-button>
      </ion-buttons>

      <!-- Title -->
      <ion-title v-if="isMultiSelect">
        <span v-if="selectedSchoolIds.length == 0">Select schools</span>
        <span v-else>{{ selectedSchoolIds.length }} school{{ selectedSchoolIds.length == 1 ? '' : 's' }} selected</span>
      </ion-title>
      <ion-title v-else>Your school</ion-title>
    </ion-toolbar>

    <!-- Search bar -->
    <ion-toolbar style="--min-height: 36px">
      <ion-searchbar style="padding-bottom: 0; height: 36px" mode="ios" v-model="searchKeyword" :placeholder="isMultiSelect ? 'Search' : 'Search / Add New'"
                    @ionFocus="isSearching = true" @ionBlur="isSearching = false" @keyup.enter="(e) => e.target.blur()"></ion-searchbar>
    </ion-toolbar>

    <!-- Selected Schools -->
    <ion-toolbar v-if="selectedSchoolIds.length > 0" style="--min-height: 24px">
      <div>
        <ion-chip class="small-chip" v-for="schoolId in selectedSchoolIds" :key="schoolId">
          <ion-label>{{ schoolId.toUpperCase() }}</ion-label>
          <ion-icon :icon="close" @click="selectedSchools[schoolId] = false"></ion-icon>
        </ion-chip>
      </div>
    </ion-toolbar>
  </ion-header>
  <ion-content :fullscreen="true">
    <ion-list>
      <ion-item v-for="school in filteredSchools(schools).slice(0, numOfVisibleItems)" :key="school.id"
                @click="isMultiSelect ? undefined : closeModal(`[${school.nameShort}] ${school.name}`, school.id)"
                :button="!isMultiSelect" :detail="!isMultiSelect">

        <!-- For multi-select (admin) -->
        <ion-checkbox v-if="isMultiSelect" justify="start" labelPlacement="end" v-model="selectedSchools[school.id]">
          <ion-label class="ion-text-wrap">
            <h2>{{ school.nameShort }}</h2>
            <p><span>{{ school.name }}<br /></span><span>{{ school.nameChi }}</span></p>
          </ion-label>
        </ion-checkbox>

        <!-- Single Select -->
        <ion-label class="ion-text-wrap" v-else>
          <h2>{{ school.nameShort }}</h2>
          <p><span>{{ school.name }}<br /></span><span>{{ school.nameChi }}</span></p>
        </ion-label>
      </ion-item>
      <ion-item v-show="filteredSchools(schools).length == 0" @click="closeModal(searchKeyword)" button detail>
        <ion-label>
          <h2>{{ searchKeyword }}</h2>
        </ion-label>
      </ion-item>

      <ion-infinite-scroll @ionInfinite="loadData($event, schools)" threshold="100px">
        <ion-infinite-scroll-content
          loading-spinner="bubbles"
          loading-text="Loading...">
        </ion-infinite-scroll-content>
      </ion-infinite-scroll>
    </ion-list>
  </ion-content>

  <!-- Confirm Button (for multi-select) -->
  <ion-footer v-if="isMultiSelect && selectedSchoolIds.length > 0">
    <ion-toolbar>
      <ion-button expand="block" color="success" @click="confirmMultiSelect()">
        <ion-icon slot="end" :icon="checkmark"></ion-icon>
        Done
      </ion-button>
    </ion-toolbar>
  </ion-footer>
</template>

<script lang="ts">
// Vue reactivity
import { computed, defineComponent, onMounted, ref } from 'vue';

// icons
import { close, checkmark, } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonFooter, IonContent, IonButtons, IonButton, IonIcon,
        IonList, IonSearchbar, IonItem, IonLabel, IonInfiniteScroll, IonInfiniteScrollContent,
        IonCheckbox, IonChip,
        modalController } from '@ionic/vue';

import { utils } from '@/composables/utils';

export default defineComponent({
  name: 'SchoolSelectModal',
  props: ["schools", "showAllSchools", "title", "isMultiSelect", "prefilledSchoolIds"],
  components: { IonHeader, IonToolbar, IonTitle, IonFooter, IonContent, IonButtons, IonButton, IonIcon,
                IonList, IonSearchbar, IonItem, IonLabel, IonInfiniteScroll, IonInfiniteScrollContent,
                IonCheckbox, IonChip, },
  setup(props) {
    const searchKeyword = ref("");
    const isSearching = ref(false);
    const { infiniteScrollLoadData } = utils();

    const numOfVisibleItems = ref(50);
    const loadData = (ev: any, schools: any) => {
      infiniteScrollLoadData(ev, numOfVisibleItems, schools, 50);
    }

    const selectedSchools = ref<{ [key: string]: boolean }>({});
    const selectedSchoolIds = computed(() => (Object.keys(selectedSchools.value).filter(schoolId => selectedSchools.value[schoolId])))

    const closeModal = async (selectedSchool = "", schoolId = null) => {
      return await modalController.dismiss({ selectedSchool, schoolId, })
    };

    const filteredSchools = (schools: any) => {
      const skippedSchools = props.showAllSchools ? [] : ['demo', 'beacon1', 'beacon3', 'beacon4'];
      return schools.filter((sch: any) => {
        const schoolAbbr = sch.name.split(' ').map(str => str.charAt(0)).join('');
        const cleanedSchoolName = `${sch.nameShort} ${sch.name} ${schoolAbbr}`.replace(/[^a-z0-9]/gi, "").toLowerCase();
        const cleanedKeyword = searchKeyword.value.replace(/[^a-z0-9]/gi, "").toLowerCase();
        return !skippedSchools.includes(sch.id) && (cleanedSchoolName.includes(cleanedKeyword) || sch.nameChi?.includes(cleanedKeyword));
      }).sort((a: any, b: any) => {
        const textA = a.nameShort.toUpperCase(), textB = b.nameShort.toUpperCase();
        if (textA == "") return 1;
        if (textB == "") return -1;
        return textA < textB ? -1 : (textA > textB ? 1 : 0); // put to back if empty
      });
    }

    onMounted(() => {
      const { prefilledSchoolIds } = props;
      if (prefilledSchoolIds) {
        prefilledSchoolIds.forEach(schoolId => {
          selectedSchools.value[schoolId] = true;
        })
      }
    })

    // 3. return variables & methods to be used in template HTML
    return {
      // icons
      close, checkmark,
      
      // variables
      searchKeyword, isSearching, numOfVisibleItems,
      selectedSchools, selectedSchoolIds,
      
      // methods
      closeModal, filteredSchools, loadData,
      confirmMultiSelect: async () => {
        return await modalController.dismiss({ selectedSchoolIds: selectedSchoolIds.value })
      }
    }
  }
});
</script>

<style scoped>
</style>