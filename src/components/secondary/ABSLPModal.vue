<template>
  <ion-header>
    <ion-toolbar>
      <ion-buttons slot="start">
        <ion-button slot="icon-only" @click="closeModal({}, !isPreview)"><ion-icon :icon="close"></ion-icon></ion-button>
      </ion-buttons>
      <ion-title>
        <ion-label>
          <h2 style="font-size: 20px">AchieveSLP</h2>
          <p>
            <small>This tool will be disabled at 23:59 the day after this event.</small>
          </p>
        </ion-label>
      </ion-title>

      <!-- For Admin only: switch client -->
      <ion-select slot="end" interface="popover" v-model="selectedClientId">
        <ion-select-option v-for="client in allClients" :key="client.id" :value="client.id">
          {{ client.fullName }}
        </ion-select-option>
      </ion-select>
    </ion-toolbar>

    <!--
      Step filters
    -->
    <ion-toolbar style="--min-height: 32px">
      <ion-segment mode="ios" v-model="currStep" scrollable>
        <ion-segment-button :value="1">
          <ion-label><p>Key strength</p></ion-label>
        </ion-segment-button>
        <ion-segment-button :value="2" :disabled="recommendedProfessions.length == 0">
          <ion-label><p>Professions</p></ion-label>
        </ion-segment-button>
        <ion-segment-button :value="3" :disabled="recommendedProfessions.length == 0">
          <ion-label><p>Claims</p></ion-label>
        </ion-segment-button>
        <ion-segment-button :value="4" :disabled="recommendedProfessions.length == 0">
          <ion-label><p>Elaborate</p></ion-label>
        </ion-segment-button>
        <ion-segment-button :value="5" :disabled="recommendedProfessions.length == 0">
          <ion-label><p>Finalize</p></ion-label>
        </ion-segment-button>
      </ion-segment>
    </ion-toolbar>

    <!-- Chat GPT / Original -->
    <ion-toolbar style="--min-height: 32px" v-show="currStep == 5">
      <ion-segment mode="ios" v-model="selectedSLPType" @ionChange="checkGPTAction()">
        <ion-segment-button value="original">
          <ion-label>
            <p>Original</p>
          </ion-label>
        </ion-segment-button>
        <ion-segment-button value="gpt">
          <ion-label class="ion-text-wrap" style="line-height: 1">
            <p>ChatGPT</p>
            <small style="text-transform: none">(Availability subject to geography)</small>
          </ion-label>
        </ion-segment-button>
      </ion-segment>
    </ion-toolbar>
  </ion-header>

  <ion-content :fullscreen="true">

    <div class="spin" v-if="loading || loadingProgram">
      <ion-spinner></ion-spinner>
    </div>

    <div v-else>
      <!--
        Step 1: Interests & Strengths
        -->
      <div v-show="currStep == 1">
        <!-- Descriptions -->
        <div style="padding: 8px; width: 100%">
          <ion-textarea placeholder="By writing and/or tags, describe your strengths, passions, and habits as specifically as possible"
                        fill="outline" v-model="userSLP.userInputText" :rows="3" :auto-grow="true"></ion-textarea>
        </div>

        <!-- Filter Groups -->
        <ion-toolbar style="--min-height: 24px">
          <ion-segment class="filter-group-segment" mode="md" v-model="selectedFilterGroup" scrollable>
            <ion-segment-button value="Passion">
              <ion-label class="ion-text-wrap">Passion</ion-label>
            </ion-segment-button>
            <ion-segment-button value="Subject">
              <ion-label class="ion-text-wrap">Subject</ion-label>
            </ion-segment-button>
          </ion-segment>
        </ion-toolbar>

        <div class="material-div" style="padding-top: 8px; height: 100%; max-height: 40vh">
          <ion-chip v-for="(tag, i) in passionTags" :key="i" @click="toggleSelectedTag(tag.id, tag.text)"
                    :class="{ 'active-tag': userSLP.tagId == tag.id }" v-show="selectedFilterGroup == 'Passion'">
            <ion-label>{{ tag.text }}</ion-label>
          </ion-chip>
          <ion-chip v-for="(tag, i) in subjectTags" :key="i" @click="toggleSelectedTag(tag.id, tag.text)"
                    :class="{ 'active-tag': userSLP.tagId == tag.id }" v-show="selectedFilterGroup == 'Subject'">
            <ion-label>{{ tag.text }}</ion-label>
          </ion-chip>
          <ion-chip @click="toggleSelectedTag(tagObjOther.id, tagObjOther.text)"
                    :class="{ 'active-tag': userSLP.tagId == tagObjOther.id }">
            <ion-label>{{ tagObjOther.text }}</ion-label>
          </ion-chip>
        </div>
      </div>

      <!--
        Step 2: Explore Professions
      -->
      <div style="height: 100%" v-show="currStep == 2">
        <div class="spin" v-if="userSLP.waitingGPTResp">
          <ion-spinner></ion-spinner>
        </div>
        <swiper
            :navigation="true"
            class="card-slides"
            id="card-swiper-slides"
            :effect="'cards'"
            :grabCursor="true"
            :modules="modules"
            v-else
        >
          <!-- Main Slides -->
          <swiper-slide class="card-slide" v-for="profession in recommendedProfessions" :key="profession.id"
                        :class="{ 'highlighted-border': isProfessionSelected(profession) }" :data-profession-id="profession.id">
            <!-- Profession Image (AI-generated) -->
            <div class="top-badge ion-text-center" v-if="profession.imgLink && profession.imgLink != 'N/A'" style="top: 0; width: 100%; height: 100%">
              <img style="max-width: 100%; object-fit: cover; height: 100%; filter: brightness(60%)" :src="profession.imgLink" />
            </div>

            <!-- Profession Name & Details -->
            <ion-row style="text-shadow: 3px 3px 15px black">
              <ion-col size="12">
                <h1>{{ profession.name }}<br /><small>{{ profession.nameChinese }}</small></h1>
              </ion-col>
              <ion-col size="12">
                <ion-button size="small" color="light" class="no-text-transform" @click="openProfessionModal(profession.id)">
                  Details
                </ion-button>
              </ion-col>
            </ion-row>

            <ion-fab slot="fixed" vertical="top" horizontal="end">
              <ion-fab-button v-on="{ [isMobileWeb() ? 'touchstart' : 'click']: () => { onThumbsUp(profession) } }"
                              style="--background: transparent" :color="profession.imgLink ? 'light' : undefined">
                <ion-icon size="large" :icon="isProfessionSelected(profession) ? thumbsUp : thumbsUpOutline"></ion-icon>
              </ion-fab-button>
            </ion-fab>
            <!--<ion-fab slot="fixed" vertical="top" horizontal="start">
              <ion-fab-button v-on="{ [isMobileWeb() ? 'touchstart' : 'click']: () => { onThumbsDown(profession) } }"
                              style="--background: transparent" :color="profession.imgLink ? 'light' : undefined">
                <ion-icon size="large" :icon="isProfessionDisliked(profession) ? thumbsDown : thumbsDownOutline"></ion-icon>
              </ion-fab-button>
            </ion-fab>-->

          </swiper-slide>
        </swiper>
      </div>

      <!--
        Step: select discipline claims
        -->
      <div v-show="currStep == 3">
        <ion-list>
          <ion-item lines="full">
            <ion-label class="ion-no-margin ion-text-wrap">
              <h3><i>Please select and prioritize (drag & move) <b>at most 3 claims</b></i></h3>
            </ion-label>
          </ion-item>

          <!-- Selected Claims -->
          <ion-reorder-group @ionItemReorder="doReorder($event, chosenClaims)" :disabled="isReadonly()">
            <ion-item lines="full" v-for="(clm, i) in chosenClaims" :key="clm.id" style="--min-height: 55px">
              <ion-reorder mode="ios" slot="start"></ion-reorder>
              <ion-checkbox
                justify="space-between"
                labelPlacement="start"
                @update:modelValue="onCheckClaim($event, clm)"
                :modelValue="chosenClaims.find(c => c.id == clm.id) != null"
                :disabled="isReadonly()"
              ><ion-label class="ion-text-wrap" style="opacity: 1">{{ i+1 }}. {{ clm.text }}</ion-label></ion-checkbox>
            </ion-item>
          </ion-reorder-group>

          <!-- Remaining Claims -->
          <ion-item lines="full" v-for="clm in filteredClaims()" :key="clm.id" style="--min-height: 55px">
            <ion-checkbox
              justify="start"
              labelPlacement="end"
              @update:modelValue="onCheckClaim($event, clm)"
              :modelValue="chosenClaims.find(c => c.id == clm.id) != null"
            ><ion-label class="ion-text-wrap">{{ clm.text }}</ion-label></ion-checkbox>
          </ion-item>

          <!-- Add new claim -->
          <ion-item lines="full" style="--min-height: 32px" button detail @click="openNewClaimPrompt()">
            <ion-icon slot="start" :icon="add" style="margin-right: 12px"></ion-icon>
            <ion-label class="ion-text-wrap">Add a new claim</ion-label>
          </ion-item>
        </ion-list>
      </div>


      <!--
        Step: Elaborate selected claims
        -->
      <div v-show="currStep == 4">
        <ion-card v-for="(clm, idx) in chosenClaims" :key="idx">
          <ion-card-content style="color: var(--ion-color-dark)">
            <ion-textarea fill="solid" v-model="clm.userText" :readonly="isReadonly()"></ion-textarea>

            <ion-textarea fill="solid" v-model="clm.userElaboration" :rows="12" :readonly="isReadonly()"></ion-textarea>
          </ion-card-content>
        </ion-card>
      </div>

      <!--
        Step: Results & ChatGPT Proofread
        -->
      <div v-show="currStep == 5">
        <ion-grid>
          <!-- Original -->
          <ion-textarea style="font-size: 14px; color: #737373" fill="solid" :auto-grow="true" :counter="true" :maxlength="Infinity"
                        :counter-formatter="getCounterFormatterTextOriginal"
                        v-model="userSLP.original" readonly v-show="selectedSLPType == 'original'"></ion-textarea>

          <!-- ChatGPT -->
          <div v-if="selectedSLPType == 'gpt'">
            <div class="spin" v-if="userSLP.waitingGPTResp">
              <ion-spinner></ion-spinner>
            </div>

            <ion-textarea fill="outline" :auto-grow="true" :counter="true" :maxlength="Infinity"
                          :counter-formatter="getCounterFormatterTextGPT" :readonly="isReadonly()"
                          v-model="userSLP.gpt" v-else></ion-textarea>
          </div>
        </ion-grid>
      </div>
    </div>
  </ion-content>

  <ion-footer>
    <!-- Profession explanation -->
    <div v-show="currStep == 2">
      <ion-card style="margin-top: 0; border-radius: 16px">
        <div>
          <ion-chip style="height: 24px; font-size: 11px; margin: 0">
            <ion-avatar style="height: 20px; width: 20px">
              <img :src="require('@/assets/logo_gpt.png')" />
            </ion-avatar>
            <ion-label>
              <p>ChatGPT</p>
            </ion-label>
            <p></p>
          </ion-chip>
          <b v-if="userSLP.waitingGPTResp"> Thinking...</b>
          <!--<b v-else-if="currViewingProfession?.suitabilityScore"> Suitability: {{ currViewingProfession?.suitabilityScore }} / 10</b>-->
        </div>
        <ion-card-content style="padding: 0; max-height: 25vh; overflow: scroll">
          <ion-item lines="full" v-show="currViewingProfession?.explanation">
            <ion-label class="ion-text-wrap"><span v-html="parseMsg(currViewingProfession?.explanation)"></span></ion-label>
          </ion-item>
          <ion-item lines="full" v-show="currViewingProfession?.point1">
            <ion-label class="ion-text-wrap"><span>1. {{ currViewingProfession?.point1 }}</span></ion-label>
          </ion-item>
          <ion-item lines="full" v-show="currViewingProfession?.point2">
            <ion-label class="ion-text-wrap"><span>2. {{ currViewingProfession?.point2 }}</span></ion-label>
          </ion-item>
          <ion-item lines="full" v-show="currViewingProfession?.point3">
            <ion-label class="ion-text-wrap"><span>3. {{ currViewingProfession?.point3 }}</span></ion-label>
          </ion-item>

          <!-- Disclaimer -->
          <ion-item lines="full">
            <ion-label class="ion-text-wrap">
              <p style="line-height: 1"><small>Disclaimer: The data presented is for reference purposes only. We do not guarantee their correctness, completeness, or timeliness.</small></p>
            </ion-label>
          </ion-item>
        </ion-card-content>
      </ion-card>
    </div>

    <!-- Regenerate ChatGPT Response -->
    <ion-toolbar style="padding-top: 5px" class="ion-text-center" v-if="!isReadonly() && currStep == 5 && selectedSLPType == 'gpt' && !userSLP.waitingGPTResp && userSLP.gpt">
      <ion-button style="text-transform: none" shape="round" color="medium" @click="regenerateGPTResponse()">
        Regenerate response
        <ion-icon slot="end" :icon="refresh"></ion-icon>
      </ion-button>
    </ion-toolbar>

    <!-- Prev / Next buttons -->
    <ion-toolbar v-show="!userSLP.waitingGPTResp" style="--min-height: 32px">
      <ion-row>
        <!-- Back Button -->
        <ion-col class="ion-no-padding" size="2" v-show="currStep > 1">
          <ion-button color="dark" fill="clear" @click="currStep--">
            <ion-icon slot="icon-only" :icon="arrowBack"></ion-icon>
          </ion-button>
        </ion-col>

        <!-- Next Button -->
        <ion-col class="ion-no-padding" v-show="currStep == 1">
          <ion-button expand="block" @click="currStep = 2; getRecommendedProfessionsFromGPT()"
                      :disabled="(!user.isAdmin && isPreview && isReadonly()) || (!userSLP.tagId && !userSLP.userInputText) || userSLP.isInCooldownTime"
                      class="no-text-transform">
            {{ userSLP.isInCooldownTime ? `Please wait (${userSLP.cooldownSecLeft}s)` : `GO` }}
            <ion-icon slot="end" :icon="arrowForward"></ion-icon>
          </ion-button>
        </ion-col>

        <ion-col class="ion-no-padding" v-show="currStep > 1 && currStep < 5">
          <ion-button expand="block" @click="currStep++">
            Next
            <ion-icon slot="end" :icon="arrowForward"></ion-icon>
          </ion-button>
        </ion-col>

        <!-- WhatsApp Button -->
        <ion-col size="10" v-show="currStep == 5">
          <ion-button style="transform: none" color="success" expand="block" @click="sendSLPWhatsAppRecord()">
            Send to My WhatsApp
            <ion-icon slot="end" :icon="send"></ion-icon>
          </ion-button>
        </ion-col>
      </ion-row>
    </ion-toolbar>
  </ion-footer>
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, watch, reactive, onMounted, onBeforeMount, onBeforeUnmount } from 'vue';

// icons
import { add, close, checkmark, arrowUp,  arrowForward, arrowBack, openOutline, trashOutline, send, refresh, thumbsUp, thumbsUpOutline, } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
        IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonReorderGroup, IonReorder,
        IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption,
        IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid,
        IonSpinner, IonCheckbox, IonTextarea, IonInput, IonFab, IonFabButton, IonAvatar,
        IonicSlides, loadingController, alertController, } from '@ionic/vue';
import ProfessionModal from '@/components/pss/ProfessionModal.vue';

// composables
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { utilsGPT } from '@/composables/utilsGPT';
import { useStore } from '@/store';

// Swiper
import 'swiper/swiper.min.css';
import 'swiper/modules/effect-cards/effect-cards.min.css';
import 'swiper/modules/navigation/navigation.min.css';
import '@ionic/vue/css/ionic-swiper.css';
import { Swiper, SwiperSlide } from 'swiper/vue/swiper-vue';
import { EffectCards, Navigation, } from 'swiper';

// types
import { DisciplineClaim, Profession, Service, Step1Option, UserClaim, UserProfession, Program, } from '@/types';

// services
import SLPService from "@/services/SLPService";
import ABSService from '@/services/ABSService';

// lib
import { jsonrepair } from 'jsonrepair'; // for repairing JSON from GPT responses
import config from '@/config';

export default defineComponent({
  name: 'ABSLPModal',
  props: ["ev", "serviceId", "relatedProgramId", "relatedClientId", "isPreview"],
  components: { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
                IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonReorderGroup, IonReorder,
                IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption,
                IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid,
                IonSpinner, IonCheckbox, IonTextarea, IonInput, IonFab, IonFabButton, IonAvatar,
                Swiper, SwiperSlide, },
  setup(props) {
    // methods or filters
    const store = useStore();
    const { openModal, closeModal, presentAlert, presentPrompt, sleep, presentToast,
            setUserItemReaction, isItemSelected, isItemDisliked, isMobileWeb,
            onThumbsUpItem, onThumbsDownItem, animateToFirstCard, htmlToPlainText, uniqueId, } = utils();
    const { t } = useI18n();

    // state variables
    const loading = computed(() => store.state.loadingData || store.state.loadingPortalData);
    const loadingProgram = ref(false);
    const user = computed(() => store.state.user); // user claims
    const disciplineClaims = computed(() => store.state.disciplineClaims); // all claims
    const customClaims = ref<any>([]);
    const client = computed(() => store.getters.getClientById(props.ev?.clientId || props.relatedClientId));
    const allProfessions = computed(() => store.state.allProfessions);

    // For admin switching clients
    const allClients = computed(() => store.state.allClients);
    const selectedClientId = ref(props.ev?.clientId || props.relatedClientId || "caeb5730e");

    // GPT (interests & strengths)
    const BOT_URL = "https://ab-chatgpt-api.fdmt.hk/slp";
    const { tagObjOther, parseMsg, fetchGPTResponse, parseGPTResponse, whatsAppSendGPTResults, upsertUserItems, getStrengthDescription, initGPTObj, } = utilsGPT();
    const selectedFilterGroup = ref("Passion");
    const passionTags = computed<Step1Option[]>(() => store.getters.getStep1OptionsByQuestionId("q-192282d2"));
    const subjectTags = computed<Step1Option[]>(() => store.getters.getStep1OptionsByQuestionId("q-2c213906"));
    const userClaims = ref<UserClaim[]>(user.value.claims || []); // use user claims to store selected tags
    const getStep1OptionIds: any = () => ([...passionTags.value, ...subjectTags.value].map(tag => tag.id));

    // Recommended professions (shown in card view)
    const currViewingProfession = ref<Profession>();
    const recommendedProfessions = ref<Profession[]>([]);
    const selectedProfessions = ref<Profession[]>([]);
    const userProfessions = ref<UserProfession[]>(user.value.userProfessions || []);
    const isProfessionSelected = (profession: any) => (isItemSelected(profession, selectedProfessions));
    const isProfessionDisliked = (profession: any) => (isItemDisliked(profession, 'professionId', userProfessions));
    const tmpNewUserProfessions = ref<UserProfession[]>([]); // for storing slideChange user professions
    const setUserProfessionReaction = (professionId: any, reaction: any, skipIfExists = false) => {
      setUserItemReaction(
        professionId, reaction, 'professionId', userProfessions, tmpNewUserProfessions, {}, skipIfExists
      );
    }

    // Current step
    const currStep = ref(1);
    const isReadonly = () => (!user.value.isAdmin && user.value.slp?.accessType != 'full');

    // SLP
    const relatedService = computed<Service>(() => store.getters.getServiceById(props.serviceId));
    const chosenClaims = ref<DisciplineClaim[]>([]); // selected claims
    const selectedSLPType = ref('original');
    const userSLP = reactive({
      userInputText: "", // user input prompt
      tagId: "",
      tagText: "",
      original: "",
      gpt: "",
      waitingGPTResp: false,
      isInCooldownTime: false, // prevent users sending requests consecutively
      cooldownSecLeft: 0,
    });
    const combineUserClaims = () => {
      if (!isReadonly()) {
        userSLP.original = chosenClaims.value.map(c => (`${c.userText}. ${c.userElaboration || ""}`)).join("\n\n");
      }
    }
    const upsertSLPClaims = (commitToStore = true) => {
      if (!isReadonly() && chosenClaims.value.length > 0) {
        // Make all existing claims not selected first
        const latestUserClaims: any = (user.value.claims || []).filter(c => !c.type).map(c => {
          c.selected = false;
          return c;
        });

        // Only make chosenClaims selected
        for (const cc of chosenClaims.value) {
          const existingClaim = latestUserClaims.find(c => c.claimId == cc.id);
          const { id, userText, userElaboration, order } = cc;
          if (existingClaim) {
            existingClaim.text = userText || "";
            existingClaim.elaboration = userElaboration || "";
            existingClaim.order = order;
            existingClaim.selected = true;
          } else {
            latestUserClaims.push({
              claimId: id,
              text: userText || "",
              elaboration: userElaboration || "",
              selected: true,
              order,
            });
          }
        }

        // Update store & DB
        if (commitToStore) store.commit('upsertUserClaims', latestUserClaims);
        SLPService.upsertUserClaims(latestUserClaims);
      }
    }
    const wordCounter = (text) => {
      if (text) {
        const chineseWords = text.match(/[\u4e00-\u9fa50-9]/g) || [];
        const englishWords = text.match(/[a-zA-Z0-9]+/g) || [];
        return chineseWords.length + englishWords.length;
      }
      return 0;
    }
    const checkGPTAction = async (forceGenerate = false) => {
      if (!isReadonly() && !userSLP.waitingGPTResp && selectedSLPType.value == "gpt" && userSLP.original && (forceGenerate || !userSLP.gpt.trim())) {
        userSLP.waitingGPTResp = true;
        try {
          let content = `Key strength: ${getStrengthDescription(userSLP)}\n`;
          if (recommendedProfessions.value.length > 1 && selectedProfessions.value.length > 0) {
            content += `Interested professions: ${selectedProfessions.value.map(p => p.name).join(" , ")}\n`;
          }
          //content += `\nStudying elective subjects: ${studyingElectives}`;
          content += `\nAbove is the information about a Hong Kong F5/6 student, who is preparing the personal statement used in JUPAS SLP (Student Learning Profile),` +
                      ` and s/he wrote below draft claims. Please proofread & polish the claims in both English and 繁體中文. Please reply directly with the revised paragraphs ONLY.` + 
                      ` No need to translate the company 'FDMT Consulting' and the event name '${relatedService.value.name}' into Chinese. \n\n\n${userSLP.original}`;
          
          const data = await fetchGPTResponse(userSLP, BOT_URL, content, user.value.id);
          if (!data) throw new Error("No data");

          userSLP.waitingGPTResp = false;
          userSLP.gpt = "";

          const reader = data.getReader(), decoder = new TextDecoder();
          let done = false;
          while (!done) {
            const { value, done: doneReading } = await reader.read();
            done = doneReading;
            const chunkValue = decoder.decode(value);
            for (const message of chunkValue.split("\n")) {
              const matches = message.match(/\{([^}]+)\}/);
              if (matches) {
                const parsed = JSON.parse(matches[0]);
                if (parsed.text) userSLP.gpt += parsed.text;
                if (userSLP.gpt.startsWith('\n')) userSLP.gpt = userSLP.gpt.trimStart();
              }
            }
          }

          // Insert latest response to DB
          SLPService.upsertUserSLP(userSLP.original, userSLP.gpt);
          store.commit('upsertUserSLP', userSLP);
        } catch (e) {
          presentAlert("ChatGPT did not response. Please try again");
        } finally {
          userSLP.waitingGPTResp = false;
        }
      }
    }

    const getRecommendedProfessionsFromGPT = async () => {
      currViewingProfession.value = undefined;
      try {
        let clientId = props.ev?.clientId || props.relatedClientId;
        if ((props.isPreview || user.value.isAdmin) && selectedClientId.value) clientId = selectedClientId.value; // for admin
        const overrideBotName = config.separatePoeBotClientIds.includes(clientId) ? clientId : "";
        const MAX_PROFESSIONS = 8;

        let content = `Key strength: ${getStrengthDescription(userSLP)}`;
        content += `\n\nAbove is the information about a Hong Kong F5/6 student, who is joining the event of the client "${clientId}". Please suggest at most ${MAX_PROFESSIONS} unique professions that are most suitable for the student based on his/her strengths. The professions must ONLY be those related to the client "${clientId}".`;
        //content += ` For each profession, please write 3 most related points in TRADITIONAL CHINESE to convince the student why the profession is suitable and how it is related to student's interests (e.g. part of work is associated to student's interest). Be specific & natural`;
        content += ` For each profession, please explain in detail how the student's key strength is applied in most related daily tasks in TRADITIONAL CHINESE to convince the student that the profession is suitable. Be as specific & concrete as possible.`;

        // Customized for SurveyorWork
        if (clientId == "PolyU_BRE") {
          content += `For Surveyor profession (client: PolyU_BRE), please give your explanation for each division (建築測量組, 產業測量組, 規劃及發展組, 物業設施管理組, 工料測量組) paragraph by paragraph, with the first paragraph being the most suitable division. Paragraphs are separated with blank newlines, shown like numbered list, with the divisions highlighted in bold.`;
        }
        // Customized for NursingWork
        if (clientId == "c45a8af14") {
          //content += 'For Nurse profession, please give your detailed explanation for each stream (General Nursing, Mental Health Nursing).' // 20240929: already split into 2 professions
          //content += 'For Nurse profession, please give your explanation for each ENTRY roles/divisions paragraph by paragraph, with the first paragraph being the most suitable role/division. Paragraphs are separated with blank newlines, shown like numbered list, with the role/division highlighted in bold.'
          //content += 'For Nurse profession, please give your explanation for each common employer group (e.g. 公立醫院, 私家醫院, 安老院, ...) or each common entry role (e.g. Registered nurse, Admin Nurse, Research Nurse, ...) paragraph by paragraph, with the first paragraph being the most suitable division. Paragraphs are separated with blank newlines, shown like numbered list, with the divisions highlighted in bold.'
        }
        content += `\n\nYour response MUST be formatted in JSON with only an array of JavaScript objects (at most ${MAX_PROFESSIONS} objects), each object must contain exactly 2 fields: "id", "explanation"\n`;
        content += `Example response: [{"id":"499","explanation":"XXX"}]`;

        // Send request
        const data = await fetchGPTResponse(userSLP, BOT_URL, content, user.value.id, overrideBotName);
        if (!data) throw new Error("No data");

        // Parse data (bot responses)
        await parseGPTResponse(userSLP, data, currViewingProfession, recommendedProfessions, allProfessions, '#card-swiper-slides', 'professionId');

        // Insert to DB for records first
        upsertUserItems(user.value.id, recommendedProfessions, userProfessions, 'professionId');

        // Send out the results to student's WhatsApp group for records
        whatsAppSendGPTResults(user.value.phone, user.value.waGroupId, getStrengthDescription(userSLP), recommendedProfessions, 'profession');
        //const program: Program = store.getters.getProgramsById(props.relatedProgramId || client.value.programId);
        //if (program) msg += ` and the program *${program.displayName}* `; // 20240929: hide this for NursingWork SLP
      } catch (e) {
        console.error(e);
        presentAlert("ChatGPT did not response. Please try again");
        currStep.value = 1;
      } finally {
        userSLP.waitingGPTResp = false;
      }
    }

    onMounted(() => {
      const { slp, } = user.value;

      // Restore previous saved SLP text
      if (slp) {
        userSLP.original = slp.original; // will be replaced from step 2 -> 3
        userSLP.gpt = slp.gpt;
      }

      // AI: Prefill previously input text & selected tag
      initGPTObj(userSLP, user.value.claims);
    });

    onBeforeUnmount(() => {
      //upsertUserClaims(true); // save before leave
    })

    watch(currStep, (curr, prev) => {
      if (prev == 3 && curr == 4) { // SLP claims combine
        combineUserClaims();
        upsertSLPClaims();
      }
      else if (prev == 4 && curr == 5) { // Finalize
        upsertSLPClaims();
      }
    });

    // 3. return variables & methods to be used in template HTML
    return {
      // icons
      add, close, checkmark, arrowUp, arrowForward, arrowBack, openOutline, trashOutline, send, refresh,
      thumbsUp, thumbsUpOutline,

      // variables
      loading, loadingProgram,
      user, currStep,

      // methods
      t, closeModal,
      openProfessionModal: async (professionId: any) => (await openModal(ProfessionModal, { professionId, useBackButton: true })),

      // SLP
      selectedSLPType,
      isReadonly,
      getCounterFormatterTextOriginal: () => (`${wordCounter(userSLP.original)} words`),
      getCounterFormatterTextGPT: () => (`${wordCounter(userSLP.gpt)} words`),

      sendSLPWhatsAppRecord: async () => {
        presentPrompt("Send the above SLP draft to your WhatsApp group for records?", async () => {
          const loading = await loadingController.create({});
          await loading.present();
          SLPService.sendSLPWhatsAppRecord(selectedSLPType.value, userSLP.original, userSLP.gpt, user.value.phone, user.value.waGroupId);
          await sleep(2);
          loading.dismiss();
          presentToast("Your SLP draft will be sent to your WhatsApp group in minutes.");
        });
      },

      // Swiper
      setUserItemReaction, isItemSelected, isItemDisliked, isMobileWeb, onThumbsUpItem, onThumbsDownItem, animateToFirstCard,
      modules: [EffectCards, IonicSlides, Navigation],
      onThumbsUp: (profession: any) => {
        onThumbsUpItem(profession, 'professionId', selectedProfessions, userProfessions, tmpNewUserProfessions, {}, false);

        // Update DB (save user reaction)
        const newReaction = selectedProfessions.value.find(d => d.id == profession.id) ? 'like' : '';
        const existingUP = (user.value.userProfessions || []).find(up => up.professionId == profession.id);
        if (existingUP) {
          existingUP.reaction = newReaction;
          ABSService.upsertUserProfessions([existingUP]);
        }
      },
      onThumbsDown: (profession: any) => (onThumbsDownItem(profession, 'professionId', selectedProfessions, userProfessions, tmpNewUserProfessions, {})),
      setUserProfessionReaction,
      isProfessionSelected, isProfessionDisliked,

      // SLP claims
      userClaims,
      disciplineClaims,
      chosenClaims,
      doReorder: async (event: CustomEvent, claims: any) => {
        claims = event.detail.complete(claims).map((claim: any, idx: any) => {
          claim.order = idx+1;
          return claim;
        });
        upsertSLPClaims(false); // save once
      },
      filteredClaims: () => {
        return disciplineClaims.value.filter(c => {
          return c.clientId == (selectedClientId.value || client.value?.id) && chosenClaims.value.find(cc => cc.id == c.id) == null;
        }).concat(customClaims.value);
      },
      onCheckClaim: (checked: CustomEvent, claim: any) => {
        if (checked) {
          if (chosenClaims.value.find(c => c.id == claim.id) == null) {
            if (!claim.userText) claim.userText = claim.text;
            if (!claim.userElaboration) claim.userElaboration = claim.elaboration;
            chosenClaims.value.push(claim);

            if (chosenClaims.value.length > 5) {
              presentAlert("You can only select at most 5 claims!");
              setTimeout(() => {
                const idx = chosenClaims.value.findIndex(c => c.id == claim.id);
                if (idx !== -1) chosenClaims.value.splice(idx, 1);
              }, 100);
            }
          }
        }
        else {
          const idx = chosenClaims.value.findIndex(c => c.id == claim.id);
          if (idx !== -1) chosenClaims.value.splice(idx, 1);
        }
      },
      openNewClaimPrompt: async () => {
        const alert = await alertController.create({
          header: 'Claim',
          inputs: [
            {
              name: 'claim',
              type: 'text',
              placeholder: 'Enter your claim here',
            },
          ],
          buttons: [
            {
              text: t('cancel'),
              role: 'cancel',
              cssClass: 'secondary',
            }, {
              text: t('confirm'),
              handler: (value) => {
                if (value.claim) {
                  customClaims.value.push({ id: `c${uniqueId()}`, text: value.claim, });
                  alert.dismiss();
                }
                return false;
              }
            }
          ]
        });
        await alert.present();
      },

      // GPT
      selectedFilterGroup,
      passionTags, subjectTags, tagObjOther,
      toggleSelectedTag: (tagId, tagText) => {
        if (userSLP.tagId == tagId) {
          userSLP.tagId = '';
          userSLP.tagText = '';
        } else {
          userSLP.tagId = tagId;
          userSLP.tagText = tagText;
        }
      },
      getRecommendedProfessionsFromGPT, parseMsg,
      userSLP, currViewingProfession, recommendedProfessions,
      
      checkGPTAction,
      regenerateGPTResponse: async () => {
        presentPrompt("Do you confirm?", async () => {
          checkGPTAction(true);
        });
      },

      // For switching clients
      allClients, selectedClientId,
    }
  }
});
</script>

<style scoped>
  ion-textarea {
    --placeholder-font-weight: bold;
  }
</style>
