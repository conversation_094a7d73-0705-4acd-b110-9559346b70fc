<template>
  <div>
    <!-- Banner -->
    <img src="@/assets/banner_work.jpeg" style="width: 100%; margin-bottom: -3px">

    <!-- AchieveSLP (Advertisement: need join event first) -->
    <ion-toolbar style="--min-height: 27px">
      <ion-item lines="full" button detail color="primary" @click="openSLPModal(true)">
        <ion-label class="ion-text-wrap ion-text-center">
          <p style="margin: 0; color: #fff">AchieveSLP preview</p>
          <small>unlocked upon successful Work event enrollment</small>
        </ion-label>
      </ion-item>
    </ion-toolbar>

    <ion-card style="margin-top: 0">
      <ion-card-content style="padding: 0">
        <!-- Videos to be parent consented -->
        <ActionAccordionGroup :noPadding="false" value="parent-consent-videos" :headerColor="'fdmtgrey'" v-if="user.relatedVideos?.length > 0">
          <template v-slot:header>
            <ion-label><b>Video(s)</b></ion-label>
          </template>
          <home-action-item v-for="video in user.relatedVideos" :key="video.id"
                            @click="openVideoParentConsentModal(video.id)" :isLesson="true"
                            :title="video.title"></home-action-item>
        </ActionAccordionGroup>

        <!-- Eligible Work applicant -->
        <ActionAccordionGroup :noPadding="false" value="eligible-work-applicant" :headerColor="'light'">
          <template v-slot:header>
            <p style="color: #888888"><b>1. Eligible Work applicant</b></p>
          </template>
          <div>
            <div v-for="service in getWorkshopServicesByUserLevel('eligible-work-applicant')" :key="service.id"
                v-show="user.isAdmin ? !['s87bb705d', 's1b2ed9be'].includes(service.id) : true">
              <home-action-item @click="openServiceModal(service.id, service.lastAppliedSession, true)" :isLesson="true"
                                :title="getServiceName(service)"></home-action-item>
              <event-card v-if="service.lastAppliedSession" :ev="service.lastAppliedSession" :user="user" :hideEventDetails="true"
                          :hideApplyButton="true" style="margin: 0; padding-left: 32px; box-shadow: none"></event-card>
            </div>

            <!-- Work Visit Sessions (Public) -->
            <div v-for="ev in workVisitEvents" :key="ev.id" v-show="user.isAdmin ? !['sd93a34f8'].includes(ev.serviceId) : true">
              <home-action-item @click="openServiceModal(ev.serviceId, ev, true)" :isLesson="true"
                                :title="`${ev.name} (${ev.date} ${ev.startTime})`"></home-action-item>
              <event-card v-if="ev.userResponse" :ev="ev" :user="user" :hideEventDetails="true"
                          :hideApplyButton="true" style="margin: 0; padding-left: 32px; box-shadow: none"></event-card>
            </div>
          </div>
        </ActionAccordionGroup>

        <!-- Shortlisted Work applicant -->
        <ActionAccordionGroup :noPadding="false" value="shortlisted-work-applicant" :headerColor="'light'">
          <template v-slot:header>
            <p style="color: #888888"><b>2. Shortlisted Work applicant</b></p>
          </template>
          <div v-if="user.isAdmin">
            <home-action-item v-for="service in getServicesByIds(['s87bb705d'])" :key="service.id"
                              @click="openServiceModal(service.id, service.lastAppliedSession, true)" :isLesson="true"
                              :title="getServiceName(service)"></home-action-item>
          </div>
          <div v-for="service in getWorkshopServicesByUserLevel('shortlisted-work-applicant')" :key="service.id">
            <home-action-item @click="openServiceModal(service.id, service.lastAppliedSession, true)" :isLesson="true"
                              :title="getServiceName(service)"></home-action-item>
            <event-card v-if="service.lastAppliedSession" :ev="service.lastAppliedSession" :user="user" :hideEventDetails="true"
                        :hideApplyButton="true" style="margin: 0; padding-left: 32px; box-shadow: none"></event-card>
          </div>
          <!--
            - Workshop slide 
            - All details 
            - Confirm button on the top right 
            Target action: Attend the workshop
          -->
        </ActionAccordionGroup>

        <!-- Outstanding Work applicant -->
        <ActionAccordionGroup :noPadding="false" value="outstanding-work-applicant" :headerColor="'light'">
          <template v-slot:header>
            <p style="color: #888888"><b>3. (Outstanding) Work participant</b></p>
          </template>
          <div v-if="user.isAdmin">
            <div v-for="service in getServicesByIds(['sd93a34f8'])" :key="service.id">
              <home-action-item @click="openServiceModal(service.id, service.lastAppliedSession, true)" :isLesson="true"
                                :title="getServiceName(service)"></home-action-item>
              <event-card v-if="service.lastAppliedSession" :ev="service.lastAppliedSession" :user="user" :hideEventDetails="true"
                          :hideApplyButton="true" style="margin: 0; padding-left: 32px; box-shadow: none"></event-card>
            </div>
          </div>

          <!-- TODO: workshop & 30-min sessions as well -->
          <div v-for="ev in workVisitEvents" :key="ev.id" v-show="(ev.userResponse && ev.userResponse.isOutstandingStudent)">
            <home-action-item @click="openServiceModal(ev.serviceId, ev, true)" :isLesson="true"
                              :title="`${ev.name} (${ev.date} ${ev.startTime})`"></home-action-item>
          </div>

          <!-- Video taken (YouTube video) -->
          <!-- Parent consent -->
          <!-- TODO: video users + Type = outstanding - parent consent -->

          <!--
          - Sharing video preview & Parent consent 
          - Participation and outstanding certs 
          - Workshop photos 

          Target action: Parents give consent to the sharing video 
          -->
        </ActionAccordionGroup>

        <!-- Work Ambassador -->
        <ActionAccordionGroup :noPadding="false" value="work-ambassador" :headerColor="'light'">
          <template v-slot:header>
            <p style="color: #888888"><b>4. Work Ambassador</b></p>
          </template>
          <div v-if="user.isAdmin">
            <div v-for="service in getServicesByIds(['s1b2ed9be'])" :key="service.id">
              <home-action-item @click="openServiceModal(service.id, service.lastAppliedSession, true)" :isLesson="true"
                                :title="getServiceName(service)"></home-action-item>
              <event-card v-if="service.lastAppliedSession" :ev="service.lastAppliedSession" :user="user" :hideEventDetails="true"
                          :hideApplyButton="true" style="margin: 0; padding-left: 32px; box-shadow: none"></event-card>
            </div>
          </div>
          <div v-if="user.isAdmin || workVisitEvents.some(ev => ev.userResponse && ev.userResponse.isOutstandingStudent)">
            <div v-for="ev in workVisitEvents.filter(ev => ev.userResponse && ev.userResponse.isOutstandingStudent)" :key="ev.id">
              <home-action-item @click="openServiceModal(ev.serviceId, null, true, { isOutstandingParticipantView: true })" :isLesson="true"
                                title="Example Work video & previous shooting photos"></home-action-item>
            </div>
          </div>

          <!-- Video Sessions (invited) - program videos -->
          <!-- Past video shooting moments -->
          
        </ActionAccordionGroup>

        <!-- 5. Bluebird Seed Eligible applicant -->
        <ActionAccordionGroup :noPadding="false" value="bluebird-seed-eligible-applicant" :headerColor="'light'">
          <template v-slot:header>
            <p style="color: #888888"><b>5. Bluebird Seed Eligible applicant</b></p>
          </template>
          <!-- Video, Contract, Apply button (notifications) -->
          <home-action-item :isLesson="true" title="Bluebird Seed" @click="openServiceModal('bluebird-seed')"></home-action-item>
        </ActionAccordionGroup>
          
        <!-- 6. Bluebird Seed Member -->
        <ActionAccordionGroup :noPadding="false" value="bluebird-seed-member" :headerColor="'light'">
          <template v-slot:header>
            <p style="color: #888888"><b>6. Bluebird Seed Member</b></p>
          </template>
          <!-- Project logs (personalized) -->
          <div v-if="user.isAdmin && user.schoolId == 'htc'">
            <ion-item lines="full">
              <ion-label style="padding-left: 32px">
                <b>Co-supervisors: Ms. Lilian Ho (HTC), Brandon Chan (FDMT)</b>
              </ion-label>
            </ion-item>

            <ActionAccordionGroup style="padding-left: 20px" :noPadding="true" :headerColor="'light'" value="tasks">
              <template v-slot:header><p><b>Project logs</b></p></template>
              <div>
                <ion-item button lines="full">
                  <ion-label class="ion-text-wrap" style="padding-left: 32px">
                    <b>AchieveJUPAS UI Updates</b>
                    <p>Show version history of program choices</p>
                    <p>Expected completion: 2025-04-30</p>
                  </ion-label>
                  <ion-badge color="medium" slot="end">Planned</ion-badge>
                </ion-item>
              </div>
            </ActionAccordionGroup>
          </div>
        </ActionAccordionGroup>

        <!-- 7. Bluebird Seed Scholarship holder -->
        <ActionAccordionGroup :noPadding="false" value="bluebird-seed-scholarship-holder" :headerColor="'light'">
          <template v-slot:header>
            <p style="color: #888888"><b>7. Bluebird Seed Scholarship holder</b></p>
          </template>
          <div></div>
        </ActionAccordionGroup>

        <!-- 8. Bluebird Member -->
        <ActionAccordionGroup :noPadding="false" value="bluebird-member" :headerColor="'light'">
          <template v-slot:header>
            <p style="color: #888888"><b>8. Bluebird Member</b></p>
          </template>
          <div></div>
        </ActionAccordionGroup>

        <!-- 9. Bluebird Scholarship holder -->
        <ActionAccordionGroup :noPadding="false" value="bluebird-scholarship-holder" :headerColor="'light'">
          <template v-slot:header>
            <p style="color: #888888"><b>9. Bluebird Scholarship holder</b></p>
          </template>
          <div></div>
        </ActionAccordionGroup>


        <!-- Other: Mock JUPAS / UCircle, etc -->
        <!-- 20250212: TMP not show for now -->
        <ActionAccordionGroup :noPadding="false" value="Other" :headerColor="'light'">
          <template v-slot:header>
            <p style="color: #888888"><b>Other</b></p>
          </template>
          <home-action-item @click="openServiceModal('lms-scholarship', getSessionById('e4c145b9'), true)" :isLesson="true"
                            title="HK$300k scholarship" :isDisabled="!user.isAdmin && Number(user.yearDSE) >= getF5YearDSE()"></home-action-item>
          <!--<home-action-item title="University-connected student club: Bluebird Seed" :isLesson="true" @click="openWorkEventsModal('c3faecaac')"
                            v-if="user.isAdmin || user.secondarySchoolStudent?.group == 'ucircle'"></home-action-item>
          <home-action-item :noIndent="true" title="AI images for professions" @click="openAIProfessionImageModal()"></home-action-item>-->
        </ActionAccordionGroup>
      </ion-card-content>
    </ion-card>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue';

// icons
import { personCircle, compass, arrowForward, navigate, settingsOutline, checkmark, arrowBack,
        add, close, checkmarkCircle, alertCircle, pencil, peopleOutline, qrCodeOutline,
        createOutline, calendarOutline, calendarClearOutline, play, } from 'ionicons/icons';

// components
import { IonPage, IonToolbar, IonContent, IonModal, IonHeader, IonTitle,
        IonCard, IonCardTitle, IonCardSubtitle, IonCardHeader, IonCardContent, IonSegment, IonSegmentButton,
        IonGrid, IonCol, IonRow, IonSpinner, IonNote, IonButtons, IonButton, IonIcon,
        IonBadge, IonChip, IonList, IonItem, IonLabel, IonAccordion, IonAccordionGroup,
        loadingController, modalController, toastController, } from '@ionic/vue';
import ActionAccordionGroup from '@/components/shared/ActionAccordionGroup.vue';
import ParentConsentSignatureModal from '@/components/modals/ParentConsentSignatureModal.vue';

// Secondary School
import AchieveJUPASResultPageModal from '@/components/achievejupas/AchieveJUPASResultPageModal.vue';
import AB4ProfessionResultPageModal from '@/components/secondary/AB4ProfessionResultPageModal.vue';
import AB4DisciplineResultPageModal from '@/components/secondary/AB4DisciplineResultPageModal.vue';
import WorkEventsModal from '@/components/secondary/events/WorkEventsModal.vue';
import SLPModal from '@/components/secondary/ABSLPModal.vue';
import ABProfessionSelectModal from '@/components/pss/profession/ABProfessionSelectModal.vue';

// types
import { Session, Discipline, Profession, Program, Step1Option, User, Service, } from '@/types';

// composables
import { useI18n } from 'vue-i18n';
import { useStore } from '@/store';
import { utils } from '@/composables/utils';

// services
import ABSService from '@/services/ABSService';

// methods
const { t } = useI18n();
const store = useStore();
const { openImageModal, openModal, getF3YearDSE, getF4YearDSE, getF5YearDSE, getF6YearDSE, promptRollCallCode,
        getProxyImgLink, getQRCodeUrl, copyText, openServiceModal, } = utils();

// state variables
const loadingData = computed(() => store.state.loadingUser || store.state.loadingData);
const loadingPortalData = computed(() => store.state.loadingUser || store.state.loadingData || store.state.loadingPortalData);
const settings = computed(() => store.state.settings);
const user = computed(() => store.state.user);
const userRelatedSchool = computed(() => store.getters.userRelatedSchool);

// Work Services / Sessions
const workServices = computed<Service[]>(() => store.getters.getServicesByTypes(["Work"]));
const workVisitEvents = computed<Session[]>(() => store.getters.getSessionsByAnchorEventId('work-campus-visit'));
const workWorkshopEvents = computed<Session[]>(() => store.getters.getSessionsByAnchorEventId('work-workshop-2'));

const getServices = (targetNature: any = null) => {
  const yearDSE = Number(user.value.yearDSE);
  const targetForms = (yearDSE == getF6YearDSE() ? ['F.6', 'F.5'] : (yearDSE == getF4YearDSE() ? ['F.4'] : ['F.5']));
  const targetServices = (workServices.value || []).filter(s => {
    if (targetNature && s.nature != targetNature) return false; // Workshop / Visit
    if (store.getters.getLastAppliedSessionByServiceId(s.id)) return true; // contain applied session
    if (s.status == 'Inactive' || !s.nature) return false;
    
    // only include services related to the form
    if (targetForms && s.targetForms && s.targetForms.length > 0 && !s.targetForms.some(f => targetForms.includes(f))) return false;
    
    // skip because nominated by teachers (TBC: align layout with teachers?)
    if (['Talk at your school', 'Video'].includes(s.nature)) return false;

    return s.targetSchoolBands.length == 0 || userRelatedSchool.value.id == 'beacon1' ||
            s.targetSchoolBands.includes(userRelatedSchool.value.band);
  }).map(s => {
    s.lastAppliedSession = store.getters.getLastAppliedSessionByServiceId(s.id); // SLP visit / workshop
    return s;
  });
  return targetServices;
};
const getWorkshopServicesByUserLevel = (userLevel) => {
  const services = getServices('Workshop');
  const isShortlisted = (lastAppliedSession) => {
    if (!lastAppliedSession) return false;
    const { interviewStatus, response } = lastAppliedSession.userResponse;
    return interviewStatus?.includes('Accepted') || ['Attended', 'Confirmed'].includes(response);
  }
  switch (userLevel) {
    case 'eligible-work-applicant': 
      return services.filter(s => s.status != 'Inactive' && !isShortlisted(s.lastAppliedSession));
    case 'shortlisted-work-applicant':
      return services.filter(s => isShortlisted(s.lastAppliedSession));
  }
  return services;
}

/**
 * INIT
 */
const isUserAttendedLesson = (lessonId) => (user.value.sessionResponses?.find(r => (r.lessonId == lessonId && r.attended == 'Yes')));

// modals
const openSLPModal = async (isPreview = false) => {
  const ev = store.getters.getLastAttendedSession;
  return await openModal(SLPModal, { isPreview, ev, serviceId: ev?.serviceId, relatedProgramId: ev?.relatedProgramId, relatedClientId: user.value.clientId }, "", false)
};

// Other
const getSessionById = (sessionId: any) => (store.getters.getSessionById(sessionId));
const getServiceName = (service) => {
  const { lastAppliedSession, name } = service;
  return lastAppliedSession ? `${name} (${lastAppliedSession.date} ${lastAppliedSession.startTime})` : name;
};
const getServicesByIds = (ids) => (workServices.value || []).filter(s => ids.includes(s.id)); // TMP: for demo

// Videos
const openVideoParentConsentModal = (videoId) => {
  return openModal(ParentConsentSignatureModal, { videoId, target: 'video' });
}
</script>

<style scoped>
  ion-icon[slot="start"] {
    margin: 2px;
  }
  ion-card {
    margin-inline: 1px;
  }
  ion-item::part(native) {
    padding-inline-start: 0;
  }
</style>