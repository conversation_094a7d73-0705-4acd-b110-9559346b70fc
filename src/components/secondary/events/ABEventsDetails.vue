<template>
  <ion-header v-if="showHeader">
    <ion-toolbar>
      <ion-buttons slot="start">
        <ion-button slot="icon-only" @click="closeModal()"><ion-icon :icon="arrowBack"></ion-icon></ion-button>
      </ion-buttons>
      <ion-title style="padding-left: 0">
        <ion-label class="ion-text-wrap">
          <!--<p>{{ targetAnchorEvId == 'abs-session1' ? 'Lesson 1' : 'Lesson 2' }}</p>-->
          <p>{{ lesson.lessonShortName || lesson.lessonDisplayName }}</p>
        </ion-label>
      </ion-title>
      <ion-select fill="outline" interface="popover" slot="end" v-if="userSchoolAB4Intakes.length > 0"
                  style="min-width: 130px" v-model="selectedIntakeId">
        <ion-select-option v-for="intake in userSchoolAB4Intakes" :key="intake.id" :value="intake.id">
          {{ intake.academicYear }}
        </ion-select-option>
      </ion-select>
    </ion-toolbar>
  </ion-header>

  <!--
    Teacher View
  -->
  <!-- Event Date & Time -->
  <ion-card v-for="ev in getFilteredSessions()" :key="ev.id" :disabled="isPastEvent(ev)">
    <ion-card-content>
      <ion-row>
        <!-- Session date -->
        <ion-col class="ion-text-left" size="3">On</ion-col>
        <ion-col class="ion-text-left" size="9">
          <ion-datetime-button :datetime="`${ev.id}_date`" style="font-size: 14px; justify-content: left"></ion-datetime-button>
          <ion-modal class="datetime-modal" :keep-contents-mounted="true">
            <ion-datetime presentation="date" :id="`${ev.id}_date`" v-model="ev.date" :showDefaultButtons="true"></ion-datetime>
          </ion-modal>
        </ion-col>

        <!-- Venue & Embedded Maps -->
        <ion-col class="ion-text-left" size="3">Venue</ion-col>
        <ion-col class="ion-text-left" size="9">
          <ion-input mode="ios" label-placement="fixed" v-model="ev.venue"></ion-input>
        </ion-col>

        <iframe v-if="ev.schoolAddrGmapEmbedLink && !isPastEvent(ev)" :src="ev.schoolAddrGmapEmbedLink"
                class="responsive-embed" width="100%" height="250" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>

        <!-- Number of students -->
        <ion-col class="ion-text-left" size="3"># students</ion-col>
        <ion-col class="ion-text-left" size="9">
          <ion-input mode="ios" type="number" inputmode="numeric" v-model="ev.numOfStudents" fill="outline"></ion-input>
        </ion-col>

        <!-- Student Forms -->
        <ion-col class="ion-text-left" size="3">Form(s)</ion-col>
        <ion-col class="ion-text-left" size="9">
          <ion-input mode="ios" v-model="ev.studentForms" fill="outline"></ion-input>
        </ion-col>

        <!-- Class(es) -->
        <ion-col class="ion-text-left" size="3">Class(s)</ion-col>
        <ion-col class="ion-text-left" size="9">
          <ion-input mode="ios" v-model="ev.classes" fill="outline"></ion-input>
        </ion-col>
        
        <!-- Arrival Time -->
        <ion-col class="ion-text-left" size="3">Arrival & standby</ion-col>
        <ion-col class="ion-text-left" size="9">
          <ion-input mode="ios" type="time" v-model="ev.arrivalTime" fill="outline"></ion-input>
        </ion-col>

        <!-- Venue preparation -->
        <ion-col class="ion-text-left" size="3">Venue preparation</ion-col>
        <ion-col class="ion-text-left" size="9">
          <ion-input mode="ios" type="time" v-model="ev.roomAvailableTime" fill="outline"></ion-input>
        </ion-col>

        <!-- Class Start -->
        <ion-col class="ion-text-left" size="3">Class start</ion-col>
        <ion-col class="ion-text-left" size="9">
          <ion-input mode="ios" type="time" v-model="ev.startTime" fill="outline"></ion-input>
        </ion-col>

        <!-- Class End -->
        <ion-col class="ion-text-left" size="3">Class end</ion-col>
        <ion-col class="ion-text-left" size="9">
          <ion-input mode="ios" type="time" v-model="ev.endTime" fill="outline"></ion-input>
        </ion-col>

        <ion-col>
          <p v-if="suggestedDuration">Suggested duration: {{ suggestedDuration }}+ mins</p>
          <p>If availability allows we are happy to arrive 45 min before class start to ensure seating is correct as this critically determines the learners' experience.</p>
        </ion-col>
      </ion-row>
    </ion-card-content>

    <!--<ion-button color="success" expand="block" :disabled="getUpdatedSessions().length == 0" @click="saveUpdatedSessions()">-->
    <ion-button color="success" expand="block" @click="saveUpdatedSessions([ev])">
      Save
    </ion-button>
  </ion-card>

  <!-- Seating Plan -->
  <div class="ion-text-center">
    <!--<span><u>Grouping remarks</u></span>
    <div class="divider"></div>-->
    <img style="width: 100%" :src="getSeatingPlanImgLink()" @click="openImageModal(getSeatingPlanImgLink())" />
  </div>
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, watch, reactive, onMounted } from 'vue';

// icons
import { add, close, checkmark, arrowUp,  arrowForward, arrowBack, trashOutline, } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
        IonItem, IonLabel, IonIcon, IonButtons, IonButton,
        IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption,
        IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid, IonCheckbox,
        IonInput, IonDatetimeButton, IonDatetime, IonModal,
        loadingController, modalController, } from '@ionic/vue';

// composables / services
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';

// types
import { User, Session, Lesson, } from '@/types';
import { alertController } from '@ionic/core';
import TeacherService from '@/services/TeacherService';

export default defineComponent({
  props: ["targetAnchorEvId", "showHeader", "suggestedDuration", "ab4IntakeId"],
  components: { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
                IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonChip, IonText, IonGrid, IonCheckbox,
                IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption,
                IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonCardTitle,
                IonInput, IonDatetimeButton, IonDatetime, IonModal, },
  setup(props) {
    // methods or filters
    const store = useStore();
    const { openModal, presentPrompt, getProxyImgLink, openImageModal, presentToast, formatDate } = utils();
    const { t } = useI18n();

    //const selectedABSEventFilter = ref("all");
    const user = computed(() => store.state.user);
    const isAB4User = computed(() => store.getters.isAB4User);
    const lesson = computed<Lesson>(() => store.getters.getLessonByAnchorEventId(props.targetAnchorEvId));
    const userABSIntakeSessions = computed<Session[]>(() => store.getters.userABSIntakeSessions);
    const userSchoolAB4Intakes = computed(() => store.getters.userSchoolAB4Intakes);
    const selectedIntakeId = ref(userSchoolAB4Intakes.value[0]?.id);
    const getFilteredSessions = () => {
      const { targetAnchorEvId, ab4IntakeId } = props;
      const checkIntakeId = selectedIntakeId.value || ab4IntakeId;
      const res = userABSIntakeSessions.value.filter(s => (s.anchorEventId == targetAnchorEvId && (!checkIntakeId || checkIntakeId == s.absIntakeId)));
      //return res.map(s => ({ ...s }));
      for (const ev of res) ev.date = ev.date ? formatDate(ev.date, "YYYY-MM-DD") : null;
      return res;
    }

    const getUpdatedSessions = () => {
      // Only update sessions with value updated
      const checkFields = ["date", "venue", "numOfStudents", "studentForms", "classes", "arrivalTime", "roomAvailableTime", "startTime", "endTime"];
      const updatedSessions: any = [];
      for (const fs of getFilteredSessions()) {
        const originalSession = userABSIntakeSessions.value.find(s => s.id == fs.id) || {};
        for (const field of checkFields) {
          if (fs[field] != originalSession[field]) {
            updatedSessions.push(fs);
            break;
          }
        }
      }
      return updatedSessions;
    }
    const presentSavePrompt = async (callback) => {
      // prompt save or not before leave
      const alert = await alertController.create({
        message: "Save before leaving?",
        buttons: [
          {
            text: t('no'),
            handler: () => {
              modalController.dismiss({});
              return true;
            },
          },
          {
            text: t('yes'),
            handler: callback,
          }
        ]
      });
      return alert.present();
    }
    const saveUpdatedSessions = async (targetSessions = null) => {
      //const updatedSessions = getUpdatedSessions();
      const updatedSessions = targetSessions || getFilteredSessions();
      const loading = await loadingController.create({ });
      await loading.present();
      for (const s of updatedSessions) {
        const teacherResponse = {
          eventName: s.displayName,
          estNumOfStudents: s.numOfStudents,
          preferredSessionDates: s.date,
          preferredSessionTime: s.startTime,
          preferredVenue: s.venue,
          studentForms: s.studentForms,
          classes: s.classes,
          arrivalTime: s.arrivalTime,
          roomAvailableTime: s.roomAvailableTime,
          endTime: s.endTime,
          response: "Change Request",
        };
        TeacherService.upsertTeacherResponse(s, "", teacherResponse, user.value.phone, user.value.schoolId, user.value.id);
      }
      store.commit('upsertSessions', updatedSessions);
      presentToast('Your change request has been submitted successfully.')
      loading.dismiss();
    }
    const closeModal = async () => {
      const updatedSessions = getUpdatedSessions();
      if (updatedSessions.length > 0) {
        presentSavePrompt(async () => {
          await saveUpdatedSessions();
          await modalController.dismiss({}); // nothing changed, leave directly
        });
      } else {
        await modalController.dismiss({}); // nothing changed, leave directly
      }
    }

    // return variables & methods to be used in template HTML
    return {
      // icons
      add, close, checkmark, arrowUp, arrowForward, arrowBack,
      trashOutline,

      // variables
      user,
      isAB4User,
      lesson,
      userSchoolAB4Intakes, selectedIntakeId,

      // methods
      t,
      closeModal,
      getProxyImgLink,
      openImageModal,
      saveUpdatedSessions,
      getUpdatedSessions,
      getFilteredSessions,
      getSeatingPlanImgLink: () => {
        const { targetAnchorEvId: id } = props;
        const slideId = id == 'abs-session1' ? 'g24fff357604_0_102' : 'g2e993164d1c_2_49';
        return getProxyImgLink(`https://docs.google.com/presentation/d/1B5qHJu7ZV72CUOMD4VSoJ2Ptg82prAM0t1hnzxmrp0c/export/jpeg?id=1B5qHJu7ZV72CUOMD4VSoJ2Ptg82prAM0t1hnzxmrp0c&pageid=${slideId}`);
      },
      isPastEvent: (ev: any) => {
        return ev.userResponse?.attended != 'Yes' && new Date().getTime() > new Date(ev.endTimeStr).getTime()+60*60000;
      },
      submitForm: () => {
        presentPrompt(t('confirmSubmit'), () => {
          closeModal();
        });
      },
    }
  },
});
</script>

<style scoped>
  ion-input {
    min-height: 20px !important;
    --color: var(--ion-color-dark);
  }
  .divider {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
  }
</style>