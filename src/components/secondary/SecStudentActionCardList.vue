<template>
  <div class="spin" v-if="loadingData">
    <ion-spinner></ion-spinner>
  </div>

  <div v-else>
    <!--
      AchieveJUPAS / Resources
      -->
    <ion-toolbar class="sticky-toolbar">
      <ion-segment class="view-type-segment" mode="ios" v-model="selectedViews.type" scrollable>
        <ion-segment-button value="Sharing" class="short-segment-btn">
          <ion-icon :icon="qrCodeOutline"></ion-icon>
        </ion-segment-button>
        <ion-segment-button value="resources" class="short-segment-btn">
          <ion-label class="no-text-transform" style="font-size: 2em">😍</ion-label>
        </ion-segment-button>
        <ion-segment-button value="AchieveJUPAS">
          <ion-label class="no-text-transform" style="line-height: 1.5"><b>AchieveJUPAS</b><br />2.0 (beta)</ion-label>
        </ion-segment-button>
        <ion-segment-button value="school-arranged">
          <ion-label class="no-text-transform" style="line-height: 1.5">School<br />arranged</ion-label>
        </ion-segment-button>
        <ion-segment-button value="sndas" disabled>
          <ion-label class="no-text-transform">SNDAS</ion-label>
        </ion-segment-button>
        <ion-segment-button value="overseas-study" disabled>
          <ion-label class="no-text-transform">Overseas Study</ion-label>
        </ion-segment-button>
        <ion-segment-button value="e-app" disabled>
          <ion-label class="no-text-transform">E-App</ion-label>
        </ion-segment-button>
      </ion-segment>
    </ion-toolbar>

    <!--
      Share this resofurce with classmates (secondary school)
      -->
    <div v-if="selectedViews.type == 'Sharing'">
      <ion-card class="class-bar" style="margin-top: 0; margin-bottom: 0">
        <ion-card-content style="padding: 0">
          <ion-accordion-group value="Sharing">
            <ion-accordion style="--min-height: 24px; padding-left: 12px" value="Sharing">
              <ion-item lines="full" slot="header">
                <ion-label style="margin: 4px 8px 4px 12px"><p style="font-weight: 500"><b>Share this tool with my classmates</b></p></ion-label>
              </ion-item>

              <ion-list class="ion-text-center" slot="content">
                <p><img style="width: 300px" :src="getQRCodeUrl(`https://ab.fdmt.hk/s/${user.schoolId}`)" /></p>
                <p>ab.fdmt.hk/s/{{user.schoolId}}</p>
                <ion-button color="success" size="small" @click="copyText(`https://ab.fdmt.hk/s/${user.schoolId}`)">
                  Copy
                </ion-button>
              </ion-list>
            </ion-accordion>
          </ion-accordion-group>
        </ion-card-content>
      </ion-card>
    </div>

    <div v-else-if="selectedViews.type === 'resources'">
      <SecStudentActionHeartEyeEmoji></SecStudentActionHeartEyeEmoji>
    </div>

    <!-- School-arranged (visits / 30-min Zoom talks) -->
    <div v-else-if="selectedViews.type == 'school-arranged'">
      <!--
        Upcoming Events (Visit)
      -->
      <div>
        <home-action-header title="University Visits"></home-action-header>
        <div v-for="ev in workVisitEvents" :key="ev.id">
          <home-action-item @click="openServiceModal(ev.serviceId, ev, true)" :isLesson="true" :title="`${ev.name} (${ev.date} ${ev.startTime})`"
                            v-show="user.isAdmin || ev.userResponse || ev.clientSchoolId == user.schoolId"></home-action-item>
          <event-card v-if="ev.userResponse" :ev="ev" :user="user" :hideEventDetails="true" :hideApplyButton="true"
                      style="margin: 0; padding-left: 32px; box-shadow: none"></event-card>
        </div>
      </div>

      <!--
        AB4 (for specific schools only)
        -->
      <ion-card style="margin-top: 0" v-if="(user.teacher || isAB4User) && user.yearDSE <= getF4YearDSE()">
        <home-action-header title="AB4"></home-action-header>
        <ion-card-content style="padding: 0">
          <!-- AB4 L1 -->
          <ion-accordion-group value="L1">
            <ion-accordion style="--min-height: 24px" value="L1">
              <ion-item class="ion-no-padding" lines="full" slot="header">
                <ion-icon slot="start" :icon="null"></ion-icon>
                <ion-label><span><b>Lesson 1</b></span></ion-label>
              </ion-item>
              <ion-list class="ion-no-padding" slot="content">
                <event-card v-for="ev in filteredAB4Sessions('abs-session1')" :key="ev.id" :ev="ev" :user="user" v-show="ev.formattedDateTime && isShowEvent(ev)"
                            :showStudentFormsOnly="true" :useAccordionView="true"></event-card>
              </ion-list>
            </ion-accordion>
          </ion-accordion-group>

          <home-action-item title="Target 1 among 400+ professions" :isDone="isSelectedProfessions() && userChoices.step1ProfessionReason && userChoices.step1ProfessionAction"
                            @click="openProfessionSelectModal()" :isLoading="loadingPortalData"></home-action-item>
                            <!--v-if="isUserAttendedLesson('abs-session1')"></home-action-item>-->

          <!-- AB4 L2 (to be removed & merged to L1) -->
          <ion-accordion-group value="ab4-l2">
            <ion-accordion style="--min-height: 24px" value="ab4-l2">
              <ion-item class="ion-no-padding" lines="full" slot="header">
                <ion-icon slot="start" :icon="null"></ion-icon>
                <ion-label><span><b>Lesson 2</b></span></ion-label>
              </ion-item>
              <ion-list class="ion-no-padding" slot="content">
                <event-card v-for="ev in filteredAB4Sessions('abs-session2')" :key="ev.id" :ev="ev" :user="user" v-show="ev.formattedDateTime && isShowEvent(ev)"
                            :showStudentFormsOnly="true" :useAccordionView="true"></event-card>
              </ion-list>
            </ion-accordion>
          </ion-accordion-group>

          <home-action-item title="Choose 1-4 among 140+ disciplines" :isDone="isSelectedDisciplines()" @click="openDisciplineSelectModal()"
                            v-if="user.isAdmin || isUserAttendedLesson('abs-session2')"></home-action-item>
        </ion-card-content>
      </ion-card>
       
      <!-- AB3 -->
      <ion-card style="margin-top: 0" v-if="user.isAdmin || ((user.teacher || isAB4User) && user.yearDSE <= getF4YearDSE())">
        <home-action-header title="AB3"></home-action-header>
        <ion-card-content style="padding: 0">
          <ion-accordion-group value="ab3-session1">
            <ion-accordion style="--min-height: 24px" value="L1">
              <ion-item class="ion-no-padding" lines="full" slot="header">
                <ion-icon slot="start" :icon="null"></ion-icon>
                <ion-label><span><b>Parent talk</b></span></ion-label>
              </ion-item>
              <ion-list class="ion-no-padding" slot="content">
                <event-card v-for="ev in filteredAB4Sessions('ab3-session1')" :key="ev.id" :ev="ev" :user="user" v-show="ev.formattedDateTime && isShowEvent(ev)"
                            :showStudentFormsOnly="true" :useAccordionView="true"></event-card>
              </ion-list>
            </ion-accordion>
          </ion-accordion-group>

          <home-action-item title="AchieveElective (beta)" @click="openAB3Modal()" :isLoading="loadingPortalData"></home-action-item>
        </ion-card-content>
      </ion-card>

      <!-- AI Chatbot (for AI competition)
       TODO: enabled in school-level? Or after taking attendance for specific sessions?
      <ion-accordion-group value="Other">
        <ion-accordion style="--min-height: 24px" value="Other">
          <ion-item lines="full" slot="header" color="light">
            <ion-label style="padding-left: 12px"><p style="color: #888888"><b>Other</b></p></ion-label>
          </ion-item>
          <div slot="content">
            <home-action-item :noIndent="true" title="AI images for professions" @click="openAIProfessionImageModal()"></home-action-item>
          </div>
        </ion-accordion>
      </ion-accordion-group>
      -->
    </div>

    <!-- Default: AchieveJUPAS -->
    <div v-else class="achieve-jupas-wrapper">
      <AchieveJUPASResultPageModal :hideTitle="true"></AchieveJUPASResultPageModal>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue';

// icons
import { personCircle, compass, arrowForward, navigate, settingsOutline, checkmark, arrowBack,
        add, close, checkmarkCircle, alertCircle, pencil, peopleOutline, qrCodeOutline,
        createOutline, calendarOutline, calendarClearOutline, play, } from 'ionicons/icons';

// components
import { IonPage, IonToolbar, IonContent, IonModal, IonHeader, IonTitle,
        IonCard, IonCardTitle, IonCardSubtitle, IonCardHeader, IonCardContent, IonSegment, IonSegmentButton,
        IonGrid, IonCol, IonRow, IonSpinner, IonNote, IonButtons, IonButton, IonIcon,
        IonBadge, IonChip, IonList, IonItem, IonLabel, IonAccordion, IonAccordionGroup,
        loadingController, modalController, toastController, } from '@ionic/vue';
import ActionAccordionGroup from '@/components/shared/ActionAccordionGroup.vue';

// Secondary School
import AchieveJUPASResultPageModal from '@/components/achievejupas/AchieveJUPASResultPageModal.vue';
import AB4ProfessionResultPageModal from '@/components/secondary/AB4ProfessionResultPageModal.vue';
import AB4DisciplineResultPageModal from '@/components/secondary/AB4DisciplineResultPageModal.vue';
import ABProfessionSelectModal from '@/components/pss/profession/ABProfessionSelectModal.vue';
import SecStudentActionHeartEyeEmoji from '@/components/secondary/SecStudentActionHeartEyeEmoji.vue';

// types
import { Session, Discipline, Profession, Program, Step1Option, User, Service, } from '@/types';

// composables
import { useI18n } from 'vue-i18n';
import { useStore } from '@/store';
import { utils } from '@/composables/utils';

// services
import ABSService from '@/services/ABSService';

// methods
const { t } = useI18n();
const store = useStore();
const { openImageModal, openModal, getF3YearDSE, getF4YearDSE, getF5YearDSE, getF6YearDSE, promptRollCallCode,
        getProxyImgLink, getQRCodeUrl, copyText, openServiceModal, } = utils();

// state variables
const loadingData = computed(() => store.state.loadingUser || store.state.loadingData);
const loadingPortalData = computed(() => store.state.loadingUser || store.state.loadingData || store.state.loadingPortalData);
const settings = computed(() => store.state.settings);
const user = computed(() => store.state.user);
const userRelatedSchool = computed(() => store.getters.userRelatedSchool);

// View switcher
const selectedViews = reactive({
  form: "F5-6",
  type: "AchieveJUPAS",
}); // mainly for switching between AchieveJUPAS & Resources

// Work Services / Sessions
const workVisitEvents = computed<Session[]>(() => store.getters.getSessionsByAnchorEventId('work-campus-visit'));


/**
 * AB3 / AB4
 */
const userABSIntakeSessions = computed<Session[]>(() => store.getters.userABSIntakeSessions);
const isAB4User = computed(() => store.getters.isAB4User);
const userChoices = reactive({
  chosenDisciplines: [] as Discipline[],
  step1ProfessionReason: "",
  step1ProfessionAction: "",
  step2DisciplineReason: "",
  step2DisciplineAction: "",
  chosenProfessions: [] as Profession[],
  chosenOptions: [] as Step1Option[],
});
const getUserChoiceUpdatingObj = () => {
  const { chosenOptions, chosenProfessions, step1ProfessionReason, step1ProfessionAction, step2DisciplineReason, step2DisciplineAction, chosenDisciplines, } = userChoices;
  return {
    step1OptionIds: chosenOptions.map(o => o.id).join(" , "),
    step1OrderedProfessionIds: chosenProfessions.map(p => p.id).join(" , "),
    step1ProfessionReason: step1ProfessionReason,
    step1ProfessionAction: step1ProfessionAction,
    step2DisciplineIds: chosenDisciplines.map(d => d.id).join(" , "),
    step2DisciplineReason: step2DisciplineReason,
    step2DisciplineAction: step2DisciplineAction,
  };
}
const someAnswersUpdated = () => {
  const updatingObj = getUserChoiceUpdatingObj();
  return Object.keys(updatingObj).some(field => (user.value[field] != updatingObj[field]));
};
const saveUserChoices = async (noLoading = false) => {
  if (someAnswersUpdated()) {
    const loading = await loadingController.create({});
    if (!noLoading) await loading.present();

    // Update DB
    const updatingObj = getUserChoiceUpdatingObj();
    ABSService.saveUserChoices(updatingObj);

    // Update Store
    const { chosenOptions, chosenProfessions, step1ProfessionReason, step1ProfessionAction, step2DisciplineReason, step2DisciplineAction, chosenDisciplines, } = userChoices;
    store.commit('updateUser', {
      ...updatingObj,
      lastSelectedStep1Options: chosenOptions.slice(),
      lastSelectedProfessions: chosenProfessions.slice(),
      step1ProfessionReason: step1ProfessionReason,
      step1ProfessionAction: step1ProfessionAction,
      step2DisciplineReason: step2DisciplineReason,
      step2DisciplineAction: step2DisciplineAction,
      lastSelectedDisciplines: chosenDisciplines.slice(),
    });
    loading.dismiss();
    if (!noLoading) {
      const toast = await toastController.create({
        message: t('successSave'),
        duration: 3000,
        position: 'top',
      });
      toast.present();
    }
  }
};

/**
 * Professions
 */
const openProfessionSelectModal = async () =>  {
  const modal = await modalController.create({
    cssClass: 'tall-modal',
    component: AB4ProfessionResultPageModal,
    componentProps: {
      prefilledProfessions: userChoices.chosenProfessions.slice(),
      oldUserProfessions: user.value.userProfessions?.slice() || [],
    },
    backdropDismiss: false,
  });
  modal.onDidDismiss().then(({ data }) => {
    if (data && data.selectedProfessions) {
      // Chosen professions
      userChoices.step1ProfessionReason = data.selectedProfessions[0]?.reason || "";
      userChoices.step1ProfessionAction = data.selectedProfessions[0]?.action || "";
      userChoices.chosenProfessions = data.selectedProfessions;
      saveUserChoices(data.noLoading);

      // User profession reactions
      ABSService.upsertUserProfessions(data.userProfessions);
      store.commit('updateUser', { userProfessions: data.userProfessions });
    }
  });
  return modal.present();
}

/**
 * Disciplines
 */
const openDisciplineSelectModal = async () =>  {
  const modal = await modalController.create({
    cssClass: 'tall-modal',
    component: AB4DisciplineResultPageModal,
    componentProps: {
      prefilledDisciplines: userChoices.chosenDisciplines.slice(),
      targetProfessions: userChoices.chosenProfessions.slice(0, 5),
      oldUserDisciplines: user.value.userDisciplines?.slice() || [],
    }
  });
  modal.onDidDismiss().then(({ data }) => {
    if (data && data.chosen) {
      // Chosen disciplines
      userChoices.step2DisciplineReason = data.chosen[0]?.reason || "";
      userChoices.step2DisciplineAction = data.chosen[0]?.action || "";
      userChoices.chosenDisciplines = data.chosen;
      saveUserChoices(data.noLoading);

      // User discipline reactions
      ABSService.upsertUserDisciplines(data.userDisciplines);
      store.commit('updateUser', { userDisciplines: data.userDisciplines });
    }
  });
  return modal.present();
}

/**
 * INIT
 */
const loadSavedUserChoices = (user: User) => {
  if (user.id) {
    userChoices.chosenOptions = user.lastSelectedStep1Options || [];
    userChoices.chosenProfessions = user.lastSelectedProfessions || [];
    userChoices.step1ProfessionReason = user.step1ProfessionReason || "";
    userChoices.step1ProfessionAction = user.step1ProfessionAction || "";
    userChoices.chosenDisciplines = user.lastSelectedDisciplines || [];
    userChoices.step2DisciplineReason = user.step2DisciplineReason || "";
  }
}
onMounted(() => {
  loadSavedUserChoices(user.value);
});
watch(loadingPortalData, () => { // triggered only when direct access to this page
  loadSavedUserChoices(user.value);
});

const isUserAttendedLesson = (lessonId) => (user.value.sessionResponses?.find(r => (r.lessonId == lessonId && r.attended == 'Yes')));

// AB3 / AB4 Functions
const filteredAB4Sessions = (targetAnchorEvId) => (userABSIntakeSessions.value.filter(s => s.anchorEventId == targetAnchorEvId));
const isShowEvent = (ev: Session) => {
  const evStart = new Date(ev.startTimeStr);
  return (evStart > new Date(+new Date() - 5*86400000) && evStart < new Date(+new Date() + 30*86400000)) || ev.userResponse?.confirmed == "Yes"
};
const openAB3Modal = async () =>  {
  const modal = await modalController.create({
    cssClass: 'tall-modal',
    component: AchieveJUPASResultPageModal,
    componentProps: { isAB3: true, }
  });
  return modal.present();
};
const isSelectedProfessions = () => (userChoices.chosenProfessions.length > 0);
const isSelectedDisciplines = () => (userChoices.chosenDisciplines.length > 0);

// AI Chatbot (for AI competition - image generation)
const openAIProfessionImageModal = () => {
  //return openModal(ChatbotModal, { isImageBot: true }); // image bot
  return openModal(ABProfessionSelectModal, { isAIImageCompetition: true, }); // image bot
}
</script>

<style scoped>
  ion-icon[slot="start"] {
    margin: 2px;
  }
  ion-card {
    margin-inline: 1px;
  }
  ion-item::part(native) {
    padding-inline-start: 0;
  }

  .view-type-segment ion-segment-button {
    min-width: 50px;
    --indicator-color: var(--ion-color-primary);
    --color-checked: #fff;
    --border-radius: 20px;
  }
</style>