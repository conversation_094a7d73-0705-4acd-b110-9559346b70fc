<template>
  <ion-header>
    <ion-toolbar>
      <ion-title>
        <ion-label>
          <p v-if="discipline">
            Electives related to <b>{{ discipline.name }}</b>
          </p>
          <h2 style="font-size: 20px" v-else>
            Electives
          </h2>
        </ion-label>
      </ion-title>
      <ion-buttons slot="start">
        <ion-button slot="icon-only" @click="closeModal()"><ion-icon :icon="close"></ion-icon></ion-button>
      </ion-buttons>
    </ion-toolbar>
  </ion-header>

  <ion-content :fullscreen="true" style="min-height: 150px">

    <div class="spin" v-if="loading">
      <ion-spinner></ion-spinner>
    </div>

    <ion-list v-else>       
      <ion-item lines="full" v-for="elective in discipline.relatedElectives" :key="elective.id"
                style="--min-height: 55px">
        <ion-label>
          <h2 style="white-space: pre-line">{{ elective.name }}</h2>
        </ion-label>
      </ion-item>

      <div class="ion-text-center" v-show="discipline.relatedElectives.length == 0">
        <p>No elective information</p>
      </div>
    </ion-list>
  </ion-content>
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, watch, reactive, onMounted } from 'vue';

// icons
import { add, close, checkmark, arrowUp,  arrowForward, arrowBack, openOutline, trashOutline, } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonGrid, IonRow, IonCol,
        IonList, IonItem, IonLabel, IonIcon, IonButtons, IonButton,
        IonSearchbar, IonSegment, IonSegmentButton, IonSpinner, } from '@ionic/vue';

// composables / services
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';

export default defineComponent({
  name: 'ListElectiveModal',
  props: ["discipline"],
  components: { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonGrid, IonRow, IonCol,
                IonList, IonItem, IonLabel, IonIcon, IonButtons, IonButton,
                IonSearchbar, IonSegment, IonSegmentButton, IonSpinner, },
  setup(props) {
    // methods or filters
    const store = useStore();
    const { closeModal, infiniteScrollLoadData, } = utils();
    const { t } = useI18n();

    const loading = computed(() => store.state.loadingData);

    // 3. return variables & methods to be used in template HTML
    return {
      // icons
      add, close, checkmark, arrowUp, arrowForward, arrowBack, openOutline, trashOutline,

      // variables
      loading,

      // methods
      t,
      closeModal,
    }
  }
});
</script>

<style scoped>
</style>