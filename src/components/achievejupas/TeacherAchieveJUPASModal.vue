<template>
  <ion-header>
    <ion-grid class="ion-no-padding" fixed>
      <div style="border: 1px solid #ccc; border-radius: 18px; margin-top: 8px; padding: 6px">
        <ion-toolbar>
          <ion-buttons slot="start" v-if="!isDemo">
            <ion-button slot="icon-only" @click="closeModal()"><ion-icon :icon="arrowBack"></ion-icon></ion-button>
          </ion-buttons>

          <!-- Search Students -->
          <ion-searchbar style="padding-bottom: 0; height: 28px" mode="ios" v-model="searchKeyword"></ion-searchbar>

          <!-- Report Button -->
          <ion-buttons slot="end">
            <ion-button class="no-text-transform" target="_blank" size="small">
              Score<br />Input
            </ion-button>
            <!-- <small>(F.5 & F.6 Final & Mock)</small> -->
            <!-- Integration with SAMS https://www.edb.gov.hk/tc/sch-admin/admin/sch-it-systems/cloudsams/index.html -->
            <ion-button class="no-text-transform" :href="`https://fdmt.hk/r/${user.schoolId}`" target="_blank" size="small">
              Report
            </ion-button>
          </ion-buttons>
        </ion-toolbar>

        <!-- Filters -->
        <ion-toolbar>
          <!-- Custom Filter Tags -->
          <div style="max-height: 80px; overflow: scroll">
            <template v-if="!isAddingTag">
              <!-- All -->
              <ion-chip class="small-chip" @click="toggleCustomFilter('All')" :class="{ 'active-tag': filters.custom.length == 0 }">
                <ion-label>All</ion-label>
              </ion-chip>

              <!-- Teacher-defined Tags -->
              <ion-chip
                v-for="tag in getUserDefinedTags()" :key="tag.id" class="small-chip"
                @click="toggleCustomFilter(tag.id)"
                :class="{ 'active-tag': filters.custom.includes(tag.id) }"
              >
                <ion-label>{{ tag.name }}</ion-label>
              </ion-chip>

              <ion-button size="small" fill="clear" class="add-tag-btn" color="medium" @click="startAddingTag">
                <ion-icon :icon="addCircleOutline"></ion-icon>
              </ion-button>
              <ion-button size="small" fill="clear" class="add-tag-btn" color="medium" @click="openManageTags">
                <ion-icon :icon="settingsOutline"></ion-icon>
              </ion-button>
            </template>
            <template v-else>
              <div class="tag-input-container">
                <ion-input
                  fill="outline"
                  v-model="newTagName"
                  placeholder="New student group"
                  class="tag-input"
                  @keyup.enter="saveNewTag"
                ></ion-input>
                <ion-button size="small" fill="clear" @click="saveNewTag" :disabled="!canSaveTag">
                  <ion-icon slot="icon-only" :icon="checkmark"></ion-icon>
                </ion-button>
                <ion-button size="small" fill="clear" @click="cancelAddingTag">
                  <ion-icon slot="icon-only" :icon="close"></ion-icon>
                </ion-button>
              </div>
            </template>
          </div>

          <!-- Year DSE Filter -->
          <ion-segment v-model="filters.yearDSE" mode="ios" scrollable>
            <ion-segment-button value="all">All</ion-segment-button>
            <ion-segment-button v-for="year in dseYears" :key="year" :value="year">DSE {{ year }}</ion-segment-button>
          </ion-segment>

          <!-- Class Filter -->
          <ion-segment v-model="filters.studentClass" mode="ios" scrollable style="margin-top: 2px">
            <ion-segment-button value="all">All</ion-segment-button>
            <ion-segment-button v-for="c in allClasses()" :key="c" :value="c">{{ c }}</ion-segment-button>
          </ion-segment>

          <!-- AchieveJUPAS Completion Status -->
          <ion-segment v-model="filters.status" mode="ios" scrollable style="margin-top: 2px">
            <ion-segment-button value="all">All</ion-segment-button>
            <ion-segment-button value="less-than-3-programs">Chose &lt; 3 programs</ion-segment-button>
            <ion-segment-button value="less-than-6-programs">&lt; 6 programs</ion-segment-button>
            <ion-segment-button value="missing-target-scores">Missing Target Scores</ion-segment-button>
            <ion-segment-button value="complete">Completed</ion-segment-button>
          </ion-segment>

          <!-- Parent Consent Filter -->
          <ion-segment v-model="filters.parentConsent" mode="ios" scrollable style="margin-top: 2px" v-if="userRelatedSchool.achievejupasNeedParentConsent">
            <ion-segment-button value="all">All</ion-segment-button>
            <ion-segment-button value="yes">Obtained parent consent</ion-segment-button>
            <ion-segment-button value="no">Not yet obtained</ion-segment-button>
          </ion-segment>

          <!-- Target Score Filter -->
          <div class="score-filter">
            <div class="score-label">
              <ion-select
                interface="popover"
                mode="ios"
                class="prediction-select"
                value="school"
              >
                <ion-select-option value="school">School Prediction</ion-select-option>
                <ion-select-option value="student">Student's Prediction</ion-select-option>
              </ion-select>
            </div>
            <ion-range color="primary" mode="ios" :value="{ lower: 18, upper: 25 }" :min="0" :max="35"
                      :dual-knobs="true" :pin="true" :ticks="true"></ion-range>
          </div>
        </ion-toolbar>
      </div>

      <ion-toolbar>
        <!-- Student Count & Sort -->
        <ion-item color="primary">
          <ion-label>
            <p v-if="isAddingTag">{{ Object.values(selectedStudents).filter(v => v).length }} student{{ Object.values(selectedStudents).filter(v => v).length > 1 ? 's' : '' }} selected</p>
            <p v-else>{{  getSortedStudents.length }} student{{ getSortedStudents.length > 1 ? 's' : '' }}</p>
          </ion-label>

          <ion-buttons slot="end" class="sort-buttons">
            <ion-button size="small" @click="toggleSortDirection">
              <ion-icon size="small" slot="icon-only" :icon="sortConfig.ascending ? arrowUp : arrowDown"></ion-icon>
            </ion-button>
          </ion-buttons>
          <ion-select aria-label="Sort by" slot="end" interface="popover" v-model="sortConfig.field" placeholder="Sort by" class="sort-select">
            <ion-select-option value="class">Class</ion-select-option>
            <ion-select-option value="studentNumber">Student No.</ion-select-option>
            <ion-select-option value="fullName">Name</ion-select-option>
            <ion-select-option value="completion">Completion</ion-select-option>
          </ion-select>
        </ion-item>
      </ion-toolbar>
    </ion-grid>
  </ion-header>

  <ion-content>
    <!-- Student List -->
    <ion-grid class="ion-no-padding" fixed>
      <ion-item v-for="(student, idx) in getSortedStudents" :key="idx" lines="full"
                @click="isAddingTag ? undefined : openUserDetails(student)" button>
        <!-- Checkbox for student selection when adding tag -->
        <ion-checkbox
          v-show="isAddingTag"
          aria-label="Select student"
          slot="start"
          v-model="selectedStudents[student.id]"
        ></ion-checkbox>

        <!-- Student Info -->
        <ion-label class="ion-text-wrap">
          <p>{{ student.class }}{{ formatStudentNumber(student.studentNumber) }} {{ student.fullName }}
            <span v-if="student.chineseName">({{ student.chineseName }})</span>
          </p>
        </ion-label>

        <!-- Student Parent Consent -->
        <ion-chip class="small-chip" v-if="userRelatedSchool.achievejupasNeedParentConsent"
                  :color="hasParentConsent(student) ? 'success' : 'danger'">
          <ion-icon size="small" :icon="hasParentConsent(student) ? checkmark : close"></ion-icon>
          <ion-label>Consent</ion-label>
        </ion-chip>

        <!-- AchieveJUPAS number of programs selected -->
        <ion-badge color="primary" v-if="getStudentProgramCount(student) >= 20">Complete</ion-badge>
        <ion-badge color="warning" v-else>{{ getStudentProgramCount(student) }}/20</ion-badge>

        <!-- Actions
        <ion-buttons slot="end">
          <ion-button @click.stop="exportStudentData(student)">
            <ion-icon slot="icon-only" :icon="downloadOutline"></ion-icon>
          </ion-button>
        </ion-buttons>-->
      </ion-item>
    </ion-grid>

    <!-- Tag Management Modal -->
    <ion-modal :is-open="isManagingTags" @didDismiss="isManagingTags = false">
      <ion-header>
        <ion-toolbar>
          <ion-title>Manage Tags</ion-title>
          <ion-buttons slot="end">
            <ion-button @click="isManagingTags = false">
              <ion-icon slot="icon-only" :icon="close"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content>
        <ion-list>
          <ion-item v-for="tag in getUserDefinedTags()" :key="tag.id" lines="full">
            <ion-label>
              {{ tag.name }} ({{ getNumOfStudentsOfTag(tag.id) }})
            </ion-label>
            <ion-button fill="clear" slot="end" @click="editTag(tag)">
              <ion-icon slot="icon-only" :icon="createOutline"></ion-icon>
            </ion-button>
            <ion-button fill="clear" slot="end" color="danger" @click="deleteTag(tag)">
              <ion-icon slot="icon-only" :icon="trashOutline"></ion-icon>
            </ion-button>
          </ion-item>
        </ion-list>
      </ion-content>
    </ion-modal>
  </ion-content>
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, watch, reactive, onMounted } from 'vue';

// icons
import { add, close, checkmark, arrowUp,  arrowForward, arrowBack, trashOutline, sendOutline, helpCircleOutline,
        createOutline, openOutline, chevronForwardOutline, informationCircleOutline, personAddOutline, pencil,
        downloadOutline, star, starOutline, arrowDown, addCircleOutline, settingsOutline, } from 'ionicons/icons';

// components
import { IonPage, IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
        IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonBadge,
        IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption,
        IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid, IonCheckbox,
        IonReorder, IonReorderGroup, IonFab, IonFabButton, IonRange, IonInput, IonModal,
        loadingController, modalController, alertController, } from '@ionic/vue';
import UserProfileFormModal from '@/components/modals/UserProfileFormModal.vue';
import UserDetailsModal from '@/components/modals/UserDetailsModal.vue';
import AchieveJUPASResultPageModal from '@/components/achievejupas/AchieveJUPASResultPageModal.vue';

// composables / services
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';
import { useRouter } from 'vue-router';

// services
import TeacherService from '@/services/TeacherService';
import AchieveJUPASService from '@/services/AchieveJUPASService';

// types
import { Session, Tag, User, School, } from '@/types';

import { jsPDF } from 'jspdf';

export default defineComponent({
  name: 'TeacherAchieveJUPASModal',
  props: ["isPage", "isDemo", "hideHeader"],
  components: { IonPage, IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
                IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonBadge,
                IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption,
                IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid, IonCheckbox,
                IonReorder, IonReorderGroup, IonFab, IonFabButton, IonRange, IonInput, IonModal, },
  setup(props) {
    // methods or filters
    const store = useStore();
    const { openModal, presentPrompt, presentToast, infiniteScrollLoadData, formatStudentNumber,
            getQRCodeUrl, sendLinkToMyWhatsApp, getF6YearDSE, getF5YearDSE, randInt, uniqueId, } = utils();
    const { t } = useI18n();
    const router = useRouter();
    const userRelatedSchool = computed<School>(() => store.getters.userRelatedSchool);

    const closeModal = async () => {
      if (props.isPage) router.replace('/home');
      else await modalController.dismiss({})
    };

    // Get count of programs with 'like' reaction
    // Generate random userPrograms from store.state.allPrograms
    const generateRandomUserPrograms = (count: number) => {
      if (count <= 0) return [];

      // Get all JUPAS programs from the store
      const jupasPrograms = store.state.allPrograms.filter(p => p.programType === 'jupas' && p.status !== 'Draft');

      // Shuffle and select random programs
      const shuffled = [...jupasPrograms].sort(() => 0.5 - Math.random());
      const selectedPrograms = shuffled.slice(0, count);

      // Create userPrograms with 'like' reaction
      return selectedPrograms.map((program, idx) => ({
        programId: program.id,
        reaction: 'like',
        reason: '',
        createdAt: new Date().toISOString(),
        appState: {},
        order: idx + 1
      }));
    };

    const user = computed(() => store.state.user);
    const schoolStudents = computed<User[]>(() => store.state.schoolStudents);
    const visibleStudents = computed(() => {
      // Default demo student is current users
      const demoStudents: any = [{
        ...user.value,
        class: '5X', studentNumber: 1, fullName: 'Demo Student', chineseName: '',
        yearDSE: getF5YearDSE(),
        isDemo: true, likedProgramCount: user.value.userPrograms?.filter(up => up.reaction === 'like').length || 0,
        userPrograms: (user.value.userPrograms || []).slice(),
      }];
      for (const cl of ['5X', '5Y', '6X', '6Y']) {
        const numOfStudents = randInt(91, 95);
        const possibleLikedProgramCounts = [0, 3, 6, 20]; // possibilities
        for (let studentNumber = 90; studentNumber < numOfStudents; studentNumber++) {
          const likedProgramCount = possibleLikedProgramCounts[randInt(0, possibleLikedProgramCounts.length-1)];
          const demoStudent = {
            ...user.value,
            class: cl, studentNumber, fullName: 'Demo Student', chineseName: '',
            yearDSE: cl.startsWith('5') ? getF5YearDSE() : getF6YearDSE(),
            isDemo: true, likedProgramCount,
            userPrograms: generateRandomUserPrograms(likedProgramCount),
          }
          demoStudents.push(demoStudent);
        }
      }
      if (user.value.isAdmin || schoolStudents.value.length === 0) {
        // Show also demo students for admin users / schools without any registered students (new schools)
        return [
          ...demoStudents,
          ...schoolStudents.value.filter(s => s.schoolId != 'beacon1'),
        ];
      }
      return schoolStudents.value;
    });
    const searchKeyword = ref("");
    const filters = reactive({
      yearDSE: "all",
      studentClass: "all",
      status: "all",
      parentConsent: "all",
      custom: [] as string[],  // Changed to array for multiple selection
    })

    // Toggle custom filter tags
    const toggleCustomFilter = (tagId: any) => {
      if (tagId === 'All') filters.custom = [];
      else {
        const index = filters.custom.indexOf(tagId);
        if (index === -1) filters.custom.push(tagId);
        else filters.custom.splice(index, 1);
      }
    };

    const getStudentProgramCount = (student: User) => {
      if (student.isDemo) return student.likedProgramCount || 0;
      return (student.userPrograms || []).filter(up => up.reaction === 'like').length;
    };

    // Open user details modal
    const openUserDetails = async (student: User) => {
      return await openModal(AchieveJUPASResultPageModal, { targetUser: student });
    };

    // Get unique DSE years
    const dseYears = computed(() => {
      const uniqueYrs = [...new Set(visibleStudents.value.map(s => Number(s.yearDSE)).filter(year => year))];
      return uniqueYrs.sort((a, b) => b - a); // Sort descending
    });

    // Export student data as PDF
    const exportStudentData = async (student: User) => {
      try {
        const loading = await loadingController.create({
          message: 'Generating PDF...',
        });
        await loading.present();

        // Get student's program selections
        const programSelections = (student.userPrograms || [])
          .filter(up => up.reaction === 'like')
          .map(up => {
            const program = store.state.allPrograms.find(p => p.id === up.programId);
            return program ? `${program.jupasCode} - ${program.name}` : '';
          })
          .filter(Boolean);

        // Create PDF
        const doc = new jsPDF();
        const margin = 20;
        let y = margin;

        // Add school logo if available
        const school = store.state.schools.find(s => s.id === student.schoolId);
        if (school?.logoLink) {
          doc.addImage(school.logoLink, 'JPEG', margin, y, 30, 30);
          y += 40;
        }

        // Title
        doc.setFontSize(20);
        doc.text('Student JUPAS Profile', margin, y);
        y += 15;

        // Basic Information
        doc.setFontSize(14);
        doc.text('Basic Information', margin, y);
        y += 10;

        // Student details
        doc.setFontSize(12);
        const details = [
          `Name: ${student.fullName}`,
          `Class: ${student.class}${formatStudentNumber(student.studentNumber)}`,
          `DSE Year: ${student.yearDSE || 'N/A'}`,
          student.phone ? `Phone: ${student.phone}` : null,
          student.email ? `Email: ${student.email}` : null,
          student.studyingElectives ? `Electives: ${student.studyingElectives}` : null,
        ].filter(Boolean);

        details.forEach((detail: any) => {
          doc.text(detail, margin, y);
          y += 8;
        });
        y += 5;

        // Selected Programs
        doc.setFontSize(14);
        doc.text('Selected JUPAS Programs', margin, y);
        y += 10;
        doc.setFontSize(12);

        if (programSelections.length === 0) {
          doc.text('No programs selected yet', margin, y);
          y += 8;
        } else {
          programSelections.forEach((prog, index) => {
            if (y > 270) {
              doc.addPage();
              y = margin;
            }
            doc.text(`${index + 1}. ${prog}`, margin, y);
            y += 8;
          });
        }

        // Save PDF
        doc.save(`${student.fullName}_JUPAS_Profile.pdf`);
        await loading.dismiss();
        await presentToast('PDF generated successfully', 2000);
      } catch (error) {
        console.error('Error generating PDF:', error);
        await presentToast('Error generating PDF', 2000);
      }
    };

    const hasParentConsent = (student: any) => {
      return student.userConsentRecords?.find(r => r.target == 'achievejupas_parent_consent');
    }

    // Filter students based on all criteria
    const filteredSchoolStudents = computed(() => {
      let filteredStudents = visibleStudents.value.slice();

      // Parent consent filters
      if (filters.parentConsent !== 'all') {
        filteredStudents = filteredStudents.filter(s => {
          return filters.parentConsent === 'yes' ? hasParentConsent(s) : !hasParentConsent(s);
        });
      }

      // Year DSE filter
      if (filters.yearDSE !== 'all') {
        filteredStudents = filteredStudents.filter(s => s.yearDSE == parseInt(filters.yearDSE));
      }

      // Text search
      if (searchKeyword.value) {
        const cleanedKeyword = searchKeyword.value.toLowerCase();
        filteredStudents = filteredStudents.filter((s: User) => {
          const searchInWords = `${s.fullName} ${s.chineseName} ${s.preferredName} ${s.phone} ${s.email} ${s.class}${s.studentNumber}`.toLowerCase();
          return searchInWords.includes(cleanedKeyword);
        });
      }

      // Class filter
      if (filters.studentClass !== 'all') {
        filteredStudents = filteredStudents.filter(s => s.class === filters.studentClass);
      }

      // Custom filter (multiple tags - AND condition)
      if (filters.custom.length > 0) {
        filteredStudents = filteredStudents.filter(s =>
          filters.custom.every(tag => s.tagIds?.includes(tag))
        );
      }

      // AchieveJUPAS completion status filter
      if (filters.status !== 'all') {
        filteredStudents = filteredStudents.filter(s => {
          const programCount = getStudentProgramCount(s);
          if (filters.status === 'complete') return programCount >= 20;
          if (filters.status === 'less-than-3-programs') return programCount < 3;
          if (filters.status === 'less-than-6-programs') return programCount < 6;
          if (filters.status === 'missing-target-scores') {
            const existsTargetScore = (s.userSubjectGrades || []).some(g => g.source == 'overallTarget');
            return !existsTargetScore;
          }
          return true;
        });
      }

      // Sort by DSE year (desc), then class, then student number
      return filteredStudents.sort((a, b) => {
        if (a.yearDSE !== b.yearDSE) return (b.yearDSE || 0) - (a.yearDSE || 0);
        const classA = a.class?.toUpperCase() || '';
        const classB = b.class?.toUpperCase() || '';
        if (classA !== classB) return classA.localeCompare(classB);
        return (a.studentNumber || '').toString().localeCompare(b.studentNumber || '');
      });
    });


    // Sorting
    const sortConfig = ref({
      field: 'class',  // Default to no sorting
      ascending: true
    });

    const toggleSortDirection = () => {
      sortConfig.value.ascending = !sortConfig.value.ascending;
    };

    const getSortedStudents = computed(() => {
      const students = [...filteredSchoolStudents.value];

      if (!sortConfig.value.field) return students; // Return unsorted if no sort field selected

      const sortedStudents = students.sort((a, b) => {
        let compareA, compareB;

        switch (sortConfig.value.field) {
          case 'class':
            compareA = a.class + formatStudentNumber(a.studentNumber);
            compareB = b.class + formatStudentNumber(b.studentNumber);
            break;
          case 'studentNumber':
            compareA = Number(a.studentNumber);
            compareB = Number(b.studentNumber);
            break;
          case 'fullName':
            compareA = a.fullName;
            compareB = b.fullName;
            break;
          case 'completion':
            compareA = getStudentProgramCount(a);
            compareB = getStudentProgramCount(b);
            break;
          default:
            return 0;
        }

        if (compareA < compareB) return sortConfig.value.ascending ? -1 : 1;
        if (compareA > compareB) return sortConfig.value.ascending ? 1 : -1;
        return 0;
      });

      if (user.value.isAdmin) {
        return [
          ...sortedStudents.filter(s => s.isDemo), // show demo students first
          ...sortedStudents.filter(s => !s.isDemo),
        ];
      }
      return sortedStudents;
    });

    // Tag management
    const getUserDefinedTags = () => (user.value.createdTags || []);
    const isAddingTag = ref(false);
    const isManagingTags = ref(false);
    const currEditingTag = ref<Tag | null>(null);
    const newTagName = ref("Group " + (getUserDefinedTags().length + 1));
    const selectedStudents = ref<{ [key: string]: boolean }>({});

    const startAddingTag = () => {
      filters.custom = []; // remove existing filters
      isAddingTag.value = true;
      newTagName.value = "Group " + (getUserDefinedTags().length + 1);
      selectedStudents.value = {};
    };

    const cancelAddingTag = () => {
      isAddingTag.value = false;
      newTagName.value = "";
      selectedStudents.value = {};
    };

    const canSaveTag = computed(() => {
      return newTagName.value.trim() !== '';
      //return newTagName.value.trim() !== '' && Object.values(selectedStudents.value).some(v => v) && !getUserDefinedTags().some(t => t.name.toLowerCase() == newTagName.value.trim().toLowerCase());
    });

    const saveNewTag = async () => {
      if (!canSaveTag.value) return;

      const loading = await loadingController.create({ duration: 20000 });
      await loading.present();

      // Tag name & ID
      const tagId = currEditingTag.value?.id || `t${uniqueId()}`; // ID in database
      const tagName = newTagName.value.trim();

      // Add tag to selected students
      const studentsToUpdate = filteredSchoolStudents.value.filter(s => selectedStudents.value[s.id]);
      studentsToUpdate.forEach(student => {
        if (!student.tagIds) student.tagIds = [];
        if (!student.tagIds.includes(tagId)) {
          student.tagIds.push(tagId);
        }
      });

      // Save to DB & update store
      AchieveJUPASService.upsertTeacherDefinedTag(tagId, tagName, studentsToUpdate.map(s => s.id));
      store.commit('upsertUserCreatedTags', [{ id: tagId, name: tagName }]);

      // Select only the new tag
      filters.custom = [tagId];

      // Reset the add tag state
      cancelAddingTag();

      loading.dismiss();
    };

    const openManageTags = () => {
      isManagingTags.value = true;
    };

    const editTag = async (tag: Tag) => {
      // Switch to edit mode
      isManagingTags.value = false;
      isAddingTag.value = true;
      newTagName.value = tag.name;
      currEditingTag.value = tag;

      // Pre-select students with this tag
      selectedStudents.value = {};
      filteredSchoolStudents.value.forEach(student => {
        if (student.tagIds?.includes(tag.id)) {
          selectedStudents.value[student.id] = true;
        }
      });

      // Release filter (to allow adding other students)
      filters.custom = [];
    };

    const deleteTag = async (tag: Tag) => {
      const alert = await alertController.create({
        header: 'Delete Tag',
        message: `Are you sure you want to delete "${tag.name}"?`,
        buttons: [
          {
            text: 'Cancel',
            role: 'cancel',
          },
          {
            text: 'Delete',
            role: 'destructive',
            handler: async () => {
              const loading = await loadingController.create({ message: 'Deleting tag...' });
              await loading.present();

              // Remove tag from all students
              filteredSchoolStudents.value.forEach(student => {
                if (student.tagIds?.includes(tag.id)) {
                  student.tagIds = student.tagIds.filter(t => t !== tag.id);
                }
              });

              // Remove from filters if selected
              filters.custom = filters.custom.filter(tagId => tagId != tag.id);

              // Delete from DB
              AchieveJUPASService.deleteTeacherDefinedTag(tag.id);
              store.commit('deleteUserCreatedTag', tag.id);

              loading.dismiss();
            },
          },
        ],
      });
      await alert.present();
    };

    return {
      // icons
      add, close, checkmark, arrowUp, arrowForward, arrowBack,
      trashOutline, sendOutline, helpCircleOutline, createOutline, openOutline,
      chevronForwardOutline, informationCircleOutline, personAddOutline, pencil,
      downloadOutline, star, starOutline, arrowDown, addCircleOutline, settingsOutline,

      // variables
      user,
      searchKeyword,
      dseYears,
      userRelatedSchool,

      filters, // selected filters
      toggleCustomFilter, // custom groups

      // Tag management
      getUserDefinedTags,
      isAddingTag,
      isManagingTags,
      newTagName,
      selectedStudents,
      startAddingTag,
      cancelAddingTag,
      saveNewTag,
      canSaveTag,
      editTag, deleteTag,

      // methods
      t, getQRCodeUrl, sendLinkToMyWhatsApp,
      closeModal,
      formatStudentNumber,
      allClasses: () => {
        let checkStudents = visibleStudents.value;
        if (filters.yearDSE != 'all') {
          checkStudents = checkStudents.filter(s => s.yearDSE == filters.yearDSE);
        }
        return [...new Set(checkStudents.map(s => s.class?.replace(/[^a-z0-9]/gi, "").toLowerCase()))]
                .filter(c => !!c).map((c: any) => c?.toUpperCase()).sort();
      },
      getStudentProgramCount,
      openUserDetails,
      exportStudentData,

      // Sorting
      sortConfig,
      toggleSortDirection,
      getSortedStudents,
      openManageTags,
      getNumOfStudentsOfTag: (tagId: any) => {
        return visibleStudents.value.filter(s => s.tagIds?.includes(tagId)).length;
      },

      // Parent consent
      hasParentConsent,
    };
  },
});
</script>

<style scoped>
  :host {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  ion-content {
    flex: 1;
    height: calc(100vh - var(--ion-safe-area-top, 0px) - var(--ion-safe-area-bottom, 0px));
    --overflow: scroll;
  }
  ion-header {
    flex-shrink: 0;
  }

  ion-checkbox {
    margin-top: 9px;
    margin-bottom: 9px;
    margin-inline-end: 16px;
  }
  ion-toolbar {
    --min-height: 40px;
  }
  ion-title {
    font-weight: normal;
    padding-right: 8px;
    padding-top: 5px;
    padding-bottom: 5px;
  }
  ion-title p {
    font-size: 14px;
  }
  ion-segment ion-segment-button {
    height: 20px;
    min-height: 20px;
  }
  ion-item ion-label {
    margin: 0;
  }

  ion-item {
    --min-height: 28px;
  }

  /**
   * Dual knob range slider (e.g. best 5 scores)
   */
  ion-range {
    padding-top: 0 !important;
  }
  ion-range::part(tick) {
    background: var(--ion-color-medium-tint);
  }
  ion-range::part(tick-active) {
    background: var(--ion-color-primary-tint);
  }
  ion-range::part(pin) {
    display: inline-flex;
    align-items: center;
    justify-content: center;

    background: var(--ion-color-primary);
    color: white;

    border-radius: 50%;
    transform: scale(1.01);

    top: 10px;
    z-index: 9999;

    min-width: 28px;
    height: 28px;
    transition: transform 120ms ease, background 120ms ease;
  }
  ion-range::part(pin)::before {
    content: none;
  }
  ion-range::part(knob) {
    background: var(--ion-color-primary);
  }
  ion-range::part(bar) {
    background: var(--ion-color-medium-tint);
  }
  ion-range::part(bar-active) {
    background: var(--ion-color-primary);
  }

  /**
   * Score filter with label
   */
  .score-filter {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 0 16px;
  }
  .score-label {
    color: var(--ion-color-medium);
    font-size: 14px;
    white-space: nowrap;
    min-width: 50px;
  }
  .score-filter ion-range {
    flex: 1;
  }

  /**
   * Sorting
   */
  .sort-select {
    min-height: 28px;
    max-width: 110px;
    --padding-start: 4px;
    --padding-end: 4px;
    margin-right: 4px;
    margin-left: 0;
  }
  .sort-buttons {
    margin-left: 0;
  }

  ion-item ion-buttons {
    min-width: 28px !important;
    height: 28px !important;
    width: 36px;
  }
</style>