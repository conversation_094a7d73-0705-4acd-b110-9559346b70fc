<template>
  <div style="width: 100%">
    <!-- Student target subject scores -->
    <div style="margin-bottom: 8px" v-if="showTargetScores">
      <SubjectScoreInputSection :title="targetUser ? 'Student Prediction' : 'My Prediction'" source="overallTarget" :targetUser="targetUser" :program="program"
                                :readonly="!!targetUser" :fromDeckView="true"></SubjectScoreInputSection>
    </div>

    <!-- Program Info -->
    <div>
      <!-- Stats grid -->
      <div v-if="showProgramAdmissionData && (program?.interviewArrangements || program?.numOfPlaces || program?.admissions?.length)">
        <div class="stats-grid">
          <!-- Past Mean -->
          <ion-badge class="stat-badge" :style="{ backgroundColor: transparent ? 'transparent' : undefined }">
            <div class="stat-label">Past Mean</div>
            <div class="stat-value" :style="{
              border: `1px solid #ccc`,
              color: transparent ? 'var(--ion-color-dark)' : 'white'
            }">
              {{ program?.admissions?.[0]?.scoreMean || '-' }}
            </div>
          </ion-badge>

          <!-- Lower Quartile -->
          <ion-badge class="stat-badge" :style="{ backgroundColor: transparent ? 'transparent' : undefined }">
            <div class="stat-label">Past LQ</div>
            <div class="stat-value" :style="{
              border: program?.admissions?.[0]?.scoreLowerQuartile ? `1px solid #ccc` : 'none',
              color: transparent ? 'var(--ion-color-dark)' : 'white'
            }">
              {{ program?.admissions?.[0]?.scoreLowerQuartile || '-' }}
            </div>
          </ion-badge>

          <!-- Median -->
          <ion-badge class="stat-badge" :style="{ backgroundColor: transparent ? 'transparent' : undefined }">
            <div class="stat-label">Past Median</div>
            <div class="stat-value" :style="{
              border: `1px solid #ccc`,
              color: transparent ? 'var(--ion-color-dark)' : 'white'
            }">
              {{ program?.admissions?.[0]?.scoreMedian || '-' }}
            </div>
          </ion-badge>

          <!-- Number of Seats -->
          <ion-badge class="stat-badge" :style="{ backgroundColor: transparent ? 'transparent' : undefined }"
                      @click="setPopoverOpen('seats', true, $event)">
            <div class="stat-label">Seat</div>
            <div class="stat-value" :style="{
              //border: `1px solid var(--ion-color-${getCompetitivenessColor(program?.admissions?.[0]?.bandACompRatio)})`,
              color: transparent ? 'var(--ion-color-dark)' : 'white'
            }">
              {{ program?.numOfPlaces || '-' }}
            </div>

            <ion-popover :isOpen="popoverState.seats" :event="popoverEvent" :dismiss-on-select="true" @didDismiss="setPopoverOpen('seats', false, $event)">
              <ion-content>
                <!-- Competition ratio card -->
                <ion-card class="ion-text-center" :color="getCompetitivenessColor(program?.admissions?.[0]?.bandACompRatio)"
                          style="padding: 2px; font-size: 0.9em; margin: 0 0 4px 0"
                          v-if="program?.admissions?.length && program.admissions[0].bandACompRatio">
                  <span>
                    <b>{{ program.admissions[0].bandACompRatio }}</b> compete for 1 seat ({{ program.admissions[0].intakeYear }})<br />
                  </span>
                </ion-card>
              </ion-content>
            </ion-popover>
          </ion-badge>

          <!-- Last Round Applicants -->
          <ion-badge class="stat-badge" :style="{ backgroundColor: transparent ? 'transparent' : undefined }" v-if="program?.admissions?.length && program.admissions[0].bandACompRatio">
            <div class="stat-label">Past Applicant</div><!-- Prev: last round applicant -->
            <div class="stat-value" :style="{
              //border: '1px solid #ccc',
              color: transparent ? 'var(--ion-color-dark)' : 'white'
            }">
              {{ (program.admissions[0].bandACompRatio * program.admissions[0].numOfBandAOffers).toFixed(0) }}
            </div>
          </ion-badge>

          <!-- Interview Arrangement -->
          <ion-badge class="stat-badge" :style="{ backgroundColor: transparent ? 'transparent' : undefined }">
            <div class="stat-label">Interview</div>
            <div class="stat-value" :style="{
              //border: '1px solid #ccc',
              color: transparent ? 'var(--ion-color-dark)' : 'white'
            }">
              <ion-icon v-if="program?.interviewArrangements?.includes('Yes')" :icon="checkmark"></ion-icon>
              <ion-icon v-else-if="program?.interviewArrangements?.includes('No')" :icon="close"></ion-icon>
              <span v-else>-</span>
            </div>
          </ion-badge>
        </div>
      </div>

      <!-- Program Name + Website Buttons -->
      <div v-if="showNames">
        <ProgramItemContent :item="program" :fromDeckView="true" :hideDisciplineBadges="true" :showRecommendedEvents="false"></ProgramItemContent>
        <!--<h1 style="font-size: 0.9em"><b>{{ program?.institutionNameShort }}</b></h1>
        <p style="font-size: 0.85em; margin: 2px">{{ program?.name }}</p>-->
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, computed, watch } from 'vue'

// icons
import { checkmark, close, ellipseOutline, triangleOutline, } from 'ionicons/icons';

// components
import {
  IonCol,
  IonButton,
  IonBadge,
  IonCard,
  IonIcon,
  IonSelect,
  IonSelectOption,
  IonRow, IonText,
  IonPopover, IonContent, IonItem, IonLabel,
} from '@ionic/vue';
import ProgramItemContent from '@/components/achievejupas/ProgramItemContent.vue';
import SubjectScoreInputSection from '@/components/achievejupas/SubjectScoreInputSection.vue';

// composables
import { utils } from '@/composables/utils';
import { useStore } from '@/store';

// types
import { Service } from '@/types';

export default defineComponent({
  name: 'ProgramInfoStats',
  components: {
    IonCol,
    IonButton,
    IonBadge,
    IonCard,
    IonIcon,
    IonSelect,
    IonSelectOption,
    IonRow, IonText,
    IonPopover, IonContent, IonItem, IonLabel,
    ProgramItemContent, SubjectScoreInputSection,
  },
  props: {
    program: {
      type: Object,
      required: true
    },
    showNames: {
      type: Boolean,
      default: true
    },
    showWebsiteButtons: {
      type: Boolean,
      default: true
    },
    transparent: {
      type: Boolean,
      default: false
    },
    showTargetScores: {
      type: Boolean,
      default: false
    },
    showProgramAdmissionData: {
      type: Boolean,
      default: true
    },
    targetUser: {
      type: Object,
      default: null,
    }
  },
  setup(props) {
    const { openImageModal, openServiceModal, } = utils();

    // Store
    const store = useStore();
    const user = computed(() => props.targetUser || store.state.user);

    // TODO: Work Events (implanted promotion)
    const workServices = computed<Service[]>(() => store.getters.getServicesByTypes(["Work"]));

    // Popover
    const popoverState = reactive({
      seats: false,
    });
    const popoverEvent = ref();
    const setPopoverOpen = (popoverKey: any, state: boolean, ev: any) => {
      popoverEvent.value = ev; 
      popoverState[popoverKey] = state;
    };

    const getCompetitivenessColor = (ratio: any) => {
      if (ratio <= 6.5) {  // Bottom 35-40% - Less competitive
        return 'success';
      } else if (ratio <= 20) {  // Middle 45-60% - Moderately competitive
        return 'warning';
      } else {  // Top 15-20% - Highly competitive
        return 'danger';
      }
    };

    return {
      // icons
      checkmark, close, ellipseOutline, triangleOutline,

      // variables
      user,

      // methods
      getCompetitivenessColor,
      openImageModal,

      // popover
      popoverState, popoverEvent,
      setPopoverOpen,
    }
  }
})
</script>

<style scoped>
ion-badge {
  padding: 4px !important;
}
.stats-grid {
  display: grid;
  grid-template-columns: repeat(6, minmax(0, 1fr));
  gap: 1px;
  text-align: center;
  font-size: 0.9em;
}
.stat-badge {
  align-content: flex-end;
  padding: 4px;
}
.stat-label {
  white-space: normal;
  font-size: 0.8em;
  font-weight: normal;
}
.stat-value {
  /*border: 1px solid #ccc;*/
  padding: 2px;
  margin: 2px;
  min-width: 24px;
  color: var(--ion-color-dark);
}
</style>
