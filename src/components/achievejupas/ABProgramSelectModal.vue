<template>
  <ion-header>
    <ion-toolbar>
      <!-- Back Button -->
      <ion-buttons slot="start">
        <ion-button slot="icon-only" @click="closeModal()"><ion-icon :icon="close"></ion-icon></ion-button>
      </ion-buttons>

      <!-- Title -->
      <ion-title>
        <ion-label>
          <p v-if="specificDiscipline">
            Programs related to <b>{{ specificDiscipline.name }}</b>
          </p>
          <h2 style="font-size: 20px" v-else>
            Select program(s)
          </h2>
          <p><small>sorted in ascending order by their JUPAS codes</small></p>
        </ion-label>
      </ion-title>
    </ion-toolbar>

    <!-- Search Bar -->
    <ion-toolbar v-if="!readOnly">
      <ion-searchbar style="padding-bottom: 1px" mode="ios" v-model="programSearchKeyword" :placeholder="t('search')"
                    @keyup.enter="(e) => e.target.blur()"></ion-searchbar>
    </ion-toolbar>
  </ion-header>

  <ion-content :fullscreen="true" style="min-height: 150px">

    <div class="spin" v-if="loading">
      <ion-spinner></ion-spinner>
    </div>

    <div v-else>
      <!--
        Program list (sorting)
      -->
      <div style="min-height: 100px">
        <ion-item lines="full" v-for="program in currVisiblePrograms.slice(0, numOfVisibleItems)" :key="program.id" style="--min-height: 55px">
          <ion-checkbox
            style="margin-inline-start: 8px"
            slot="start"
            @update:modelValue="onCheckProgram($event, program)"
            :modelValue="chosenPrograms.find(p => p.id == program.id) != null"
            v-if="!readOnly"
          ></ion-checkbox>

          <ion-label>
            <p style="white-space: pre-line">{{ program.displayName }}</p>
      
            <div class="link-buttons ion-text-right">
              <ion-button fill="outline" :href="selectedGroup == 'eapp' ? program.cspeProgramUrl : program.jupasUrl" target="_blank">
                {{ selectedGroup == 'eapp' ? 'E-APP' : 'JUPAS' }}
                <!--<ion-icon slot="end" :icon="openOutline"></ion-icon>-->
              </ion-button>
              <ion-button fill="outline" :href="program.programWebsite" target="_blank" v-if="program.programWebsite">
                Website
                <!--Program Website-->
                <!--<ion-icon slot="end" :icon="openOutline"></ion-icon>-->
              </ion-button>
              <!--<ion-button color="tertiary">Apply</ion-button>-->
            </div>
          </ion-label>
        </ion-item>

        <ion-infinite-scroll
          @ionInfinite="loadData($event)" 
          threshold="100px" 
          id="infinite-scroll"
        >
          <ion-infinite-scroll-content
            loading-spinner="bubbles"
            loading-text="Loading...">
          </ion-infinite-scroll-content>
        </ion-infinite-scroll>
      </div>

      <div class="ion-text-center" v-show="currVisiblePrograms.length == 0">
        <p>No programs available</p>
      </div>
    </div>
  </ion-content>

  <ion-footer v-if="!readOnly && chosenPrograms.length > 0">
    <ion-toolbar>
      <ion-button color="success" expand="block" @click="confirmSelect()">
        Done
        <ion-icon slot="end" :icon="checkmark"></ion-icon>
      </ion-button>
    </ion-toolbar>
  </ion-footer>
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, watch, reactive, onMounted } from 'vue';

// icons
import { add, close, checkmark, arrowUp,  arrowForward, arrowBack, openOutline, trashOutline, } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
        IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonReorderGroup, IonReorder,
        IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption,
        IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid,
        IonSpinner, IonCheckbox, IonInfiniteScroll, IonInfiniteScrollContent, } from '@ionic/vue';

// composables / services
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';

// types
import { Program } from '@/types';
export default defineComponent({
  name: 'ABProgramSelectModal',
  props: ["targetDisciplines", "prefilledPrograms", "specificDiscipline", "readOnly"],
  components: { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
                IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonReorderGroup, IonReorder,
                IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption,
                IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid,
                IonSpinner, IonCheckbox, IonInfiniteScroll, IonInfiniteScrollContent, },
  setup(props) {
    // methods or filters
    const store = useStore();
    const { closeModal, infiniteScrollLoadData, } = utils();
    const { t } = useI18n();

    const allPrograms = computed(() => store.getters.programsSortedByJUPASCodes);
    const loading = computed(() => store.state.loadingData);
    const masterCurrVisiblePrograms = ref<Program[]>([]);
    const chosenPrograms = ref<Program[]>(props.prefilledPrograms?.slice() || []);
    const selectedGroup = ref('jupas');
    const selectedDisciplineId = ref('all');
    const programSearchKeyword = ref('');
    
    const currVisiblePrograms = computed(() => {
      return masterCurrVisiblePrograms.value.filter((p: Program) => p.displayName.toLowerCase().includes(programSearchKeyword.value.toLowerCase().trim()));
    })

    const numOfVisibleItems = ref(20);
    const loadData = (ev: any) => {
      infiniteScrollLoadData(ev, numOfVisibleItems, masterCurrVisiblePrograms.value);
    }

    const refreshCurrProgramList = () => {
      const targetProgramType = (selectedGroup.value == 'eapp' ? 'ipass' : 'jupas');
      const relatedPrograms = allPrograms.value.filter(p => p.programType == targetProgramType);
      masterCurrVisiblePrograms.value = selectedDisciplineId.value == 'all' ? relatedPrograms.slice(0, 500)
                                        : relatedPrograms.filter((p: Program) => p.disciplines.some(d => d.id == selectedDisciplineId.value)).slice(0, 500);
      numOfVisibleItems.value = 20;
      programSearchKeyword.value = '';
    }
    
    const confirmSelect = async () => {
      await closeModal({ "chosen": chosenPrograms.value });
    }

    watch(selectedDisciplineId, (curr) => {
      refreshCurrProgramList();
    });
    watch(allPrograms, (curr) => {
      refreshCurrProgramList();
    });

    onMounted(() => {
      refreshCurrProgramList();
      const { targetDisciplines, specificDiscipline } = props;
      if (specificDiscipline) selectedDisciplineId.value = specificDiscipline.id;
      if (targetDisciplines && targetDisciplines.length > 0) selectedDisciplineId.value = targetDisciplines[0].id;
    })

    // 3. return variables & methods to be used in template HTML
    return {
      // icons
      add, close, checkmark, arrowUp, arrowForward, arrowBack, openOutline, trashOutline,

      // variables
      loading,
      selectedGroup, selectedDisciplineId, programSearchKeyword,
      currVisiblePrograms, masterCurrVisiblePrograms, chosenPrograms,
      numOfVisibleItems,

      // methods
      t, loadData,
      closeModal,
      refreshCurrProgramList, confirmSelect,

      onCheckProgram: (checked: any, program: Program) => {
        if (checked) chosenPrograms.value.unshift(program);
        else {
          const idx = chosenPrograms.value.findIndex(p => p.id == program.id);
          if (idx !== -1) chosenPrograms.value.splice(idx, 1);
        }
      },
    }
  }
});
</script>

<style scoped>
  .link-buttons ion-button {
    text-transform: none;
  }
  .inst-select {
    --padding-top: 0;
    --padding-bottom: 0;
  }
</style>