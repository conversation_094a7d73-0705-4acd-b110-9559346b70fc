<template>
  <div>
    <!-- Exist elective requirements -->
    <ion-accordion-group
      :multiple="true" :value="['re1', 're2', 're3', 'pes']"
      v-if="['requiredElective1', 'requiredElective2', 'requiredElective3', 'preferredSubjects'].some(key => item[key].length > 0)"
      readonly
    >
      <!-- Required Elective 1 -->
      <ion-accordion value="re1" v-if="item.requiredElective1.length > 0">
        <ion-item lines="full" slot="header" @click.prevent>
          <ion-label class="ion-text-wrap"><p><b>Must take at least 1 elective from</b></p></ion-label>
        </ion-item>
        <div class="ion-padding-start" slot="content">
          <ion-item v-for="electiveName in item.requiredElective1" :key="electiveName">
            <ion-label class="ion-text-wrap"><p>{{ electiveName }}</p></ion-label>
          </ion-item>
        </div>
      </ion-accordion>

      <!-- Required Elective 2 -->
      <ion-accordion value="re2" v-if="item.requiredElective2.length > 0">
        <ion-item lines="full" slot="header">
          <ion-label class="ion-text-wrap"><p><b>Must take at least 1 elective from</b></p></ion-label>
        </ion-item>
        <div class="ion-padding-start" slot="content">
          <ion-item v-for="electiveName in item.requiredElective2" :key="electiveName">
            <ion-label class="ion-text-wrap"><p>{{ electiveName }}</p></ion-label>
          </ion-item>
        </div>
      </ion-accordion>

      <!-- Required Elective 3 -->
      <ion-accordion value="re3" v-if="item.requiredElective3.length > 0">
        <ion-item lines="full" slot="header">
          <ion-label class="ion-text-wrap"><p><b>Must take at least 1 elective from</b></p></ion-label>
        </ion-item>
        <div class="ion-padding-start" slot="content">
          <ion-item v-for="electiveName in item.requiredElective3" :key="electiveName">
            <ion-label class="ion-text-wrap"><p>{{ electiveName }}</p></ion-label>
          </ion-item>
        </div>
      </ion-accordion>

      <!-- Upweighted / Preferred Subject -->
      <ion-accordion value="pes" v-if="item.preferredSubjects.length > 0">
        <ion-item lines="full" slot="header">
          <ion-label class="ion-text-wrap"><p><b>Suggested/Upweighted subjects</b></p></ion-label>
        </ion-item>
        <div class="ion-padding-start" slot="content">
          <ion-item v-for="electiveName in item.preferredSubjects" :key="electiveName">
            <ion-label class="ion-text-wrap"><p>{{ electiveName }}</p></ion-label>
          </ion-item>
        </div>
      </ion-accordion>
    </ion-accordion-group>

    <!-- No special requirements -->
    <div v-else>
      <ion-item lines="full">
        <ion-label class="ion-text-wrap"><p><b>No special elective requirements</b></p></ion-label>
      </ion-item>
    </div>

    <!-- Electives linked to programs
    <ion-item v-for="num in getElectiveNums(item.id)" :key="num" :disabled="isDisableElectiveSelect(item.id, num)">
      <ion-select :label="`Elective ${num}`" label-placement="floating" interface="popover"
                  :value="getUserElective(item.id, num, 'like', 'electiveId')" @ionChange="onUpdateElectiveForProgram($event, item.id, num)">
        <ion-select-option v-for="opt in getAvailableElectives(num, item.id, item)" :key="opt.id" :value="opt.id" :disabled="opt.isGroup">
          <span v-if="opt.isGroup">{{ opt.name }}：</span>
          <span v-else>&nbsp;&nbsp;{{ opt.name }}</span>
        </ion-select-option>
      </ion-select>
      <ion-buttons slot="end" v-if="getUserElective(item.id, num, 'like', 'electiveId')">
        <ion-button color="danger" @click.stop="clearUserElectiveSelect(item.id, num)">
          <ion-icon size="small" slot="start" :icon="trashOutline"></ion-icon>
        </ion-button>
      </ion-buttons>
    </ion-item>
    -->
  </div>
</template>

<script lang="ts">
// vue
import { computed } from 'vue';

// icon
import { checkmarkCircle, alertCircle, createOutline, pencil,
        handRightOutline, peopleOutline, personOutline, calendarClearOutline, calendarOutline, } from 'ionicons/icons';

// components
import { IonItem, IonIcon, IonLabel, IonNote, IonSpinner,
        IonAccordion, IonAccordionGroup, } from '@ionic/vue';

export default {
  props: [
    "item",
  ],
  components: {
    IonItem, IonIcon, IonLabel, IonNote, IonSpinner,
    IonAccordion, IonAccordionGroup,
  },
  setup(props) {
    return {
      // icons
      checkmarkCircle, alertCircle, createOutline, pencil,
      handRightOutline, peopleOutline, personOutline, calendarClearOutline, calendarOutline,
    }
  }
}
</script>

<style scoped>
</style>