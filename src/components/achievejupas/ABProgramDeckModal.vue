<template>
  <ion-header>
    <ion-toolbar v-if="isAB3 || !singleSelectMode">
      <!-- Back buttons (back / close modal) -->
      <ion-buttons slot="start">
        <ion-button slot="icon-only" @click="confirmSelect(true)">
          <ion-icon :icon="arrowBack"></ion-icon>
        </ion-button>
      </ion-buttons>

      <ion-title style="padding-left: 16px">
        <ion-label class="ion-text-wrap">
          <h2 v-if="isAB3">
            <b><span style="vertical-align: middle"><ion-icon :icon="thumbsUpOutline"></ion-icon></span>
            &nbsp;2+ programs</b>
          </h2>
          <h2 v-else>
            <b><span style="vertical-align: middle"><ion-icon :icon="thumbsUpOutline"></ion-icon></span>
            &nbsp;20 programs</b>
          </h2>
          <p v-if="targetDiscipline">for {{ targetDiscipline?.name }}</p>
          <p v-else-if="!isAB3">{{ chosenPrograms.length }}/20 selected</p>
        </ion-label>
      </ion-title>

      <ion-buttons slot="end" v-if="takenActions">
        <ion-button fill="solid" color="success" @click="confirmSelect()">
          Save
          <ion-icon slot="end" :icon="checkmark"></ion-icon>
        </ion-button>
      </ion-buttons>
    </ion-toolbar>
  </ion-header>

  <ion-content :fullscreen="true">
    <!-- Searchbar Input -->
    <ion-toolbar v-show="isSearching">
      <ion-searchbar id="keyword-searchbar" style="padding-bottom: 1px" mode="md" v-model="searchKeyword" :placeholder="t('search')"
                    @ionFocus="isSearching = true" @ionCancel="isSearching = false"
                    @keyup.enter="(e) => e.target.blur()" show-cancel-button="always"></ion-searchbar>
    </ion-toolbar>

    <!--
      Choose Programs (Cards)
      -->
    <div style="height: 100%">
      <swiper
          class="program-slides"
          id="card-swiper-slides"
          :grabCursor="true"
          :navigation="true"
          :modules="modules"
          :virtual="{ enabled: true }"
          v-if="!delayLoading && filteredPrograms().length > 0"
      >
        <swiper-slide class="program-slide" v-for="program in filteredPrograms()" :key="program.id" :data-program-id="program.id">
          <div>
            <program-info-stats :program="program" :showNames="false" :showTargetScores="true" :showProgramAdmissionData="false"
                                  :showWebsiteButtons="false" :transparent="true" :targetUser="targetUser" />
          </div>

          <!-- Upper Section: Program Card: Name / Image / Website Button-->
          <div class="program-slide-upper-section" :class="{ 'highlighted-border': isSelected(program) }">
            <!--<div style="width: 100%; height: 100%" v-if="program.cardSlideImgLink && selectedOption == 'highlight'">
              <img style="width: 100%; height: 100%; object-fit: contain" :src="getProxyImgLink(program.cardSlideImgLink)"
                    @load="program.loadedCardSlideImg = true" />
              <ion-spinner style="position: absolute; top: 50%; left: 50%" v-if="!program.loadedCardSlideImg"></ion-spinner>

              <div class="ion-text-center bottom-badge" style="width: 100%" v-if="program.jupasUrl || program.programWebsite">
                <program-info-stats :program="program" :show-names="false" :targetUser="targetUser" />
              </div>
            </div>-->

            <!--<ion-row style="width: 100%" v-if="!(program.cardSlideImgLink && program.loadedCardSlideImg) || selectedOption != 'highlight'">-->
            <ion-row style="width: 100%">
              <program-info-stats :program="program" :showProgramAdmissionData="true" :targetUser="targetUser" />
            </ion-row>

            <!-- For highlighted programs (client program) -->
            <ion-row style="align-items: center">
              <!-- Highlights Button -->
              <ion-col class="ion-text-center">
                <ion-button class="no-text-transform" size="small" color="danger" @click="openImageModal(program.cardSlideImgLink)" v-if="program?.cardSlideImgLink">
                  Highlights
                </ion-button>
              </ion-col>

              <!-- Like Button -->
              <ion-col v-if="!readonly">
                <ion-fab-button style="margin: 0 auto" size="large" color="primary" v-if="singleSelectMode && isSelected(program) && program.id != showFirstProgramId">
                  {{ getBandLabel(chosenPrograms.findIndex(p => p.id == program.id)) }}
                </ion-fab-button>
                <ion-fab-button style="margin: 0 auto" size="large" color="primary" v-on="{ [isMobileWeb() ? 'touchstart' : 'click']: () => { onThumbsUp(program) } }"
                                :disabled="!passProgramElectiveRequirements(program)" v-else>
                  <ion-icon size="large" :icon="isSelected(program) ? thumbsUp : thumbsUpOutline"></ion-icon>
                </ion-fab-button>
              </ion-col>

              <!-- Strength2Careers -->
              <ion-col class="ion-text-center">
                <ion-button class="no-text-transform" size="small" color="primary" style="margin: 0; height: 36px; font-size: 0.75em"
                            @click="openAIProfessionDeckModal(program.id)" v-if="allClientProgramIds.includes(program.id)">
                  Careers
                </ion-button>
              </ion-col>

              <!-- Row: extra info (recommended events, disciplines, etc.) -->
              <ion-col size="12">
                <ProgramItemContent :item="program" :hideNames="true" :fromDeckView="true" :showRecommendedEvents="program.id == 161"></ProgramItemContent>
              </ion-col>
            </ion-row>
          </div>

          <!-- Friendlier in 中文 - Starbuck coupons / WhatsApp -->
          <!--<ion-button expand="block" color="medium" size="small" @click.stop="openReportModal(program)">-->
          <div style="margin-bottom: 200px">
            <ion-button expand="block" color="medium" size="small" @click.stop="undefined" :href="loggedInUser?.waGroupLink" target="_blank">
              <ion-icon slot="start" :icon="flagOutline"></ion-icon>
              有唔妥？即時cap圖/錄音俾我哋！
              <ion-icon slot="end" :icon="logoWhatsapp"></ion-icon>
            </ion-button>
          </div>
        </swiper-slide>
      </swiper>
    </div>

    <!-- Program Report Modal -->
    <ion-modal :is-open="showReportModal" @didDismiss="showReportModal = false">
      <program-issue-report-modal :program="selectedProgramForReport" v-if="selectedProgramForReport"></program-issue-report-modal>
    </ion-modal>
  </ion-content>

  <!--
    Chip Filter Panel
    -->
  <ion-footer v-show="!isSearching">
    <ion-toolbar style="--min-height: 24px">
      <!-- Back Button -->
      <ion-buttons slot="start">
        <ion-button class="nav-category-btn" @click="confirmSelect(true)">
          <ion-icon size="small" slot="icon-only" :icon="arrowBack"></ion-icon>
        </ion-button>
      </ion-buttons>

      <!-- Filter Groups -->
      <ion-segment class="filter-group-segment" mode="ios" v-model="selectedFilterGroup" scrollable>
        <ion-segment-button value="disciplines" v-if="!targetDiscipline">
          <ion-label>Disciplines</ion-label>
        </ion-segment-button>
        <ion-segment-button value="institutions">
          <ion-label class="ion-text-wrap">Institutions</ion-label>
        </ion-segment-button>
        <ion-segment-button value="like" v-if="!singleSelectMode && userPrograms.some(ud => ud.reaction == 'like')">
          <ion-icon size="small" :icon="thumbsUpOutline"></ion-icon>
        </ion-segment-button>
        <ion-segment-button value="search">
          <ion-ripple-effect></ion-ripple-effect>
          <ion-icon size="small" :icon="search"></ion-icon>
        </ion-segment-button>
      </ion-segment>
    </ion-toolbar>

    <!-- Materials by Type -->
    <ion-toolbar class="material-div">
      <!-- My Choices -->
      <ion-row v-show="selectedFilterGroup == 'myChoices'">
        <ion-chip @click="selectedOption = 'dislike'" :class="{ 'active-tag': selectedOption == 'dislike' }">
          <ion-icon :color="selectedOption == 'dislike' ? 'light' : 'dark'" style="margin-inline-start: 0" :icon="thumbsDownOutline"></ion-icon>
        </ion-chip>
        <ion-chip @click="selectedOption = 'like'" :class="{ 'active-tag': selectedOption == 'like' }">
          <ion-icon :color="selectedOption == 'like' ? 'light' : 'dark'" style="margin-inline-start: 0" :icon="thumbsUpOutline"></ion-icon>
        </ion-chip>
      </ion-row>

      <!-- Disiciplines -->
      <ion-row v-show="selectedFilterGroup == 'disciplines'">
        <!-- Client Advertisement: Banner -->
        <div style="width: 100%" v-if="user.isAdmin && relatedAdvBanner">
          <div class="ad-banner" v-if="!relatedAdvBanner.bannerImgLink">
            Your department's advertisement
          </div>
          <div class="ad-banner" v-else>
            <img :src="getProxyImgLink(relatedAdvBanner.bannerImgLink)" />
          </div>
        </div>

        <!-- Client / hypothetical programs -->
        <ion-chip @click="selectedOption = 'highlight'"
                  :class="{ 'active-tag': selectedOption == 'highlight' }" v-if="!isAB3">
          <ion-label>Highlight</ion-label>
        </ion-chip>

        <!-- 1st level: Discipline Groups -->
        <ion-chip v-for="dg in disciplineGroups" :key="dg.id" @click="selectedOption = dg" :class="{ 'active-tag': selectedOption.id == dg.id }">
          <ion-label>{{ dg.name }} {{ dg.nameChi }}</ion-label>
        </ion-chip>

        <!-- 2nd level: Disciplines -->
        <hr />
        <div v-if="selectedOption.id">
          <ion-chip outline v-for="dis in disciplines.filter(d => d.disciplineGroupId == selectedOption.id)" :key="dis.id" @click="nestedOption = dis.id" :class="{ 'active-tag-primary': nestedOption == dis.id }">
            <ion-label>{{ dis.name }}</ion-label>
          </ion-chip>
        </div>
      </ion-row>

      <!-- Institutions -->
      <ion-row v-show="selectedFilterGroup == 'institutions'">
        <!-- 1st level: institution -->
        <ion-chip v-for="inst in relatedInstitutions" :key="inst.id" @click="selectedOption = inst" :class="{ 'active-tag': selectedOption.id == inst.id }">
          <ion-label>{{ inst.nameShort }}</ion-label>
        </ion-chip>

        <!-- 2nd level: discipline group -->
        <hr />
        <div>
          <ion-chip outline v-for="dg in disciplineGroups" :key="dg.id" @click="nestedOption = dg.id" :class="{ 'active-tag-primary': nestedOption == dg.id }">
            <ion-label>{{ dg.name }} {{ dg.nameChi }}</ion-label>
          </ion-chip>
        </div>
      </ion-row>
    </ion-toolbar>
  </ion-footer>
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, onMounted, watch, reactive, } from 'vue';

// icons
import { add, close, checkmark, arrowUp,  arrowForward, arrowBack, checkbox, trashOutline,
         thumbsUpOutline, thumbsDownOutline, thumbsUp, thumbsDown, heart, heartOutline,
         chevronBack, chevronForward, repeat, search, flagOutline, logoWhatsapp } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
        IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonReorderGroup, IonReorder, IonProgressBar,
        IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption, IonSpinner, IonListHeader,
        IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid, IonRippleEffect,
        IonNote, IonTextarea, IonFab, IonFabButton, IonBadge, IonInfiniteScroll, IonInfiniteScrollContent, IonModal, IonAvatar,
        IonicSlides, isPlatform, getPlatforms, modalController, loadingController, } from '@ionic/vue';
import ProgramInfoStats from '@/components/achievejupas/ProgramInfoStats.vue';
import ProgramItemContent from '@/components/achievejupas/ProgramItemContent.vue';
import ProgramIssueReportModal from '@/components/achievejupas/ProgramIssueReportModal.vue';
import ABProfessionSelectModal from '@/components/pss/profession/ABProfessionSelectModal.vue';

// Swiper
import 'swiper/swiper.min.css';
import 'swiper/modules/effect-cards/effect-cards.min.css';
import 'swiper/modules/navigation/navigation.min.css';
import '@ionic/vue/css/ionic-swiper.css';
import { Swiper, SwiperSlide } from 'swiper/vue/swiper-vue';
import { EffectCards, Navigation, Virtual, } from 'swiper';

// composables / services
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';
import { Institution, Program, Service, UserClaim, UserProgram } from '@/types';

export default defineComponent({
  name: 'ABProgramDeckModal',
  props: [
    "isAB3", "isGPT", "targetDiscipline", "prefilledPrograms", "oldUserPrograms",
    "showFirstProgramId", "singleSelectMode",
    "prefilledDisciplines", "oldUserDisciplines",
    "targetUser", "isDemo", "readonly",
  ],
  components: { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
                IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonReorderGroup, IonReorder, IonProgressBar,
                IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption, IonSpinner, IonListHeader,
                IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid, IonRippleEffect,
                IonNote, IonTextarea, IonBadge, IonFab, IonFabButton, IonInfiniteScroll, IonInfiniteScrollContent, IonModal, IonAvatar,
                Swiper, SwiperSlide, ProgramInfoStats, ProgramItemContent, ProgramIssueReportModal, },
  setup(props) {
    // methods or filters
    const store = useStore();
    const { closeModal, doReorder, openModal, navigateMaterialCategories, getProxyImgLink,
            processUserItems, setUserItemReaction, isItemSelected, isItemDisliked, isMobileWeb,
            onThumbsUpItem, onThumbsDownItem, onClickMoreBtn, onClickPrevBtn, animateToFirstCard,
            recordCurrSlideReaction, resetActiveSlide, syncChosenItems, resetFilters, focusKeywordSearchbar,
            openImageModal, presentPrompt, sleep, htmlToPlainText, presentAlert,
            getBandLabel, getBandClass, openServiceModal,
            isMeetProgramElectiveRequirements, getUserSubjects, } = utils();
    const { t } = useI18n();

    const searchKeyword = ref("");
    const isSearching = ref(false);
    const delayLoading = ref(true);

    // Report Modal
    const showReportModal = ref(false);
    const selectedProgramForReport = ref(null);

    const openReportModal = (program) => {
      selectedProgramForReport.value = program;
      showReportModal.value = true;
    };

    const loggedInUser = computed(() => store.state.user);
    const user = computed(() => props.targetUser || store.state.user);
    const disciplineGroups = computed(() => store.state.allDisciplineGroups);
    const disciplines = computed(() => store.state.allDisciplines); // for discipline tab
    const relatedInstitutions = ref<Institution[]>([]);
    const allPrograms = ref<Program[]>([]);
    const chosenPrograms = ref<Program[]>(props.prefilledPrograms || []);
    const userPrograms = ref<UserProgram[]>(props.oldUserPrograms || []);
    const tmpNewUserPrograms = ref<UserProgram[]>([]); // for storing slideChange reaction
    const takenActions = ref(false);

    // TODO: Work Events (implanted promotion)
    const workServices = computed<Service[]>(() => store.getters.getServicesByTypes(["Work"]));

    // client programs
    const allClientProgramIds = computed(() => store.getters.allClientProgramIds);
    const advertisements = computed(() => store.state.allAdvertisements);

    // filters
    const selectedFilterGroup = ref('disciplines');
    const selectedOption = ref<any>(props.isAB3 ? 'all' : 'highlight');
    const nestedOption = ref('all'); // for nested filters for institutions
    const settings = computed(() => store.state.settings);

    const getAppState = () => ({
      selectedFilterGroup: selectedFilterGroup.value,
      selectedOption: selectedOption.value,
      searchKeyword: searchKeyword.value,
    });
    const confirmSelect = async (leaveWithoutSave = false) => {
      if (leaveWithoutSave) {
        if (takenActions.value) {
          await presentPrompt(`當前操作未儲存，確認要離開嗎?<br />Are you sure to leave without saving?`, async () => {
            await closeModal({});
          }, "注意 Attention");
        } else {
            await closeModal({});
        }
      } else {
        let newUserItems = processUserItems(chosenPrograms.value, userPrograms.value, tmpNewUserPrograms.value, 'programId', store.state.user.id);
        if (props.targetDiscipline?.id) {
          newUserItems = newUserItems.map(ui => ({ ...ui, disciplineId: props.targetDiscipline.id })); // link to related discipline
        }
        const loading = await loadingController.create({});
        await loading.present();
        await sleep(1);
        loading.dismiss();

        await closeModal({ "chosen": chosenPrograms.value, "userPrograms": newUserItems, }); // return selected program & order here
      }
    };

    // Helper functions for program reaction
    const setReaction = (programId: any, reaction: any, skipIfExists = false) => {
      setUserItemReaction(
        programId, reaction, 'programId', userPrograms, tmpNewUserPrograms, getAppState(), skipIfExists
      );
    }
    const isSelected = (program: any) => (isItemSelected(program, chosenPrograms));
    const isDisliked = (program: any) => (isItemDisliked(program, 'programId', userPrograms));

    const startIdx = ref(0); // for card slides

    const recordActiveSlideReaction = () => {
      setTimeout(() => {
        const slides: any = document.querySelector('#card-swiper-slides');
        if (slides) {
          const programId = slides.swiper.visibleSlides[0]?.dataset['programId'];
        }
      }, 200);
    }
    const initSlideListener = (reset = false) => {
      setTimeout(() => {
        // Init card slides
        delayLoading.value = false;
        setTimeout(() => {
          const slides: any = document.querySelector('#card-swiper-slides');
          if (slides) {
            slides.swiper.off('slideChange', recordActiveSlideReaction);
            slides.swiper.on('slideChange', recordActiveSlideReaction);
            recordActiveSlideReaction(); // initial slide
          }
        }, 200);
      }, 200);

      if (reset) resetActiveSlide(startIdx, delayLoading, 'programId', userPrograms, tmpNewUserPrograms, getAppState());
    }

    // INIT
    onMounted(() => {
      const { targetDiscipline, showFirstProgramId, } = props;
      if (targetDiscipline) {
        // AB3
        relatedInstitutions.value = store.getters.getInstitutionsByDisciplineId(targetDiscipline.id, true);
        allPrograms.value = store.getters.getProgramsByDisciplineId(targetDiscipline.id, true);
      } else {
        // Mock JUPAS
        relatedInstitutions.value = store.getters.getInstitutionsByProgramType('jupas'); // only JUPAS intitutions
        allPrograms.value = store.getters.getProgramsByType('jupas'); // JUPAS programs
      }
      if (showFirstProgramId) {
        selectedOption.value = 'all';
        allPrograms.value = [
          ...allPrograms.value.filter(p => p.id == showFirstProgramId),
          ...allPrograms.value.filter(p => p.id != showFirstProgramId),
        ]
      }
      syncChosenItems('programId', chosenPrograms, userPrograms, allPrograms.value);
      initSlideListener();
    })
    watch(selectedOption, () => {
      initSlideListener();
      if (selectedFilterGroup.value == 'disciplines') {
        nestedOption.value = 'all'; // discipline groups changed
      }
    })
    watch(nestedOption, () => {
      initSlideListener(true);
    })
    watch(selectedFilterGroup, (currGroup) => {
      if (['like', 'dislike'].includes(currGroup)) {
        selectedOption.value = currGroup;
      }
      else if (currGroup == 'random') {
        resetFilters(selectedFilterGroup, selectedOption);
        allPrograms.value = store.getters.shuffledPrograms;
        animateToFirstCard();
      }
      else if (currGroup == 'search') {
        resetFilters(selectedFilterGroup, selectedOption);
        focusKeywordSearchbar(isSearching);
      }
      else {
        if (currGroup == 'institutions') { // Mock JUPAS: first show highlight programs
          selectedOption.value = relatedInstitutions.value[0];
        }
        else if (currGroup == 'disciplines') {
          selectedOption.value = 'highlight';
          //selectedOption.value = disciplines.value[0];
        }
        nestedOption.value = 'all'; // reset filter
      }
    })

    // 3. return variables & methods to be used in template HTML
    return {
      // icons
      add, close, checkmark, arrowUp, arrowForward, arrowBack, checkbox, trashOutline,
      thumbsUpOutline, thumbsDownOutline, thumbsUp, thumbsDown, heart, heartOutline,
      chevronBack, chevronForward, repeat, search, flagOutline, logoWhatsapp,

      // variables
      user, loggedInUser,
      workServices, openServiceModal,
      selectedOption, nestedOption, disciplineGroups,
      allPrograms, disciplines,
      chosenPrograms, userPrograms, tmpNewUserPrograms,
      searchKeyword, isSearching,
      delayLoading, settings,
      takenActions,

      // Report Modal
      showReportModal,
      selectedProgramForReport,
      openReportModal,

      // methods
      t, isMobileWeb,
      getProxyImgLink, openImageModal,
      closeModal, confirmSelect,
      doReorder,
      isSelected, isDisliked,
      setReaction,
      onThumbsUp: (program: any) => {
        if (props.singleSelectMode) {
          return closeModal({ selectedProgram: program }); // choose & leave
        }
        onThumbsUpItem(program, 'programId', chosenPrograms, userPrograms, tmpNewUserPrograms, getAppState())
        takenActions.value = true;
      },
      onThumbsDown: (program: any) => {
        onThumbsDownItem(program, 'programId', chosenPrograms, userPrograms, tmpNewUserPrograms, getAppState())
        takenActions.value = true;
      },

      filteredPrograms: () => {
        if (props.isDemo) {
          //return allPrograms.value.filter(p => [161, 158].includes(Number(p.id)));
          return allPrograms.value.filter(p => [161, 285].includes(Number(p.id)));
        }
        if (searchKeyword.value) {
          return allPrograms.value.filter(p => {
            const targetText = `${p.name.toLowerCase()} ${(p.jupasCode || '').toLowerCase()}`;
            return targetText.includes(searchKeyword.value.toLowerCase().trim());
          });
        }
        if (['like', 'dislike'].includes(selectedOption.value)) {
          return userPrograms.value.filter(ue => ue.reaction == selectedOption.value).map(ue => {
            return allPrograms.value.find(e => e.id == ue.programId);
          });
        }
        let res = allPrograms.value;
        if (selectedOption.value == 'highlight') {
          res = allPrograms.value.filter(p => p.sortOrder != 99999).sort((a,b) => (a.sortOrder-b.sortOrder)); // featured programs
        }
        else if (selectedFilterGroup.value == 'institutions' && selectedOption.value.id) {
          res = allPrograms.value.filter(p => p.institutionId == selectedOption.value.id); // filter by institutions
          if (nestedOption.value != 'all') {
            res = res.filter(p => p.disciplines.some(d => d.disciplineGroupId.toString() == nestedOption.value)); // further filter by discipline groups
          }
        }
        else if (selectedFilterGroup.value == 'disciplines' && selectedOption.value.id) {
          res = allPrograms.value.filter(p => p.disciplines.some(d => d.disciplineGroupId.toString() == selectedOption.value.id)); // filter by discipline groups
          if (nestedOption.value != 'all') {
            res = res.filter(p => p.disciplines.some(d => d.id == nestedOption.value)); // further filter by disciplines
          }
        }
        return [
          ...res.filter(d => !isDisliked(d)),
          ...res.filter(d => isDisliked(d)),
        ];
      },

      // Selected programs (for single select mode)
      getBandLabel, getBandClass,

      // swiper
      modules: [EffectCards, Navigation, Virtual, IonicSlides],
      startIdx,
      onClickMoreBtn: () => { onClickMoreBtn(startIdx) },
      onClickPrevBtn: () => { onClickPrevBtn(startIdx) },

      // Filter groups & filters
      selectedFilterGroup, navigateMaterialCategories,
      relatedInstitutions,
      //relatedDisciplineGroups: computed(() => getDisciplineGroupsByInstitutionId(state, getters)(selectedOption.value.id)),

      getCompetitivenessColor: (ratio: any) => {
        if (ratio <= 6) {  // Bottom 35-40% - Less competitive
          return 'success';
        } else if (ratio <= 20) {  // Middle 45-60% - Moderately competitive
          return 'warning';
        } else {  // Top 15-20% - Highly competitive
          return 'danger';
        }
      },

      // For 'Careers' button
      openAIProfessionDeckModal: async (programId) => {
        const clientId = store.getters.getClientIdByProgramId(programId);
        return await openModal(ABProfessionSelectModal, { clientId, isFromProgramDeck: true });
      },
      allClientProgramIds,
      
      // Program Elective Requirements
      passProgramElectiveRequirements: (program) => {
        const { allSubjects } = store.state;
        const userObj = props.targetUser || user.value;
        const userSubjects = getUserSubjects(allSubjects, userObj);
        const userSubjectGrades = userObj.userSubjectGrades || [];
        return isMeetProgramElectiveRequirements(program, allSubjects, userSubjects, userSubjectGrades, 'overallTarget');
      },

      // Advertisements (assume 1 discipline / discipline group only 1 client)
      relatedAdvBanner: computed(() => {
        return advertisements.value.find(ad => {
          if (nestedOption.value != 'all') return ad.disciplineId == nestedOption.value; // Discipline
          return ad.disciplineGroupId == selectedOption.value.id; // Discipline Group
        });
      }),
    }
  }
});
</script>

<style scoped>
  .featured-tag {
    color: #fff;
    background-color: var(--ion-color-danger);
  }

  .ad-banner {
    width: 100%;
    padding: 32px 0;
    color: #fff;
    text-align: center;
    font-size: 20px;
    background-color: transparent;
    margin: 4px 0;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
  }
</style>
