<template>
  <ion-header>
    <ion-toolbar>
      <ion-title>Report Issue</ion-title>
      <ion-buttons slot="end">
        <ion-button @click="closeModal()">
          <ion-icon :icon="close"></ion-icon>
        </ion-button>
      </ion-buttons>
    </ion-toolbar>
  </ion-header>
  <ion-content class="ion-padding">
    <div v-if="viewMode === 'form'">
      <ion-list>
        <ion-item>
          <ion-label position="stacked">Program</ion-label>
          <ion-input readonly :value="programName"></ion-input>
        </ion-item>
        
        <ion-item>
          <ion-label position="stacked">Issue Type</ion-label>
          <ion-select interface="popover" v-model="issueType" placeholder="Select issue type">
            <ion-select-option value="inaccurate_info">Inaccurate Information</ion-select-option>
            <ion-select-option value="calculation_error">Calculation Error</ion-select-option>
            <ion-select-option value="missing_data">Missing Data</ion-select-option>
            <ion-select-option value="other">Other</ion-select-option>
          </ion-select>
        </ion-item>
        
        <ion-item>
          <ion-label position="stacked">Description</ion-label>
          <ion-textarea 
            v-model="description" 
            placeholder="Please describe the issue in detail"
            :rows="5"
          ></ion-textarea>
        </ion-item>
        
        <ion-item>
          <ion-label position="stacked">Screenshot (optional)</ion-label>
          <ion-input type="file" accept="image/*" @change="onImageFileChange($event)"></ion-input>
          <ion-buttons slot="end" v-if="photo">
            <ion-button @click="clearUploadedImage()">
              <ion-icon :icon="close"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-item>
        
        <div v-if="photo" class="image-preview">
          <img :src="photo" />
        </div>
      </ion-list>
      
      <ion-button expand="block" @click="submitReport" :disabled="!isFormValid">
        Submit Report
      </ion-button>
      
      <ion-button expand="block" fill="clear" @click="viewMode = 'history'" v-if="hasSubmittedReports">
        View Previous Reports
      </ion-button>
    </div>
    
    <div v-else-if="viewMode === 'history'">
      <ion-list>
        <ion-list-header>
          <ion-label>Previous Reports</ion-label>
        </ion-list-header>
        
        <ion-item v-for="(report, index) in previousReports" :key="index">
          <ion-label>
            <h2>{{ getIssueTypeLabel(report.issueType) }}</h2>
            <p>{{ formatDate(report.createdAt) }}</p>
            <p>{{ report.message }}</p>
          </ion-label>
          <ion-thumbnail slot="end" v-if="report.photo">
            <img :src="report.photo" @click="openImageModal(report.photo)" />
          </ion-thumbnail>
        </ion-item>
        
        <ion-item v-if="previousReports.length === 0">
          <ion-label class="ion-text-center">
            No previous reports found
          </ion-label>
        </ion-item>
      </ion-list>
      
      <ion-button expand="block" @click="viewMode = 'form'">
        Back to Report Form
      </ion-button>
    </div>
  </ion-content>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue';
import { 
  IonHeader, IonToolbar, IonTitle, IonContent, IonButtons, IonButton, 
  IonIcon, IonList, IonItem, IonLabel, IonInput, IonTextarea, 
  IonSelect, IonSelectOption, IonThumbnail, IonListHeader,
  loadingController, toastController
} from '@ionic/vue';
import { close } from 'ionicons/icons';
import { useStore } from '@/store';
import { utils } from '@/composables/utils';
import CommonService from '@/services/CommonService';

export default defineComponent({
  name: 'ProgramIssueReportModal',
  components: {
    IonHeader, IonToolbar, IonTitle, IonContent, IonButtons, IonButton, 
    IonIcon, IonList, IonItem, IonLabel, IonInput, IonTextarea, 
    IonSelect, IonSelectOption, IonThumbnail, IonListHeader
  },
  props: {
    program: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const store = useStore();
    const { closeModal, openImageModal, formatDate } = utils();
    const user = computed(() => store.state.user);
    
    // Form data
    const issueType = ref('');
    const description = ref('');
    const photo = ref('');
    const photoFile = ref<any>(null);
    const viewMode = ref('form');
    const previousReports = ref<any[]>([]);
    
    // Computed properties
    const programName = computed(() => {
      return `${props.program.institutionNameShort} - ${props.program.name} (${props.program.jupasCode})`;
    });
    
    const isFormValid = computed(() => {
      return issueType.value && description.value.trim().length > 0;
    });
    
    const hasSubmittedReports = ref(false);
    
    // Load previous reports
    const loadPreviousReports = async () => {
      try {
        // This would need to be implemented in the backend
        // For now, we'll just set a placeholder
        previousReports.value = [];
        hasSubmittedReports.value = previousReports.value.length > 0;
      } catch (error) {
        console.error('Error loading previous reports:', error);
      }
    };
    
    // Call this on component mount
    loadPreviousReports();
    
    // Methods
    const onImageFileChange = (e: any) => {
      if (e.target.files && e.target.files[0]) {
        photoFile.value = e.target.files[0];
        const reader = new FileReader();
        reader.onload = (e: any) => {
          photo.value = e.target.result;
        };
        reader.readAsDataURL(photoFile.value);
      }
    };
    
    const clearUploadedImage = () => {
      photo.value = '';
      photoFile.value = null;
    };
    
    const getIssueTypeLabel = (type: string) => {
      const types: Record<string, string> = {
        'inaccurate_info': 'Inaccurate Information',
        'calculation_error': 'Calculation Error',
        'missing_data': 'Missing Data',
        'other': 'Other'
      };
      return types[type] || type;
    };
    
    const submitReport = async () => {
      try {
        const loading = await loadingController.create({});
        await loading.present();
        
        const feedback = `[${getIssueTypeLabel(issueType.value)}] ${description.value}`;
        await CommonService.createNewFeedback(
          user.value.fullName || '',
          user.value.email || '',
          feedback,
          photoFile.value,
          props.program.id
        );
        
        loading.dismiss();
        
        const toast = await toastController.create({
          message: 'Your report has been submitted. Thank you!',
          duration: 3000,
          position: 'bottom'
        });
        toast.present();
        
        // Reset form
        issueType.value = '';
        description.value = '';
        clearUploadedImage();
        
        // Reload previous reports
        await loadPreviousReports();
        
        // Close modal
        closeModal();
      } catch (error) {
        console.error('Error submitting report:', error);
        const toast = await toastController.create({
          message: 'Failed to submit report. Please try again.',
          duration: 3000,
          position: 'bottom',
          color: 'danger'
        });
        toast.present();
      }
    };
    
    return {
      close,
      issueType,
      description,
      photo,
      viewMode,
      previousReports,
      programName,
      isFormValid,
      hasSubmittedReports,
      closeModal,
      onImageFileChange,
      clearUploadedImage,
      submitReport,
      getIssueTypeLabel,
      openImageModal,
      formatDate
    };
  }
});
</script>

<style scoped>
.image-preview {
  margin: 16px 0;
  text-align: center;
}

.image-preview img {
  max-width: 100%;
  max-height: 200px;
  border-radius: 8px;
}

ion-thumbnail {
  --size: 60px;
  cursor: pointer;
}
</style>
