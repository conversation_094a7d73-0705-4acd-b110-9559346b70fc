<template>
  <ion-page>
    <ion-header>
      <ion-grid class="ion-no-padding" fixed>
        <ion-toolbar style="--min-height: 32px">
          <ion-segment v-model="selectedView" mode="md" scrollable color="dark" style="min-height: 24px">
            <ion-segment-button value="teacher" class="no-text-transform short-segment-btn">Teacher view</ion-segment-button>
            <ion-segment-button value="student" class="no-text-transform short-segment-btn">Student view</ion-segment-button>
          </ion-segment>
        </ion-toolbar>
      </ion-grid>
    </ion-header>

    <ion-content>
      <ion-grid class="ion-no-padding" fixed>
        <TeacherAchieveJUPASModal :isDemo="true" v-if="selectedView === 'teacher'" />
        <AchieveJUPASResultPageModal :isDemo="true" v-else />
      </ion-grid>
    </ion-content>
  </ion-page>
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, watch, reactive, onMounted } from 'vue';

// icons
import { add, close, } from 'ionicons/icons';

// components
import { IonPage, IonHeader, IonToolbar, IonContent, IonGrid, IonSegment, IonSegmentButton } from '@ionic/vue';
import TeacherAchieveJUPASModal from '@/components/achievejupas/TeacherAchieveJUPASModal.vue';
import AchieveJUPASResultPageModal from '@/components/achievejupas/AchieveJUPASResultPageModal.vue';

// composables / services
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';

export default {
  components: { IonPage, IonHeader, IonToolbar, IonContent, IonGrid, IonSegment, IonSegmentButton,
                TeacherAchieveJUPASModal, AchieveJUPASResultPageModal, },
  setup(props) {
    const { t } = useI18n();
    const store = useStore();
    const selectedView = ref('teacher');

    return {
      selectedView,
    }
  }
};
</script>

<style scoped>
.short-segment-btn {
  min-height: 32px;
  height: 32px;
  --padding-top: 2px;
  --padding-bottom: 2px;
  font-size: 13px;
}

ion-segment {
  --background: transparent;
}

:deep(ion-segment-button) {
  min-height: 32px;
  --padding-top: 2px;
  --padding-bottom: 2px;
}
</style>