<template>
  <page-header :isAB3ParentView="true" v-if="isAB3ParentView"></page-header>

  <ion-header v-else>
    <ion-grid class="ion-no-padding" fixed>
      <ion-toolbar v-if="!isAB3ParentView && !hideTitle">
        <!-- Back buttons (back / close modal) -->
        <ion-buttons slot="start" v-if="!isDemo">
          <ion-button slot="icon-only" @click="confirmSelect()">
            <ion-icon :icon="close"></ion-icon>
          </ion-button>
        </ion-buttons>

        <!-- Title -->
        <ion-title class="ion-text-center" style="padding-right: 0">
          <ion-label class="ion-text-wrap" v-if="targetUser">
            <p><b>{{ user.schoolId == 'beacon1' ? 'Demo School' : user.schoolId?.toUpperCase() }}</b></p>
            <h2><span v-show="user.class">{{ user.class }}{{ formatStudentNumber(user.studentNumber) }}</span> {{ user.fullName }} <span v-if="user.chineseName">({{ user.chineseName }})</span></h2>
          </ion-label>
          <ion-label class="ion-text-wrap" v-else-if="isAB3">
            <h2><b>AB3</b></h2>
          </ion-label>
          <ion-label class="ion-text-wrap" v-else>
            <h2><b>AchieveJUPAS 2.0 (Beta)</b></h2>
          </ion-label>
        </ion-title>

        <!-- Header action buttons -->
        <ion-buttons slot="end" v-if="!loading && !isAB3">
          <!--<ion-button @click="openAchieveJUPASChartsModal()">
            <ion-icon slot="icon-only" :icon="statsChart"></ion-icon>-->
          <ion-button v-if="targetUser" @click="openUserProfileFormModal(targetUser)">
            <ion-icon slot="icon-only" :icon="pencil"></ion-icon>
          </ion-button>
          <ion-button v-else>
            <ion-icon slot="icon-only" :icon="undefined"></ion-icon>
          </ion-button>
        </ion-buttons>

        <!-- AB3 -->
        <ion-buttons slot="end" v-if="isAB3">
          <!-- QR code sharing with parents 
          <ion-button @click="openAB3QRCodeShareModal()">
            <ion-icon slot="icon-only" :icon="shareSocial"></ion-icon>
          </ion-button>-->

          <!-- AB3 Slide -->
          <ion-button @click="openImageModal(getProxyImgLink(`https://docs.google.com/presentation/d/1v8O0j-EYeGItQuS8N-lEj9qJQsV6oXOYtCG_y3x4F-U/export/jpeg?pageid=g2fa5c90539b_0_42`))">
            <ion-icon slot="icon-only" :icon="helpCircleOutline"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>

      <!--
        AB3: Step filters
      -->
      <ion-toolbar style="--min-height: 32px" v-if="isAB3">
        <ion-segment mode="ios" v-model="currStep" scrollable>
          <ion-segment-button :value="1">
            <ion-label><p>1. Disciplines</p></ion-label>
          </ion-segment-button>
          <ion-segment-button :value="2">
            <ion-label><p>2. Programs</p></ion-label>
          </ion-segment-button>
          <ion-segment-button :value="3" v-show="electiveCombinations[userRelatedSchool.id]">
            <ion-label><p>3. Electives</p></ion-label>
          </ion-segment-button>
        </ion-segment>
      </ion-toolbar>

      <!--
        AchieveJUPAS teacher view (checking specific student)
        -->
      <div v-if="targetUser">
        <!-- Student Tags-->
        <ion-toolbar class="tags-section">
          <div class="tags-container">
            <ion-chip v-for="tag in getTagOptions()" :key="tag.id" class="small-chip"
                      :outline="!targetUser.tagIds.includes(tag.id)" @click="toggleTag(tag)">
              <ion-label>{{ tag.name }}</ion-label>
            </ion-chip>
            <ion-button v-if="!showAddTag" class="add-tag-btn" size="small" fill="clear" @click="toggleAddTag">
              <ion-icon :icon="addCircleOutline"></ion-icon>
            </ion-button>
          </div>
          
          <!-- Add New Tag -->
          <div v-if="showAddTag" class="add-tag-section">
            <ion-item class="add-tag-input" lines="none">
              <ion-input v-model="newTagName" placeholder="New tag" @keyup.enter="addNewTag" class="small-input"></ion-input>
              <ion-button slot="end" size="small" @click="addNewTag">Add</ion-button>
              <ion-button slot="end" size="small" fill="clear" @click="toggleAddTag">Cancel</ion-button>
            </ion-item>
          </div>
        </ion-toolbar>

        <!--
          Teachers input consultation data & tags + Comments
          -->
        <div style="border: 1px solid #ccc; border-radius: 18px; margin-top: 16px; padding-top: 8px">
          <ion-accordion-group value="input">
            <ion-accordion value="input" style="background: transparent">
              <ion-item lines="full" slot="header" style="--background: transparent">
                <ion-label class="ion-text-wrap" style="color: var(--ion-color-dark)">
                  Next Consultation
                </ion-label>
              </ion-item>

              <!-- Teacher Councelling Students / Tag -->
              <div slot="content" class="consultation-section">
                <!-- Next Consultation -->
                <div class="consultation-inputs">
                  <div class="datetime-inputs">
                    <ion-input type="date" v-model="nextConsultation.date" @change="handleConsultationChange()"></ion-input>
                    <ion-input type="time" v-model="nextConsultation.time" @change="handleConsultationChange()"></ion-input>
                    <ion-input type="text" v-model="nextConsultation.venue" placeholder="Venue" @change="handleConsultationChange()"></ion-input>
                  </div>
                </div>

                <!-- Previous Consultations -->
                <ion-accordion-group class="compact-accordion">
                  <ion-accordion value="previous">
                    <ion-item slot="header" lines="none">
                      <ion-label style="color: var(--ion-color-medium)">Previous Consultations</ion-label>
                    </ion-item>
                    <div slot="content" class="previous-consultations">
                      <ion-item v-for="(consultation, index) in previousConsultations" :key="index" lines="none">
                        <ion-label>
                          <h3>{{ new Date(consultation.date).toLocaleDateString() }}</h3>
                          <p>{{ consultation.venue }}</p>
                          <p>{{ consultation.notes }}</p>
                        </ion-label>
                      </ion-item>
                    </div>
                  </ion-accordion>
                </ion-accordion-group>
              </div>
            </ion-accordion>
          </ion-accordion-group>

          <!-- Teacher's Remarks (separate for different choice plans?) -->
          <div style="padding: 4px 4px 0 4px" v-if="targetUser">
            <ion-textarea label-placement="floating" label="Any comments / remarks?" fill="outline" :rows="1"
                          style="--padding-start: 12px" v-model="nextConsultation.remarks"
                          @change="handleConsultationChange()"></ion-textarea>
            <ion-button class="ion-text-left no-text-transform" color="medium" fill="clear" size="small" @click="sendRemarksToTargetStudent()">
              Send to student's WhatsApp group
              <ion-icon slot="end" :icon="send"></ion-icon>
            </ion-button>
          </div>
        </div>
      </div>
    </ion-grid>
  </ion-header>
  
  <ion-content>
    <div class="spin" v-if="loading">
      <ion-spinner></ion-spinner>
    </div>
    <ion-grid class="ion-no-padding" fixed v-else>
      <!-- AB3 Parent View-->
      <ion-item lines="full" v-if="isAB3ParentView">
        <ion-label class="ion-text-wrap">
          <h3><i>The followings are chosen by your child / custodee at this moment:</i></h3>
        </ion-label>
      </ion-item>

      <!--
        AB3
        -->
      <div v-if="isAB3">
        <!-- 1. Discipline select -->
        <div v-show="currStep == 1">
          <AB3DisciplineResultList
            @addProgramToChoices="addProgramToChoices"
            @unselectProgram="unselectProgram"
            @selectedDisciplinesUpdated="selectedDisciplinesUpdated"
            :selectedPrograms="selectedPrograms"
            :readOnly="isAB3ParentView"
            v-if="isAB3"
          ></AB3DisciplineResultList>
        </div>

        <!-- 2. Browse elective requirements for programs -->
        <ion-accordion-group v-model="expandedProgramIds" :multiple="true" v-show="currStep == 2">
          <ion-reorder-group @ionItemReorder="onReorderProgram($event, selectedPrograms)" :disabled="isAB3ParentView ? true : false">
            <div v-for="(item, idx) in selectedPrograms" :key="item.id">
              <ion-accordion :value="item.id">
                <ion-item class="choice-item" lines="full" :class="getBandClass(idx)" slot="header">
                  <ion-reorder slot="start" style="margin-inline-end: 8px"></ion-reorder>

                  <!-- Program name & band -->
                  <ion-label class="ion-text-wrap">
                    <p>
                      <b>{{ getBandLabel(idx) }}.</b>&thinsp;
                      <span v-if="item.jupasUrl">[<a :href="item.jupasUrl" target="_blank">{{ item.jupasCode }}</a>] {{ item.label }}</span>
                      <span v-else>{{ item.displayName }}</span>
                    </p>
                  </ion-label>

                  <!-- Not allow deleting programs in parent's view -->
                  <ion-buttons v-if="!isAB3ParentView">
                    <ion-button @click.stop="onDeleteChosenProgram(idx, item)">
                      <ion-icon size="small" slot="icon-only" :icon="trashOutline"></ion-icon>
                    </ion-button>
                  </ion-buttons>
                </ion-item>

                <!-- Elective Requirements -->
                <ion-list style="padding: 0 0 0 40px" slot="content">
                  <program-elective-requirements :item="item"></program-elective-requirements>
                </ion-list>
              </ion-accordion>
            </div>
          </ion-reorder-group>
        </ion-accordion-group>

        <!-- 3. Elective select (school-based) -->
        <div v-show="currStep == 3">
          <ion-accordion-group :multiple="true" :value="0">
            <ion-accordion v-for="programId in [0,-1,-2]" :key="programId" :value="programId" style="--min-height: 24px">
              <ion-item lines="full" slot="header" color="light">
                <ion-label class="ion-text-wrap">
                  <!-- Choice Group -->
                  <b><p>{{ getElectiveChoiceLabel(programId) }}</p></b>

                  <div v-if="getSelectedUserElectives(programId).length > 0">
                    <!-- Possible Classes -->
                    <p v-if="getPossibleClasses(programId).length > 0">
                      Possible class{{ getPossibleClasses(programId).length > 1 ? 'es' : '' }}: <b>{{ getPossibleClasses(programId).join(" , ")}}</b>
                    </p>

                    <!-- Selected Elective Names -->
                    <p>Selected: <b>{{ getElectiveShortNames(getSelectedUserElectives(programId)).join(" , ")}}</b></p>
                  </div>
                </ion-label>
              </ion-item>
              <div slot="content">
                <ion-item v-for="num in [1,2,3,4]" :key="num" :disabled="isDisableElectiveSelect(programId, num)">
                  <!-- Available Elective Options -->
                  <ion-select :label="num == 4 ? `Math Options` : `Elective ${num}`" label-placement="floating" interface="popover"
                              :value="getUserElective(programId, num, 'like', 'electiveId')" @ionChange="onUpdateElectiveForProgram($event, programId, num)">
                    <ion-select-option v-for="opt in getAvailableElectives(num, programId)" :key="opt.id" :value="opt.id" :disabled="opt.isGroup">
                      <span v-if="opt.isGroup">{{ opt.name }}：</span>
                      <span v-else>&nbsp;&nbsp;&nbsp;{{ opt.name }}</span>
                    </ion-select-option>
                  </ion-select>

                  <!-- Clear Selection -->
                  <ion-buttons slot="end" v-if="getSelectedUserElectives(programId, 4).length <= num && getUserElective(programId, num, 'like', 'electiveId')">
                    <ion-button color="medium" @click.stop="clearUserElectiveSelect(programId, num)">
                      <ion-icon size="small" slot="start" :icon="trashOutline"></ion-icon>
                    </ion-button>
                  </ion-buttons>
                </ion-item>
                <!--<ion-item class="ion-margin-top">
                  <ion-label><p>Possible class{{ getPossibleClasses(programId).length > 1 ? 'es' : '' }}</p></ion-label>
                  <ion-chip v-for="cl in getPossibleClasses(programId)" :key="cl">{{ cl }}</ion-chip>
                </ion-item>-->
              </div>
            </ion-accordion>
          </ion-accordion-group>

          <!-- F3 Class Chart (only available for AB3 schools) -->
          <img style="width: 100%" :src="getProxyImgLink(userRelatedSchool.f3ClassChart)" v-if="userRelatedSchool.f3ClassChart"
                @click="openImageModal(getProxyImgLink(userRelatedSchool.f3ClassChart))" />
        </div>
      </div>

      <!--
        AchieveJUPAS (F4 - F6)
        -->
      <div v-else>
        <!-- Need Parent Consent to unlock access -->
        <div v-if="!targetUser && userRelatedSchool.achievejupasNeedParentConsent">
          <ion-item lines="none" button @click="openParentConsentModal()"
                    v-if="user.userConsentRecords?.find(r => r.target == 'achievejupas_parent_consent')">
            <ion-label class="ion-text-wrap">
              <h3><b>Parent Consent Obtained</b></h3>
            </ion-label>
            <ion-icon slot="end" :icon="checkmarkCircle" color="success"></ion-icon>
          </ion-item>

          <ion-item lines="none" color="danger" button detail @click="openParentConsentModal()" v-else>
            <ion-label class="ion-text-wrap">
              <h3><b>Parent Consent Required!</b></h3>
              <p>To continue using AchieveJUPAS, we need your parent's or guardian's consent.</p>
            </ion-label>
            <ion-button class="no-text-transform" slot="end">
              Start
            </ion-button>
          </ion-item>
        </div>

        <div>
          <!-- Deadlines (for F5 students only) -->
          <ion-accordion-group value="deadlines" v-if="(user.isAdmin || Number(user.yearDSE) >= getF5YearDSE()) && (userRelatedSchool.achievejupasDeadline1 || userRelatedSchool.achievejupasDeadline2)">
            <ion-accordion value="deadlines">
              <ion-item slot="header" lines="none">
                <ion-label><h3>Deadlines</h3></ion-label>
              </ion-item>
              <div slot="content">
                <ion-item v-if="userRelatedSchool.achievejupasDeadline1">
                  <ion-label class="ion-text-wrap">
                    <p>Fill in A1 to A3 by {{ userRelatedSchool.achievejupasDeadline1 }}</p>
                  </ion-label>
                  <ion-icon slot="end" :icon="checkmarkCircle" color="success" v-if="isFilledA1ToA3"></ion-icon>
                  <ion-icon slot="end" :icon="alertCircle" color="danger" v-else></ion-icon>
                </ion-item>
                <ion-item v-if="userRelatedSchool.achievejupasDeadline2">
                  <ion-label class="ion-text-wrap">
                    <p>Fill in all 20 choices by {{ userRelatedSchool.achievejupasDeadline2 }}</p>
                  </ion-label>
                  <ion-icon slot="end" :icon="checkmarkCircle" color="success" v-if="isFilled20Choices"></ion-icon>
                  <ion-icon slot="end" :icon="alertCircle" color="danger" v-else></ion-icon>
                </ion-item>
              </div>
            </ion-accordion>
          </ion-accordion-group>
          
          
          <!-- Teacher's comments -->
          <ion-accordion-group v-if="teacherComments.length > 0">
            <ion-accordion value="comments">
              <ion-item slot="header" lines="none">
                <ion-label><h3>Teacher Comments ({{ teacherComments.length }})</h3></ion-label>
              </ion-item>
              <div slot="content">
                <ion-item v-for="c in teacherComments" :key="c.id" lines="none">
                  <ion-label class="ion-text-wrap">
                    <p><small>From {{ c.fromUser.fullName }} ({{ formatDate(c.createdAt) }})</small></p>
                    <h3>{{ c.comment }}</h3>
                  </ion-label>
                </ion-item>
              </div>
            </ion-accordion>
          </ion-accordion-group>

          <!-- Demo Comment -->
          <div class="ion-padding" v-else-if="user.isAdmin || isDemo">
            <small>From Ms. Wong (2024/12/31 10:00)</small><br />
            中大有個人類學嘅Program，你可以考慮下
          </div>

          <!-- Switch View -->
          <div style="margin-top: 16px; margin-bottom: 24px">
            <!-- JUPAS / SNDAS / Overseas Study (show different views / forms) 
            <ion-segment mode="ios" scrollable v-model="selectedView">
              <ion-segment-button value="JUPAS Choices">JUPAS Choices</ion-segment-button>
              <ion-segment-button v-for="view in ['SNDAS', 'Overseas Study', 'E-APP']" :key="view" :value="view" disabled>
                <ion-label>{{ view }}</ion-label>
              </ion-segment-button>
            </ion-segment>-->

            <!-- Plan / Choice Combination Switcher (allow adding new) -->
            <ion-segment color="medium" mode="md" scrollable v-model="selectedPlan">
              <!-- ['Dec submission', 'Jul release: Outperform', 'Jul release: safety net', 'Best 5 20-35'] -->
              <ion-segment-button class="no-text-transform plan-segment-btn" value="Dec submission">
                <ion-label style="color: var(--ion-color-dark)">Dec Submission</ion-label>
              </ion-segment-button>

              <!-- TODO: allow adding plans dynamically -->
              <ion-segment-button class="no-text-transform plan-segment-btn" v-for="plan in ['+ (Safety Net...)']" :key="plan" :value="plan" disabled>
                <ion-label style="color: var(--ion-color-medium)">{{ plan }}</ion-label>
              </ion-segment-button>
            </ion-segment>

            <!--
              Global Score Inputs
              -->
            <div style="margin: 8px 0" :style="{ 'margin-top': targetUser ? 0 : '8px' }">
              <SubjectScoreInputSection :targetUser="targetUser" :title="targetUser ? 'Student Prediction' : 'My Prediction'" source="overallTarget" :passIcon="ellipse" :hideSubjectNames="true" :readonly="!!targetUser"
                                        :noBottomBorderRadius="true" :gradeSelectLargeFont="true"></SubjectScoreInputSection>

              <SubjectScoreInputSection :targetUser="targetUser" title="School Prediction" source="schoolPredict" :passIcon="star" :hideSubjectNames="false" :readonly="!targetUser"
                                        :noTopBorderRadius="true" :gradeSelectLargeFont="true"></SubjectScoreInputSection>
            </div>
          </div>

          <div v-if="selectedPlan == 'Dec submission'">
            <!-- A1 to E5 choices -->
            <ion-reorder-group @ionItemReorder="onReorderProgram($event, selectedPrograms)" :disabled="targetUser ? true : false">

              <ion-item-sliding v-for="(item, idx) in selectedPrograms" :key="item.id">
                <ion-item class="choice-item" lines="none" :class="getBandClass(idx)"
                          @click.stop="openABProgramDeckModal(item.id, false, true)" button>
                  <ProgramItemContent :item="item" :idx="idx" :allowDelete="!targetUser"
                                      @clickedPenBtn="openABProgramDeckModal(item.id, false, true)"
                                      @clickedTrashBtn="onDeleteChosenProgram(idx, item)"
                                      :hideDisciplineBadges="true" :showRecommendedEvents="!targetUser && item.id == 161"
                                      :schoolPredictScoreIconColor="getProgramScoreIconColor(item, 'schoolPredict')"
                                      :targetScoreIconColor="getProgramScoreIconColor(item, 'overallTarget')"
                  ></ProgramItemContent>

                  <ion-reorder slot="end" style="margin-inline-start: 4px; margin-inline-end: 0"></ion-reorder>
                </ion-item>

                <!-- Delete Choices -->
                <ion-item-options side="end">
                  <ion-item-option color="danger" @click.stop="onDeleteChosenProgram(idx, item)">
                    <ion-icon slot="icon-only" :icon="trashOutline"></ion-icon>
                  </ion-item-option>
                </ion-item-options>
              </ion-item-sliding>

            </ion-reorder-group>

            <!-- Dummy Program Placeholder (only pencil button & band label)-->
            <ion-item v-for="idx in Array.from({length: 20 - selectedPrograms.length}, (_, i) => i + selectedPrograms.length)" 
                      :key="`empty-${idx}`" class="choice-item" lines="full" :class="getBandClass(idx)"
                      @click="!targetUser && idx == selectedPrograms.length ? openABProgramDeckModal(null, false, true) : undefined"
                      :disabled="isDemo || (!targetUser && idx == selectedPrograms.length ? false : true)" button>
              <div class="program-content" style="width: 100%; display: flex; flex-direction: column; gap: 4px; padding: 5px 0">
                <div style="display: flex; gap: 4px; align-items: flex-start">
                  <!-- Left: Band Label -->
                  <div style="flex: 1">
                    <p style="margin: 0; font-size: 0.9em; line-height: 1.2">
                      <b>{{ getBandLabel(idx) }}</b>
                    </p>
                  </div>

                  <!-- Right: Edit Button -->
                  <div style="display: flex; gap: 8px" v-show="!isDemo && idx == selectedPrograms.length">
                    <ion-button size="small" fill="clear" style="margin: 0; height: 24px; --padding-start: 0; --padding-end: 0">
                      <ion-icon size="small" slot="icon-only" :icon="pencil"></ion-icon>
                    </ion-button>
                  </div>
                </div>
              </div>
            </ion-item>
          </div>

          <div v-else>
            <!-- Dummy Program Placeholder (only pencil button & band label)-->
            <ion-item v-for="idx in Array.from({length: 6}, (_, i) => i)" 
                      :key="`empty-${idx}`" class="choice-item" lines="full" :class="getBandClass(idx)"
                      @click="idx == 0 ? openABProgramDeckModal(null, false, true) : undefined"
                      :disabled="isDemo || (idx == 0 ? false : true)" button>
              <div class="program-content" style="width: 100%; display: flex; flex-direction: column; gap: 4px; padding: 5px 0">
                <div style="display: flex; gap: 4px; align-items: flex-start">
                  <!-- Left: Band Label -->
                  <div style="flex: 1">
                    <p style="margin: 0; font-size: 0.9em; line-height: 1.2">
                      <b>{{ getBandLabel(idx) }}</b>
                    </p>
                  </div>

                  <!-- Right: Edit Button -->
                  <div style="display: flex; gap: 8px" v-show="!isDemo && idx == 0">
                    <ion-button size="small" fill="clear" style="margin: 0; height: 24px; --padding-start: 0; --padding-end: 0">
                      <ion-icon size="small" slot="icon-only" :icon="pencil"></ion-icon>
                    </ion-button>
                  </div>
                </div>
              </div>
            </ion-item>
          </div>
        </div>
      </div>

      <!-- Small button for clearing all choices -->
      <div class="ion-text-center ion-padding" v-if="selectedPrograms.length > 0">
        <ion-button size="small" fill="clear" color="danger" @click="clearAllPrograms">
          <ion-icon size="small" slot="start" :icon="trashOutline"></ion-icon>
          <small>Clear All Choices</small>
        </ion-button>
      </div>

      <!-- Disclaimer -->
      <ion-card>
        <ion-card-content>
          <p style="line-height: 1">
            <small>Disclaimer: The data presented is for reference purposes only. While we strive to provide up-to-date information, we do not guarantee their correctness, completeness, or timeliness. Users are advised to verify the details with the relevant educational authorities or institutions.</small>
          </p>
        </ion-card-content>
      </ion-card>
    </ion-grid>
  </ion-content>
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, onMounted, watch, reactive } from 'vue';
import { trashOutline } from 'ionicons/icons';

// icons
import { add, close, checkmark, arrowUp, arrowForward, arrowBack, checkbox,
         thumbsUpOutline, thumbsDownOutline, thumbsUp, thumbsDown, heart, heartOutline,
         pencil, statsChart, send, search, helpCircleOutline, shareSocial, addCircleOutline, caretDown,
         starOutline, star, ellipse, ellipseOutline, checkmarkCircle, alertCircle, closeCircle } from 'ionicons/icons';

// components
import { IonPage, IonGrid, IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonReorder, IonReorderGroup,
        IonList, IonItem, IonItemSliding, IonItemOptions, IonItemOption, IonLabel, IonIcon, IonButtons, IonButton, IonTextarea, IonText,
        IonCard, IonCardHeader, IonCardTitle, IonCardSubtitle, IonCardContent, IonSegment, IonSegmentButton,
        IonBadge, IonAccordionGroup, IonAccordion, IonSpinner, IonSelect, IonSelectOption, IonChip, IonPopover, IonModal,
        IonInput, isPlatform, modalController, loadingController, } from '@ionic/vue';
import ABProgramDeckModal from '@/components/achievejupas/ABProgramDeckModal.vue';
import AchieveJUPASChartsModal from '@/components/achievejupas/AchieveJUPASChartsModal.vue';
import AB3DisciplineResultList from '@/components/secondary/ab3/AB3DisciplineResultList.vue';
import ProgramInfoStats from '@/components/achievejupas/ProgramInfoStats.vue';
import ProgramElectiveRequirements from '@/components/achievejupas/ProgramElectiveRequirements.vue';
import ProgramItemContent from '@/components/achievejupas/ProgramItemContent.vue';
import SubjectScoreInputSection from '@/components/achievejupas/SubjectScoreInputSection.vue';
import UserProfileFormModal from '@/components/modals/UserProfileFormModal.vue';

// Swiper
import Slides from '@/components/shared/Slides.vue';
import { SwiperSlide } from 'swiper/vue/swiper-vue';

// composables / services
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';
import { useRoute, useRouter } from 'vue-router';

// types
import { Elective, Program, School, Service, User, UserElective, UserProgram } from '@/types';

// services
import ABSService from '@/services/ABSService';
import SLPService from '@/services/SLPService';
import AchieveJUPASService from '@/services/AchieveJUPASService';

import ParentConsentSignatureModal from '@/components/modals/ParentConsentSignatureModal.vue';

export default defineComponent({
  name: 'AchieveJUPASResultPageModal',
  props: ["isPage", "isAB3", "isAB3ParentView", "targetUser", "isDemo", "hideTitle"],
  components: { IonPage, IonHeader, IonToolbar, IonTitle, IonContent, IonButtons, IonButton, IonIcon, IonList, IonItem,
    IonItemSliding, IonItemOptions, IonItemOption, IonLabel, IonRow, IonCol, IonTextarea, IonText, IonFooter, IonGrid, IonReorder, IonReorderGroup,
    IonCard, IonCardHeader, IonCardTitle, IonCardSubtitle, IonCardContent, IonSegment, IonSegmentButton,
    IonBadge, IonAccordionGroup, IonAccordion, IonSpinner, IonSelect, IonSelectOption, IonChip, IonPopover, IonModal,
    IonInput, AB3DisciplineResultList, ProgramInfoStats, ProgramElectiveRequirements,
    SwiperSlide, Slides, ProgramItemContent, SubjectScoreInputSection,
  },
  setup(props) {
    // methods or filters
    const store = useStore();
    const { closeModal, doReorder, openModal, syncChosenItems, presentPrompt, getBandLabel, getBandClass, getDisciplineGroupColor, presentToast, sleep,
            linkNestedChosenItems, processUserItems, getQRCodeUrl, getProxyImgLink, openImageModal, openBrowser, formatStudentNumber, uniqueId,
            getUserSubjects, formatDate, getScoreDiff, getDSEScore, getF5YearDSE,
            isMeetUniEntranceRequirements, isMeetProgramElectiveRequirements, } = utils();
    const { t } = useI18n();
    const router = useRouter();
    const route = useRoute();

    const loading = computed(() => store.state.loadingPortalData || store.state.loadingUser);
    const user = computed<User>(() => props.targetUser || store.state.user);
    const userRelatedSchool = computed<School>(() => store.getters.userRelatedSchool);
    const allClientProgramIds = computed(() => store.getters.allClientProgramIds);
    const allPrograms = computed<Program[]>(() => store.state.allPrograms);
    const selectedPrograms = ref<Program[]>([]);
    const userPrograms = ref<UserProgram[]>([]);
    const oldUserPrograms = ref("");
    const expandedProgramIds = ref([]);

    // Fake Demo
    const selectedView = ref("JUPAS Choices");
    const selectedPlan = ref("Dec submission");

    // Consultation (for teachers)
    const nextConsultation = reactive({
      id: '',
      date: '',
      time: '',
      venue: '',
      remarks: '',
    });
    const previousConsultations = ref([]);
    const teacherComments = ref([]);

    // AB3
    const selectedDisciplines = ref([]);
    const currStep = ref(props.isAB3 ? 1 : 2);
    const allElectives = computed<Elective[]>(() => store.state.allElectives);
    const userElectives = ref<UserElective[]>([]);
    const getUserElective = (programId, targetOrder, targetReaction: any = null, targetKey = '') => {
      const res = userElectives.value.find(ue => ue.programId == programId && ue.order == targetOrder && (targetReaction == null || ue.reaction == targetReaction));
      return res ? (targetKey ? res[targetKey] : res) : null;
    }
    const getElectiveChoiceLabel = (programId) => {
      if (programId == 0) return `1st Choice`;
      if (programId == -1) return `2nd Choice`;
      if (programId == -2) return `3rd Choice`;
      return `Choice`;
    }

    const syncUserElectivesToDB = (targetUserElectives?: UserElective[]) => {
      ABSService.upsertUserElectives(targetUserElectives || userElectives.value); // For AB3: save user selected electives
      //store.commit('updateUser', { userElectives: userElectives.value });
    }
    const syncUserProgramsToDB = (commitUserPrograms = false) => {
      if (props.targetUser || props.isDemo) return false; // checking user choices: readonly

      // Sync User Selected Electives (under each program)
      for (const prog of selectedPrograms.value) {
        if (prog.selectedElectives) {
          userElectives.value = processUserItems(prog.selectedElectives, userElectives.value, [], 'electiveId', user.value.id)
        }
      }

      // Save User Selected Programs & Electives
      userPrograms.value = processUserItems(selectedPrograms.value, userPrograms.value, [], 'programId', user.value.id);
      const data = {
        "selectedPrograms": selectedPrograms.value,
        "userPrograms": userPrograms.value,
        "userElectives": userElectives.value,
      }
      try {
        const { userPrograms, userElectives, } = data;
        const updatedUserObj = {};
        let userProgramsStr = "Updated";
        try {
          userProgramsStr = JSON.stringify(userPrograms);
        } catch (e) {
          console.error(e);
        }
        if (oldUserPrograms.value != userProgramsStr) {
          ABSService.upsertUserPrograms(userPrograms);
          if (commitUserPrograms) updatedUserObj['userPrograms'] = userPrograms;
          user.value.userPrograms = userPrograms; // Bug fix: switch between teacher & student view
        }
        if (props.isAB3) {
          ABSService.upsertUserElectives(userElectives); // For AB3: save user selected electives
          if (commitUserPrograms) updatedUserObj['userElectives'] = userElectives;
        }
        if (Object.keys(updatedUserObj).length > 0) store.commit('updateUser', updatedUserObj);
      } catch (e) {
        console.error(e);
      }
      return data;
    }
    const confirmSelect = async () => {
      if (props.targetUser) {
        await closeModal({}); // not allow saving (readonly)
      } else {
        const data = syncUserProgramsToDB(true);
        if (props.isPage) {
          router.replace(user.value.teacher ? '/services/mock-jupas' : '/home');
        } else {
          await closeModal(data); // return selected items & order here
        }
      }
    };

    const syncUserPrograms = () => {
      if (props.isDemo) {
        // Show demo programs only
        userPrograms.value = [161, 285].map((programId, idx) => ({
          "programId": programId,
          "reaction": "like",
          "reason": "",
          "createdAt": "2025-01-12T00:00:00.000+00:00",
          "appState": {},
          "order": idx+1
        }));
        syncChosenItems('programId', selectedPrograms, userPrograms, allPrograms.value);
      } else {
        // Load previous choices
        // TODO: sync only current plan choices (or just disable the button first?)
        // score also need to be plan-dependant
        oldUserPrograms.value = JSON.stringify((user.value.userPrograms || []).sort((a,b) => Number(a.order)-Number(b.order)));
        userPrograms.value = JSON.parse(oldUserPrograms.value || '[]');

        // Prefill with previously written reasons
        syncChosenItems('programId', selectedPrograms, userPrograms, allPrograms.value);
      }

      // AB3
      if (props.isAB3) {
        // Load previous choices
        const oldUserElectives = JSON.stringify((user.value.userElectives || []).sort((a,b) => Number(a.order)-Number(b.order)));
        userElectives.value = JSON.parse(oldUserElectives || '[]');

        // link selected electives to programs
        linkNestedChosenItems(userElectives, allElectives, 'electiveId', allPrograms, 'programId', 'selectedElectives');
      }
    }

    onMounted(() => {
      syncUserPrograms();

      // AB3 parent's view: retrieve student's data
      if (props.isAB3ParentView) {
        const targetUserId = route.params.userId;
        ABSService.getStudentByUserId(targetUserId).then(res => {
          console.log(res);
          store.commit('receiveUser', res);
          syncUserPrograms();
        });
      }

      // Sync consultation data
      if (props.targetUser) {
        AchieveJUPASService.getJUPASConsultationRecords(props.targetUser.id).then(res => {
          const latestConsultation = res[0];
          // TODO: add to previousConsultations (when to define a new consultation?)
          if (latestConsultation) {
            nextConsultation.id = latestConsultation.id;
            nextConsultation.date = latestConsultation.date;
            nextConsultation.time = latestConsultation.time;
            nextConsultation.venue = latestConsultation.venue;
            nextConsultation.remarks = latestConsultation.remarks;
          }
        })
      } else {
        AchieveJUPASService.getUserComments('achievejupas').then(res => {
          teacherComments.value = res || [];
        })
      }
    })
    watch(user, () => {
      syncUserPrograms(); // direct access /mock-jupas
    })
    watch(loading, () => {
      syncUserPrograms(); // direct access /mock-jupas
    })

    // School customization
    const electiveCombinations: any = {
      'htc': {
        "4A": [
          [4,10,1,2], // CHist / Geog / Bio / BAFS (Accounting)
          [7,10,12,23,13,1], // Econ / Geog / History / VA / ICT / Bio
          [7,2,1,13], // Econ / BAFS (Accounting / Business Management) / Bio / ICT
          [],
        ],
        "4B": [
          [4,10,1,2], // CHist / Geog / Bio / BAFS (Accounting)
          [7,10,12,23,13,1], // Econ / Geog / History / VA / ICT / Bio
          [7,2,1,13], // Econ / BAFS (Accounting / Business Management) / Bio / ICT
          [],
        ],
        "4C": [
          [4,10,1,2], // CHist / Geog / Bio / BAFS (Accounting)
          [7,10,12,23,13,1], // Econ / Geog / History / VA / ICT / Bio
          [7,2,1,13], // Econ / BAFS (Accounting / Business Management) / Bio / ICT
          [],
        ],
        "4D": [
          [3],
          [10,3,1,13,7], // Geog / Chem / Bio / ICT / Econ
          [7,2,1,18,13], // Econ / BAFS (Accounting / Business Management) / Bio / Phy / ICT
          [14], // M1 / M2
        ],
        "4E": [
          [18],
          [10,3,1,13,7], // Geog / Chem / Bio / ICT / Econ
          [7,2,1,18,13], // Econ / BAFS (Accounting / Business Management) / Bio / Phy / ICT
          [15], // M1 / M2
        ]
      },
      'cfss': {
        "4A": [
          [18,7,1], // Phy / Econ / Bio
          [18,1,3,13,12,2,8,4,23], // Phy / Bio / Chem / ICT / Hist / BAFS / Lit in Eng / C Hist / VA
          [10,13,3], // Geog / ICT / Chem
          [14,15], // M1 / M2
        ],
        "4B": [
          [18,7,1],
          [18,1,3,13,12,2,8,4,23],
          [10,13,3],
          [],
        ],
        "4C": [
          [3],
          [18,1,3,13,12,2,8,4,23],
          [],
          [14,15],
        ],
        "4D": [
          [7,10,5],
          [18,1,3,13,12,2,8,4,23],
          [],
          [14],
        ],
        "4E": [
          [7,10,5],
          [18,1,3,13,12,2,8,4,23],
          [],
          [],
        ],
      }
    }
    const getElectiveShortNames = (userElectives) => {
      return userElectives.map(ue => allElectives.value.find(e => e.id == ue.electiveId)?.shortName);
    }
    const getSelectedUserElectives = (checkProgramId = 0, excludeOrder: any = null) => {
      return userElectives.value.filter(ue => {
        if (excludeOrder && ue.order == excludeOrder) return false;
        if (!ue.order || ue.order < 1 || ue.order > 4) return false;
        return ue.programId == checkProgramId && ue.reaction == 'like';
      }).sort((a: any, b: any) => a.order-b.order);
    }
    const getPossibleClasses = (checkProgramId = 0) => {
      const selectedUserElectives = getSelectedUserElectives(checkProgramId);
      const possibleClasses: any = [];
      const combinations = electiveCombinations[userRelatedSchool.value.id]; // school-based customization
      if (combinations) {
        for (const cl in combinations) {
          const idGroups = combinations[cl];
          if (selectedUserElectives.every((ue: any) => (idGroups[ue.order-1].includes(Number(ue.electiveId))))) {
            possibleClasses.push(cl);
          }
        }
        return possibleClasses;
      }
      return ['4A','4B','4C','4D','4E'];
    }

    // Student Tags Management
    const defaultTags = ['Don\'t know what I want', 'Elite', 'SNDAS'].map(t => ({ id: `t${uniqueId()}`, name: t }));
    const newTagName = ref('');
    const showAddTag = ref(false);

    const addUserToTag = async (tag) => {
      props.targetUser?.tagIds.push(tag.id);
      AchieveJUPASService.addStudentToTeacherDefinedTag(tag.id, tag.name, props.targetUser.id);
      if (!store.state.user.createdTags?.some(t => t.id == tag.id)) {
        const loading = await loadingController.create({});
        await loading.present();
        store.commit('upsertUserCreatedTags', [{ id: tag.id, name: tag.name }]);
        loading.dismiss();
      }
    }

    const toggleTag = async (tag) => {
      const index = props.targetUser?.tagIds?.indexOf(tag.id);
      if (index === -1) {
        addUserToTag(tag);
      } else {
        props.targetUser?.tagIds.splice(index, 1);
        AchieveJUPASService.removeStudentFromTeacherDefinedTag(tag.id, props.targetUser.id);
      }
    }
    const toggleAddTag = () => {
      showAddTag.value = !showAddTag.value;
      if (!showAddTag.value) newTagName.value = '';
    }
    const addNewTag = () => {
      const tagToAdd = newTagName.value.trim();
      if (tagToAdd) {
        addUserToTag({ id: `t${uniqueId()}`, name: tagToAdd });
        newTagName.value = '';
        showAddTag.value = false;
      }
    }
    
    // AchieveJUPAS (score calculation)
    const allSubjects = computed(() => store.state.allSubjects);
    const userSubjects = computed(() => {
      const userObj = props.targetUser || user.value;
      return getUserSubjects(allSubjects.value, userObj);
    });
    const getUserSubjectGrades = () => {
      const userObj = props.targetUser || user.value;
      return userObj.userSubjectGrades || [];
    };
    const getProgramScoreIconColor = (program, source) => {
      if (!isMeetProgramElectiveRequirements(program, allSubjects.value, userSubjects.value, getUserSubjectGrades(), source)) {
        return 'medium'; // not pass elective requirements
      }
      if (!isMeetUniEntranceRequirements(getUserSubjectGrades().filter(sg => sg.source == source))) {
        return 'danger'; // not pass min uni entrance requirements (332)
      }
      const scoreDiff = getScoreDiff(program, allSubjects.value, userSubjects.value, getUserSubjectGrades(), source);
      if (scoreDiff == null) return 'light';
      return scoreDiff >= 0 ? 'success' : 'danger';
    }

    return {
      // icons
      add, close, checkmark, arrowUp, arrowForward, arrowBack, checkbox, trashOutline,
      thumbsUpOutline, thumbsDownOutline, thumbsUp, thumbsDown, heart, heartOutline, pencil,
      statsChart, send, search, helpCircleOutline, shareSocial, addCircleOutline, caretDown,
      starOutline, ellipseOutline, checkmarkCircle, alertCircle, closeCircle,
      star, ellipse, // meet median scores? (Red/Green light)

      // variables
      store,
      user, userRelatedSchool,
      loading, selectedPrograms, userPrograms,
      allElectives,
      currStep,
      expandedProgramIds,

      // Student tag management (for consultation etc.)
      getTagOptions: () => {
        const userCreatedTags = (store.state.user.createdTags || []); // tags defined by teacher
        if (userCreatedTags.length == 0) return defaultTags;
        return [...userCreatedTags, ...defaultTags.filter(dt => !userCreatedTags.some(uct => uct.name == dt.name))];
      },
      newTagName, showAddTag, addNewTag,
      toggleTag, toggleAddTag,

      // methods
      t, openBrowser, formatStudentNumber,
      closeModal, confirmSelect, getF5YearDSE,
      doReorder, getProxyImgLink, openImageModal,
      openAchieveJUPASChartsModal: async () => (await openModal(AchieveJUPASChartsModal, { userPrograms: userPrograms.value })),
      openABProgramDeckModal: async (showFirstProgramId = null, isGPT = false, singleSelectMode = false) => {
        const modal = await modalController.create({
          cssClass: 'tall-modal',
          component: ABProgramDeckModal, 
          componentProps: {
            prefilledPrograms: selectedPrograms.value.slice(),
            oldUserPrograms: userPrograms.value.slice() || [],
            showFirstProgramId, singleSelectMode,
            isAB3: props.isAB3,
            isGPT,
            targetUser: props.targetUser,
            isDemo: props.isDemo,
          }
        });
        modal.onDidDismiss().then(({ data }) => {
          if (data && !props.targetUser) {
            if (data.chosen) {
              selectedPrograms.value = data.chosen;
              userPrograms.value = data.userPrograms;

              syncUserProgramsToDB(); // auto-save
            }
            if (data.selectedProgram) {
              const targetIdx = selectedPrograms.value.findIndex(p => p.id == showFirstProgramId);
              if (targetIdx > -1) {
                selectedPrograms.value[targetIdx] = data.selectedProgram; // replace the choice
              } else {
                selectedPrograms.value.push(data.selectedProgram); // new choice
              }
              syncUserProgramsToDB(); // auto-save
            }
          }
        });
        return modal.present();
      },

      onDeleteChosenProgram: (idx, program) => {
        const deleteProgram = () => {
          selectedPrograms.value.splice(idx, 1);
          const relatedUserItem = userPrograms.value.find(up => up.programId == program.id);
          if (relatedUserItem) relatedUserItem.reaction = '';
          syncUserProgramsToDB(); // auto-save
        }
        if (user.value.isAdmin) deleteProgram();
        else presentPrompt("Confirm delete?", deleteProgram);
      },
      
      clearAllPrograms: () => {
        const clearAll = () => {
          // Clear all programs from the selected list
          selectedPrograms.value = [];
          // Clear reactions from all user programs
          userPrograms.value.forEach(up => up.reaction = '');
          syncUserProgramsToDB(); // auto-save
        };
        presentPrompt("Are you sure you want to clear all your program choices?", clearAll);
      },
      onReorderProgram: (event: CustomEvent, targetArr: any) => {
        doReorder(event, targetArr);
        syncUserProgramsToDB(); // auto-save
      },
      saveUserProgramReason: (idx, p) => { // mainly for auto-save after filling reasons
        userPrograms.value = processUserItems(selectedPrograms.value, userPrograms.value, [], 'programId', user.value.id);
        ABSService.upsertUserPrograms(userPrograms.value.filter(up => up.reaction == 'like'));

        const baseMsg = `reason for choosing\n*${getBandLabel(idx)}. ${p.displayName} ${p.nameChi || ""}*\nReason: _${p.reason}_`;
        const { phone, waGroupId, fullName, schoolId, } = user.value;
        const msgToStudent = `@852${phone} Thanks for filling out your ${baseMsg}`;
        const internalMsg = `${schoolId?.toUpperCase()} ${user.value.class} ${fullName} (${phone})\nsubmitted ${baseMsg}`;

        SLPService.sendWhatsAppMsg(msgToStudent, user.value.phone, user.value.waGroupId); // send to students
        SLPService.sendWhatsAppMsg(internalMsg, "", "<EMAIL>"); // send to Internal Group
      },
      allClientProgramIds, // Ask reason when selected client programs

      // Mock JUPAS
      getBandLabel, getBandClass,
      getProgramIdxInList: (p) => {
        return selectedPrograms.value.findIndex(program => program.id == p.id);
      },
      addProgramToChoices: (program, afterIdx = -1) => {
        if (afterIdx >= 0) selectedPrograms.value.splice(afterIdx+1, 0, program);
        else selectedPrograms.value.push(program);
        syncUserProgramsToDB(); // auto-save
      },
      unselectProgram: (program) => {
        const idx = selectedPrograms.value.findIndex(p => p.id == program.id);
        if (idx !== -1) {
          selectedPrograms.value.splice(idx, 1);
          const relatedUserItem = userPrograms.value.find(up => up.programId == program.id);
          if (relatedUserItem) relatedUserItem.reaction = '';
          syncUserProgramsToDB(); // auto-save
        }
      },
      getDisciplineGroupColor,

      sendMockJUPASWhatsAppRecord: async () => {
        presentPrompt("Send the above choices to your WhatsApp group for records?", async () => {
          const loading = await loadingController.create({});
          await loading.present();
          let msg = `@852${user.value.phone} `, imgLink = "";
          if (props.isAB3) {
            const encryptedUserId = await ABSService.getEncryptedUserId(user.value.id);
            const parentLink = `https://ab.fdmt.hk/ab3-pv/${encryptedUserId}`;
            imgLink = getQRCodeUrl(parentLink); // attached QR code for students sharing with their parents

            msg += `Below are your latest choices for your records:\n\n`
            msg += `Share with parents: forward ${parentLink} or scan the QR code\n\n`;

            // Disciplines
            msg += `_*Discipline(s):*_\n`
            msg += selectedDisciplines.value.map((d: any, idx) => `*${idx+1}.* ${d.name} ${d.nameChi || ""}`).join("\n");

            // Programs
            msg += `\n\n_*Program(s):*_\n`;
            msg += selectedPrograms.value.map((p, idx) => `*${getBandLabel(idx)}.* ${p.displayName} ${p.nameChi || ""}`).join("\n");

            // Electives
            msg += `\n\n_*Elective(s):*_`;
            for (const programId of [0,-1,-2]) {
              const selectedUEs = getSelectedUserElectives(programId);
              if (selectedUEs.length > 0) {
                msg += `\n\n${getElectiveChoiceLabel(programId)}: *${getElectiveShortNames(getSelectedUserElectives(programId)).join(" , ")}*`;
                msg += `\nPossible class${getPossibleClasses().length > 1 ? 'es' : ''}: *${getPossibleClasses().join(" / ")}*`; // Show possible classes
              }
            }
          }
          else {
            // AchieveJUPAS
            msg += `Below are your latest program choices for your records:\n\n`;
            msg += selectedPrograms.value.map((p, idx) => `*${getBandLabel(idx)}.* ${p.displayName} ${p.nameChi || ""}`).join("\n");
          }
          SLPService.sendWhatsAppMsg(msg, user.value.phone, user.value.waGroupId, imgLink);
          await sleep(2);
          loading.dismiss();
          presentToast("Your choices will be sent to your WhatsApp group in minutes.");
        });
      },

      // AB3
      selectedDisciplinesUpdated: (latestDisciplines) => {
        // Event listener: child component -> selected disciplines
        selectedDisciplines.value = latestDisciplines; // Mainly for sending WhatsApp records if needed
      },
      getUserElective, getElectiveChoiceLabel, getElectiveShortNames,
      onUpdateElectiveForProgram: (ev, programId, targetOrder) => {
        const newElectiveId = ev.detail.value;

        let found = false;
        const relatedUserElectives = userElectives.value.filter(ue => ue.programId == programId && ue.order == targetOrder);
        for (const ue of relatedUserElectives) {
          if (ue.electiveId == newElectiveId) { // matched
            ue.reaction = 'like';
            found = true;
          } else { // no reaction for others
            ue.reaction = '';
          }
        }
        if (!found) {
          userElectives.value.push({
            programId,
            order: targetOrder,
            reaction: 'like',
            electiveId: newElectiveId,
            reason: "",
            createdAt: new Date(),
            appState: {},
          });
        }
        if (programId > 0) syncUserProgramsToDB(); // auto save
        else syncUserElectivesToDB(relatedUserElectives); // only sync related user electives
      },
      clearUserElectiveSelect: (programId, targetOrder) => {
        const relatedUserElectives = userElectives.value.filter(ue => ue.programId == programId && ue.order == targetOrder);
        for (const ue of relatedUserElectives) ue.reaction = '';
        if (programId > 0) syncUserProgramsToDB(); // auto save
        else syncUserElectivesToDB(relatedUserElectives); // only sync related user electives
      },
      getSelectedUserElectives, getPossibleClasses,
      electiveCombinations,
      getAvailableElectives: (targetOrder, checkProgramId, program?: Program) => {
        // Skip selected elective
        const selectedUserElectives = getSelectedUserElectives(checkProgramId).filter(ue => ue.order != targetOrder); // except the current list

        // Program-specific requirements (currently not in use)
        if (program) {
          const { requiredElective1, requiredElective2, requiredElective3, preferredSubjects, } = program;
          const res = allElectives.value.filter(e => {
            if (targetOrder == 1 && requiredElective1.length > 0) return requiredElective1.includes(e.name);
            if (targetOrder == 2 && requiredElective2.length > 0) return requiredElective2.includes(e.name);
            if (targetOrder == 3 && requiredElective3.length > 0) return requiredElective3.includes(e.name);
            return !selectedUserElectives.find(ue => ue.electiveId == e.id);
          }).map(e => ({
            ...e,
            isRequired: requiredElective1.includes(e.name) || requiredElective2.includes(e.name) || requiredElective3.includes(e.name),
            isPreferred: preferredSubjects.includes(e.name),
          }));

          // Must-take electives
          const requiredElectives: any = res.filter(e => e.isRequired);
          if (requiredElectives.length > 0) requiredElectives.unshift({ isGroup: true, name: "Must-take", id: "requiredGroup" })
          
          // Preferred electives
          const preferredElectives: any = res.filter(e => e.isPreferred);
          if (preferredElectives.length > 0) preferredElectives.unshift({ isGroup: true, name: "Upweighted", id: "preferredGroup" });
          let opts = preferredElectives;
          
          // Include remaining electives
          if (preferredElectives.length > 0) opts.push({ isGroup: true, name: "Others", id: "other" })
          opts = opts.concat(res.filter(e => !e.isRequired && !e.isPreferred));
          
          return opts;
        }

        // School-based requirements (elective combinations)
        let possibleClasses =  ['4A', '4B', '4C', '4D', '4E'];
        //if (targetOrder >= 3) possibleClasses = getPossibleClasses(checkProgramId);
        if (selectedUserElectives.length > 0) possibleClasses = getPossibleClasses(checkProgramId);

        let possibleElectiveIds = [];
        const combinations = electiveCombinations[userRelatedSchool.value.id]; // school-based customization
        if (combinations) {
          for (const cl in combinations) {
            const idGroups: any = combinations[cl], ids = idGroups[targetOrder-1];

            // 1. Class default elective choices for 'targetOrder'
            // 2. Check if other class electives also fit current combinations (exclude current order elective option)
            if (possibleClasses.includes(cl) || selectedUserElectives.every((ue: any) => (idGroups[ue.order-1].includes(Number(ue.electiveId))))) {
              possibleElectiveIds = possibleElectiveIds.concat(ids);
            }
          }
        }
        const possibleElectives: any = [...new Set(possibleElectiveIds)].map(id => allElectives.value.find(e => e.id == id)).filter(e => e)
                                                                        .sort((a: any, b: any) => (a.name < b.name ? -1 : 1));
        const resultElectives = possibleElectives.filter(e => (!selectedUserElectives.find(ue => ue.electiveId == e.id)))
                                                  .map(e => ({ ...e, isPreferred: false })); // reset
        //return resultElectives;

        // Take into account A1-3 programs & calculate suggested electives
        for (const program of selectedPrograms.value.slice(0, 3)) {
          const { requiredElective1, requiredElective2, requiredElective3, preferredSubjects, } = program;
          for (const e of resultElectives) {
            if (requiredElective1.includes(e.name) || requiredElective2.includes(e.name)
                || requiredElective3.includes(e.name) || preferredSubjects.includes(e.name)) {
              e.isPreferred = true;
            }
          }
        }
        // Preferred electives
        const preferredElectives: any = resultElectives.filter(e => e.isPreferred);
        if (preferredElectives.length > 0) preferredElectives.unshift({ isGroup: true, name: "Suggested from '2. Programs'", id: "preferredGroup" });
        let opts = preferredElectives;
        
        // Include remaining electives
        if (preferredElectives.length > 0) opts.push({ isGroup: true, name: "Others", id: "other" })
        opts = opts.concat(resultElectives.filter(e => !e.isPreferred));
        
        return opts;
      },
      isDisableElectiveSelect: (programId, targetOrder) => {
        if (props.isAB3ParentView) return true; // Parent's view
        if (getUserElective(programId, targetOrder, 'like', 'electiveId')) return false;
        const targetLength = targetOrder-1; // e.g. Elective 3 needs Elective 1 & 2 selected first
        const res = userElectives.value.filter(ue => {
          return ue.order && ue.order < targetOrder && ue.programId == programId && ue.reaction == 'like';
        });
        return targetOrder == 4 ? res.length == 0 : res.length < targetLength; // 4 is Math option
      },
      getElectiveNums: (programId) => {
        const res = userElectives.value.filter(ue => (ue.order && ue.programId == programId && ue.reaction == 'like'));
        const nums: any = [];
        for (let i = 0; i < Math.min(4, res.length+1); i++) nums.push(i+1);
        return nums; // [1,2,3,4]
      },

      openAB3QRCodeShareModal: async () => {
        const loading = await loadingController.create({ });
        await loading.present();
        const encryptedUserId = await ABSService.getEncryptedUserId(user.value.id);
        const parentLink = `https://ab.fdmt.hk/ab3-pv/${encryptedUserId}`;
        const imgLink = getQRCodeUrl(parentLink); // attached QR code for students sharing with their parents
        await openImageModal(imgLink, "Share with your parents/custodian", "", true, "small-modal");
        loading.dismiss();
      },

      // Get target scores
      selectedPlan, selectedView,
      nextConsultation, previousConsultations,
      
      // Consultation handler
      formatDate,
      handleConsultationChange: () => {
        nextConsultation.id = nextConsultation.id || `c${uniqueId()}`;
        AchieveJUPASService.upsertJUPASConsultationRecord({
          targetUserId: props.targetUser.id,
          id: nextConsultation.id,
          date: nextConsultation.date,
          time: nextConsultation.time,
          venue: nextConsultation.venue,
          remarks: nextConsultation.remarks,
        });
      },
      sendRemarksToTargetStudent: () => {
        presentPrompt("Send the above remarks to your student's WhatsApp group?", async () => {
          const loading = await loadingController.create({});
          await loading.present();
          const { id, phone, waGroupId } = props.targetUser;
          const msg = `@852${phone} Your teacher *${user.value.fullName}* sent you a message about your AchieveJUPAS choices: _${nextConsultation.remarks}_`;
          SLPService.sendWhatsAppMsg(msg, phone, waGroupId);
          AchieveJUPASService.insertUserComment(id, nextConsultation.remarks);
          loading.dismiss();
          presentToast("The message will be sent to your student's WhatsApp group in minutes.");
        });
      },
      teacherComments,

      // Target score + weighting
      getProgramScoreIconColor,
      openParentConsentModal: async () => (await openModal(ParentConsentSignatureModal, {})),

      // Completion Status (Fill A1-A3 / all 20 choices)
      isFilledA1ToA3: computed(() => (selectedPrograms.value.length >= 3)),
      isFilled20Choices: computed(() => (selectedPrograms.value.length >= 20)),

      // Edit student info
      openUserProfileFormModal: async (targetUser) => {
        await openModal(UserProfileFormModal, { targetUser });
      },
    }
  }
});
</script>

<style scoped>
  .plan-segment-btn {
    font-size: 14px !important;
    min-height: 24px !important;
  }
  .plan-segment-btn ion-label {
    margin: 0 !important;
  }
  body.dark a {
    color: #666666 !important;
  }
  small a {
    color: var(--ion-color-step-600, #666666) !important;
  }

  .scores-table-wrapper {
    overflow: scroll;
  }
  .scores-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.75em;
    background: #656565;
    color: white;
    border-radius: 16px;
    margin-bottom: 8px;
  }
  .scores-table th,
  .scores-table td {
    padding: 4px 8px;
    text-align: center;
    border: 0 solid var(--ion-color-dark);
  }
  .scores-table th {
    font-weight: normal;
    background: #2f2f2f;
  }
  .header-row {
    background: #2f2f2f;
  }
  .target-row {
    background: #656565;
  }

  .consultation-section {
    padding: 1px 16px;
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .consultation-card {
    background: var(--ion-color-light);
    border-radius: 8px;
    padding: 16px;
  }

  .consultation-card h3 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 500;
  }

  .consultation-inputs {
    padding: 0;
    background: #656565;
  }

  .datetime-inputs {
    display: flex;
    gap: 2px;
    padding: 0;
  }

  .datetime-inputs ion-input {
    flex: 1;
    --padding-start: 0;
    --padding-end: 6px;
    --padding-top: 2px;
    --padding-bottom: 2px;
    margin: 0;
    min-height: 24px;
    font-size: 16px;
  }

  .date-input {
    flex: 2;
    min-width: 120px;
  }

  .time-input {
    flex: 1;
    min-width: 90px;
  }

  .venue-input {
    flex: 2;
    min-width: 120px;
  }

  /* iOS Safari specific fixes */
  @supports (-webkit-touch-callout: none) {
    .datetime-inputs ion-input {
      --padding-top: 0;
      --padding-bottom: 0;
      min-height: 24px;
    }
  }

  .compact-accordion {
    --ion-color-step-50: var(--ion-color-light);
  }

  .compact-accordion ion-item {
    --padding-start: 0;
    --inner-padding-end: 0;
  }

  .previous-consultations {
    padding: 0;
  }
  
  ion-toolbar {
    --min-height: 28px !important;
  }

  .tags-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    align-items: center;
  }

  .add-tag-input {
    --background: var(--ion-color-light);
    border-radius: 8px;
    margin: 0;
  }

  .small-input {
    --padding-start: 8px;
    font-size: 14px;
  }

  .small-chip {
    height: 24px;
    font-size: 13px;
  }

  .score-cell {
    display: flex;
    align-items: center;
    justify-content: end;
    padding: 0 4px;
  }

  /**
   * Choice item (hover always black)
   */
  ion-item ion-buttons ion-button::part(native) {
    padding-inline-start: 0;
    padding-inline-end: 0;
  }
  .choice-item {
    --min-height: 60px !important;
    color: rgb(0, 0, 0) !important;
  }
  .choice-item p, a {
    color: #666666;
  }
  .choice-item {
    --color: #000;
    --color-focused: #000;
    --color-hover: #000;
    --background-hover: transparent;
    --background-focused: transparent;
  }
  .choice-item ion-label {
    color: #000 !important;
  }
  .choice-item::part(native) {
    color: #000;
  }

  /**
   * Fix component not showing problem
   */
  :host {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  ion-content {
    flex: 1;
    height: 65vh;
    --overflow: scroll;
  }
  ion-header {
    flex-shrink: 0;
  }
</style>
