<template>
  <div class="spin" v-if="loadingData">
    <ion-spinner></ion-spinner>
  </div>

  <div v-else>
    <!--
      Home Slide Page
      -->
    <div v-if="clientHomeSlideImgUrl">
      <img style="width: 100%; cursor: pointer" :src="clientHomeSlideImgUrl" @click="openImageModal(clientHomeSlideImgUrl)" />
    </div>


    <div v-if="!user.isAdmin">
      <!--
        For proposing dates for Work events
      -->
      <ion-card class="outer-card" v-if="userRelatedClient.workServiceId">
        <ion-accordion-group value="F5">
          <ion-accordion style="--min-height: 24px" value="F5">
            <ion-item lines="full" slot="header" color="fdmtred">
              <ion-label><b>4-hr workshop</b></ion-label>
            </ion-item>
            <ion-card slot="content">
              <ion-item lines="full" color="light" @click="openSLPModal()" button detail>
                <ion-label class="ion-text-wrap"><p style="color: #888888"><b>SLP tool for students</b></p></ion-label>
              </ion-item>
              <ion-accordion-group :multiple="true" :value="['coming']">
                <ion-accordion value='coming' key="coming">
                  <ion-item slot="header" color="light">
                    <ion-label class="ion-text-wrap"><p style="color: #888888"><b>Coming schedule</b></p></ion-label>
                  </ion-item>
                  <div slot="content">
                    <!-- Already confirmed sessions -->
                    <div v-for="session in groupedSessions('work-workshop-2')['future']" :key="session.id">
                        <home-action-item 
                          :title="`${session.date} ${session.startTime} - ${session.status == 'TBC' ? 'Confirm' : session.status}`" 
                          :hideIcon="true" 
                          @click="session.status == 'Confirmed' ? openSessionStudentListModal(session.id) : openUniEventDetails(session.id)"
                          :textColor="session.status == 'Confirmed' ? 'success' : 'danger'">
                        </home-action-item>                        
                    </div>

                    <!-- Propose new sessions only for Workshop -->
                    <div v-for="service in clientServices" :key="service.id">
                      <home-action-item
                        v-if="service.nature == 'Workshop'"
                        :title="`Workshop at university campus - Define date`"
                        :hideIcon="true" 
                        @click="openServiceModal(service.id)"
                        :textColor="'danger'">
                      </home-action-item>                        
                    </div>
                  </div>
                </ion-accordion>

                <ion-accordion value='past' key="past">
                  <ion-item slot="header" color="light">
                    <ion-label class="ion-text-wrap"><p style="color: #888888"><b>Past participation</b></p></ion-label>
                  </ion-item>
                  <div slot="content">
                    <div v-for="session in groupedSessions('work-workshop-2')['past']" :key="session.id">
                        <home-action-item 
                          :title="`${session.date} ${session.startTime}`" 
                          :hideIcon="true" @click="openSessionStudentListModal(session.id)">
                        </home-action-item>                        
                    </div> 
                  </div>
                </ion-accordion>
              </ion-accordion-group>
            </ion-card>
          </ion-accordion>
        </ion-accordion-group>
      </ion-card>
    </div>

    <div v-else>
      <!-- Switch between Prospective & Existing Students -->
      <ion-toolbar class="sticky-toolbar">
        <ion-segment class="view-switcher" mode="ios" v-model="selectedView" scrollable>
          <ion-segment-button value="sharing" class="short-segment-btn">
            <ion-icon size="small" :icon="qrCodeOutline"></ion-icon>
          </ion-segment-button>
          <ion-segment-button value="checklist">
            <ion-label class="no-text-transform">Checklist</ion-label>
          </ion-segment-button>
          <ion-segment-button value="prospective-students">
            <ion-label class="no-text-transform">Prospective students</ion-label>
          </ion-segment-button>
          <ion-segment-button value="existing-students">
            <ion-label class="no-text-transform">Existing students</ion-label>
          </ion-segment-button>
        </ion-segment>
      </ion-toolbar>

      <!--
        QR Code sharing (TBC: generic login link / registration link)
      -->
      <div v-if="selectedView == 'sharing'">
        <ion-card class="class-bar" style="margin-top: 0; margin-bottom: 0">
          <ion-card-content style="padding: 0">
            <ion-accordion-group value="sharing">
              <ion-accordion style="--min-height: 24px; padding-left: 12px" value="sharing">
                <ion-item lines="full" slot="header">
                  <ion-label style="margin: 4px 8px 4px 12px"><p style="font-weight: 500"><b>Share this tool with my colleagues</b></p></ion-label>
                </ion-item>
                <ion-list class="ion-text-center" slot="content">
                  <p><img style="width: 300px" :src="getQRCodeUrl(userRelatedClient.genericABLoginLink || `https://ab.fdmt.hk/c/${userRelatedClient.id}`)" /></p>
                  <p>{{userRelatedClient.genericABLoginLink || `ab.fdmt.hk/c/${userRelatedClient.id}`}}</p>
                  <ion-button color="success" size="small" @click="copyText(userRelatedClient.genericABLoginLink || `https://ab.fdmt.hk/c/${userRelatedClient.id}`)">
                    Copy
                  </ion-button>
                </ion-list>
              </ion-accordion>
            </ion-accordion-group>
          </ion-card-content>
        </ion-card>
      </div>

      <!-- Checklist -->
      <div v-else-if="selectedView == 'checklist'">
        <ion-item v-if="userRelatedClient.renewalMeetingInfo">
          <ion-icon slot="start" :icon="informationCircleOutline"></ion-icon>
          <ion-label class="ion-text-wrap">{{ userRelatedClient.renewalMeetingInfo }}</ion-label>
          <ion-buttons>
            <ion-button size="small" @click="copyText(userRelatedClient.renewalMeetingInfo)">
              <ion-icon size="small" slot="icon-only" :icon="copyOutline"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-item>
        <ion-item lines="full" :href="userRelatedClient.longReportLink" target="_blank" button detail color="light"
                  v-if="userRelatedClient.longReportLink">
          <ion-icon slot="start" :icon="informationCircleOutline"></ion-icon>
          <ion-label class="ion-text-wrap"><b>Report</b> (your Google account needed for secured sharing)</ion-label>
        </ion-item>
      </div>

      <!--
        University Students (mainly: jobEX)
      -->
      <div v-else-if="selectedView == 'existing-students'">
        <ion-card class="outer-card" v-if="userRelatedClient.jobEXName">
          <ion-accordion-group value="undergraduate">
            <ion-accordion style="--min-height: 24px" value="undergraduate">
              <ion-item lines="full" slot="header" color="fdmtred">
                <ion-label>
                  <!--<b>Undergraduate Students</b>-->
                  <b>{{ userRelatedClient.department }}-specific career training</b>
                </ion-label>
              </ion-item>
              <ion-card slot="content">
                <ion-accordion-group :multiple="true" :value="['coming']">
                  <ion-accordion value='coming' key="coming">
                    <ion-item slot="header" color="light">
                      <ion-label class="ion-text-wrap">
                        <!--<p style="color: #888888"><b>Coming jobEX schedule</b></p>-->
                        <p style="color: #888888"><b>Coming</b></p>
                      </ion-label>
                    </ion-item>
                    <div slot="content">
                      <!-- Already confirmed sessions -->
                      <div v-for="session in groupedUniSessions()['future']" :key="session.id">
                          <home-action-item 
                            :title="`${session.displayGroupName} [${session.date} ${session.startTime}] - ${session.status == 'TBC' ? 'Confirm' : session.status}`" 
                            :hideIcon="true" 
                            @click="openUniEventDetails(session.id)"
                            :textColor="session.status == 'Confirmed' ? 'success' : 'danger'">
                          </home-action-item>                        
                      </div>

                      <!-- Propose new sessions -->
                      <div v-for="service in clientUniServices" :key="service.id">
                        <home-action-item
                          :title="`${service.name} - Define date`"
                          :hideIcon="true" 
                          @click="openServiceModal(service.id)"
                          :textColor="'danger'">
                        </home-action-item>                        
                      </div>
                    </div>
                  </ion-accordion>

                  <ion-accordion value='past' key="past">
                    <ion-item slot="header" color="light">
                      <ion-label class="ion-text-wrap">
                        <!--<p style="color: #888888"><b>Past jobEX participation</b></p>-->
                        <p style="color: #888888"><b>Past</b></p>
                      </ion-label>
                    </ion-item>
                    <div slot="content">
                      <div v-for="session in groupedUniSessions()['past']" :key="session.id">
                          <home-action-item 
                            :title="`${session.displayGroupName} [${session.date} ${session.startTime}]`" 
                            :hideIcon="true" 
                            @click="openSessionUniStudentListModal(session.id)">
                          </home-action-item>                        
                      </div> 
                    </div>
                  </ion-accordion>
                </ion-accordion-group>
              </ion-card>
            </ion-accordion>
          </ion-accordion-group>
        </ion-card>
      </div>

      <!--
        Prospective Students (Secondary Schools)
      -->
      <div v-else>
        <!--
          AchieveJUPAS (Statistics)
        <ion-card class="outer-card" v-if="userRelatedClient.includeAchieveJUPASData">
          <ion-accordion-group value="achievejupas">
            <ion-accordion style="--min-height: 24px" value="achievejupas">
              <ion-item lines="full" slot="header" color="fdmtred">
                <ion-label>
                  <b>Secondary School Students: NursingWork@PolyU</b>
                </ion-label>
              </ion-item>
              <ion-card slot="content" class="achieve-jupas-card">
                <ion-row class="ion-text-center" style="border: 1px solid var(--ion-color-dark); color: var(--ion-color-dark);">
                  <ion-col size="6">
                    <p>Registered schools</p>
                    <p><span class="large-text ion-padding-end">280</span>Band 1A-C: 23</p>
                  </ion-col>
                  <ion-col size="6">
                    <p>Registered students</p>
                    <p><span class="large-text ion-padding-end">1187</span>DSE 2025</p>
                    <p><span class="large-text ion-padding-end">1185</span>DSE 2026</p>
                  </ion-col>
                </ion-row>
                <div class="table-container">
                  <table class="statistics-table">
                    <thead>
                      <tr>
                        <th></th>
                        <th>A1-B3</th>
                        <th>School-predicted score</th>
                        <th>Admission (median)</th>
                        <th>Seat</th>
                      </tr>
                    </thead>

                    <tbody>
                      <tr>
                        <td><span class="large-text">JS3648 PolyU</span><br /><span class="small-text">Bachelor of Science (Honours) in Nursing</span></td>
                        <td>112</td>
                        <td>--</td>
                        <td>213.5</td>
                        <td>193</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </ion-card>
            </ion-accordion>
          </ion-accordion-group>
        </ion-card>
        -->

        <!-- Switch between Create & Communicate winning features -->
        <ion-toolbar class="sticky-toolbar">
          <ion-segment class="red-indicator-segment" mode="ios" v-model="psView.type" scrollable>
            <ion-segment-button value="create">
              <ion-label class="no-text-transform">
                <b>Create</b>&nbsp;
                <small>winning features</small>
              </ion-label>
            </ion-segment-button>
            <ion-segment-button value="communicate">
              <ion-label class="no-text-transform">
                <b>Communicate</b>&nbsp;
                <small>winning features</small>
              </ion-label>
            </ion-segment-button>
          </ion-segment>
        </ion-toolbar>

        <!-- DSE year filter -->
        <ion-toolbar class="sticky-toolbar" v-show="psView.type == 'communicate'">
          <ion-segment class="blue-indicator-segment" mode="ios" v-model="psView.yearDSE" @ionChange="setDelayLoading()">
            <ion-segment-button v-for="year in [2025, 2026, 2027]" :key="year" :value="year">
              <ion-label>DSE {{ year }} ({{ getSecStudentForm(year) }})</ion-label>
            </ion-segment-button>
          </ion-segment>
        </ion-toolbar>

        <div class="spin" v-if="delayLoading">
          <ion-spinner></ion-spinner>
        </div>

        <div v-else>

          <!--
            Create winning features
          -->
          <div class="ion-padding" v-if="psView.type == 'create'">
            <ion-row class="steps-container">
              <ion-col v-for="(step, i) in ['Competing programs', 'Pre-campaign interest', 'Winning features', 'Hypothetical program', 'Graduate profession', 'Video script', 'Video']"
                      :key="i" size="auto" @click="psView.step = i + 1">
                <div :class="['step', psView.step == i + 1 ? 'active' : '']"><div class="step-content">{{ i + 1 }}. {{ step }}</div></div>
              </ion-col>
            </ion-row>

            <!-- Competing programs -->
            <!-- Program + 1 (admission score) -->
            <!-- Filter by disciplines -->
            <!-- Useful for some clients e.g. IA (hidden for some clients) -->

            <!-- Step 1: Competing programs -->
            <div v-if="psView.step == 1">
              <!-- Program list (client programs) -->
              <ion-item lines="full" color="primary" button detail @click="openABProgramDeckModal(userRelatedClient.programId)" style="--min-height: 40px">
                <ion-label>
                  <span>{{ getProgramHeader(userRelatedClient.programId) }}</span><br />
                  <span class="small-text">{{ getProgramName(userRelatedClient.programId) }}</span>
                </ion-label>
              </ion-item>

              <!-- Program list (Competing Programs) -->
              <ion-item v-for="programId in userRelatedClient.competingProgramIds" :key="programId"
                        lines="full" color="lightblue" button detail @click="openABProgramDeckModal(programId)" style="--min-height: 40px">
                <ion-label>
                  <span>{{ getProgramHeader(programId) }}</span><br />
                  <span class="small-text">{{ getProgramName(programId) }}</span>
                </ion-label>
              </ion-item>
            </div>

            <!-- Step 5: Graduate professions -->
            <div v-if="psView.step == 5">
              <!-- University Profession Result page (graduate professions) - no like button -->
              <div v-for="(item, idx) in clientRelatedProfessions" :key="item.id">
                <ion-item button detail @click="openProfessionModal(item.id)">
                  <ion-label class="ion-text-wrap">
                    <span class="medium-text"><b>{{ idx+1 }}. {{ item.name }}</b></span>
                  </ion-label>
                </ion-item>
                <!-- Related Segments 
                <ion-list style="padding: 0 0 0 40px; border-bottom: 1px solid var(--ion-color-dark)">
                  <ion-item v-for="s in item.relatedSegments" :key="s.id" button>
                    <ion-label class="ion-text-wrap" @click.stop="openSegmentModal(s)">
                      <p><b>{{ s.name }}</b></p>
                    </ion-label>
                  </ion-item>
                </ion-list>-->
              </div>
            </div>

            <!-- Step 2,3,4,6 -->
            <div v-if="[2,3,4,6].includes(psView.step)">
              <div v-if="checkStepSlideLink(`createStep${psView.step}`)">
                <img style="width: 100%; cursor: pointer" :src="stepSlideImageUrls[`createStep${psView.step}`]" referrerPolicy="no-referrer"
                      @click="openImageModal(stepSlideImageUrls[`createStep${psView.step}`])" v-if="isImageLoaded" />
              </div>
              <div class="step-label" v-else>{{ userRelatedClient[`createStep${psView.step}`]}}</div>
            </div>

            <!-- Step 7: Video -->
            <div v-if="psView.step == 7 && userRelatedClient[`createStep${psView.step}`]">
              <div class="step-label ion-text-center" v-for="videoLink in userRelatedClient[`createStep${psView.step}`].split('\n').filter(v => v?.startsWith('http'))" :key="videoLink">
                <iframe class="responsive-embed" style="width: 100%; height: 380px" :src="getYouTubeEmbedUrl(videoLink)" frameborder="0" allowfullscreen></iframe>
              </div>
            </div>
          </div>

          <!--
            Communicate Winning Features
            -->
          <div v-else>
            <!--
              School Statistics
            -->
            <div class="table-container">
              <table class="advertisement-table">
                <tbody>
                  <tr>
                    <td style="padding-left: 12px"><span class="large-text">Band {{ getFormattedClientTargetBandRange }} schools</span><br /><small>with WhatsApp-connected principals, teachers & students</small></td>
                    <td style="padding-left: 16px; padding-right: 0"><span class="large-text">{{ numOfRegisteredClientTargetSchools }}</span> / ~{{ numOfTotalClientTargetSchools }}<br /><small>(~{{ (numOfRegisteredClientTargetSchools / numOfTotalClientTargetSchools * 100).toFixed(0) }}%)</small></td>
                  </tr>
                </tbody>
              </table>

              <!-- 
                1. Reach (In person school visit / advertisement / promotional survey)
                -->
              <action-accordion-group :no-accordion="false" :header-color="'primary'" :no-padding="true">
                <template v-slot:header>
                  <ion-item lines="full" color="primary">
                    <ion-label><span class="large-text">Reach</span> <span class="small-text">secured by school-signed MOUs</span></ion-label>
                    <!--<ion-label slot="end" class="large-text">{{ getTotalClientSectionItemNumByGroup('Reach') }}</ion-label>-->
                  </ion-item>
                </template>

                <!-- Client Sections (Group = Reach) -->
                <action-accordion-group v-for="section in getClientSectionsByGroup('Reach')" :key="section.id" :no-accordion="section.filteredItems.length === 0" :header-color="'primary'" :no-padding="false">
                  <template v-slot:header>
                    <ion-item lines="full" color="primary">
                      <ion-label class="medium-text">{{ section.title }}</ion-label>
                      <ion-label slot="end" style="margin-right: 6px">
                        <span class="medium-text">{{ section.numOfSchools || section.filteredItems.length }}</span><br /><span class="small-text">schools</span>
                      </ion-label>
                      <ion-label slot="end" v-if="section.numOfTeachers">
                        <span class="medium-text">{{ section.numOfTeachers }}</span><br /><span class="small-text">teachers</span>
                      </ion-label>
                      <ion-label slot="end" v-else>
                        <span class="medium-text">{{ getTotalFilteredItemsNum(section.filteredItems) }}</span><br /><span class="small-text">students</span>
                      </ion-label>
                    </ion-item>
                  </template>

                  <!-- Client Section Items (with photos) -->
                  <action-accordion-group v-for="item in section.filteredItems" :key="item.id"
                                          :no-accordion="!item.sessionId && !item.items?.length && !item.photo1Link && !item.photo2Link && !item.description" :header-color="'fdmtgrey'" :no-padding="true">
                    <template v-slot:header>
                      <ion-item class="ion-padding-start" lines="full" color="fdmtgrey" v-if="!!item.sessionId"
                                @click.stop="openSessionStudentListModal(item.sessionId)" button>
                        <ion-label class="small-text" style="white-space: pre-line !important">{{ item.title }}</ion-label>
                        <ion-label slot="end" class="medium-text">{{ item.number }}</ion-label>
                      </ion-item>
                      <ion-item class="ion-padding-start" lines="full" color="fdmtgrey" v-else>
                        <ion-label class="small-text" style="white-space: pre-line !important">{{ item.title }}</ion-label>
                        <ion-label slot="end" class="medium-text">{{ item.number }}</ion-label>
                      </ion-item>
                    </template>
                    <div>
                      <!-- Description (mainly for WhatsApp survey) -->
                       <div v-if="item.description">
                        <div class="step-label ion-text-center" v-if="getYouTubeEmbedUrl(item.description)">
                          <iframe class="responsive-embed" style="width: 100%; height: 380px" :src="getYouTubeEmbedUrl(item.description) || ''" frameborder="0" allowfullscreen></iframe>
                        </div>
                        <ion-item v-else>
                          <ion-label class="ion-padding-start" style="white-space: pre-line !important">
                            {{ item.description }}
                          </ion-label>
                        </ion-item>
                       </div>

                      <!-- Photos (if any) at most 2 columns per row -->
                      <ion-row>
                        <ion-col v-if="item.photo1Link"><img :src="item.photo1Link" /></ion-col>
                        <ion-col v-if="item.photo2Link"><img :src="item.photo2Link" /></ion-col>
                      </ion-row>
                      <!-- Nested Items -->
                      <div v-if="item.items?.length > 0">
                        <ion-item v-for="nestedItem in item.items" :key="nestedItem.id">
                          <ion-label class="ion-padding-start">{{ nestedItem.title }}</ion-label>
                          <ion-label slot="end" class="medium-text" style="padding-right: 60px">{{ nestedItem.number }}</ion-label>
                        </ion-item>
                      </div>
                    </div>
                  </action-accordion-group>
                </action-accordion-group>
              </action-accordion-group>

              <!-- Return: Enrolled voluntarily by students -->
              <action-accordion-group :no-accordion="false" :header-color="'mediumblue'" :no-padding="true">
                <template v-slot:header>
                  <ion-item lines="full" color="mediumblue">
                    <ion-label><span class="large-text">Insight</span> <span class="small-text">to refine winning features</span></ion-label>
                    <!--<ion-label slot="end" class="large-text">{{ getTotalClientSectionItemNumByGroup('Return') }}</ion-label>-->
                  </ion-item>
                </template>

                <action-accordion-group v-for="section in getClientSectionsByGroup('Return')" :key="section.id"
                                        :no-accordion="section.filteredItems.length == 0" :header-color="'mediumblue'" :no-padding="false">
                  <template v-slot:header>
                    <ion-item lines="full" color="mediumblue">
                      <ion-label class="medium-text">{{ section.title }}</ion-label>
                      <ion-label slot="end" class="medium-text" v-if="getTotalFilteredItemsNum(section.filteredItems) > 0">
                        [{{ getTotalFilteredItemsNum(section.filteredItems) }}]
                      </ion-label>
                      <ion-label slot="end" class="medium-text" v-else>0</ion-label>
                    </ion-item>
                  </template>

                  <action-accordion-group v-for="item in section.filteredItems" :key="item.id"
                                          :no-accordion="false" :header-color="item.sessionId ? 'fdmtgrey' : 'fdmtgrey'" :no-padding="true">
                    <template v-slot:header>
                      <ion-item class="ion-padding-start" lines="full" color="fdmtgrey" v-if="!!item.sessionId"
                                @click.stop="openSessionStudentListModal(item.sessionId)" button>
                        <ion-label>{{ item.title }}</ion-label>
                        <ion-label slot="end" class="medium-text" v-if="item.number > 0">[{{ item.number }}]</ion-label>
                        <ion-label slot="end" class="medium-text" v-else>0</ion-label>
                      </ion-item>
                      <ion-item class="ion-padding-start" lines="full" color="fdmtgrey" v-else>
                        <ion-label>{{ item.title }}</ion-label>
                        <ion-label slot="end" class="medium-text" v-if="item.number > 0">[{{ item.number }}]</ion-label>
                        <ion-label slot="end" class="medium-text" v-else>0</ion-label>
                      </ion-item>
                    </template>
                    <ion-row>
                      <!-- Description (mainly for WhatsApp survey) -->
                       <ion-col size="12" v-if="item.description">
                        <ion-item>
                          <ion-label class="ion-padding-start" style="white-space: pre-line !important">{{ item.description }}</ion-label>
                        </ion-item>
                       </ion-col>

                      <!-- Photos (if any) at most 2 columns per row -->
                      <ion-row>
                        <ion-col v-if="item.photo1Link"><img :src="item.photo1Link" /></ion-col>
                        <ion-col v-if="item.photo2Link"><img :src="item.photo2Link" /></ion-col>
                      </ion-row>
                      <!-- Nested Items -->
                      <ion-col size="12" v-if="item.items?.length > 0">
                        <ion-item v-for="nestedItem in item.items" :key="nestedItem.id">
                          <ion-label class="ion-padding-start">{{ nestedItem.title }}</ion-label>
                          <ion-label slot="end" class="medium-text" style="padding-right: 60px" v-if="nestedItem.number > 0">[{{ nestedItem.number }}]</ion-label>
                          <ion-label slot="end" class="medium-text" style="padding-right: 60px" v-else>0</ion-label>
                        </ion-item>
                      </ion-col>
                    </ion-row>
                  </action-accordion-group>
                </action-accordion-group>
              </action-accordion-group>

              <!-- Pick up: Student's choices  -->
              <action-accordion-group :no-accordion="false" :header-color="'lightblue'" :no-padding="true">
                <template v-slot:header>
                  <ion-item lines="full" color="lightblue" >
                    <ion-label><span class="large-text">Prospects</span> <span class="small-text">based on interest & predicted DSE score</span></ion-label>
                    <ion-label slot="end" class="ion-text-right">
                    </ion-label>
                  </ion-item>
                </template>

                <action-accordion-group v-for="section in getClientSectionsByGroup('Prospects')" :key="section.id" :no-accordion="false" :header-color="'lightblue'" :no-padding="false">
                  <template v-slot:header>
                    <ion-item lines="full" color="lightblue">
                      <ion-label class="medium-text">{{ section.title }}</ion-label>
                      <ion-label slot="end" class="medium-text">{{ getTotalFilteredItemsNum(section.filteredItems) }}</ion-label>
                    </ion-item>
                  </template>

                  <action-accordion-group v-for="item in section.filteredItems" :key="item.id" :no-accordion="false" :header-color="'fdmtgrey'" :no-padding="true">
                    <template v-slot:header>
                      <ion-item class="ion-padding-start" lines="full" color="fdmtgrey" v-if="!!item.sessionId" @click.stop="openSessionStudentListModal(item.sessionId)" button>
                        <ion-label>{{ item.title }}</ion-label>
                        <ion-label slot="end" class="medium-text">{{ item.number }}</ion-label>
                      </ion-item>
                      <ion-item class="ion-padding-start" lines="full" color="fdmtgrey" v-else>
                        <ion-label>{{ item.title }}</ion-label>
                        <ion-label slot="end" class="medium-text">{{ item.number }}</ion-label>
                      </ion-item>
                    </template>
                    <ion-row>
                      <!-- Description (mainly for WhatsApp survey) -->
                      <ion-col size="12" v-if="item.description">
                        <div class="step-label ion-text-center" v-if="getYouTubeEmbedUrl(item.description)">
                          <iframe class="responsive-embed" style="width: 100%; height: 380px" :src="getYouTubeEmbedUrl(item.description) || ''" frameborder="0" allowfullscreen></iframe>
                        </div>
                        <ion-item v-else>
                          <ion-label class="ion-padding-start" style="white-space: pre-line !important">
                            {{ item.description }}
                          </ion-label>
                        </ion-item>
                      </ion-col>

                      <ion-row><!-- Photos (if any) at most 2 columns per row -->
                        <ion-col v-if="item.photo1Link"><img :src="item.photo1Link" /></ion-col>
                        <ion-col v-if="item.photo2Link"><img :src="item.photo2Link" /></ion-col>
                      </ion-row>
                      <!-- Nested Items -->
                      <ion-col size="12" v-if="item.items?.length > 0">
                        <ion-item v-for="nestedItem in item.items" :key="nestedItem.id">
                          <ion-label class="ion-padding-start">{{ nestedItem.title }}</ion-label>
                          <ion-label slot="end" class="medium-text" style="padding-right: 60px">{{ nestedItem.number }}</ion-label>
                        </ion-item>
                      </ion-col>
                    </ion-row>
                  </action-accordion-group>
                </action-accordion-group>

                <action-accordion-group :no-accordion="false" :header-color="'lightblue'" :no-padding="true">
                  <template v-slot:header>
                    <ion-item class="ion-padding-start" lines="full" color="lightblue">
                      <ion-label>Mock JUPAS choices</ion-label>
                    </ion-item>
                  </template>

                  <action-accordion-group v-for="programId in userRelatedClient.allRelatedProgramIds" :key="programId" :no-accordion="false"
                                          :header-color="'lightblue'" :no-padding="false" v-show="getProgramById(programId)">
                    <template v-slot:header>
                      <ion-item lines="full" color="lightblue">
                        <ion-label class="ion-padding-start">
                          <span>{{ getProgramHeader(programId) }}</span><br />
                          <span class="small-text">{{ getProgramName(programId) }}</span>
                        </ion-label>
                        <ion-label slot="end" class="ion-text-right" v-if="getProgramById(programId, 'mainDisciplineGroupName')">
                          <span>{{ getProgramById(programId)?.groupedNumOfA1B3[psView.yearDSE] || 0 }}</span>/<span class="small-text">{{ getClientBattlefieldStudentNumber(programId) }}</span><br />
                          <span class="small-text">A1-B3 ({{ userRelatedClient.battleFieldName || getProgramById(programId, 'mainDisciplineGroupName') }})</span>
                        </ion-label>
                        <ion-label slot="end" class="ion-text-right" v-else>
                          {{ getProgramById(programId)?.groupedNumOfA1B3[psView.yearDSE] }}<br />
                          <span class="small-text">A1-B3</span>
                        </ion-label>
                      </ion-item>
                    </template>
                  </action-accordion-group>
                </action-accordion-group>
              </action-accordion-group>
            </div>
          </div>

          <!-- Department Report
          <ion-card class="outer-card">
            <ion-item lines="full" :href="userRelatedClient.longReportLink" target="_blank" button detail color="light"
                      v-if="userRelatedClient.longReportLink">
              <ion-label class="ion-text-wrap"><b>Report</b> (your Google account needed for secured sharing)</ion-label>
            </ion-item>
          </ion-card>-->

          <br />
          <br />
          <br />
          <br />
          <br />
          <br />
          <br />
          <br />

          <div v-if="false">
            <!-- Advertisement -->
            <div class="table-container">
              <table class="advertisement-table">
                <thead>
                  <tr>
                    <th>Advertisement</th>
                    <th></th>
                    <th>Click <small>(since 2024 Sep)</small></th>
                    <th>Circulation</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>2024 Nov 18</td>
                    <td>Nursing<br />Healthcare</td>
                    <td class="highlight-cell">25<br />30</td>
                    <td>1000<br />-</td>
                  </tr>
                </tbody>
              </table>
            </div>
            

            <!-- Promotional Survey -->
            <div class="table-container">
              <table class="advertisement-table">
                <thead>
                  <tr>
                    <th>Promotional survey</th>
                    <th></th>
                    <th>Responses</th>
                    <th></th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>2024 Sep 13 (F5)</td>
                    <td class="ion-text-right">XX Will: X<br />Will not: Y</td>
                    <td class="highlight-cell">85</td>
                    <td>1000</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Video shooting -->
            <div class="table-container">
              <table class="advertisement-table">
                <thead><tr><th>Video shooting</th></tr></thead>
                <tbody><tr><td>2024 Sep 30 (F5)</td></tr></tbody>
              </table>
              <ion-row v-if="getClientPhotosByType('Shooting').length > 0">
                <ion-col v-for="(p, idx) in getClientPhotosByType('Shooting')" :key="idx"><img style="cursor: pointer" :src="p.photoLink" @click="openImageModal(p.photoLink)" /></ion-col>
              </ion-row>
            </div>

            <!-- In-person visits -->
            <div class="table-container">
              <table class="advertisement-table">
                <thead><tr><th>In-person visits</th></tr></thead>
              </table>

              <!-- Map (click to show corresponding photo?) -->
              <div id="map" style="height: 400px; width: 100%;"></div>
            </div>

            <!-- 2-hr visit -->
            <div class="table-container">
              <table class="advertisement-table">
                <thead>
                  <tr>
                    <th>2-hr visit</th>
                    <th>Participants</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>2026 Feb X</td>
                    <td>TBC</td>
                  </tr>
                  <tr>
                    <td>2025 Sep X</td>
                    <td>TBC</td>
                  </tr>
                  <tr>
                    <td>2025 Jan 20</td>
                    <td>27</td>
                  </tr>
                  <tr>
                    <td>2024 Sep 30</td>
                    <td>25</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!--
              4-hr workshop
              -->

            <div class="table-container">
              <table class="advertisement-table">
                <thead>
                  <tr>
                    <th>4-hr workshop</th>
                    <th>Participants</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>2025 Jun 27</td>
                    <td>35</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Icons
import { add, remove, qrCodeOutline, informationCircleOutline, copyOutline, } from 'ionicons/icons';

// Vue reactivity
import { ref, defineComponent, computed, watch, reactive, onMounted } from 'vue';

// icons
import { close, arrowBack, checkmarkCircle, alertCircle, ellipsisHorizontal, ellipsisVertical, } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol,
        IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonSpinner, IonList,
        IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonPopover,
        IonAccordion, IonAccordionGroup, IonText, IonSegment, IonSegmentButton, IonChip,
        loadingController, modalController, alertController, } from '@ionic/vue';
import SessionStudentListModal from '@/components/modals/SessionStudentListModal.vue';
import SLPModal from '@/components/secondary/ABSLPModal.vue';
import UniEventsDetails from '@/components/client/UniEventsDetails.vue'
import ServiceModal from '@/components/modals/ServiceModal.vue';
import ActionAccordionGroup from '@/components/shared/ActionAccordionGroup.vue';
import ABProgramDeckModal from '@/components/achievejupas/ABProgramDeckModal.vue';
import ProfessionModal from '@/components/pss/ProfessionModal.vue';
import SectorModal from '@/components/pss/SectorModal.vue';

// composables / services
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';
import CommonService from '@/services/CommonService';

// methods or filters
const store = useStore();
const { t } = useI18n();
const { openModal, formatDate, openImageModal, getProxyImgLink, closeModal, getQRCodeUrl, copyText, getSecStudentForm,
        getYouTubeEmbedUrl, } = utils();

//const loadingData = computed(() => store.state.loadingUser || store.state.loadingData || store.state.loadingSchoolUserData);
const loadingData = computed(() => store.state.loadingUser || store.state.loadingData);
const user = computed(() => store.state.user);
const userRelatedClient = computed(() => store.getters.userRelatedClient);
const clientHomeSlideImgUrl = ref(null);
const clientServices = computed(() => store.getters.getSecSchoolServicesByClientId(user.value.clientId || "c955c4b06"));
const clientUniSessions = computed(() => store.getters.getUniEventSessionsByClientId(user.value.clientId || "c955c4b06"));
const clientUniServices = computed(() => store.getters.getUniServicesByClientId(user.value.clientId || "c955c4b06"));
const allPrograms = computed(() => store.state.allPrograms);
const allProfessions = computed(() => store.state.allProfessions);
const allDisciplineGroups = computed(() => store.state.allDisciplineGroups);

const clientSessions = computed(() => store.getters.getSessionsByClientId(user.value.clientId));

const allSchools = computed<School[]>(() => store.state.schools); // All Schools
const getFormattedClientTargetBandRange = computed(() => {
  const { targetBands } = userRelatedClient.value || {};
  const bands = targetBands.sort();
  return `${bands[0]}-${bands[bands.length - 1]}`;
});
const numOfRegisteredClientTargetSchools = computed(() => {
  const { targetBands } = userRelatedClient.value || {};
  return allSchools.value.filter(s => targetBands?.includes(s.band) && s.numOfRegisteredTeachers > 0).length;
});
const numOfTotalClientTargetSchools = computed(() => {
  const { targetBands } = userRelatedClient.value || {};
  return allSchools.value.filter(s => targetBands?.includes(s.band)).length;
})

const selectedView = ref('prospective-students'); // Prospective / Existing students

const psView = reactive({
  type: 'create', // create or communicate winning features
  step: 2,
  yearDSE: 2026,
})

// Slides
const stepSlideImageUrls = reactive({});
const checkStepSlideLink = (stepKey) => {
  const slideLink = userRelatedClient.value[stepKey];
  return slideLink?.includes('docs.google.com/presentation');
}
const isImageLoaded = ref(false);
onMounted(async () => {
  const { homeSlideLink } = userRelatedClient.value;
  /*if (homeSlideLink) {
    CommonService.getSlideImageUrl(homeSlideLink).then(res => {
      clientHomeSlideImgUrl.value = res;
    })
  }*/

  setTimeout(async () => {
    for (const stepKey in userRelatedClient.value) {
      if (!stepKey.startsWith('create') || !checkStepSlideLink(stepKey)) continue;
      const slideLink = userRelatedClient.value[stepKey];
      stepSlideImageUrls[stepKey] = await CommonService.getSlideImageUrl(slideLink);
    }
    setTimeout(() => {
      isImageLoaded.value = true;
    }, 200);
  }, 200); 
})


const groupedSessions = (targetAnchorEventId) => {
  const obj = {};
  for (const session of clientSessions.value) {
    let group = ""
    const { startTimeStr, anchorEventId, status } = session;
    if (anchorEventId != targetAnchorEventId) continue;
    if (status == "Rejected") continue;
    if (new Date(startTimeStr) > new Date(+new Date() - 86400000)) {
      group = "future";
    } else {
      group = "past";
    }
    obj[group] = obj[group] || [];
    obj[group].push(session);
  }
  return obj;
}

const groupedUniSessions = () => {
  const obj = {};
  for (const session of clientUniSessions.value) {
    let group = ""
    const { startTimeStr, anchorEventId, status } = session;
    if (status == "Rejected") continue
    if (new Date(startTimeStr) > new Date(+new Date() - 86400000)){
      group = "future"
    } else {
      group = "past"
    }
    if (anchorEventId == 'workshop2'){
      session.displayGroupName = "Educated job seeking"
    } else if (anchorEventId == 'cc31fb5c1') {
      session.displayGroupName = "Guest lecture"
    } else {
      session.displayGroupName = ""
    }

    obj[group] = obj[group] || [];
    obj[group].push(session);
  }
  return obj;
}

// Modals
const openServiceModal = async (serviceId) => (await openModal(ServiceModal, { serviceId, isClientView: true, hideTeacherResponse: false }));
const openSessionStudentListModal = async (sessionId) => (await openModal(SessionStudentListModal, { sessionId }));
const openSessionUniStudentListModal = async (sessionId) => (await openModal(SessionStudentListModal, { sessionId, isStudentView: false, isShowUniStudents: true }));
const openSLPModal = async () => {
  const ev = store.getters.getLastAttendedSession;
  return await openModal(SLPModal, { ev, serviceId: ev?.serviceId, relatedProgramId: ev?.relatedProgramId, relatedClientId: user.value.clientId }, "", false)
}
const openUniEventDetails = async (sessionId) => (await openModal(UniEventsDetails, { sessionId }));
const openProposeDateModal = async () => (alert("Propose"));

const getClientPhotosByType = (type: any) => {
  const { clientPhotos } = userRelatedClient.value || {};
  return clientPhotos ? clientPhotos.filter(p => p.type == type) : [];
}

const openReportLink = () => {
  const currSelectedView = selectedView.value;
  window.open(userRelatedClient.value.longReportLink, '_blank');
  setTimeout(() => {
    selectedView.value = currSelectedView;
  }, 100)
}

// For refreshing views
const delayLoading = ref(false);
const setDelayLoading = () => {
  delayLoading.value = true;
  setTimeout(() => {
    delayLoading.value = false;
  }, 100);
}

// Client Sections
const getClientSectionsByGroup = (group) => {
  const sections = userRelatedClient.value.clientSections.filter(s => s.group == group && (!s.yearDSE || s.yearDSE == psView.yearDSE));
  return sections.map(s => {
    s.filteredItems = s.items.filter(item => item.yearDSE == psView.yearDSE);
    return s;
  });
};
const getTotalFilteredItemsNum = (filteredItems) => {
  let hasBrackets = false;
  const total = filteredItems.reduce((sum, item) => {
    if (!item.number) return sum;
    
    const numStr = String(item.number);
    const hasBracket = numStr.startsWith('[') && numStr.endsWith(']');
    hasBrackets = hasBrackets || hasBracket;
    
    const num = hasBracket ? numStr.slice(1, -1) : numStr;
    return sum + Number(num) || 0;
  }, 0);
  
  return hasBrackets ? `[${total}]` : total;
}
const getTotalClientSectionItemNumByGroup = (group) => {
  return getClientSectionsByGroup(group).reduce((total, section) => total + getTotalFilteredItemsNum(section.filteredItems), 0);
}

// JUPAS Program Statistics
const getProgramById = (programId, key: any = null) => {
  const program = allPrograms.value.find(p => p.id == programId);
  return program ? (key ? program[key] : program) : null;
}
const getNumOfA1B3ByDisciplineGroupId = (disciplineGroupId) => {
  const disciplineGroup = allDisciplineGroups.value.find(d => d.id == disciplineGroupId);
  return disciplineGroup?.groupedNumOfA1B3?.[psView.yearDSE] || 0;
}
const getProgramHeader = (programId) => {
  const { jupasCode, institutionNameShort } = getProgramById(programId) || {};
  return `${jupasCode} ${institutionNameShort}`;
}
const getProgramName = (programId) => (getProgramById(programId)?.name);
const getClientBattlefieldStudentNumber = (programId) => {
  const { targetDisciplineGroupId, groupedBattleFieldNumOfA1B3 } = userRelatedClient.value || {};
  if (targetDisciplineGroupId) return getNumOfA1B3ByDisciplineGroupId(targetDisciplineGroupId);
  if (groupedBattleFieldNumOfA1B3) return groupedBattleFieldNumOfA1B3[psView.yearDSE] || 0;
  return getNumOfA1B3ByDisciplineGroupId(getProgramById(programId, 'mainDisciplineGroupId'));
}

// 1. Competing Programs
const openABProgramDeckModal = async (showFirstProgramId = null) => {
  return await openModal(ABProgramDeckModal, { showFirstProgramId, singleSelectMode: true, readonly: true, });
}

// 5. Graduate Professions
const clientRelatedProfessions = computed(() => {
  return userRelatedClient.value.programRelatedProfessionIds.map(id => (allProfessions.value.find(p => p.id == id))).filter(p => p);
})
const openSegmentModal = async (segment: any) => (await openModal(SectorModal, { sectorId: segment.sectorId, segmentId: segment.id }));
const openProfessionModal = async (professionId: any) => await openModal(ProfessionModal, { professionId, useBackButton: true });
</script>

<style scoped>
  
  ion-item {
    --min-height: 32px;
  }
  ion-item::part(native) {
    padding-inline-start: 12px !important;
  }
  a {
    cursor: pointer;
  }

  .steps-container {
    text-align: center;
    font-size: 0.9em;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }
  
  /* Hide scrollbar for Chrome, Safari and Opera */
  .steps-container::-webkit-scrollbar {
    display: none;
  }
  
  .step {
    cursor: pointer;
    max-width: 100px;
    min-width: 80px;
    min-height: 32px;
    background: #656565;
    position: relative;
    clip-path: polygon(0 0, 90% 0, 100% 50%, 90% 100%, 0 100%);
    margin: 0 4px 10px 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .step.active {
    background: var(--ion-color-primary);
    color: white;
  }
  
  .step-content {
    padding: 4px 8px;
    font-size: 11px;
    line-height: 1.2;
    text-align: center;
    width: 100%;
    white-space: normal;
    word-break: break-word;
  }
  
  .step-label {
    margin: 8px 0 16px 0;
    font-size: 11px;
    font-weight: bold;
    color: var(--ion-color-dark);
  }


  .table-container {
    width: 100%;
    overflow-x: auto;
    margin: 0;
    padding: 0;
  }
  
  .advertisement-table {
    width: 100%;
    border-collapse: collapse;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .advertisement-table th {
    background-color: var(--ion-color-primary);
    color: white;
    font-weight: 500;
    text-align: left;
    font-size: 14px;
    padding: 4px 0 4px 12px;
  }
  .program-table th {
    max-width: 120px;
    background-color: var(--ion-color-fdmtred) !important;
  }
  
  .advertisement-table td {
    min-width: 100px;
    padding: 4px 8px;
    background: #3C4043;
    line-height: 1.2;
    font-size: 14px;
  }
  
  .advertisement-table tbody tr:hover {
    background-color: #f9f9f9;
  }
  
  .advertisement-table ion-button {
    --padding-start: 0;
    --padding-end: 0;
    margin: 0;
  }

  .statistics-table {
    color: var(--ion-color-dark);
    width: 100%;
    border-collapse: collapse;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  .statistics-table th:first-child {
    font-size: 20px;
  }
  .statistics-table th {
    color: white;
    font-weight: 500;
    text-align: left;
    padding: 8px 0 8px 8px;
  }
  .statistics-table td {
    background-color: var(--ion-color-fdmtred);
    padding: 8px 16px;
    line-height: 1.6;
    font-size: 12px;
  }

  .highlight-cell {
    font-size: 20px !important;
    font-weight: bold;
    line-height: 1.1 !important;
  }
  
  ion-label h2 {
    font-size: 20px;
  }
  ion-label small {
    font-size: 12px;
  }

  .large-text {
    font-size: 20px !important;
    font-weight: bold;
  }
  .medium-text {
    font-size: 15px !important;
  }
  .small-text {
    font-size: 10px !important;
  }

  ion-item ion-label[slot="end"] {
    text-align: center;
    margin-right: -16px;
  }
  ion-item ion-label {
    margin: 0;
  }
  ion-item ion-icon[slot="start"] {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    margin-right: 8px !important;
  }
</style>