<template>
    <ion-page>
        <ion-header>
            <ion-grid fixed>
                <ion-toolbar>
                <ion-buttons slot="start">
                    <ion-button slot="icon-only" @click="closeModal()"><ion-icon :icon="arrowBack"></ion-icon></ion-button>
                </ion-buttons>
                <ion-title style="padding-left: 0">
                    <ion-label class="ion-text-wrap">
                    <p>{{ formattedSession.name }}</p>
                    </ion-label>
                </ion-title>
                </ion-toolbar>
            </ion-grid>
        </ion-header>


        <ion-content>
            <div class="spin" v-if="loadingData">
                <ion-spinner></ion-spinner>
            </div>

            <ion-grid fixed v-else>
                <ion-card>
                    <ion-card-content>
                        <ion-row>
                            <!-- Session date -->
                            <ion-col class="ion-text-left" size="3">On</ion-col>
                            <ion-col class="ion-text-left" size="9">
                                <ion-datetime-button :datetime="`${formattedSession.id}_date`" style="font-size: 14px; justify-content: left"></ion-datetime-button>
                                <ion-modal class="datetime-modal" :keep-contents-mounted="true">
                                    <ion-datetime presentation="date" :id="`${formattedSession.id}_date`" v-model="formattedSession.date" :showDefaultButtons="true"></ion-datetime>
                                </ion-modal>
                            </ion-col>

                            <!-- Venue -->
                            <ion-col class="ion-text-left" size="3">Venue</ion-col>
                            <ion-col class="ion-text-left" size="9">
                                <ion-input mode="ios" v-model="formattedSession.venue" fill="outline"></ion-input>
                            </ion-col>

                            <!-- Session start time -->
                            <ion-col class="ion-text-left" size="3">Class start</ion-col>
                            <ion-col class="ion-text-left" size="9">
                                <ion-input mode="ios" type="time" v-model="formattedSession.startTime"></ion-input>
                            </ion-col>

                            <!-- Duration -->
                            <ion-col class="ion-text-left" size="3">Duration</ion-col>
                            <ion-col class="ion-text-left" size="9">
                                <ion-input mode="ios" type="number" v-model="formattedSession.duration" disabled fill="outline"></ion-input>
                            </ion-col>

                            <!-- Session end time -->
                            <ion-col class="ion-text-left" size="3">Class end</ion-col>
                            <ion-col class="ion-text-left" size="9">
                                <ion-input mode="ios" type="time" v-model="displayEndTime" disabled fill="outline"></ion-input>
                            </ion-col>

                            <!-- Remarks -->
                            <ion-col class="ion-text-left" size="3">Remarks</ion-col>
                            <ion-col class="ion-text-left" size="9">
                                <ion-input mode="ios" v-model="formattedSession.remarks" fill="outline"></ion-input>
                            </ion-col>

                            <!-- <ion-item mode="ios" v-model="formattedSession.status">
                                <div slot="label"><ion-text color="medium">Status:</ion-text></div>
                            </ion-item> -->
                        </ion-row>
                    </ion-card-content>
                </ion-card>

                <ion-card>
                    <ion-card-content>
                        <!--How would you like to proceed with this timeslot ? <br>-->
                        I will<br />
                        <ion-item lines="none" v-for="opt in sessionStatusMapping" :key="opt.value" >
                            <input type="radio" slot="start" v-model="formattedSession.status" :name="opt.value" :value="opt.value"/>
                            <ion-label class="ion-text-wrap"><p>{{ opt.radioOption }}</p></ion-label>
                        </ion-item>

                        <div class="ion-margin-top">
                            {{ displayChoiceDescription }}
                        </div>
                    </ion-card-content>
                </ion-card>


                <ion-button color="success" expand="block" @click="saveUpdatedSession">
                    Save
                </ion-button>
            </ion-grid>
        </ion-content>
    </ion-page>
</template>
  
<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, watch, reactive, onMounted } from 'vue';

// icons
import { add, close, checkmark, arrowUp,  arrowForward, arrowBack, trashOutline, } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
        IonItem, IonLabel, IonIcon, IonButtons, IonButton,
        IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption,
        IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid, IonCheckbox,
        IonInput, IonDatetimeButton, IonDatetime, IonModal,
        loadingController, modalController, IonPage} from '@ionic/vue';

// composables / services
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';
import { useRoute, useRouter } from 'vue-router';

// types
import { User, Session, Lesson, } from '@/types';
import { alertController } from '@ionic/core';
import TeacherService from '@/services/TeacherService';
import EventService from '@/services/EventService';

export default defineComponent({
    props: ["sessionId", "isPage"],
    components: { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
                    IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonChip, IonText, IonGrid, IonCheckbox,
                    IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption,
                    IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonCardTitle,
                    IonInput, IonDatetimeButton, IonDatetime, IonModal,
                    IonPage},
    setup(props) {
        // methods or filters
        const store = useStore();
        const route = useRoute();
        const router = useRouter();
        const { openModal, presentPrompt, getProxyImgLink, openImageModal, presentToast, formatDate } = utils();
        const { t } = useI18n();
        const loadingData = computed(() => store.state.loadingUser || store.state.loadingData);

        const user = computed(() => store.state.user);
        const targetSessionId = route.params.sessionId || props.sessionId
        const session = computed<Session>(() => store.getters.getSessionById(targetSessionId))
        const formattedSession = reactive<Session>({...session.value})
        formattedSession.date = formatDate(formattedSession.date, "YYYY-MM-DD")

        // For direct routing
        watch(session, () => {
            for (const key of Object.keys(session.value)){
                formattedSession[key] = session.value[key] 
                if (key == "date") formattedSession[key] = formatDate(session.value[key], "YYYY-MM-DD")
            }
        })

        function addMinutesToTime(timeString: any, minutesToAdd: any, endTimeString: any) {
            try {
                const [hours, minutes] = timeString.split(':').map(Number);
                // Create a new Date object and set the hours and minutes
                const date = new Date();
                date.setHours(hours);
                date.setMinutes(minutes);
                // Add minutes to the date
                date.setTime(date.getTime() + minutesToAdd * 60000); // Convert minutes to milliseconds
                // Get the updated hours and minutes
                const newHours = date.getHours();
                const newMinutes = date.getMinutes();
                // Format the new time string
                const newTimeString = `${String(newHours).padStart(2, '0')}:${String(newMinutes).padStart(2, '0')}`;
                return newTimeString;
            } catch (e) {
                console.log("There is error")
                console.log(e)
                return endTimeString
            }
        }
        const displayEndTime = computed(() => addMinutesToTime(formattedSession.startTime, formattedSession.duration, formattedSession.endTime))

        const sessionStatusMapping = [
          /*{value: "Confirmed", itemTitle: "Confirmed", radioOption: "Hold a lecture at this timeslot", onSubmitChoiceDescription: "FDMT will initate the process for holding this workshop." },
          {value: "TBC", itemTitle: "Click to proceed", radioOption: "To be decided", onSubmitChoiceDescription: "This timeslot will be on hold for your further confimation." },
          {value: "Rejected", itemTitle: "Rejected", radioOption: "Reject", onSubmitChoiceDescription: "FDMT will release this timeslot." },*/
          {value: "Confirmed", itemTitle: "Confirmed", radioOption: "confirm this timeslot", onSubmitChoiceDescription: "FDMT will reserve this timeslot for the event." },
          //{value: "TBC", itemTitle: "Click to proceed", radioOption: "decide later", onSubmitChoiceDescription: "We will approach you later with" },
          {value: "Rejected", itemTitle: "Rejected", radioOption: "reject this timeslot", onSubmitChoiceDescription: "FDMT will release this timeslot." }
        ]
        const displayChoiceDescription = computed(() => sessionStatusMapping.find(mapping => mapping.value == formattedSession.status)?.onSubmitChoiceDescription)

        const checkSessionHasUpdate = () => {
            if (formattedSession['date'] != formatDate(session.value.date, "YYYY-MM-DD")) return true

            const checkFields = ["venue","startTime", "remarks", "status"];
            for (const field of checkFields) {
                if (formattedSession[field] != session[field]) {
                    // Has updated field
                    return true
                }
            }
            return false
        }

        const saveUpdatedSession = async () => {
            if (checkSessionHasUpdate()) {
                const loading = await loadingController.create({ });
                await loading.present();

                // Special Handler for converting date format
                formattedSession.date = formatDate(formattedSession.date, "DD MMM yyyy")

                store.commit('upsertSessions', [formattedSession]);
                EventService.updateSession(formattedSession)  

                presentToast('Your change request has been submitted successfully.')
                loading.dismiss();
            } 

            modalController.dismiss({});
        }

        const closeModal = async () => {
            if (props.isPage) {
                router.replace('/home');
            } else {
                await modalController.dismiss({});
            }   
        }



        // return variables & methods to be used in template HTML
        return {
            // icons
            add, close, checkmark, arrowUp, arrowForward, arrowBack,
            trashOutline,

            // variables
            loadingData,
            user,
            formattedSession,
            displayEndTime,
            sessionStatusMapping,
            displayChoiceDescription,


            // methods
            t,
            closeModal,
            saveUpdatedSession
        }
    },
});
</script>

<style scoped>
ion-input {
    min-height: 20px !important;
    --color: var(--ion-color-dark);
}
.divider {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
}
</style>