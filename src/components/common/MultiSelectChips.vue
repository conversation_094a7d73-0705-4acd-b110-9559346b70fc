<template>
  <div class="horizontal-scroll">
    <ion-chip 
      v-for="item in itemsWithAll" 
      :key="item.value"
      @click="toggleItem(item.value)"
      :outline="!isSelected(item.value)"
      :class="{ 'active-tag': isSelected(item.value) }"
    >
      <ion-label>{{ item.label }}</ion-label>
    </ion-chip>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, computed } from 'vue';

// components
import { IonChip, IonLabel, } from '@ionic/vue';

export default defineComponent({
  name: 'MultiSelectChips',
  components: { IonChip, IonLabel },
  props: {
    modelValue: {
      type: Array as any,
      default: () => []
    },
    items: {
      type: Array as any,
      required: true
    },
    allLabel: {
      type: String,
      default: 'All'
    }
  },

  emits: ['update:modelValue'],

  setup(props, { emit }) {
    const allValue = 'all';
    
    // Add 'All' as the first item in the items array
    const itemsWithAll = computed(() => [
      { value: allValue, label: props.allLabel },
      ...props.items
    ]);

    const isSelected = (value: string) => {
      // If checking 'All', return true when the array is empty
      if (value === allValue) {
        return props.modelValue.length === 0;
      }
      return props.modelValue.includes(value);
    };

    const toggleItem = (value: string) => {
      let newValue = [...props.modelValue];
      
      if (value === allValue) {
        // If clicking 'All', return empty array
        newValue = [];
      } else {
        // Toggle the clicked item
        const index = newValue.indexOf(value);
        if (index > -1) {
          newValue.splice(index, 1);
        } else {
          newValue.push(value);
        }
      }
      
      emit('update:modelValue', newValue);
    };

    return {
      allValue,
      itemsWithAll,
      isSelected,
      toggleItem
    };
  }
});
</script>

<style scoped>
.chips-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 8px 0;
}
</style>
