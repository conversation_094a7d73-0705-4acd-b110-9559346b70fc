<template>
  <div class="container" v-html="getSectorSlideHtml()"></div>

  <ion-toolbar>
    <ion-label><h6 style="margin-left: 10px; font-size: 13px">Sector</h6></ion-label>
    <div>
      <ion-chip class="ion-no-margin" style="height: 24px; font-size: 13px"
                :class="{ 'active-tag': selectedSegment == null }"
                @click="onSelectSectorChip(sector)">
        <ion-label>{{ sector.name }}</ion-label>
      </ion-chip>
    </div>
  </ion-toolbar>

  <ion-toolbar>
    <ion-label><h6 style="margin-left: 10px; font-size: 13px">Segments</h6></ion-label>
    <!--<div class="horizontal-scroll">-->
    <div>
      <ion-chip v-for="segment in relatedSegments" :key="segment.id"
                class="ion-no-margin" style="margin-end: 2px; margin-bottom: 2px; height: 24px; font-size: 13px"
                :class="{ 'active-tag': selectedSegment?.id == segment.id }" 
                @click="onSelectSegmentChip(segment)">
        <ion-label>{{ segment.name }}</ion-label>
      </ion-chip>
    </div>
  </ion-toolbar>

  <ion-toolbar v-show="selectedSegment">
    <ion-label><h6 style="margin-left: 10px; font-size: 13px">Locations</h6></ion-label>
    <div>
      <ion-chip v-for="location in locations" :key="location.id"
                class="ion-no-margin" style="margin-end: 2px; margin-bottom: 2px; height: 24px; font-size: 13px"
                @click="() => {
                  selectedLocationId = location.id;
                  loadExtraInfosLocation(selectedSegment?.id);
                }"
                :color="selectedLocationId == location.id ? '' : 'medium'"
                :class="selectedLocationId == location.id ? 'active-tag': ''">
        <ion-label>{{ location.name }}</ion-label>
      </ion-chip>
    </div>
  </ion-toolbar>

  <div style="margin-top: 10px" v-show="selectedSegment">
    <div class="ion-text-center ion-margin-top" v-show="loadingExtraInfo">
      <ion-spinner></ion-spinner>
    </div>

    <ion-accordion-group v-show="!loadingExtraInfo" value="mnc">

      <!-- Global extra info -->
      <ion-accordion v-for="(info, key) in extraInfosGlobal" :key="key" v-show="selectedLocationId == null">
        <ion-item color="dark" slot="header">
          <ion-label>{{ key }}</ion-label>
        </ion-item>
        <ion-list slot="content">
          <ion-item v-for="infoObj in info" :key="infoObj.id">
            <ion-label><a target="_blank" :href="infoObj.content">{{ infoObj.name }}</a></ion-label>
          </ion-item>
        </ion-list>
      </ion-accordion>

      <!-- Extra Info of selected location (e.g. Hong Kong) -->
      <ion-accordion v-for="(info, key) in extraInfosLocation" :key="key" v-show="selectedLocationId != null">
        <ion-item color="dark" slot="header">
          <ion-label>{{ key }}</ion-label>
        </ion-item>
        <ion-list slot="content">
          <ion-item v-for="infoObj in info" :key="infoObj.id">
            <ion-label><a target="_blank" :href="infoObj.content">{{ infoObj.name }}</a></ion-label>
          </ion-item>
        </ion-list>
      </ion-accordion>

      <!-- Employers - local -->
      <ion-accordion>
        <ion-item color="dark" slot="header">
          <ion-label>Employers - local</ion-label>
        </ion-item>
        <ion-list slot="content">
          <ion-item v-for="employer in employers.local" :key="employer.id">
            <ion-label class="ion-text-wrap">
              <a target="_blank" :href="employer.company_url">{{ employer.name }}</a>
            </ion-label>
          </ion-item>
        </ion-list>
      </ion-accordion>
      <!-- Employers - multinationals -->
      <ion-accordion value="mnc">
        <ion-item color="dark" slot="header">
          <ion-label>Employers - multinationals</ion-label>
        </ion-item>
        <ion-list slot="content">
          <ion-item v-for="employer in employers.mnc" :key="employer.id">
            <ion-label class="ion-text-wrap">
              <a target="_blank" :href="employer.company_url">{{ employer.name }}</a>
            </ion-label>
          </ion-item>
        </ion-list>
      </ion-accordion>
    </ion-accordion-group>
  </div>

  <ion-accordion-group style="margin-top: 10px" v-show="!selectedSegment">
    <ion-accordion v-for="(relatedInfo, type) in sectorExtraInfo" :key="type" v-show="type != '__CODE__'">
      <ion-item color="dark" slot="header">
        <ion-label>{{ type }}</ion-label>
      </ion-item>
      <ion-list slot="content">
        <ion-item v-for="info in relatedInfo" :key="info.name" button detail>
          <ion-label class="ion-text-wrap">
            <a target="_blank" :href="info.content">{{ info.name }}</a>
          </ion-label>
        </ion-item>
      </ion-list>
    </ion-accordion>
  </ion-accordion-group>
</template>

<script lang="ts">
// Vue reactivity
import { computed, defineComponent, onMounted, reactive, ref, watchEffect } from 'vue';

// components
import { IonToolbar, IonItem, IonLabel, IonList, IonListHeader, IonCheckbox, IonAccordion, IonAccordionGroup,
        IonRow, IonChip, IonSpinner, loadingController, } from '@ionic/vue';

// composables
import { useStore } from '@/store';
import { utils } from '@/composables/utils';

// types
import { Sector, Segment, } from '@/types';

// services
import PortalService from '@/services/PortalService';
import UserService from '@/services/UserService';

export default defineComponent({
  props: {
    sector: Object as () => Sector,
    segmentId: null
  },
  emits: ['segmentChanged'],
  components: { IonToolbar, IonItem, IonLabel, IonList, IonListHeader, IonCheckbox, IonAccordion, IonAccordionGroup,
                IonRow, IonChip, IonSpinner, },
  setup(props, { emit }) {
    const store = useStore();
    const { sleep } = utils();

    const relatedSegments = ref<Segment[]>([]);
    const selectedSegment = ref<Segment>();

    const loadingLocations = ref(true);
    const loadingExtraInfo = ref(true);
    const extraInfosGlobal = ref<any>({});
    const extraInfosLocation = ref<any>({});
    const locations = ref([]);
    const selectedLocationId = ref(1);
    const employers = reactive({
      local: [],
      mnc: []
    });

    const sectorExtraInfo = ref<any>(null);

    const getSectorSlideHtml = () => {
      const code = sectorExtraInfo.value ? sectorExtraInfo.value['__CODE__'] : null;
      return code && code[0] ? code[0].content.replace('width="480"', 'width="100%"') : '';
    }

    const getGroupedInfos = (infosByType: any) => {
      const groupedInfos = {};
      for (const info of infosByType) {
        if(!groupedInfos[info.type]){
          groupedInfos[info.type] = [];
        }
        groupedInfos[info.type] = info.infos;
      }
      return groupedInfos;
    }

    const loadExtraInfosLocation = (segmentId: any) => {
      loadingExtraInfo.value = true;
      PortalService.getSegmentExtraInfos(segmentId, selectedLocationId.value).then(res => {
        loadingExtraInfo.value = false;
        extraInfosLocation.value = getGroupedInfos(res.extra_infos_by_type);
        console.log(res);
        employers.local = res.employers.local.always.concat(res.employers.local.more);
        employers.mnc = res.employers.mnc.always.concat(res.employers.mnc.more);
      });
    }

    onMounted(() => {
      const { sector, segmentId } = props;
      if (sector) {
        const { id, extraInfo, fetchedExtraInfo, extraInfoGroupId } = sector;
        if (!fetchedExtraInfo) {
          PortalService.getExtraInfosByGroupId(extraInfoGroupId, true).then(extraInfo => {
            store.commit('setSectorExtraInfo', { id, extraInfo });
            sectorExtraInfo.value = extraInfo;
          });
        } else {
          sectorExtraInfo.value = extraInfo;
        }
        PortalService.getSector(sector.id).then(res => {
          loadingLocations.value = false;
          locations.value = res.locations;
          selectedLocationId.value = res.locations[0] ? res.locations[0].id : null;
          if (segmentId) loadExtraInfosLocation(segmentId);
        });

        // Related Segment Chips
        relatedSegments.value = store.getters.getRelatedSegments(sector.id);

        // Pre-select Segment
        if (segmentId) {
          selectedSegment.value = relatedSegments.value.find(s => s.id == segmentId);
          emit('segmentChanged', selectedSegment.value);
        }
      }
      
      /**
       * Browse History (sector / segment)
       */
      setTimeout(() => {
        if (segmentId) { // Specfic segment selected
          UserService.addUserBrowsedSegment(segmentId); // add to browse history
          store.commit('addUserBrowsedSegment', segmentId);
        } else { // Only Sector
          UserService.addUserBrowsedSector(sector?.id); // add to browse history
          store.commit('addUserBrowsedSector', sector?.id);
        }
      }, 500);
    });

    return {
      // variables
      relatedSegments, selectedSegment,
      locations, selectedLocationId, employers,
      loadingExtraInfo, extraInfosGlobal, extraInfosLocation,

      sectorExtraInfo,

      // methods
      getSectorSlideHtml, loadExtraInfosLocation,

      onSelectSegmentChip: (segment: Segment) => {
        selectedSegment.value = segment;
        emit('segmentChanged', segment);

        // extra infos of location
        loadExtraInfosLocation(segment.id);

        // extra infos of global
        PortalService.getSegmentExtraInfos(segment.id).then(res => {
          loadingExtraInfo.value = false;
          extraInfosGlobal.value = getGroupedInfos(res.extra_infos_by_type);
          UserService.addUserBrowsedSegment(segment.id); // add to browse history
          //store.commit('addUserBrowsedSegment', segment.id);
        });
      },

      onSelectSectorChip: (sector) => {
        selectedSegment.value = undefined;
        emit('segmentChanged', undefined);
        UserService.addUserBrowsedSector(sector.id); // add to browse history
        store.commit('addUserBrowsedSector', sector.id);
      },
    }
  }
})
</script>

<style scoped>
</style>