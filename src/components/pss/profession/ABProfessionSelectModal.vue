<!--
  STEP 1: Choose professions by answering school-based questions
-->
<template>
  <ion-header>
    <!-- Searchbar Input -->
    <ion-toolbar v-show="isSearching">
      <ion-searchbar id="keyword-searchbar" style="padding-bottom: 1px" mode="md" v-model="searchKeyword" :placeholder="t('search')"
                    @ionFocus="isSearching = true" @ionBlur="onSearchKeywordUpdated()"
                    @ionCancel="isSearching = false; selectedFilterGroup = (client ? 'Subject' : 'gpt')"
                    @keyup.enter="(e) => e.target.blur()" show-cancel-button="always"></ion-searchbar>
    </ion-toolbar>

    <ion-toolbar style="--min-height: 24px" v-if="showDemoOnly && user.teacher">
      <ion-button color="tertiary" size="small" expand="block" class="no-text-transform" @click="openAB4ServiceModal()">
        Learn more about this service
      </ion-button>
    </ion-toolbar>
  </ion-header>

  <ion-content :fullscreen="true">
    <!--
      Searching (show list)
    -->
    <div v-if="isSearching">
      <div v-show="recommendedProfessions().length > 0">
        <ion-item lines="none" v-if="!isFromProgramDeck">
          <ion-label class="ion-text-wrap">
            <p><b>Select</b></p>
          </ion-label>
        </ion-item>

        <ion-item lines="full" v-for="profession in recommendedProfessions().slice(0, numOfVisibleItems)" :key="profession.id" style="--min-height: 55px">
          <!-- From AchieveJUPAS: readonly -->
          <ion-label v-if="isFromProgramDeck">
            <ion-text class="ion-text-wrap">
              <p>{{ profession.name }} {{ profession.nameChinese }}</p>
            </ion-text>
          </ion-label>

          <!-- For selecting the profession -->
          <ion-checkbox
            justify="start"
            labelPlacement="end"
            @update:modelValue="onCheckProfession($event, profession)"
            :modelValue="selectedProfessions.find(p => p.id == profession.id) != null"
            v-else
          >
            <ion-label>
              <ion-text class="ion-text-wrap">
                <p>{{ profession.name }} {{ profession.nameChinese }}</p>
              </ion-text>
            </ion-label>
          </ion-checkbox>

          <ion-button @click="openProfessionModal(profession.id)" color="medium" slot="end" fill="clear" size="small" style="margin-inline-end: 16px; text-transform: none">
            More
          </ion-button>
        </ion-item>

        <ion-infinite-scroll @ionInfinite="loadData($event)" threshold="100px" id="infinite-scroll">
          <ion-infinite-scroll-content loading-spinner="bubbles" loading-text="Loading..."></ion-infinite-scroll-content>
        </ion-infinite-scroll>
      </div>

      <div class="ion-text-center" v-show="recommendedProfessions().length == 0">
        <p>No professions available</p>
      </div>
    </div>

    <ion-grid class="ion-no-padding" v-else>
      <!--
        GPT Mode (Strength -> Profession)
        -->
      <div v-if="selectedFilterGroup == 'gpt'">
        <FDMTRiceCardSlide v-show="gpt.currStep == 1 && botSuggestedProfessions.length == 0"></FDMTRiceCardSlide>

        <div style="height: 100%" v-show="botSuggestedProfessions.length > 0 || gpt.currStep == 2">
          <div class="spin" v-if="gpt.waitingGPTResp">
            <ion-spinner></ion-spinner>
          </div>

          <!-- Explore Professions (result) -->
          <swiper
              :navigation="true"
              class="card-slides"
              id="card-swiper-slides"
              :effect="'cards'"
              :grabCursor="true"
              :modules="modules"
              v-else
          >
            <!-- Main Slides -->
            <swiper-slide class="card-slide" v-for="profession in botSuggestedProfessions" :key="profession.id"
                          :class="{ 'highlighted-border': isProfessionSelected(profession) }" :data-profession-id="profession.id">
              <!-- Profession Image (AI-generated) -->
              <div class="top-badge ion-text-center" v-if="profession.imgLink && profession.imgLink != 'N/A'" style="top: 0; width: 100%; height: 100%">
                <img style="max-width: 100%; object-fit: cover; height: 100%; filter: brightness(60%)" :src="profession.imgLink" />
              </div>

              <!-- Profession Name & Details -->
              <ion-row>
                <ion-col size="12" style="text-shadow: 3px 3px 15px black">
                  <h1>{{ profession.name }}<br /><small>{{ profession.nameChinese }}</small></h1>
                </ion-col>
              </ion-row>

              <!-- Bot explanation -->
              <ion-card class="bottom-badge" style="margin-top: 0; border-radius: 16px" v-show="currViewingProfession?.explanation">
                <ion-card-content style="padding: 0">
                  <ion-item lines="full">
                    <ion-label class="ion-text-wrap">
                      <span class="trimmed-gpt-explanation" v-html="parseMsg(currViewingProfession?.explanation)"></span>
                    </ion-label>

                    <ion-button slot="end" size="small" color="light" class="ion-no-margin no-text-transform"
                                @click="openProfessionModal(profession.id, parseMsg(currViewingProfession?.explanation))">
                      More
                    </ion-button>
                  </ion-item>

                  <!--<ion-item lines="full" color="light" @click="openChatbotModal(currViewingProfession)" button detail>
                    <ion-label><p><b>Tell me more</b></p></ion-label>
                  </ion-item>-->
                </ion-card-content>
              </ion-card>

              <ion-fab slot="fixed" vertical="top" horizontal="end">
                <ion-fab-button v-on="{ [isMobileWeb() ? 'touchstart' : 'click']: () => { onThumbsUp(profession) } }"
                                style="--background: transparent" :color="profession.imgLink ? 'light' : undefined">
                  <ion-icon size="large" :icon="isProfessionSelected(profession) ? thumbsUp : thumbsUpOutline"></ion-icon>
                </ion-fab-button>
              </ion-fab>
            </swiper-slide>
          </swiper>
        </div>
      </div>

      <!--
        Choose Professions (Cards)
        -->
      <div style="height: 100%" v-else>
        <swiper
            :navigation="!isMobileWeb()"
            class="card-slides"
            id="card-swiper-slides"
            :effect="'cards'"
            :grabCursor="true"
            :modules="modules"
            v-if="!delayLoading && recommendedProfessions().length > 0"
        >
          <!-- Go Back to Previous -->
          <swiper-slide class="card-slide" style="background-color: var(--ion-color-light)" v-if="!showDemoOnly && (startIdx+1)-10 > 0">
            <ion-row>
              <ion-col size="12">
                <ion-button size="large" color="primary" class="no-text-transform" fill="outline"
                            v-on="{ [isMobileWeb() ? 'touchstart' : 'click']: () => { onClickPrevBtn() } }">
                  <ion-icon slot="end" :icon="arrowBack"></ion-icon>
                  Previous
                </ion-button>
              </ion-col>
            </ion-row>
          </swiper-slide>

          <!-- Main Slides -->
          <swiper-slide class="card-slide" v-for="profession in recommendedProfessions().slice(startIdx, startIdx+10)" :key="profession.id"
                        :class="{ 'highlighted-border': isProfessionSelected(profession) }" :data-profession-id="profession.id">
            <div class="top-badge" v-if="['like','dislike'].includes(selectedFilterGroup) && getAppStateText(profession.id)">
              <ion-chip class="small-chip">{{ getAppStateText(profession.id) }}</ion-chip>
            </div>

            <!-- Profession Image (AI-generated) -->
            <div class="top-badge ion-text-center" v-if="profession.imgLink && profession.imgLink != 'N/A'" style="top: 0; width: 100%; height: 100%">
              <img style="max-width: 100%; object-fit: cover; height: 100%; filter: brightness(60%)" :src="profession.imgLink" />
            </div>

            <!-- Profession Name & Details -->
            <ion-row style="text-shadow: 3px 3px 15px black">
              <ion-col size="12">
                <h1>{{ profession.name }}<br /><small>{{ profession.nameChinese }}</small></h1>
              </ion-col>
              <ion-col size="12">
                <ion-button size="small" color="light" class="no-text-transform" @click="openProfessionModal(profession.id)">
                  Details
                </ion-button>
                <ion-button size="small" color="primary" class="no-text-transform" @click="openImageChatbotModal(profession)">
                  AI Image
                </ion-button>
              </ion-col>
            </ion-row>

            <ion-fab slot="fixed" vertical="top" horizontal="end">
              <ion-fab-button v-on="{ [isMobileWeb() ? 'touchstart' : 'click']: () => { onThumbsUp(profession) } }"
                              style="--background: transparent" :color="profession.imgLink ? 'light' : undefined">
                <ion-icon size="large" :icon="isProfessionSelected(profession) ? thumbsUp : thumbsUpOutline"></ion-icon>
              </ion-fab-button>
            </ion-fab>

            <!--<ion-fab slot="fixed" vertical="bottom" horizontal="start">
              <ion-fab-button v-on="{ [isMobileWeb() ? 'touchstart' : 'click']: () => { onThumbsDown(profession) } }"
                              style="--background: transparent" :color="profession.imgLink ? 'light' : undefined">
                <ion-icon size="large" :icon="isProfessionDisliked(profession) ? thumbsDown : thumbsDownOutline"></ion-icon>
              </ion-fab-button>
            </ion-fab>-->

          </swiper-slide>

          <!-- More (Next) -->
          <swiper-slide class="card-slide" style="background-color: var(--ion-color-light)" v-if="!showDemoOnly && (startIdx+10 < recommendedProfessions().length)">
            <ion-row>
              <ion-col size="12">
                <ion-button size="large" color="primary" class="no-text-transform" fill="outline"
                            v-on="{ [isMobileWeb() ? 'touchstart' : 'click']: () => { onClickMoreBtn() } }">
                  More
                  <ion-icon slot="end" :icon="arrowForward"></ion-icon>
                </ion-button>
              </ion-col>
            </ion-row>
          </swiper-slide>
        </swiper>
      </div>
    </ion-grid>
  </ion-content>

  <ion-footer v-show="!isSearching">
    <ion-toolbar style="--min-height: 24px">
      <!-- Back Button -->
      <ion-buttons slot="start">
        <ion-button class="nav-category-btn" @click="confirmSelect(true)">
          <ion-icon size="small" slot="icon-only" :icon="arrowBack"></ion-icon>
        </ion-button>
      </ion-buttons>

      <!-- Filter Groups -->
      <ion-segment class="filter-group-segment" mode="ios" v-model="selectedFilterGroup" scrollable>
        <ion-segment-button value="gpt" v-if="!client && !isAIImageCompetition">
          <ion-label class="ion-text-wrap">AI</ion-label>
        </ion-segment-button>

        <!-- e.g. BBAWork -> client subject tags -->
        <ion-segment-button value="Subject" v-if="client">
          <ion-label class="ion-text-wrap">Subjects</ion-label>
        </ion-segment-button>

        <!-- Questions (to be obsoleted)
        <ion-segment-button v-for="(options, group) in step1Questions" :key="group" :value="group">
          <ion-label class="ion-text-wrap">{{ group }}</ion-label>
        </ion-segment-button>-->

        <!-- Liked -->
        <ion-segment-button value="like" v-if="userProfessions.some(up => up.reaction == 'like') && !isFromProgramDeck">
          <ion-icon size="small" :icon="thumbsUpOutline"></ion-icon>
        </ion-segment-button>

        <!--<ion-segment-button value="dislike" v-if="userProfessions.some(up => up.reaction == 'dislike')">
          <ion-icon size="small" :icon="thumbsDownOutline"></ion-icon>
        </ion-segment-button>
        <ion-segment-button value="random">
          <ion-ripple-effect></ion-ripple-effect>
          <ion-icon size="small" :icon="repeat"></ion-icon>
        </ion-segment-button>-->

        <!-- Profession list -->
        <ion-segment-button value="search" v-if="!isAIImageCompetition">
          <ion-ripple-effect></ion-ripple-effect>
          <ion-icon size="small" :icon="search"></ion-icon>
        </ion-segment-button>
      </ion-segment>
    </ion-toolbar>

    <!-- Materials by Type -->
    <ion-toolbar class="material-div">
      <!-- Client Subject Tags (For showing subject-related professions) -->
      <ion-row v-show="client && selectedFilterGroup == 'Subject'">
        <ion-chip v-for="cp in client?.professionsByTags" @click="selectedOption = cp"
                  :key="cp.id" :class="{ 'active-tag': selectedOption.id == cp.id }">
          <ion-label>{{ cp.tag }}</ion-label>
        </ion-chip>
      </ion-row>

      <!-- Question Chips (filters) -->
      <ion-row v-show="!client && selectedFilterGroup != 'myChoices'">
        <div v-for="(options, group) in step1Questions" :key="group">
          <ion-chip v-for="(tab, i) in options" :key="tab.id" :class="{ 'active-tag': selectedOption.id == tab.id }"
                    @click="selectedOption = tab" :disabled="showDemoOnly ? i != 0 : false"
                    v-show="selectedFilterGroup == group.toString()">
            <ion-label>{{ tab.text }}</ion-label>
          </ion-chip>
        </div>
      </ion-row>

      <!-- AI -->
      <ion-row v-show="selectedFilterGroup == 'gpt'">
        <!-- Disclaimer -->
        <ion-item lines="none">
          <ion-label class="ion-text-wrap ion-no-margin">
            <p style="line-height: 1"><small>Disclaimer: The data presented is for reference purposes only. We do not guarantee their correctness, completeness, or timeliness.</small></p>
          </ion-label>
        </ion-item>

        <!-- User input (descriptions of strength) -->
        <div style="padding: 8px; width: 100%">
          <ion-textarea placeholder="By writing and/or tags, describe your strengths, passions, and habits as specifically as possible"
                        fill="outline" v-model="gpt.userInputText" :rows="3" :auto-grow="true"></ion-textarea>
        </div>

        <!-- Tag group -->
        <ion-toolbar style="--min-height: 28px">
          <ion-segment class="filter-group-segment" mode="ios" v-model="gpt.selectedTagGroup">
            <!--<ion-segment-button value="">
              <ion-label>All</ion-label>
            </ion-segment-button>-->
            <ion-segment-button value="Passion">
              <ion-label>Passion</ion-label>
            </ion-segment-button>
            <ion-segment-button value="Subject">
              <ion-label>Subjects</ion-label>
            </ion-segment-button>
          </ion-segment>
        </ion-toolbar>

        <!-- Tags related to Strengths / Interests -->
        <div v-for="(options, group) in step1Questions" :key="group" v-show="!gpt.selectedTagGroup || gpt.selectedTagGroup == group.toString()">
          <ion-chip v-for="tag in options" :key="tag.id" @click="toggleSelectedTag(tag.id, tag.text)"
                    :class="{ 'active-tag': gpt.tagId == tag.id }">
            <ion-label>{{ tag.text }}</ion-label>
          </ion-chip>
        </div>
      </ion-row>
    </ion-toolbar>

    <!-- Button for triggering ChatGPT responses -->
    <ion-toolbar v-show="selectedFilterGroup == 'gpt'">
      <ion-row>
        <ion-col>
          <ion-button expand="block" @click="gpt.currStep = 2; getRecommendedProfessionsFromGPT()"
                      :disabled="(!gpt.tagId && !gpt.userInputText) || gpt.isInCooldownTime"
                      class="no-text-transform" color="primary" style="height: 56px">
            <ion-label class="ion-text-wrap">
              {{ gpt.isInCooldownTime ? `Please wait (${gpt.cooldownSecLeft}s)` : `Examples of Professions involving prompt-related activities (additional qualifications may be required to pursue them)` }}
            </ion-label>
            <ion-icon slot="end" :icon="arrowForward"></ion-icon>
          </ion-button>
        </ion-col>
      </ion-row>
    </ion-toolbar>
  </ion-footer>
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, watch, reactive, onMounted, onUnmounted, onBeforeUnmount } from 'vue';

// icons
import { add, close, checkmark, arrowUp, arrowForward, arrowBack, trashOutline,
        thumbsUpOutline, thumbsDownOutline, thumbsUp, thumbsDown, heart, heartOutline,
        chevronBack, chevronForward, repeat, search, pencil, refresh, } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
        IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonTextarea, IonThumbnail,
        IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption,
        IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid, IonCheckbox,
        IonRadioGroup, IonRadio, IonInfiniteScroll, IonInfiniteScrollContent, IonReorder, IonReorderGroup,
        IonFab, IonFabButton, IonRippleEffect, IonSpinner, IonAvatar,
        IonicSlides, isPlatform, modalController, loadingController, } from '@ionic/vue';
import ProfessionModal from '@/components/pss/ProfessionModal.vue';
import ServiceModal from '@/components/modals/ServiceModal.vue';
import ChatbotModal from '@/components/modals/ChatbotModal.vue';

// Swiper
import 'swiper/swiper.min.css';
import 'swiper/modules/effect-cards/effect-cards.min.css';
import 'swiper/modules/navigation/navigation.min.css';
import '@ionic/vue/css/ionic-swiper.css';
import { Swiper, SwiperSlide } from 'swiper/vue/swiper-vue';
import { EffectCards, Navigation, } from 'swiper';

// composables
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';
import { utilsGPT } from '@/composables/utilsGPT';

// types
import { ClientProfession, Profession, ProfessionTab, Step1Option, UserClaim, UserProfession } from '@/types';

// services
import ABSService from '@/services/ABSService';
import config from '@/config';

export default defineComponent({
  name: 'ABProfessionSelectModal',
  props: [
    "prefilledProfessions", "oldUserProfessions", "isPreview", "client", "clientId", "isFromProgramDeck",
    "isAIImageCompetition",      
  ],
  components: { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
                IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonTextarea, IonThumbnail,
                IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption,
                IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid, IonCheckbox,
                IonRadioGroup, IonRadio, IonInfiniteScroll, IonInfiniteScrollContent, IonReorder, IonReorderGroup,
                IonFab, IonFabButton, IonRippleEffect, IonSpinner, IonAvatar,
                Swiper, SwiperSlide },
  setup(props) {
    // methods or filters
    const { t } = useI18n();
    const store = useStore();
    const { openModal, closeModal, doReorder, navigateMaterialCategories, infiniteScrollLoadData,
            processUserItems, isItemSelected, isItemDisliked, isMobileWeb,
            onThumbsUpItem, onThumbsDownItem, onClickMoreBtn, onClickPrevBtn, animateToFirstCard,
            recordCurrSlideReaction, resetActiveSlide, syncChosenItems, resetFilters, focusKeywordSearchbar,
            presentAlert, } = utils();

    const showDemoOnly = !store.getters.isAB4User; // show demo options only
    const user = computed(() => store.state.user);
    const settings = computed(() => store.state.settings);

    const step1Questions = computed(() => store.getters.getStep1Questions(false, true, ['Passion', 'Subject']));
    const selectedProfessions = ref<Profession[]>(props.prefilledProfessions || []);
    const userProfessions = ref<UserProfession[]>(props.oldUserProfessions || []);
    const tmpNewUserProfessions = ref<UserProfession[]>([]); // for storing slideChange user professions
    const allProfessions = ref<Profession[]>(store.getters.shuffledProfessions(false, false));
    const uniGradProfessions = ref<Profession[]>(store.getters.shuffledProfessions(false, true));
    const allProfessionTabs = computed<ProfessionTab[]>(() => store.state.professionTabs);

    // Deck filters
    const selectedFilterGroup = ref(props.isAIImageCompetition ? 'AI Images' : (props.client ? 'Subject' : 'gpt'));
    const selectedOption = ref<any>("all");

    // GPT (strength-based recommendations)
    const { parseMsg, fetchGPTResponse, parseGPTResponse, whatsAppSendGPTResults, upsertUserItems, getStrengthDescription, initGPTObj, } = utilsGPT();
    const gpt = reactive({
      userInputText: "", // user input prompt
      tagId: "",
      tagText: "",
      currStep: 1,
      waitingGPTResp: false,
      isInCooldownTime: false, // prevent users sending requests consecutively
      cooldownSecLeft: 0,
      selectedTagGroup: "Passion",
    });
    const botSuggestedProfessions = ref<Profession[]>([]);
    const currViewingProfession = ref<Profession>();
    const getRecommendedProfessionsFromGPT = async () => {
      try {
        botSuggestedProfessions.value = []; // reset
        currViewingProfession.value = undefined; // reset
        const { studyingElectives, firstSelectedDisciplineNames, } = user.value; // TBC: include more info about the students?
        const MAX_PROFESSIONS = 10;

        let prompt = `Key strength: ${getStrengthDescription(gpt)}`;
        let overrideBotName = "", botUrl = "https://ab-chatgpt-api.fdmt.hk/ab4";

        if (props.isFromProgramDeck) {
          // Professions related to specific program (e.g. PolyU LMS)
          const { clientId } = props;
          overrideBotName = config.separatePoeBotClientIds.includes(clientId) ? clientId : "";
          botUrl = "https://ab-chatgpt-api.fdmt.hk/slp";
          prompt += `\n\nAbove is the information about a Hong Kong F5/6 student, who is joining the event of the client "${clientId}". Please suggest at most ${MAX_PROFESSIONS} unique professions that are most suitable for the student based on his/her strengths. The professions must ONLY be those related to the client "${clientId}".`;
          prompt += ` For each profession, please explain in detail how the student's key strength is applied in most related daily tasks in TRADITIONAL CHINESE to convince the student that the profession is suitable. Be as specific & concrete as possible.`;
          if (clientId == "PolyU_BRE") { // Customized for SurveyorWork
            prompt += `For Surveyor profession (client: PolyU_BRE), please give your explanation for each division (建築測量組, 產業測量組, 規劃及發展組, 物業設施管理組, 工料測量組) paragraph by paragraph, with the first paragraph being the most suitable division. Paragraphs are separated with blank newlines, shown like numbered list, with the divisions highlighted in bold.`;
          }
        } else {
          // AB4: data source is university-graduate professions
          //prompt += `\nStudying elective subjects: ${studyingElectives}`;
          //prompt += firstSelectedDisciplineNames ? `\nInterested university disciplines: ${firstSelectedDisciplineNames}` : '';
          prompt += `\n\nAbove is the information about a Hong Kong F5/6 student. Please suggest at most ${MAX_PROFESSIONS} unique professions that are most relevant to the student based on his/her strength. `;
          prompt += `The profession must ONLY be one of those in the data source "fdmt-professions-X" (consider all chunks).`;
          prompt += ` For each profession, please explain in detail how the student's key strength is applied in most related daily tasks`;
          prompt += ` in TRADITIONAL CHINESE to convince the student that the profession is suitable. Be as specific & concrete as possible.`;
        }
        prompt += `\n\nYour response MUST be formatted in JSON with only an array of JavaScript objects (at most ${MAX_PROFESSIONS} objects), each object must contain exactly 2 fields: "id", "explanation"\n`;
        prompt += `Example response: [{"id":"499","explanation":"XXX"}]`;

        // Send request
        const data = await fetchGPTResponse(gpt, botUrl, prompt, user.value.id, overrideBotName);
        if (!data) throw new Error("No data");
  
        // Parse data (bot responses)
        await parseGPTResponse(gpt, data, currViewingProfession, botSuggestedProfessions, allProfessions, '#card-swiper-slides', 'professionId');

        // Insert to DB for records first
        upsertUserItems(user.value.id, botSuggestedProfessions, userProfessions, 'professionId');

        // Send out the results to student's WhatsApp group for records
        whatsAppSendGPTResults(user.value.phone, user.value.waGroupId, getStrengthDescription(gpt), botSuggestedProfessions, 'profession');
      } catch (e) {
        console.error(e);
        presentAlert("ChatGPT did not response. Please try again");
        gpt.currStep = 1;
      } finally {
        gpt.waitingGPTResp = false;
      }
    }

    // Search
    const searchKeyword = ref('');
    const isSearching = ref(false);
    const delayLoading = ref(true);

    const numOfVisibleItems = ref(20);
    const loadData = (ev: any) => { infiniteScrollLoadData(ev, numOfVisibleItems, store.state.allProfessions) }

    const getAppState = () => ({
      selectedFilterGroup: selectedFilterGroup.value,
      selectedOption: selectedOption.value,
      searchKeyword: searchKeyword.value,
    });
    const confirmSelect = async (noLoading = false) => {
      await closeModal({
        "selectedProfessions": selectedProfessions.value,
        "userProfessions": processUserItems(selectedProfessions.value, userProfessions.value, tmpNewUserProfessions.value, 'professionId', store.state.user.id),
        noLoading,
      }); // return selected profession & order here
    };
    const openProfessionModal = async (professionId: any, gptExplanationHTML = "") => await openModal(ProfessionModal, { gptExplanationHTML, professionId, useBackButton: true });

    // Helper functions for profession reaction
    const recordActiveSlideReaction = () => {
      recordCurrSlideReaction('professionId', userProfessions, tmpNewUserProfessions, getAppState());
    }
    const isProfessionSelected = (profession: any) => (isItemSelected(profession, selectedProfessions));
    const isProfessionDisliked = (profession: any) => (isItemDisliked(profession, 'professionId', userProfessions));

    const startIdx = ref(0); // for card slides

    // Record Access & Leave Time
    let accessTime, duration = 0, counterInterval;
    onMounted(() => {
      accessTime = new Date();
      counterInterval = setInterval(() => (duration++), 1000);

      // Prefill with previously written reasons
      syncChosenItems('professionId', selectedProfessions, userProfessions, allProfessions.value);

      setTimeout(() => {
        delayLoading.value = false;
        setTimeout(() => {
          const slides: any = document.querySelector('#card-swiper-slides');
          if (slides) {
            slides.swiper.on('slideChange', recordActiveSlideReaction);
            recordActiveSlideReaction(); // initial slide
          }
          const opts = step1Questions.value[selectedFilterGroup.value] || [];
          selectedOption.value = opts.length > 0 ? opts[0] : 'all';

          if (props.client) { // check & prefill with teacher school roles (if exist related tags)
            const userSchoolRoles = (user.value.teacher?.schoolRoles || "").split(" , ").map(r => store.state.schoolRoles.find(sr => sr.title == r)).filter(r => r);
            const relatedClientProfession = props.client.professionsByTags.find(p => userSchoolRoles.find(sr => p.tag == sr?.relatedSubject));
            if (relatedClientProfession) selectedOption.value = relatedClientProfession;
          }
        }, 200);
      }, 200);

      // AI: Prefill previously input text & selected tag
      initGPTObj(gpt, user.value.claims);
    })
    onBeforeUnmount(() => {
      if (accessTime && duration >= 5) {
        ABSService.insertPageAccessRecord('ABProfessionSelectModal', accessTime, new Date(accessTime.getTime() + duration*1000));
        accessTime = undefined;
        duration = 0; // reset;
        clearInterval(counterInterval);
      }
    })
    watch(selectedOption, () => {
      resetActiveSlide(startIdx, delayLoading, 'professionId', userProfessions, tmpNewUserProfessions, getAppState());
    })
    watch(selectedFilterGroup, (currGroup) => {
      if (currGroup) {
        if (['like', 'dislike'].includes(currGroup)) {
          selectedOption.value = currGroup;
        }
        else if (currGroup == 'random') {
          resetFilters(selectedFilterGroup, selectedOption);
          allProfessions.value = store.getters.shuffledProfessions(false, true);
          animateToFirstCard();
        }
        else if (currGroup == 'search') {
          resetFilters(selectedFilterGroup, selectedOption);
          focusKeywordSearchbar(isSearching);
        }
        else {
          selectedOption.value = step1Questions.value[currGroup] ? step1Questions.value[currGroup][0] : {};
        }
      }
    })

    // return variables & methods to be used in template HTML
    return {
      // icons
      add, close, checkmark, arrowUp, arrowForward, arrowBack, trashOutline,
      thumbsUpOutline, thumbsDownOutline, thumbsUp, thumbsDown, heart, heartOutline,
      pencil, refresh,

      // variables
      step1Questions,
      selectedOption,
      selectedProfessions, userProfessions, tmpNewUserProfessions,
      searchKeyword, isSearching,
      delayLoading,
      settings,

      // for schools not paid for AB4, show demo options only
      showDemoOnly, user,

      // methods
      t, isMobileWeb,
      confirmSelect, closeModal, openProfessionModal,
      doReorder,
      onSearchKeywordUpdated: () => {
        ABSService.insertProfessionSearchKeywordRecord(searchKeyword.value);
      },
      recommendedProfessions: () => {
        //let filteredProfessions: any = allProfessions.value; // Default shuffle all professions (every time enter)
        let filteredProfessions: any = uniGradProfessions.value; // Default shuffle all professions (every time enter)

        // AI image competition (only show specific professions)
        if (props.isAIImageCompetition) {
          const targetProfessionIds = [279, 203, 409, 500, 455, 62, 267, 128, 337, 244, 428, 55, 406, 81, 80]
          filteredProfessions = [
            ...filteredProfessions.filter(p => targetProfessionIds.includes(p.id)),
            ...filteredProfessions.filter(p => !p.imagePrompt || !p.imagePrompt.wearing),
          ];
        }

        // Subject-related career prospects, default show professions related to the client
        if (props.client) {
          filteredProfessions = props.client.programRelatedProfessionIds.map(id => (allProfessions.value.find((p: Profession) => p.id == id.toString()))).filter(p => p != null);
        }

        const selectedOpt = selectedOption.value;
        if (searchKeyword.value) {
          const filteredProfessions = searchKeyword.value == '' ? allProfessions.value : allProfessions.value.filter(p => {
            return p.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) || (p.nameChinese && p.nameChinese.includes(searchKeyword.value));
          });
          return showDemoOnly ? filteredProfessions.slice(0, 2) : filteredProfessions;
        }
        if (['like', 'dislike'].includes(selectedOpt)) {
          return userProfessions.value.filter(up => up.reaction == selectedOpt).map(up => {
            return allProfessions.value.find(p => p.id == up.professionId);
          });
        }
        if (selectedOpt != 'all') {
          if (props.client) {
            // Subject-related professions
            const chosenTag: ClientProfession = props.client.professionsByTags.find(t => t.id == selectedOpt.id);
            if (chosenTag) {
              filteredProfessions = chosenTag.professionIds.map(id => (allProfessions.value.find((p: Profession) => p.id == id.toString()))).filter(p => p != null);
            }
          }
          else {
            const { tabIds, professionIds } = selectedOpt;
            let tabProfessionIds = [];
            for (const tid of tabIds) {
              const pTab = allProfessionTabs.value.find(pt => pt.id == tid);
              if (pTab) tabProfessionIds = tabProfessionIds.concat(pTab.relatedProfessionIds);
            }
            const relatedProfessionIds = professionIds.length > 0 ? professionIds : tabProfessionIds; // override
            filteredProfessions = [
              ...relatedProfessionIds.map(id => (allProfessions.value.find((p: Profession) => p.id == id.toString()))),
            ].filter(p => p != null);
          }
        }
        filteredProfessions = [
          ...filteredProfessions.filter(d => !isProfessionDisliked(d)),
          ...filteredProfessions.filter(d => isProfessionDisliked(d)),
        ];
        return showDemoOnly ? filteredProfessions.slice(0, 2) : filteredProfessions;
      },

      onThumbsUp: (profession: any, flipToNextSlide = true) => (onThumbsUpItem(profession, 'professionId', selectedProfessions, userProfessions, tmpNewUserProfessions, getAppState(), flipToNextSlide, true)),
      onThumbsDown: (profession: any) => (onThumbsDownItem(profession, 'professionId', selectedProfessions, userProfessions, tmpNewUserProfessions, getAppState())),
      isProfessionSelected,
      isProfessionDisliked,

      // swiper
      modules: [EffectCards, IonicSlides, Navigation],
      startIdx,
      onClickMoreBtn: () => { onClickMoreBtn(startIdx) },
      onClickPrevBtn: () => { onClickPrevBtn(startIdx) },

      // Filter groups & filters
      chevronBack, chevronForward, repeat, search,
      selectedFilterGroup, navigateMaterialCategories,

      getAppStateText: (professionId: any) => {
        const up = userProfessions.value.find(up => (up.professionId == professionId));
        const { selectedOption, searchKeyword } = up?.appState || {};
        return searchKeyword || (selectedOption ? selectedOption.text : null);
      },

      // For teachers of non-AB4 schools
      openAB4ServiceModal: () => {
        openModal(ServiceModal, { serviceId: "s9215dbaf" });
      },

      // List professions
      loadData, numOfVisibleItems,
      onCheckProfession: (checked: any, profession: Profession) => {
        if (checked) {
          if (selectedProfessions.value.find(p => p.id == profession.id) == null) {
            selectedProfessions.value.unshift(profession);
          }
        }
        else {
          const idx = selectedProfessions.value.findIndex(p => p.id == profession.id);
          if (idx !== -1) selectedProfessions.value.splice(idx, 1);
        }
      },

      // GPT
      gpt, botSuggestedProfessions, currViewingProfession,
      getRecommendedProfessionsFromGPT, parseMsg,
      toggleSelectedTag: (tagId: any, tagText: any) => {
        if (gpt.tagId == tagId && gpt.tagText == tagText) { // unselect tag
          gpt.tagId = "";
          gpt.tagText = "";
        } else {
          gpt.tagId = tagId;
          gpt.tagText = tagText
        }
      },

      // Chatbot (explain professions)
      openChatbotModal: async (profession: any) => {
        const { name, nameChinese, explanation } = profession;
        const professionName = `${name} ${nameChinese}`; // Tell me more about the profession? (too general)
        const botExplanation = `${professionName}\n${explanation}`;
        const prefilledPrompt = `Please elaborate more about how my key strength is applied in ${professionName}: ${getStrengthDescription(gpt)}`;
        return await openModal(ChatbotModal, { isFromAB4: true, professionName, botExplanation, professionId: profession.id, prefilledPrompt });
      },

      // AI image competition
      openImageChatbotModal: (profession: Profession) => {
        return openModal(ChatbotModal, { isImageBot: true, professionId: profession.id, imageBotTargetProfession: profession }); // image bot
      },
    }
  },
});
</script>

<style scoped>
</style>
