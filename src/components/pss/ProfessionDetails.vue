<template>
  <!--<div class="ion-text-center" v-if="profession.imgLink && profession.imgLink != 'N/A'">
    <img style="max-width: 480px; max-height: 300px" :src="profession.imgLink" />
  </div>-->

  <div v-if="professionExtraInfo && professionExtraInfo['Video']">
    <iframe class="responsive-embed" v-for="info in professionExtraInfo['Video']" :key="info.id" style="width: 100%; height: 250px"
            :src="getEmbeddedYTLink(info.content)" frameborder="0" allowfullscreen></iframe>
  </div>
  
  <ion-accordion-group :value="gptExplanationHTML ? 'gptExplanation' : (expandAlumniSection ? 'Alumni' : null)">

    <!-- GPT explanation -->
    <ion-accordion value="gptExplanation" v-if="gptExplanationHTML">
      <ion-item color="dark" slot="header">
        <ion-label>How it's related to my strength?</ion-label>
      </ion-item>
      <ion-list slot="content">
        <ion-card>
          <ion-card-content>
            <p style="white-space: pre-line" v-html="gptExplanationHTML"></p>
            <b><small>**This information provided by ChatGPT is for reference purposes only and may not be entirely accurate. It should not be relied upon for any purpose without verification. We are not responsible for any errors or omissions in the information provided.</small></b>
          </ion-card-content>
        </ion-card>
      </ion-list>
    </ion-accordion>

    <!-- Alumni profiles -->
    <ion-accordion v-if="getFilteredAlumniContactRelations().length > 0" value="Alumni">
      <ion-item color="primary" slot="header">
        <ion-label>Alumni</ion-label>
      </ion-item>
      <ion-list slot="content">
        <ion-item v-for="cr in getFilteredAlumniContactRelations()" :key="cr.id">
          <ion-label>
            <a target="_blank" :href="cr.sourceLink">{{ cr.sourceLink }}</a>
          </ion-label>
        </ion-item>
      </ion-list>
    </ion-accordion>

    <!-- GPT short intro -->
    <ion-accordion v-if="professionExtraInfo && professionExtraInfo['ChatGPT']">
      <ion-item color="dark" slot="header">
        <ion-label>Introduction by <b>ChatGPT</b></ion-label>
      </ion-item>
      <ion-list slot="content">
        <ion-card>
          <ion-card-content>
            <p style="white-space: pre-line" v-html="professionExtraInfo['ChatGPT'][0]?.content.replace(/.*Yue Cantonese.*\n/g, '').replace('\n\n\n\n', '\n\n').trim()"></p>
            <b><small><br />**This information provided by ChatGPT is for reference purposes only and may not be entirely accurate. It should not be relied upon for any purpose without verification. We are not responsible for any errors or omissions in the information provided.</small></b>
          </ion-card-content>
        </ion-card>
      </ion-list>
    </ion-accordion>

    <ion-accordion>
      <ion-item color="dark" slot="header">
        <ion-label>Search Jobs</ion-label>
      </ion-item>
      <ion-list slot="content">
        <ion-item><a target="_blank" :href="encodeJobsURI(profession, 'google')">fr Google Jobs</a></ion-item>
        <ion-item><a target="_blank" :href="encodeJobsURI(profession, '51job')">fr 51job</a></ion-item>
        <ion-item><a target="_blank" :href="encodeJobsURI(profession, 'zhipin')">fr BOSS直聘</a></ion-item>
      </ion-list>
    </ion-accordion>

    <div v-if="professionExtraInfo && professionExtraInfo['Job Example']">
      <ion-accordion>
        <ion-item color="dark" slot="header">
          <ion-label>Job Example</ion-label>
        </ion-item>
        <ion-list slot="content">
          <ion-item v-for="info_content in professionExtraInfo['Job Example']" :key="info_content.id">
            <a target="_blank" :href="info_content.content">{{ info_content.name }}</a>
          </ion-item>
        </ion-list>
      </ion-accordion>
    </div>

    <ion-accordion v-if="relatedSectors && relatedSectors.length > 0">
      <ion-item color="dark" slot="header">
        <ion-label>Related employer sectors</ion-label>
      </ion-item>
      <ion-list slot="content">
        <ion-item v-for="(sector, idx) in relatedSectors" :key="sector.id" button detail
                  @click="openSectorModal(sector.id, '')">
          <ion-label class="ion-text-wrap">
            <h3>{{ idx+1 }}. {{ sector.name }}</h3>
          </ion-label>
        </ion-item>
      </ion-list>
    </ion-accordion>

    <ion-accordion v-else>
      <ion-item color="dark" slot="header">
        <ion-label>Employed in</ion-label>
      </ion-item>
      <ion-list slot="content">
        <ion-item v-for="segment in profession.relatedSegments" :key="segment.id">
          <ion-label class="ion-text-wrap">
            <!--<h3>{{ index+1 }}. {{ segment.name }}</h3>-->
            <h3><a @click="openSectorModal(segment.sector.id, segment.id)">{{ segment.name }}</a> -</h3>
            <p>segment of the <a @click="openSectorModal(segment.sector.id, '')">{{ segment.sector.name }}</a> sector</p>
          </ion-label>
        </ion-item>
      </ion-list>
    </ion-accordion>

    <div v-if="professionExtraInfo">
      <ion-accordion v-for="(info, group) in professionExtraInfo" :key="group" v-show="!['Job Example', 'Salary', 'Awards', 'ChatGPT'].includes(group)">
        <ion-item color="dark" slot="header">
          <ion-label>{{ group }}</ion-label>
        </ion-item>
        <ion-list slot="content">
          <ion-item v-for="info_content in info" :key="info_content.id">
            <a target="_blank" :href="info_content.content">
              {{ group.toString() == 'LinkedIn Alumni' ? info_content.content.replace("https://", "") : info_content.name }}
            </a>
          </ion-item>
        </ion-list>
      </ion-accordion>
    </div>

    <ion-accordion>
      <ion-item color="light" slot="header">
        <ion-label>More</ion-label>
      </ion-item>
      <ion-list class="ion-no-padding" slot="content">
        <ion-accordion-group>
          
          <div v-if="professionExtraInfo">
            <ion-accordion v-for="(info, key) in moreProfessionExtraInfo(profession)" :key="key">
              <ion-item color="dark" slot="header">
                <ion-label>{{ key }}</ion-label>
              </ion-item>
              <ion-list slot="content">
                <ion-item v-for="info_content in info" :key="info_content.id">
                  <a target="_blank" :href="info_content.content">{{ info_content.name }}</a>
                </ion-item>
              </ion-list>
            </ion-accordion>
          </div>

          <ion-accordion>
            <ion-item color="dark" slot="header">
              <ion-label>Jobs with these entry qualification found</ion-label>
            </ion-item>
            <ion-list slot="content">
              <ion-item>
                <ion-checkbox justify="start" labelPlacement="end" :checked="profession.requiresSecondarySchool" disabled>Secondary School</ion-checkbox>
              </ion-item>
              <ion-item>
                <ion-checkbox justify="start" labelPlacement="end" :checked="profession.requiresHigherDiplomaAndAssociate" disabled>
                  Higher Diploma and Associate
                </ion-checkbox>
              </ion-item>
              <ion-item>
                <ion-checkbox justify="start" labelPlacement="end" :checked="profession.requiresBachelor" disabled>
                  Bachelor
                </ion-checkbox>
              </ion-item>
              <ion-item>
                <ion-checkbox justify="start" labelPlacement="end" :checked="profession.requiresMaster" disabled>
                  Master
                </ion-checkbox>
              </ion-item>
              <ion-item>
                <ion-checkbox justify="start" labelPlacement="end" :checked="profession.requiresDoctorate" disabled>
                  Doctorate
                </ion-checkbox>
              </ion-item>
            </ion-list>
          </ion-accordion>
          
          <ion-accordion v-for="(info, key) in cvcl" :key="key">
            <ion-item color="dark" slot="header">
              <ion-label>{{ key == 'curriculum_vitae' ? "CV examples" : (key == 'cover_letter' ? "Sample claims" : key) }}</ion-label>
            </ion-item>
            <ion-list slot="content">
              <div v-html="info"></div>
            </ion-list>
          </ion-accordion>

        </ion-accordion-group>
      </ion-list>
    </ion-accordion>

    <div class="ion-text-center" v-if="loadingExtraInfo">
      <ion-spinner></ion-spinner>
    </div>
  </ion-accordion-group>
</template>

<script lang="ts">
// Vue reactivity
import { computed, defineComponent, onMounted, ref } from 'vue';

// components
import { IonItem, IonLabel, IonList, IonListHeader, IonCheckbox, IonAccordion, IonAccordionGroup,
        IonCard, IonCardContent, IonSpinner, } from '@ionic/vue';
import SectorModal from '@/components/pss/SectorModal.vue';

// composables
import { utils } from '@/composables/utils';
import { useStore } from '@/store';
import PortalService from '@/services/PortalService';

// types
import { Profession } from '@/types';

export default defineComponent({
  props: {
    profession: Object as () => Profession,
    relatedSectors: null,
    overrideSegments: null,
    expandAlumniSection: null,
    gptExplanationHTML: null,
  },
  components: { IonItem, IonLabel, IonList, IonListHeader, IonCheckbox,
                IonAccordion, IonAccordionGroup, IonCard, IonCardContent, IonSpinner, },
  setup(props) {
    const store = useStore();
    const cvcl = ref(null);
    const professionExtraInfo = ref<any>(null);
    const loadingExtraInfo = ref(true);
    const currProgram = computed(() => store.state.currProgram); // for getting progam alumni contacts

    const { openModal } = utils();

    const encodeJobsURI = (profession: any, source: any = 'google') => {
      const encodedProfessionName = encodeURIComponent(profession.name);
      const encodedProfessionNameChi = encodeURIComponent(profession.nameChi);
      switch (source) {
        case '51job': // 51job
          return `https://we.51job.com/pc/search?jobArea=&keyword=${encodedProfessionNameChi}&searchType=2&keywordType=guess_exp_tag6`;
        case 'zhipin': // boss直聘
          return `https://www.zhipin.com/web/geek/jobs?query=${encodedProfessionNameChi}`;
        default:
          return `https://www.google.com/search?q=${encodedProfessionName}+fresh+graduate&ibp=htl;jobs`;

      }
    }

    const openSectorModal = async (sectorId: any, segmentId = '') => {
      return await openModal(SectorModal, { sectorId, segmentId })
    };

    onMounted(() => {
      const { profession, } = props;
      if (profession) {
        const { id, extraInfo, fetchedExtraInfo, extraInfoGroupId } = profession;
        if (!fetchedExtraInfo) {
          PortalService.getExtraInfosByGroupId(extraInfoGroupId).then(extraInfo => {
            store.commit('setProfessionExtraInfo', { id, extraInfo });
            professionExtraInfo.value = extraInfo;
            loadingExtraInfo.value = false;
          });
        } else {
          professionExtraInfo.value = extraInfo;
          loadingExtraInfo.value = false;
        }
        PortalService.getCvCl(profession.id).then(res => {
          cvcl.value = res;
        });
      }
    })

    return {
      // variables
      cvcl, professionExtraInfo, loadingExtraInfo,
      currProgram,

      // methods
      encodeJobsURI, openSectorModal,
      moreProfessionExtraInfo: (profession: any) => {
        const searchName = profession?.name.toLowerCase().replace(/ /g, '-') || ""; 
        const glassdoorLink = `https://www.glassdoor.com.hk/Salaries/${searchName}-salary-SRCH_KO0,${searchName.length}.htm`;
        const payscaleLink = `https://www.payscale.com/rcsearch.aspx?category=Job&str=${encodeURIComponent(profession.name)}&CountryName=Hong+Kong`;
        return {
          'Salary': [
            { name: "fr Payscale", content: payscaleLink },
            { name: "fr Glassdoor", content: glassdoorLink },
          ].concat(professionExtraInfo.value['Salary'] || []),
          'Awards': professionExtraInfo.value['Awards'] || [],
        }
      },
      getEmbeddedYTLink: (link) => {
        const matches = link.match(/.*(?:youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=)([^#&?]*).*/i);
        return matches ? `https://www.youtube.com/embed/${matches[1]}` : link;
        // https://www.youtube.com/embed/2JyW4yAyTl0?autoplay=1&modestbranding=1
      },
      getFilteredAlumniContactRelations: () => {
        const { alumniContactRelations } = currProgram.value;
        return (alumniContactRelations || []).filter(cr => cr.entityType == 'profession' && cr.entityId == props.profession?.id);
      }
    }
  }
})
</script>

<style scoped>
</style>