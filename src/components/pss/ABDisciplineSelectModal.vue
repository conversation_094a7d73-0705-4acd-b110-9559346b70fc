<template>
  <ion-header>
    <!-- Searchbar Input -->
    <ion-toolbar v-show="isSearching || isListSelect">
      <ion-searchbar id="keyword-searchbar" style="padding-bottom: 1px" mode="md" v-model="searchKeyword" :placeholder="t('search')"
                    @ionFocus="isSearching = true" @ionCancel="() => { isListSelect ? closeModal() : isSearching = false }"
                    @keyup.enter="(e) => e.target.blur()" show-cancel-button="always"></ion-searchbar>
    </ion-toolbar>
  </ion-header>
  
  <ion-content :fullscreen="true">

    <!-- Allow multiple select & return as callbacks -->
    <div v-if="isSearching || isListSelect">
      <div v-show="filteredDisciplines().length > 0">
        <ion-item lines="full" v-for="item in filteredDisciplines().slice(0, numOfVisibleItems)" :key="item.id" style="--min-height: 36px">
          <ion-checkbox
            justify="start"
            labelPlacement="end"
            @update:modelValue="onCheckDiscipline($event, item)"
            :modelValue="chosenDisciplines.find(x => x.id == item.id) != null"
            :disabled="readOnly"
          >
            <ion-label>
              <ion-text class="ion-text-wrap">
                <p><b>{{ item.name }}</b> {{ item.nameChi }}</p>
              </ion-text>
              <small>{{ item.groupName }}</small>
            </ion-label>
          </ion-checkbox>
          <ion-button @click="openProgramModal(item)" color="medium" slot="end" fill="clear" size="small" style="margin-inline-end: 16px; text-transform: none">
            Programs
          </ion-button>
        </ion-item>
        
        <ion-infinite-scroll @ionInfinite="loadData($event)" threshold="100px" id="infinite-scroll">
          <ion-infinite-scroll-content loading-spinner="bubbles" loading-text="Loading..."></ion-infinite-scroll-content>
        </ion-infinite-scroll>
      </div>
      <div class="ion-text-center" v-show="filteredDisciplines().length == 0">
        <p>No disciplines available</p>
      </div>
    </div>

    <div v-else>
      <!--
        Choose Disciplines (Cards)
        -->
      <div style="height: 100%" :class="{ 'valign': !isSearching }" v-if="selectedFilterGroup != 'gpt'">
        <swiper
            :navigation="!isMobileWeb()"
            class="card-slides"
            id="card-swiper-slides"
            :effect="'cards'"
            :grabCursor="true"
            :modules="modules"
            v-if="!delayLoading && (relatedCards().length > 0 || filteredDisciplines().length > 0)"
        >
          <!-- Go Back to Previous -->
          <swiper-slide class="card-slide" style="background-color: var(--ion-color-light)" v-if="(startIdx+1)-10 > 0">
            <ion-row>
              <ion-col size="12">
                <ion-button size="large" color="primary" class="no-text-transform" fill="outline"
                            v-on="{ [isMobileWeb() ? 'touchstart' : 'click']: () => { onClickPrevBtn() } }">
                  <ion-icon slot="end" :icon="arrowBack"></ion-icon>
                  Previous
                </ion-button>
              </ion-col>
            </ion-row>
          </swiper-slide>

          <swiper-slide class="card-slide highlighted-border" v-for="card in relatedCards()" :key="card.id" v-show="fromAB3Modal">
            <img style="width: 100%; height: 100%; object-fit: cover" :src="getProxyImgLink(card.imageLink)" />
            <!-- TODO: show links (where to put the buttons?) | card.btn1Text | card.btn1Link -->
          </swiper-slide>

          <!-- Main Slides -->
          <swiper-slide class="card-slide" v-for="discipline in filteredDisciplines().slice(startIdx, startIdx+10)" :key="discipline.id"
                        :class="{ 'highlighted-border': isDisciplineSelected(discipline) }" :data-discipline-id="discipline.id">
            <ion-row>
              <ion-col size="12">
                <!-- Discipline Group Name -->
                <h2><small>{{ discipline.groupName }}</small></h2>

                <!-- Discipline Name -->
                <h2>{{ discipline.name }}<br /><small>{{ discipline.nameChi }}</small></h2>
              </ion-col>
  
              <ion-col size="12">
                <!-- Related programs -->
                <ion-button size="small" color="light" class="no-text-transform" @click="openProgramModal(discipline)">
                  JUPAS
                </ion-button>

                <!-- Related professions -->
                <ion-button size="small" color="light" class="no-text-transform" @click="openProfessionListModal(discipline)" v-if="fromAB3Modal">
                  Career prospects
                </ion-button>
              </ion-col>
            </ion-row>
            
            <ion-fab slot="fixed" vertical="top" horizontal="end">
              <ion-fab-button style="--background: transparent" :disabled="readOnly"
                              v-on="{ [isMobileWeb() ? 'touchstart' : 'click']: () => { onThumbsUp(discipline) } }">
                <ion-icon size="large" :icon="isDisciplineSelected(discipline) ? thumbsUp : thumbsUpOutline"></ion-icon>
              </ion-fab-button>
            </ion-fab>

            <!--<ion-fab slot="fixed" vertical="top" horizontal="start">
              <ion-fab-button style="--background: transparent" v-on="{ [isMobileWeb() ? 'touchstart' : 'click']: () => { onThumbsDown(discipline) } }">
                <ion-icon size="large" :icon="isDisciplineDisliked(discipline) ? thumbsDown : thumbsDownOutline"></ion-icon>
              </ion-fab-button>
            </ion-fab>-->

          </swiper-slide>

          <!-- More (Next) -->
          <swiper-slide class="card-slide" style="background-color: var(--ion-color-light)" v-if="startIdx+10 < filteredDisciplines().length">
            <ion-row>
              <ion-col size="12">
                <ion-button size="large" color="primary" class="no-text-transform" fill="outline"
                            v-on="{ [isMobileWeb() ? 'touchstart' : 'click']: () => { onClickMoreBtn() } }">
                  More
                  <ion-icon slot="end" :icon="arrowForward"></ion-icon>
                </ion-button>
              </ion-col>
            </ion-row>
          </swiper-slide>
        </swiper>
      </div>

      <!-- Recommended Disciplines (AI-assisted) -->
      <div v-else>
        <FDMTRiceCardSlide v-show="gpt.currStep == 1 && botSuggestedDisciplines.length == 0"></FDMTRiceCardSlide>

        <div style="height: 100%" v-show="botSuggestedDisciplines.length > 0 || gpt.currStep == 2">
          <div class="spin" v-if="gpt.waitingGPTResp">
            <ion-spinner></ion-spinner>
          </div>

          <swiper
            :navigation="true"
            class="card-slides discipline-slides"
            id="card-swiper-slides"
            :effect="'cards'"
            :grabCursor="true"
            :modules="modules"
            v-else
          >
            <!-- Main Slides -->
            <swiper-slide class="card-slide" v-for="discipline in botSuggestedDisciplines" :key="discipline.id" :data-discipline-id="discipline.id">
              <ion-row>
                <!-- Discipline Names -->
                <ion-col size="12">
                  <h2><small>{{ discipline.groupName }}</small></h2>
                  <h2>{{ discipline.name }}<br /><small>{{ discipline.nameChi }}</small></h2>
                </ion-col>

                <!-- Buttons -->
                <ion-col size="12">
                  <ion-button size="small" color="light" class="no-text-transform" @click="openProgramModal(discipline)">
                    JUPAS
                  </ion-button>
                  <ion-button size="small" color="light" class="no-text-transform" @click="openProfessionListModal(discipline)">
                    Career prospects
                  </ion-button>
                </ion-col>

                <!-- Discipline explanation -->
                <ion-card style="margin-top: 0; border-radius: 16px" v-show="currViewingDiscipline?.explanation">
                  <ion-card-content style="padding: 0">
                    <ion-item lines="full">
                      <ion-label class="ion-text-wrap">
                        <span class="gpt-explanation" v-html="parseMsg(currViewingDiscipline?.explanation)"></span>
                      </ion-label>
                    </ion-item>
                  </ion-card-content>
                </ion-card>
              </ion-row>
              
              <!-- Button for selecting the discipline -->
              <ion-fab slot="fixed" vertical="top" horizontal="end">
                <ion-fab-button :disabled="readOnly" v-on="{ [isMobileWeb() ? 'touchstart' : 'click']: () => { onThumbsUp(discipline, true) } }">
                  <ion-icon size="large" :icon="isDisciplineSelected(discipline) ? thumbsUp : thumbsUpOutline"></ion-icon>
                </ion-fab-button>
              </ion-fab>
            </swiper-slide>
          </swiper>
        </div>
      </div>
    </div>
  </ion-content>

  <!--<ion-footer v-if="isListSelect && chosenDisciplines.length > 0">-->
  <ion-footer v-if="isListSelect">
    <ion-toolbar>
      <ion-button color="success" expand="block" @click="confirmSelect()">
        Done
        <ion-icon slot="end" :icon="checkmark"></ion-icon>
      </ion-button>
    </ion-toolbar>
  </ion-footer>

  <ion-footer v-show="!isSearching && !isListSelect">
    <ion-toolbar style="--min-height: 24px">
      <!-- Back Button -->
      <ion-buttons slot="start">
        <ion-button class="nav-category-btn" @click="confirmSelect(true)">
          <ion-icon size="small" slot="icon-only" :icon="arrowBack"></ion-icon>
        </ion-button>
      </ion-buttons>

      <!-- Filter group -->
      <ion-segment class="filter-group-segment" mode="ios" v-model="selectedFilterGroup" scrollable>
        <!-- AI (ChatGPT) mainly for AB3 -->
        <ion-segment-button value="gpt" layout="icon-start" v-if="fromAB3Modal">
          <ion-label>AI</ion-label>
        </ion-segment-button>

        <!-- Filter disciplines by target professions (AB4) -->
        <ion-segment-button value="professions" v-if="targetProfessions && targetProfessions.length > 0">
          <ion-label class="ion-text-wrap">Chosen professions</ion-label>
        </ion-segment-button>

        <!-- By discipline groups -->
        <ion-segment-button value="disciplineGroups">
          <ion-label>Disciplines</ion-label>
        </ion-segment-button>

        <!-- Liked disciplines -->
        <ion-segment-button value="like" v-if="userDisciplines.some(ud => ud.reaction == 'like')">
          <ion-icon size="small" :icon="thumbsUpOutline"></ion-icon>
        </ion-segment-button>

        <!-- Search by keywords (list) -->
        <ion-segment-button value="search">
          <ion-ripple-effect></ion-ripple-effect>
          <ion-icon size="small" :icon="search"></ion-icon>
        </ion-segment-button>
      </ion-segment>
    </ion-toolbar>

    <!-- Chip Filter Panel -->
    <ion-toolbar class="material-div" style="height: 25vh">
      <!-- Professions based on Step 1 choices -->
      <ion-row v-show="selectedFilterGroup == 'professions'">
        <ion-chip v-for="profession in targetProfessions" :key="profession.id" :class="{ 'active-tag': selectedOption.id == profession.id }" 
                  @click="selectedOption = profession">
          <ion-label>{{ profession.name }}</ion-label>
        </ion-chip>
      </ion-row>

      <!-- Disicipline groups -->
      <ion-row v-show="selectedFilterGroup == 'disciplineGroups'">
        <ion-chip v-for="dg in disciplineGroups" :key="dg.id" @click="selectedOption = dg.id" :class="{ 'active-tag': selectedOption == dg.id }">
          <ion-label>{{ dg.name }} {{ dg.nameChi }}</ion-label>
        </ion-chip>
      </ion-row>

      <!-- AI -->
      <ion-row v-show="selectedFilterGroup == 'gpt'">
        <!-- Disclaimer -->
        <ion-item lines="none">
          <ion-label class="ion-text-wrap ion-no-margin">
            <p style="line-height: 1"><small>Disclaimer: The data presented is for reference purposes only. We do not guarantee their correctness, completeness, or timeliness.</small></p>
          </ion-label>
        </ion-item>
        
        <!-- User input (descriptions of strength) -->
        <div style="padding: 8px; width: 100%">
          <ion-textarea placeholder="By writing and/or tags, describe your strengths, passions, and habits as specifically as possible"
                        fill="outline" v-model="gpt.userInputText" :rows="3" :auto-grow="true"></ion-textarea>
        </div>

        <!-- Select tag -->
        <div v-for="(options, group) in step1Questions" :key="group">
          <ion-chip v-for="tag in options" :key="tag.id" @click="toggleSelectedTag(tag.id, tag.text)"
                    :class="{ 'active-tag': gpt.tagId == tag.id }">
            <ion-label>{{ tag.text }}</ion-label>
          </ion-chip>
          <ion-chip @click="toggleSelectedTag(tagObjOther.id, tagObjOther.text)"
                    :class="{ 'active-tag': gpt.tagId == tagObjOther.id }">
            <ion-label>{{ tagObjOther.text }}</ion-label>
          </ion-chip>
        </div>
      </ion-row>
    </ion-toolbar>

    <!-- Button for triggering ChatGPT responses -->
    <ion-toolbar v-show="selectedFilterGroup == 'gpt'">
      <ion-row>
        <ion-col>
          <ion-button expand="block" @click="gpt.currStep = 2; getRecommendedDisciplinesFromGPT()"
                      :disabled="(!gpt.tagId && !gpt.userInputText) || gpt.isInCooldownTime" class="no-text-transform">
            {{ gpt.isInCooldownTime ? `Please wait (${gpt.cooldownSecLeft}s)` : `GO` }}
            <ion-icon slot="end" :icon="arrowForward"></ion-icon>
          </ion-button>
        </ion-col>
      </ion-row>
    </ion-toolbar>
  </ion-footer>
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, onMounted, watch, reactive, } from 'vue';

// icons
import { add, close, checkmark, arrowUp,  arrowForward, arrowBack, checkbox, trashOutline,
         thumbsUpOutline, thumbsDownOutline, thumbsUp, thumbsDown, heart, heartOutline,
         chevronBack, chevronForward, repeat, search, pencil, } from 'ionicons/icons';

// components
import { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
        IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonProgressBar, IonAvatar,
        IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption, IonCheckbox, IonSpinner,
        IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid, IonRippleEffect,
        IonNote, IonTextarea, IonFab, IonFabButton, IonBadge, IonInfiniteScroll, IonInfiniteScrollContent, IonModal,
        IonicSlides, modalController } from '@ionic/vue';
import ABProgramSelectModal from '@/components/achievejupas/ABProgramSelectModal.vue';
import ListProfessionModal from '@/components/secondary/ab3/ListProfessionModal.vue'; // for AB3

// Swiper
import 'swiper/swiper.min.css';
import 'swiper/modules/effect-cards/effect-cards.min.css';
import 'swiper/modules/navigation/navigation.min.css';
import '@ionic/vue/css/ionic-swiper.css';
import { Swiper, SwiperSlide } from 'swiper/vue/swiper-vue';
import { EffectCards, Navigation, } from 'swiper';

// composables / services
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { utilsGPT } from '@/composables/utilsGPT';
import { useStore } from '@/store';

// types
import { Card, Discipline, UserClaim, UserDiscipline } from '@/types';

export default defineComponent({
  name: 'ABDisciplineSelectModal',
  props: ["prefilledDisciplines", "targetProfessions", "oldUserDisciplines", "fromAB3Modal", "readOnly", "isListSelect"],
  components: { IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
                IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonAvatar, IonProgressBar,
                IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption, IonCheckbox, IonSpinner,
                IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid, IonRippleEffect,
                IonNote, IonTextarea, IonBadge, IonFab, IonFabButton, IonInfiniteScroll, IonInfiniteScrollContent, IonModal,
                Swiper, SwiperSlide, },
  setup(props, { emit }) {
    // methods or filters
    const { t } = useI18n();
    const store = useStore();
    const { closeModal, openModal, getProxyImgLink,
            processUserItems, isItemSelected, isItemDisliked, isMobileWeb, infiniteScrollLoadData, presentAlert,
            onThumbsUpItem, onThumbsDownItem, onClickMoreBtn, onClickPrevBtn, animateToFirstCard,
            recordCurrSlideReaction, resetActiveSlide, syncChosenItems, resetFilters, focusKeywordSearchbar, } = utils();

    const searchKeyword = ref("");
    const isSearching = ref(false);
    const delayLoading = ref(true);
    const selectedFilterGroup = ref('disciplineGroups');
    const selectedOption = ref<any>("all");
    
    const user = computed(() => store.state.user);

    // AB3
    const responseCards = computed<Card[]>(() => store.state.allCards);
    const disciplineGroups = computed(() => store.state.allDisciplineGroups);
    const allDisciplines = ref<Discipline[]>(store.getters.shuffledDisciplines);
    const chosenDisciplines = ref<Discipline[]>(props.prefilledDisciplines || []);
    const userDisciplines = ref<UserDiscipline[]>(props.oldUserDisciplines || []);
    const tmpNewUserDisciplines = ref<UserDiscipline[]>([]); // for storing slideChange user disciplines
    const numOfVisibleItems = ref(20);
    const loadData = (ev: any) => { infiniteScrollLoadData(ev, numOfVisibleItems, allDisciplines.value) }

    /**
     * GPT (strength-based recommendations)
     */
    const { tagObjOther, parseMsg, fetchGPTResponse, parseGPTResponse, whatsAppSendGPTResults, upsertUserItems, initGPTObj, } = utilsGPT();
    const step1Questions = computed(() => store.getters.getStep1Questions(false, true, ['Subject']));
    const gpt = reactive({
      userInputText: "", // user input prompt
      tagId: "",
      tagText: "",
      selectedFilterGroup: 'Subject', // AB3 focuses on Subject first
      currStep: 1,
      waitingGPTResp: false,
      isInCooldownTime: false, // prevent users sending requests consecutively
      cooldownSecLeft: 0,
    });
    const botSuggestedDisciplines = ref<Discipline[]>([]);
    const currViewingDiscipline = ref<Discipline>();
    const getRecommendedDisciplinesFromGPT = async () => {
      try {
        botSuggestedDisciplines.value = []; // reset
        currViewingDiscipline.value = undefined; // reset
        const MAX_DISCIPLINES = 10;
        const strengthDescriptions = `${gpt.tagText ? `${gpt.tagText} - ` : ``}${gpt.userInputText || ""}`;

        let prompt = `Key strength: ${strengthDescriptions}`;
        prompt += `\n\nAbove is the information about a Hong Kong F3 student. Please suggest at most ${MAX_DISCIPLINES} unique disciplines that are most relevant to the student based on his/her strength. `;
        prompt += `The discipline must ONLY be one of those in the data source "fdmt-disciplines-chunk-X" (consider all chunks).`;
        prompt += ` For each discipline, please explain in detail how the student's key strength is applied in most related study areas`;
        prompt += ` in TRADITIONAL CHINESE to convince the student that the discipline is suitable. Be as specific & concrete as possible.`;
        prompt += `\n\nYour response MUST be formatted in JSON with only an array of JavaScript objects (at most ${MAX_DISCIPLINES} objects), each object must contain exactly 2 fields: "id", "explanation"\n`;
        prompt += `Example response: [{"id":"499","explanation":"XXX"}]`;

        // Send request
        const data = await fetchGPTResponse(gpt, "https://ab-chatgpt-api.fdmt.hk/ab3", prompt, user.value.id, "", props.readOnly);
        if (!data) throw new Error("No data");

        // Parse data (bot responses)
        await parseGPTResponse(gpt, data, currViewingDiscipline, botSuggestedDisciplines, allDisciplines, '.discipline-slides', 'disciplineId');

        // Not update data / send WhatsApp for AB3 parents
        if (!props.readOnly) {
          // Insert to DB for records first
          upsertUserItems(user.value.id, botSuggestedDisciplines, userDisciplines, 'disciplineId');

          // Send out the results to student's WhatsApp group for records
          whatsAppSendGPTResults(user.value.phone, user.value.waGroupId, strengthDescriptions, botSuggestedDisciplines, 'discipline');
        }
      } catch (e) {
        console.error(e);
        presentAlert("ChatGPT did not response. Please try again");
        gpt.currStep = 1;
      } finally {
        gpt.waitingGPTResp = false;
      }
    }

    // Card Slide helper functions
    const getAppState = () => ({
      selectedFilterGroup: selectedFilterGroup.value,
      selectedOption: selectedOption.value,
      searchKeyword: searchKeyword.value,
    });
    const confirmSelect = async (noLoading = false) => {
      if (props.readOnly) return await closeModal({});
      if (props.isListSelect) return await closeModal({ "chosen": chosenDisciplines.value });
      return await closeModal({
        "chosen": chosenDisciplines.value,
        "userDisciplines": processUserItems(chosenDisciplines.value, userDisciplines.value, tmpNewUserDisciplines.value, 'disciplineId', store.state.user.id),
        noLoading,
      }); // return selected discipline & order here
    };

    // Helper functions for discipline reaction
    const recordActiveSlideReaction = () => {
      recordCurrSlideReaction('disciplineId', userDisciplines, tmpNewUserDisciplines, getAppState());
    }
    const isDisciplineSelected = (discipline: any) => (isItemSelected(discipline, chosenDisciplines));
    const isDisciplineDisliked = (discipline: any) => (isItemDisliked(discipline, 'disciplineId', userDisciplines));

    const startIdx = ref(0); // for card slides

    // INIT
    onMounted(() => {
      syncChosenItems('disciplineId', chosenDisciplines, userDisciplines, allDisciplines.value);

      setTimeout(() => {
        // Pre-select profession from last step
        const { targetProfessions } = props;
        if (targetProfessions && targetProfessions.length > 0) {
          const professionWithDisciplines = targetProfessions.find(p => p.relatedDisciplines.length > 0);
          if (professionWithDisciplines) {
            selectedFilterGroup.value = "professions";
            selectedOption.value = professionWithDisciplines;
          }
        }

        // Init card slides
        delayLoading.value = false;
        setTimeout(() => {
          const slides: any = document.querySelector('#card-swiper-slides');
          if (slides) {
            slides.swiper.on('slideChange', recordActiveSlideReaction);
            recordActiveSlideReaction(); // initial slide
          }
        }, 200);
      }, 200);

      // AI: Prefill previously input text & selected tag
      initGPTObj(gpt, user.value.claims);
    })

    watch(selectedOption, () => {
      resetActiveSlide(startIdx, delayLoading, 'disciplineId', userDisciplines, tmpNewUserDisciplines, getAppState());
    })
    watch(selectedFilterGroup, (currGroup, prevGroup) => {
      if (currGroup) {
        if (['like', 'dislike'].includes(currGroup)) {
          selectedOption.value = currGroup;
        }
        else if (currGroup == 'random') {
          resetFilters(selectedFilterGroup, selectedOption);
          allDisciplines.value = store.getters.shuffledDisciplines;
          animateToFirstCard();
        }
        else if (currGroup == 'search') {
          resetFilters(selectedFilterGroup, selectedOption);
          focusKeywordSearchbar(isSearching);
        }
        else {
          if (currGroup == 'professions') {
            const { targetProfessions: professions } = props;
            selectedOption.value = professions.find(p => p.relatedDisciplines.length > 0) || professions[0];
          } else if (currGroup == 'disciplineGroups') {
            selectedOption.value = disciplineGroups.value[0].id;
          }
        }
      }
    })
    
    // 3. return variables & methods to be used in template HTML
    return {
      // icons
      add, close, checkmark, arrowUp, arrowForward, arrowBack, checkbox, trashOutline,
      thumbsUpOutline, thumbsDownOutline, thumbsUp, thumbsDown, heart, heartOutline, pencil,

      // variables
      selectedOption, delayLoading,
      disciplineGroups, allDisciplines,
      chosenDisciplines, userDisciplines, tmpNewUserDisciplines,
      searchKeyword, isSearching,

      // methods
      t, confirmSelect, closeModal, isMobileWeb,
      isDisciplineSelected, isDisciplineDisliked,
      onThumbsUp: (discipline: any, flipToNextSlide = true) => (onThumbsUpItem(discipline, 'disciplineId', chosenDisciplines, userDisciplines, tmpNewUserDisciplines, getAppState(), flipToNextSlide)),
      onThumbsDown: (discipline: any) => (onThumbsDownItem(discipline, 'disciplineId', chosenDisciplines, userDisciplines, tmpNewUserDisciplines, getAppState())),
      
      filteredDisciplines: () => {
        if (searchKeyword.value) {
          const filteredDisciplines = allDisciplines.value.filter(d => d.name.toLowerCase().includes(searchKeyword.value.toLowerCase().trim()));
          return filteredDisciplines;
        }
        if (['like', 'dislike'].includes(selectedOption.value)) {
          return userDisciplines.value.filter(ud => ud.reaction == selectedOption.value).map(ud => {
            return allDisciplines.value.find(d => d.id == ud.disciplineId); 
          });
        }
        const { relatedDisciplines, disciplineIds } = selectedOption.value;
        let res: any = allDisciplines.value;
        if (selectedOption.value != 'all') res.sort((a,b) => (a.seq-b.seq)); // sort if not random
        if (relatedDisciplines) res = relatedDisciplines;
        else if (disciplineIds) res = res.filter(d => disciplineIds.includes(d.id));
        else if (selectedOption.value != 'all') {
          res = allDisciplines.value.filter(d => d.disciplineGroupId.toString() == selectedOption.value);
        }
        return [
          ...res.filter(d => !isDisciplineDisliked(d)),
          ...res.filter(d => isDisciplineDisliked(d)),
        ];
      },
      openProgramModal: (specificDiscipline: any) => (openModal(ABProgramSelectModal, { specificDiscipline, readOnly: true })),

      // swiper
      modules: [EffectCards, IonicSlides, Navigation],
      startIdx,
      onClickMoreBtn: () => { onClickMoreBtn(startIdx) },
      onClickPrevBtn: () => { onClickPrevBtn(startIdx) },

      // Filter groups & filters
      chevronBack, chevronForward, repeat, search,
      selectedFilterGroup,

      // AB3
      step1Questions, responseCards, getProxyImgLink,
      openProfessionListModal: async (discipline) => {
        return openModal(ListProfessionModal, { discipline });
      },
      relatedCards: (): Card[] => {
        //if (selectedOption.value == 'all') return responseCards.value;
        return selectedOption.value.cardSpecs?.map(cs => responseCards.value.find(c => c.id == cs.cardId)) || [];
      },

      // List Disciplines
      loadData, numOfVisibleItems,
      onCheckDiscipline: (checked: any, d: Discipline) => {
        if (checked) {
          if (chosenDisciplines.value.find(x => x.id == d.id) == null) {
            chosenDisciplines.value.unshift(d);
          }
        }
        else {
          const idx = chosenDisciplines.value.findIndex(x => x.id == d.id);
          if (idx !== -1) chosenDisciplines.value.splice(idx, 1);
        }
      },

      // GPT (AI-assisted)
      gpt, botSuggestedDisciplines, currViewingDiscipline,
      getRecommendedDisciplinesFromGPT, parseMsg,
      tagObjOther, toggleSelectedTag: (tagId, tagText) => { gpt.tagId = tagId; gpt.tagText = tagText },
    }
  }
});
</script>

<style scoped>
</style>
