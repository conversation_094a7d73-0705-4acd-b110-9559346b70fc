<template>
  <!--
    Search query form -> refresh -> filtered student list
    -->
  <ion-header>
    <ion-grid class="ion-no-padding" fixed>
      <!-- Role Selection (Teacher / Student) -->
      <ion-toolbar>
        <ion-segment v-model="selectedView" mode="ios" scrollable>
          <ion-segment-button value="secondary-school-student">Student</ion-segment-button>
          <ion-segment-button value="teacher">Teacher</ion-segment-button>
          <ion-segment-button value="school">School</ion-segment-button>
          <ion-segment-button value="program">Program</ion-segment-button>
        </ion-segment>
      </ion-toolbar>

      <!-- Keyword search (global search regardless of existing filters) -->
      <ion-toolbar v-if="!['school', 'program'].includes(selectedView)">
        <ion-searchbar style="padding-bottom: 0; height: 28px" mode="ios" v-model="searchKeyword"></ion-searchbar>
      </ion-toolbar>

      <!-- Filters -->
      <ion-toolbar>
        <ion-row class="filter-items" v-if="selectedView != 'program'">
          <!-- School -->
          <ion-col size-xs="6" size-md="6" size-lg="6" size-xl="3">
            <ion-item lines="none" class="filter-item" :color="filters.schoolIds.length == 0 ? 'light' : 'primary'" @click="openSchoolSelectModal()" button>
              <ion-icon slot="start" :icon="filter" v-show="filters.schoolIds.length == 0"></ion-icon>
              <ion-icon slot="start" :icon="closeCircle" @click.stop="updateFilters('schoolIds', [])" v-show="filters.schoolIds.length > 0"></ion-icon>
              <ion-label class="ion-text-nowrap">
                {{ filters.schoolIds.length == 0 ? "School" : `${filters.schoolIds.length} school${filters.schoolIds.length > 1 ? 's' : ''}` }}
              </ion-label>
            </ion-item>
          </ion-col>

          <!-- Electives -->
          <ion-col size-xs="6" size-md="6" size-lg="6" size-xl="3" v-if="selectedView == 'secondary-school-student'">
            <ion-item lines="none" class="filter-item" :color="selectedFilterCheckboxVals('electives').length == 0 ? 'light' : 'primary'" @click="setPopoverOpen('electives', true, $event)" button>
              <ion-icon slot="start" :icon="filter" v-show="selectedFilterCheckboxVals('electives').length == 0"></ion-icon>
              <ion-icon slot="start" :icon="closeCircle" @click.stop="updateFilters('electives', {})" v-show="selectedFilterCheckboxVals('electives').length > 0"></ion-icon>
              <ion-label>
                {{ selectedFilterCheckboxVals('electives').length == 0 ? "Electives" : `${selectedFilterCheckboxVals('electives').length} elective${selectedFilterCheckboxVals('electives').length > 1 ? 's' : ''} selected` }}
              </ion-label>
            </ion-item>
            <ion-popover :isOpen="popoverState.electives" :event="popoverEvent" :dismiss-on-select="false" @didDismiss="setPopoverOpen('electives', false, $event)">
              <ion-content>
                <ion-item lines="full" v-for="elective in electives" :key="elective.id" button>
                  <ion-checkbox v-model="filters.electives[elective.name]" :value="elective.name">
                    <ion-label>{{ elective.name }}</ion-label>
                  </ion-checkbox>
                </ion-item>
              </ion-content>
            </ion-popover>
          </ion-col>

          <!-- JUPAS Choice Condition (for filtering students based on A1-E5 choices) -->
          <ion-col size-xs="6" size-md="6" size-lg="6" size-xl="3" v-if="selectedView == 'secondary-school-student'">
            <ion-item lines="none" class="filter-item" :color="filters.jupasChoiceActive && localJupasConditions.length > 0 ? 'primary' : 'light'" @click="openJUPASChoiceConditionModal()" button>
              <ion-icon slot="start" :icon="filter" v-show="!filters.jupasChoiceActive || localJupasConditions.length == 0"></ion-icon>
              <ion-icon slot="start" :icon="closeCircle" @click.stop="updateFilters('jupasChoiceActive', false)" v-show="filters.jupasChoiceActive && localJupasConditions.length > 0"></ion-icon>
              <ion-label class="ion-text-nowrap">
                {{ filters.jupasChoiceActive && localJupasConditions.length > 0 ? `${localJupasConditions.length} JUPAS Choice Condition${localJupasConditions.length > 1 ? 's' : ''}` : "JUPAS Choices" }}
              </ion-label>
            </ion-item>
          </ion-col>

          <!-- Filters for Teachers -->

          <!-- Roles of teachers -->
          <ion-col size-xs="6" size-md="6" size-lg="6" size-xl="3" v-if="selectedView == 'teacher'">
            <ion-item lines="none" class="filter-item" :color="selectedFilterCheckboxVals('schoolRoles').length == 0 ? 'light' : 'primary'" @click="setPopoverOpen('schoolRoles', true, $event)" button>
              <ion-icon slot="start" :icon="filter" v-show="selectedFilterCheckboxVals('schoolRoles').length == 0"></ion-icon>
              <ion-icon slot="start" :icon="closeCircle" @click.stop="updateFilters('schoolRoles', {})" v-show="selectedFilterCheckboxVals('schoolRoles').length > 0"></ion-icon>
              <ion-label class="ion-text-nowrap">
                {{ selectedFilterCheckboxVals('schoolRoles').length == 0 ? "School Roles" : selectedFilterCheckboxVals('schoolRoles').sort().join(', ') }}
              </ion-label>
            </ion-item>
            <ion-popover :isOpen="popoverState.schoolRoles" :event="popoverEvent" :dismiss-on-select="false" @didDismiss="setPopoverOpen('schoolRoles', false, $event)">
              <ion-content>
                <ion-item lines="full" v-for="role in visibleSchoolRoles" :key="role" button>
                  <ion-checkbox v-model="filters.schoolRoles[role]" :value="role">
                    <ion-label>{{ role }}</ion-label>
                  </ion-checkbox>
                </ion-item>
              </ion-content>
            </ion-popover>
          </ion-col>

          <!-- Teaching Subjects -->

          <!-- Class teaching -->

          <!-- In WhatsApp Groups -->
        </ion-row>

        <!-- Program Filter (ion-segment-button) -->
        <div v-if="selectedView == 'program'">
          <!-- Discipline Groups -->
          <MultiSelectChips
            v-model="filters.disciplineGroupIds"
            :items="disciplineGroups.map(group => ({ value: group.id, label: group.name }))"
          />

          <!-- Disciplines -->
          <MultiSelectChips
            v-model="filters.disciplineIds"
            :items="disciplines.map(discipline => ({ value: discipline.id, label: discipline.name }))"
          />

          <!-- Institutions -->
          <MultiSelectChips
            v-model="filters.institutionIds"
            :items="institutions.map(institution => ({ value: institution.id, label: institution.nameShort }))"
          />
        </div>

        <!-- School Banding (Multi-select: chips) -->
        <div v-if="selectedView != 'program'">
          <MultiSelectChips
            v-model="filters.selectedBands"
            :items="['1A', '1B', '1C', '2A', '2B', '2C', '3A', '3B', '3C'].map(band => ({ value: band, label: band }))"
          />

          <!-- School Districts -->
          <MultiSelectChips
            v-model="filters.schoolDistricts"
            :items="visibleSchoolDistricts.map(district => ({ value: district, label: district }))"
          />
        </div>

        <div v-if="selectedView == 'secondary-school-student'">
          <MultiSelectChips
            v-model="filters.rankInForms"
            :items="optionRankInForms.map(rank => ({ value: rank, label: rank }))"
          />

          <!-- Year DSE Filter -->
          <MultiSelectChips
            v-model="filters.yearDSE"
            :items="dseYears.map(year => ({ value: year, label: `DSE ${year}` }))"
          />

          <!-- AchieveJUPAS Completion Status -->
          <MultiSelectChips
            v-model="filters.choiceStatus"
            :items="[
              { value: 'incomplete', label: 'Chose < 6 programs' },
              { value: 'missing-target-scores', label: 'Missing Target Scores' },
              { value: 'complete', label: 'Completed' }
            ]"
          />
          
          <!-- Applied Workshop Session (hardcoded tag first) -->
          <ion-segment v-model="filterAppliedSessionId" mode="ios" scrollable style="margin-top: 2px">
            <ion-segment-button value="all">-</ion-segment-button>
            <ion-segment-button v-for="(name, sessionId) in filterSessions" :key="sessionId" :value="sessionId">{{ name }}</ion-segment-button>
            <ion-segment-button value="joined-more-than-1">Joined more than 1</ion-segment-button>
          </ion-segment>

          <!-- TODO: Target Score Filter 
          <div class="score-filter">
            <div class="score-label">
              <ion-select interface="popover" mode="ios" class="prediction-select" value="school">
                <ion-select-option value="school">School Prediction</ion-select-option>
                <ion-select-option value="student">Student's Prediction</ion-select-option>
              </ion-select>
            </div>
            <ion-range color="primary" mode="ios" :value="{ lower: 18, upper: 25 }" :min="0" :max="35"
                      :dual-knobs="true" :pin="true" :ticks="true"></ion-range>
          </div>-->
        </div>

        <ion-list-header color="primary" style="min-height: 36px">
          <!-- Show number of students/teachers -->
          <ion-label>{{ recipientCountDescription }}</ion-label>

          <!-- Schools of visible students/teachers -->
          <div class="horizontal-scroll" v-if="filters.schoolIds.length > 0">
            <ion-chip class="small-chip" v-for="schoolId in visibleSchoolIds as any" :key="schoolId">
              <ion-label><b>{{ schoolId.toUpperCase() }}</b></ion-label>
              <ion-icon :icon="close" @click.stop="updateFilters('schoolIds', filters.schoolIds.filter(s => s != schoolId))"></ion-icon>
            </ion-chip>
          </div>

          <!-- Open message sending modal -->
          <ion-buttons v-if="['secondary-school-student', 'teacher'].includes(selectedView)">
            <ion-button size="small" @click="modalState.sendMessage = true">
              <ion-icon size="small" slot="icon-only" :icon="sendOutline"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-list-header>
      </ion-toolbar>
    </ion-grid>
  </ion-header>

  <ion-content>
    <ion-grid class="ion-no-padding" fixed>
      <!--
        Mega Student List
      -->
      <div v-if="selectedView == 'secondary-school-student'">
        <!-- Loading Data (fetch all students at once) -->
        <p class="ion-text-center" v-if="!fetchedAllStudents"><ion-spinner></ion-spinner></p>

        <div v-else>
          <!-- The AG Grid component -->
          <ag-grid-vue
            class="ag-theme-alpine"
            :suppressRowClickSelection="true"
            rowSelection="multiple"
            :autoSizeStrategy="{ type: 'fitCellContents' }"
            :rowData="filteredStudents"
            :columnDefs="studentColDefs"
            style="height: 500px"
            :modules="agGridModules"
            :pagination="true"
            @selectionChanged="onSelectionChanged"
            :getRowStyle="getRowStyle"
          >
          </ag-grid-vue>
        </div>
      </div>

      <!--
        Mega Teacher List
      -->
      <div v-else-if="selectedView == 'teacher'">
        <!-- Loading Data (fetch all teachers at once) -->
        <p class="ion-text-center" v-if="!fetchedAllTeachers"><ion-spinner></ion-spinner></p>

        <div v-else>
          <!-- The AG Grid component -->
          <ag-grid-vue
            class="ag-theme-alpine"
            :suppressRowClickSelection="true"
            rowSelection="multiple"
            :autoSizeStrategy="{ type: 'fitCellContents' }"
            :rowData="filteredTeachers"
            :columnDefs="teacherColDefs"
            style="height: 500px"
            :modules="agGridModules"
            :pagination="true"
            @selectionChanged="onSelectionChanged"
            :getRowStyle="getRowStyle"
          >
          </ag-grid-vue>
        </div>
      </div>

      <!--
        Mega School List
      -->
      <div v-if="selectedView == 'school'">
        <!-- Loading Data (fetch all schools at once) -->
        <p class="ion-text-center" v-if="!fetchedAllSchools"><ion-spinner></ion-spinner></p>

        <!-- The AG Grid component -->
        <ag-grid-vue
          :rowData="filteredSchools"
          :columnDefs="schoolColDefs"
          style="height: 500px"
          :modules="agGridModules"
          :pagination="true"
          @cellValueChanged="onCellValueChanged"
        >
        </ag-grid-vue>
      </div>

      <!--
        Mega Program List
      -->
      <div v-if="selectedView == 'program'">
        <ag-grid-vue
          :rowData="filteredPrograms"
          :columnDefs="programColDefs"
          style="height: 500px"
          :modules="agGridModules"
          :pagination="true"
          @cellValueChanged="onCellValueChanged"
        >
        </ag-grid-vue>
      </div>
    </ion-grid>

    <!-- Message Sending Modal -->
    <ion-modal :is-open="modalState.sendMessage" @didDismiss="modalState.sendMessage = false">
      <ion-header>
        <!-- Title -->
        <ion-toolbar>
          <ion-title v-if="selectedView == 'secondary-school-student'">Send message to {{ recipientStudents.length }} student{{ recipientStudents.length > 1 ? 's' : '' }}</ion-title>
          <ion-title v-else>Send message to {{ recipientTeachers.length }} teacher{{ recipientTeachers.length > 1 ? 's' : '' }}</ion-title>
          <ion-buttons slot="end"><ion-button @click="modalState.sendMessage = false"><ion-icon slot="icon-only" :icon="close"></ion-icon></ion-button></ion-buttons>
        </ion-toolbar>

        <!-- Link to WA Outbox -->
        <ion-toolbar>
          <ion-item href="https://docs.google.com/spreadsheets/d/1kw9NswTxpI2HrKJAc3bTJnNDfOsCoceJkzmIcCPNSiI/edit?gid=1478216460#gid=1478216460" target="_blank" detail button>
            <ion-label>17/F, F Tower: WhatsApp terminal (Outbox)</ion-label>
          </ion-item>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding">
        <!-- Message Variable Description -->
        <ion-item v-for="(description, key) in msgVariables" :key="key" @click="insertVariableAtCursor(key)" button>
          <ion-icon slot="start" :icon="add" style="margin: 0"></ion-icon>
          <ion-label><ion-chip class="small-chip">{{ key }}</ion-chip> {{ description }}</ion-label>
        </ion-item>

        <!-- Message Content Input -->
        <ion-textarea id="messageTextarea" class="ion-margin-top" v-model="msgOptions.content"
            fill="outline" :rows="5" label-placement="floating" label="Message"></ion-textarea>

        <!-- Message Image -->
        <ion-item>
          <ion-label position="stacked">Image</ion-label>
          <ion-input type="file" accept="image/*" @change="onImageFileChange($event)"></ion-input>
          <ion-buttons slot="end" v-if="msgOptions.photo">
            <ion-button slot="icon-only" @click="clearUploadedImage()"><ion-icon :icon="close"></ion-icon></ion-button>
          </ion-buttons>
        </ion-item>
        <img v-if="msgOptions.photo" :src="msgOptions.photo" style="display: block; margin: auto" />

        <!-- Trigger Send -->
        <ion-button @click="sendMessage()" expand="block" color="success" :disabled="!msgOptions.content">Send</ion-button>

        <!-- Send to teachers of students -->
        <ion-button class="ion-margin-top" @click="sendMsgsToRelatedClassTeachers()" expand="block" :disabled="!msgOptions.content"
                    v-if="selectedView == 'secondary-school-student'">Send to related class teachers</ion-button>
      </ion-content>
    </ion-modal>

    <!-- JUPAS Choice Condition Modal -->
    <ion-modal :is-open="modalState.jupasChoiceConditions" @didDismiss="closeJUPASChoiceConditionModal()">
      <ion-header>
        <!-- Title -->
        <ion-toolbar>
          <ion-title>JUPAS Choice Conditions</ion-title>
          <ion-buttons slot="end"><ion-button @click="closeJUPASChoiceConditionModal()"><ion-icon slot="icon-only" :icon="close"></ion-icon></ion-button></ion-buttons>
        </ion-toolbar>

        <!-- JUPAS Choice Condition Sets - Chips UI -->
        <div style="display: flex; align-items: center; flex-wrap: wrap; gap: 8px; margin-bottom: 8px;">
          <!-- 'New' chip always shown -->
          <ion-chip :outline="selectedJupasSetId !== 'new'" color="primary" @click="selectJupasSet('new')">
            <ion-label>New</ion-label>
          </ion-chip>
          <!-- Saved sets as chips -->
          <ion-chip
            v-for="set in jupasSets"
            :key="set.id"
            :outline="selectedJupasSetId !== set.id"
            color="primary"
            @click="selectJupasSet(set.id)"
          >
            <ion-label>{{ set.name }}</ion-label>
            <ion-icon :icon="pencil" @click.stop="startRenameSet(set)" style="margin-left:2px; font-size: 18px;" />
          </ion-chip>
          <!-- Save button only for 'New' and dirty -->
          <ion-button v-if="selectedJupasSetId === 'new' && isJupasDirty" size="small" color="success" @click="saveNewJupasSet">
            Save
          </ion-button>
        </div>
        <!-- Rename Set Alert -->
        <ion-alert
          :is-open="renameSetModalOpen"
          header="Rename Condition Set"
          :inputs="[{ name: 'newName', type: 'text', value: renameSetInitialName, placeholder: 'New set name' }]"
          :buttons="[
            { text: 'Cancel', role: 'cancel', handler: () => { renameSetModalOpen = false; } },
            { text: 'OK', handler: (data) => confirmRenameSet(data.newName) }
          ]"
          @didDismiss="renameSetModalOpen = false"
        />
        <!-- Add Set Name Alert -->
        <ion-alert
          :is-open="addSetPromptOpen"
          header="Name for this Condition Set"
          :inputs="[{ name: 'newSetName', type: 'text', value: addSetPromptName, placeholder: 'e.g. Band 1 Only' }]"
          :buttons="[
            { text: 'Cancel', role: 'cancel', handler: () => { addSetPromptOpen = false; } },
            { text: 'Save', handler: (data) => { addSetPromptOpen = false; confirmAddSetPrompt(data.newSetName); } }
          ]"
          @didDismiss="addSetPromptOpen = false"
        />
      </ion-header>

      <ion-content class="ion-padding">
        <ion-card v-for="(condition, idx) in localJupasConditions" :key="idx">
          <ion-item>
            <!-- Must fulfill (AND / OR) -->
            <ion-checkbox justify="start" labelPlacement="end" v-model="condition.isNecessary">And?</ion-checkbox>

            <!-- Is client program -->
            <ion-checkbox justify="start" labelPlacement="end" v-model="condition.isClientProgram">Client Program?</ion-checkbox>

            <ion-buttons slot="end">
              <ion-button @click="localJupasConditions.splice(idx, 1)"><ion-icon :icon="close"></ion-icon></ion-button>
            </ion-buttons>
          </ion-item>

          <!-- Entity Type -->
          <ion-segment mode="ios" v-model="condition.targetEntityType">
            <ion-segment-button v-for="entityType in ['Program', 'Discipline', 'Discipline Group']" :key="entityType" :value="entityType">
              {{ entityType }}
            </ion-segment-button>
          </ion-segment>

          <!-- Entity Names (programs, disciplines, discipline groups) --> 
          <div style="margin: 8px 0" v-if="condition.targetEntityType">
            <!-- Discipline Group -->
            <ion-select v-if="condition.targetEntityType == 'Discipline Group'" :label="`Select ${condition.targetEntityType}(s)`" label-placement="floating"
                        v-model="condition.targetDisciplineGroups" :compare-with="(a, b) => a.id === b.id"
                        interface="popover" multiple fill="outline">
              <ion-select-option v-for="disciplineGroup in disciplineGroups" :key="disciplineGroup.id" :value="disciplineGroup">
                {{ disciplineGroup.name }}
              </ion-select-option>
            </ion-select>

            <!-- Program / Discipline -->
            <ion-textarea :label="`Select ${condition.targetEntityType}(s)`" label-placement="floating" :rows="5" fill="outline" :value="getTargetEntityNames(condition)"
                          :clearInput="true" @click="openEntitySelectModal(condition)" style="font-size: 18px !important"
                          v-else></ion-textarea>
          </div>

          <!-- Choice Range -->
          <ion-select label="Target Choice Range" label-placement="floating"
                      :value="ordersProxy(condition)" @ionChange="onOrdersChange($event, condition)"
                      interface="popover" multiple fill="outline">
            <ion-select-option :value="'__select_all__'" :selected="isAllSelected(condition)">
              <b>Select All</b>
            </ion-select-option>
            <ion-select-option v-for="idx in Array.from({length: 20}, (_, i) => i)" :key="idx" :value="idx+1">
              {{ getBandLabel(idx) }} ({{ getNumOfStudents(idx, condition) }})
            </ion-select-option>
          </ion-select>
        </ion-card>

        <!-- Add New Condition -->
        <div class="ion-text-center"><ion-fab><ion-fab-button @click="localJupasConditions.push({ orders: [], targetEntityType: 'Program', targetPrograms: [], targetDisciplines: [], targetDisciplineGroups: [], isNecessary: false, isClientProgram: false })"><ion-icon :icon="add"></ion-icon></ion-fab-button></ion-fab></div>
      </ion-content>
    </ion-modal>
  </ion-content>
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, watch, reactive, onMounted, nextTick, } from 'vue';

// icons
import { add, close, checkmark, arrowUp,  arrowForward, arrowBack, trashOutline, sendOutline, helpCircleOutline,
        createOutline, openOutline, chevronForwardOutline, informationCircleOutline, personAddOutline, pencil,
        downloadOutline, star, starOutline, arrowDown, addCircleOutline, settingsOutline,
        filter, closeCircle, logoWhatsapp, } from 'ionicons/icons';

// components
import { IonPage, IonHeader, IonToolbar, IonTitle, IonContent, IonFooter, IonRow, IonCol, IonAccordion, IonAccordionGroup,
        IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonBadge, IonAlert,
        IonSearchbar, IonSegment, IonSegmentButton, IonList, IonSelect, IonSelectOption,
        IonCard, IonCardHeader, IonCardSubtitle, IonCardContent, IonChip, IonText, IonCardTitle, IonGrid, IonCheckbox,
        IonInfiniteScroll, IonInfiniteScrollContent, IonReorder, IonReorderGroup, IonListHeader,
        IonFab, IonFabButton, IonRange, IonInput, IonModal, IonPopover, IonSpinner, IonTextarea,
        loadingController, modalController, alertController, } from '@ionic/vue';
import UserDetailsModal from '@/components/modals/UserDetailsModal.vue';
import SchoolSelectModal from '@/components/secondary/SchoolSelectModal.vue';
import ABProgramSelectModal from '@/components/achievejupas/ABProgramSelectModal.vue';
import ABDisciplineSelectModal from '@/components/pss/ABDisciplineSelectModal.vue';
import SchoolFormModal from '@/components/modals/SchoolFormModal.vue';
import MultiSelectChips from '@/components/common/MultiSelectChips.vue';

// composables / services
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useJupasChoiceConditionSets } from '@/composables/useJupasChoiceConditionSets';
import { useStore } from '@/store';
import { useRouter } from 'vue-router';
import { Camera, CameraResultType, CameraSource } from "@capacitor/camera";

// types
import { Program, Discipline, DisciplineGroup, School, Session, Tag, User } from '@/types';
import InternalService from '@/services/InternalService';

// config
import config from '@/config';

// data visualization
import { AgGridVue } from "ag-grid-vue3"; // Vue Data Grid Component
import {
  AllCommunityModule, 
  RowSelectionModule,
  ClientSideRowModelModule,
} from 'ag-grid-community';
import SchoolService from '@/services/SchoolService';

interface JUPASChoiceCondition {
  id: any;
  orders: number[];
  targetEntityType: "Program" | "Discipline" | "Discipline Group";
  targetPrograms: Program[];
  targetDisciplines: Discipline[];
  targetDisciplineGroups: DisciplineGroup[];
  isNecessary: boolean;
  isClientProgram: boolean;
}

export default defineComponent({
  name: 'FDMTStaffActionCardList',
  props: [],
  components: { 
    IonPage, 
    IonHeader, 
    IonToolbar, 
    IonTitle, 
    IonContent, 
    IonFooter, 
    IonRow, 
    IonCol, 
    IonAccordion, 
    IonAccordionGroup,
    IonItem, 
    IonLabel, 
    IonIcon, 
    IonButtons, 
    IonButton, 
    IonBadge, 
    IonAlert,
    IonSearchbar, 
    IonSegment, 
    IonSegmentButton, 
    IonList, 
    IonSelect, 
    IonSelectOption,
    IonCard, 
    IonCardHeader, 
    IonCardSubtitle, 
    IonCardContent, 
    IonChip, 
    IonText, 
    IonCardTitle, 
    IonGrid, 
    IonCheckbox,
    IonInfiniteScroll, 
    IonInfiniteScrollContent, 
    IonReorder, 
    IonReorderGroup, 
    IonListHeader,
    IonFab, 
    IonFabButton, 
    IonRange, 
    IonInput, 
    IonModal, 
    IonPopover, 
    IonSpinner, 
    IonTextarea,
    AgGridVue, 
    MultiSelectChips,
  },
  setup(props) {
    // methods or filters
    const store = useStore();
    const { openModal, presentPrompt, presentToast, infiniteScrollLoadData, formatStudentNumber,
            getQRCodeUrl, getF6YearDSE, getF5YearDSE, uniqueId, getBandLabel, getSecStudentForm,
            calculateSumWeightedScores, getUserSubjects, } = utils();
    const { t } = useI18n();
    const router = useRouter();

    // JUPAS Choice Condition Sets logic
    const {
      sets: jupasSets,
      fetchSets: fetchJupasSets,
      selectedSetId: jupasSetSelectedId,
      selectedSet: jupasSet,
      addSet: addJupasSet,
      updateSet: updateJupasSet,
      renameSet: renameJupasSet,
      removeSet: removeJupasSet,
    } = useJupasChoiceConditionSets();

    // --- JUPAS Choice Condition Sets (ion-chip, local/DB logic) ---
    const localJupasConditions = ref<JUPASChoiceCondition[]>([]); // local (unsaved) set
    const selectedJupasSetId = ref<'new' | string>('new');
    const isJupasDirty = ref(false); // true if localJupasConditions changed since last save/load
    const lastLoadedLocal = ref<string>('[]'); // for dirty check

    // Load set into local variable
    function loadJupasSet(id: 'new' | string) {
      if (id === 'new') {
        // restore local draft or empty
        const draft = localStorage.getItem('jupasDraft');
        localJupasConditions.value = draft ? JSON.parse(draft) : [];
        lastLoadedLocal.value = JSON.stringify(localJupasConditions.value);
      } else {
        const found = jupasSets.value.find(s => s.id === id);
        localJupasConditions.value = found ? JSON.parse(JSON.stringify(found.conditions || [])) : [];
        lastLoadedLocal.value = JSON.stringify(localJupasConditions.value);
      }
      isJupasDirty.value = false;
    }

    // Select chip handler
    function selectJupasSet(id: 'new' | string) {
      selectedJupasSetId.value = id;
      localStorage.setItem('lastJupasSetId', id);
      loadJupasSet(id);
    }

    // Watch for changes to localJupasConditions to track dirty state
    watch(localJupasConditions, (val) => {
      isJupasDirty.value = JSON.stringify(val) !== lastLoadedLocal.value;
      // Save draft if editing 'new'
      if (selectedJupasSetId.value === 'new') {
        localStorage.setItem('jupasDraft', JSON.stringify(val));
      } else if (selectedJupasSetId.value !== 'new') {
        // auto-save to DB if editing a saved set
        updateJupasSet(selectedJupasSetId.value, val);
      }
    }, { deep: true });

    // Save new set to DB
    // State for add set prompt
    const addSetPromptOpen = ref(false);
    const addSetPromptName = ref('');

    async function saveNewJupasSet() {
      addSetPromptName.value = '';
      addSetPromptOpen.value = true;
    }

    async function confirmAddSetPrompt(name: string) {
      if (!name || !name.trim()) return;
      const newSet: any = await addJupasSet(name.trim(), localJupasConditions.value);
      await fetchJupasSets();
      selectedJupasSetId.value = newSet.id;
      loadJupasSet(newSet.id);
      localStorage.removeItem('jupasDraft');
    }

    // Rename set logic
    const renameSetModalOpen = ref(false);
    const renameSetInitialName = ref('');
    let setToRename: any = null;
    function startRenameSet(set: any) {
      setToRename = set;
      renameSetInitialName.value = set.name;
      renameSetModalOpen.value = true;
    }
    function confirmRenameSet(newName: string) {
      if (setToRename && newName && newName.trim()) {
        renameJupasSet(setToRename.id, newName.trim());
      }
      renameSetModalOpen.value = false;
      setToRename = null;
    }

    // ion-select select all
    const prevSelectedValues = ref<any>({});

    // State variables
    const user = computed(() => store.state.user);
    const allSubjects = computed(() => store.state.allSubjects);
    const allStudents = computed<User[]>(() => store.state.allStudents); // All students
    const allTeachers = computed<User[]>(() => store.state.allTeachers); // All teachers
    const fetchedAllStudents = computed(() => store.state.fetchedAllStudents);
    const fetchedAllTeachers = computed(() => store.state.fetchedAllTeachers);
    const fetchedAllSchools = computed(() => store.state.fetchedAllSchools);
    const allSchools = computed<School[]>(() => store.state.schools); // All Schools
    const schoolsWithStudents = computed(() => {
      return [...new Set(allStudents.value.map(s => s.schoolId))].map(id => allSchools.value.find(s => s.id == id)).filter(s => s);
    });
    const electives = computed(() => store.state.allElectives);
    const programs = computed(() => store.state.allPrograms);
    const disciplineGroups = computed(() => store.state.allDisciplineGroups);
    const disciplines = computed(() => store.state.allDisciplines); // for discipline tab
    const institutions = computed(() => store.getters.getInstitutionsByProgramType('jupas'));

    // Filter by sessions
    const filterAppliedSessionId = ref('all');
    const getStudentSessionResp = (s, targetKey = 'response') => {
      return (s.sessionResponses?.find(r => r.sessionId == filterAppliedSessionId.value) || {})[targetKey];
    };
    const filterSessions = {
      'e128c552': 'IAWork',
      'e39213ae': 'GBusWork',
      'e7269dfd': 'NursingWork',
      'edc935d8': 'BBAWork',
    };

    // Filters or search
    const searchKeyword = ref("");
    const selectedView = ref('secondary-school-student');
    const filters = reactive({
      // ...other filters
      jupasChoiceActive: false, // Track if JUPAS filter is active

      choiceStatus: [] as any[],
      schoolIds: [] as any[],
      selectedBands: [] as any[],
      schoolDistricts: [] as any[],

      // Students
      yearDSE: [] as any[],
      studentClass: "all",
      electives: {},
      rankInForms: [] as any[],
      jupasChoiceConditions: [] as JUPASChoiceCondition[],

      // Teachers
      schoolRoles: {},

      // Programs:
      institutionIds: [] as any[],
      disciplineIds: [] as any[],
      disciplineGroupIds: [] as any[],
    });
    
    const popoverState = reactive({
      // students
      electives: false,

      // teachers
      schoolRoles: false,
    })
    const popoverEvent = ref();
    const setPopoverOpen = (popoverKey: any, state: boolean, ev: any) => {
      popoverEvent.value = ev; 
      popoverState[popoverKey] = state;
    };
    const selectedFilterCheckboxVals = (key: string) => {
      return Object.keys(filters[key]).filter(k => filters[key][k]);
    };

    // For AchieveJUPAS choice filters
    const addChoiceFilter = () => {
      localJupasConditions.value.push({
        id: `c${uniqueId()}`,
        orders: [], // 1 = A1, 2 = A2, ... 
        targetEntityType: "Program",
        targetPrograms: [],
        targetDisciplines: [],
        targetDisciplineGroups: [],
        isNecessary: false,
        isClientProgram: false,
      });
    }
    const getValidJUPASChoiceConditions = () => {
      return localJupasConditions.value.filter(c => {
        if (c.targetEntityType === 'Program') return c.targetPrograms.length > 0;
        if (c.targetEntityType === 'Discipline') return c.targetDisciplines.length > 0;
        if (c.targetEntityType === 'Discipline Group') return c.targetDisciplineGroups.length > 0;
        return false;
      });
    }

    // Modal
    const modalState = reactive({
      schoolSelect: false,
      sendMessage: false,
      jupasChoiceConditions: false,
    });

    // For message sending
    const msgOptions = reactive({
      content: "",
      photoFile: null,
      photo: "",
    })
    const msgVariables = {
      '{{name}}': "Full name",
      '@852{{phone}}': "Tag in group",
    }
    const selectedStudents = ref([]);
    const selectedTeachers = ref([]);

    // Infinite Scroll
    const numOfVisibleItems = ref(20);
    const loadData = (ev: any) => {
      infiniteScrollLoadData(ev, numOfVisibleItems, allStudents.value, 50);
    }

    // Get count of programs with 'like' reaction
    const getStudentProgramCount = (student: User) => {
      return (student.userPrograms || []).filter(up => up.reaction === 'like').length;
    };

    // Open user details modal
    const openUserDetails = async (student: User) => {
      return await openModal(UserDetailsModal, { user: student, isStudentView: false });
    };

    // Get unique DSE years
    const dseYears = computed(() => {
      const uniqueYrs = [...new Set(allStudents.value.map(s => Number(s.yearDSE)).filter(year => year))];
      return uniqueYrs.sort((a, b) => b - a); // Sort descending
    });

    // Filter teachers based on all criteria
    const filteredTeachers = computed(() => {
      let teachers = allTeachers.value.slice();

      // Text search (global search)
      if (searchKeyword.value) {
        const cleanedKeyword = searchKeyword.value.toLowerCase();
        return teachers.filter((t: User) => {
          const searchInWords = `${t.fullName} ${t.chineseName} ${t.preferredName} ${t.phone} ${t.email}`.toLowerCase();
          return searchInWords.includes(cleanedKeyword);
        });
      }

      // School filter
      if (filters.schoolIds.length > 0) {
        teachers = teachers.filter(t => filters.schoolIds.includes(t.schoolId));
      }
      // District filter
      if (filters.schoolDistricts.length > 0) {
        teachers = teachers.filter(t => {
          const school = allSchools.value.find(school => school.id === t.schoolId);
          return school && filters.schoolDistricts.includes(school.district);
        });
      }
      // Band filter
      if (filters.selectedBands.length > 0) {
        teachers = teachers.filter(t => {
          const school = allSchools.value.find(school => school.id === t.schoolId);
          return school && filters.selectedBands.includes(school.band);
        });
      }
      // School Roles filter
      if (selectedFilterCheckboxVals('schoolRoles').length > 0) {
        teachers = teachers.filter(t => {
          const roles = t.teacher?.schoolRoles?.split(' , ').map(r => r.trim().toLowerCase());
          return roles && selectedFilterCheckboxVals('schoolRoles').some(r => roles.includes(r));
        });
      }

      // Only show active teachers
      teachers = teachers.filter(t => t.teacher?.employmentStatus == 'Active');

      return teachers;
    });

    // We'll use AG Grid's built-in checkbox selection for reliability
    const teacherColDefs = ref([
      { 
        headerName: 'Select', 
        checkboxSelection: true,
        headerCheckboxSelection: true,
        pinned: "left",
      },
      // Details button column
      { 
        headerName: 'Action', 
        cellRenderer: (params: any) => `<ion-button size="small" class="details-btn">Details</ion-button>`, 
        onCellClicked: (params: any) => openUserDetails(params.data),
        filter: false,
        sortable: false,
      },
      { field: "schoolId", headerName: "School", filter: true, floatingFilter: true, valueFormatter: (params: any) => params.value?.toUpperCase(), },
      { field: "fullName", headerName: "Full Name", filter: true, floatingFilter: true, },
      { field: "preferredName", headerName: "Preferred Name", filter: true, floatingFilter: true, },
      { field: "phone", headerName: "Phone", filter: true, floatingFilter: true, },
      { field: "email", headerName: "Email", filter: true, floatingFilter: true, },
      { valueGetter: (params: any) => params.data.teacher?.schoolRoles, headerName: "School Roles", filter: true, floatingFilter: true, },
      { cellRenderer: (params: any) => (params.data.teacher?.classRoles || []).filter(cr => cr.status != 'removed').map(cr => (`<b>${cr.role} (${cr.classes})</b>`)).join(' & '), headerName: "Class Roles", filter: true, floatingFilter: true, },
      { valueGetter: (params: any) => params.data.teacher?.employmentStatus, headerName: "Employment Status", filter: true, floatingFilter: true, },
      { field: "userInWaGroup", headerName: "In WhatsApp Group?", filter: true, floatingFilter: true, },
      { cellRenderer: (params: any) => `<a href="${params.data.waGroupLink}" target="_blank">Group Link</a>`, headerName: "WhatsApp Group Link", filter: false, floatingFilter: false, },
    ]);

    const hasParentConsent = (student: any) => {
      return student.userConsentRecords?.find(r => r.target == 'achievejupas_parent_consent');
    }

    // Filter students based on all criteria
    const getCheckChoicesByJUPASCondition = (s: User, condition: JUPASChoiceCondition, specificOrder: any = null) => {
      let checkChoices = s.userPrograms || [];
      if (specificOrder) {
        checkChoices = checkChoices.filter(c => c.order == specificOrder);
      } else if (condition.orders.length > 0) {
        checkChoices = checkChoices.filter(c => (c.order ? condition.orders.includes(c.order) : false));
      }
      switch (condition.targetEntityType) {
        case 'Program':
          checkChoices = checkChoices.filter(c => condition.targetPrograms.some(p => p.id == c.programId));
          break;
        case 'Discipline':
          checkChoices = checkChoices.filter(c => {
            const p = programs.value.find(p => p.id == c.programId);
            return p && condition.targetDisciplines.some(d => p.disciplines.map(pd => pd.id).includes(d.id));
          });
          break;
        case 'Discipline Group':
          checkChoices = checkChoices.filter(c => {
            const p = programs.value.find(p => p.id == c.programId);
            return p && condition.targetDisciplineGroups.some(dg => p.disciplines.map(pd => pd.disciplineGroupId).includes(dg.id));
          });
          break;
      }
      return checkChoices;
    }
    const filteredStudents = computed(() => {
      let students = allStudents.value.slice();

      // Text search (global search)
      if (searchKeyword.value) {
        const cleanedKeyword = searchKeyword.value.toLowerCase();
        return students.filter((s: User) => {
          const searchInWords = `${s.fullName} ${s.chineseName} ${s.preferredName} ${s.phone} ${s.email} ${s.class}${s.studentNumber}`.toLowerCase();
          return searchInWords.includes(cleanedKeyword);
        });
      }

      // School filter
      if (filters.schoolIds.length > 0) {
        students = students.filter(s => filters.schoolIds.includes(s.schoolId));
      }

      // Band filter
      if (filters.selectedBands.length > 0) {
        students = students.filter(s => {
          const school = allSchools.value.find(school => school.id === s.schoolId);
          return school && filters.selectedBands.includes(school.band);
        });
      }

      // District filter
      if (filters.schoolDistricts.length > 0) {
        students = students.filter(s => {
          const school = allSchools.value.find(school => school.id === s.schoolId);
          return school && filters.schoolDistricts.includes(school.district);
        });
      }

      // Elective filter
      if (selectedFilterCheckboxVals('electives').length > 0) {
        students = students.filter(s => selectedFilterCheckboxVals('electives').some(electiveName => s.studyingElectives?.includes(electiveName)));
      }

      // Rank in form filter
      if (filters.rankInForms.length > 0) {
        students = students.filter(s => filters.rankInForms.some(r => r == s.rankInForm));
      }

      // Year DSE filter
      if (filters.yearDSE.length > 0) {
        students = students.filter(s => filters.yearDSE.some(y => y == s.yearDSE));
      }

      // Filtered by JUPAS choices
      if (filters.jupasChoiceActive && getValidJUPASChoiceConditions().length > 0) {
        students = students.filter(s => {
          let allFulfilled = false;
          s.selectedClientProgram = false;
          for (const condition of getValidJUPASChoiceConditions()) {
            let fulfilled = false;
            const checkChoices = getCheckChoicesByJUPASCondition(s, condition);
            if (checkChoices.length > 0) {
              fulfilled = true;
              allFulfilled = true; // OR condition (any 1 condition matches)
              if (condition.isClientProgram) s.selectedClientProgram = true; // show students first in the list
            }
            if (condition.isNecessary && !fulfilled) { // AND condition
              allFulfilled = false;
              break;
            }
          }
          return allFulfilled;
        });
      }

      // Applied Workshop Session filter
      if (filterAppliedSessionId.value !== 'all') {
        if (filterAppliedSessionId.value == 'joined-more-than-1') { // Join more than 1 Work
          const sessionIds = Object.keys(filterSessions); // workshop session IDs
          students = students.filter(s => {
            const filteredResponses = (s.sessionResponses || []).filter(sr => (['Yes', 'Confirmed', 'Attended'].includes(sr.response) && sessionIds.includes(sr.sessionId)));
            if (filteredResponses.length > 1) {
              s.filteredSessionResponses = filteredResponses;
              return true;
            }
            return false;
          });
        } else {
          students = students.filter(s => {
            return s.sessionResponses?.find(sr => (['Yes', 'Confirmed', 'Attended'].includes(sr.response) && sr.sessionId === filterAppliedSessionId.value));
          });
        }
      }

      // Class filter
      if (filters.studentClass !== 'all') {
        students = students.filter(s => s.class === filters.studentClass);
      }

      // AchieveJUPAS completion status filter
      if (filters.choiceStatus.length > 0) {
        const cs = filters.choiceStatus;
        students = students.filter(s => {
          const programCount = getStudentProgramCount(s);
          if (cs.includes('complete') && programCount >= 20) return true;
          if (cs.includes('incomplete') && programCount < 6) return true;
          if (cs.includes('missing-target-scores')) {
            const existsTargetScore = (s.userSubjectGrades || []).some(g => g.source == 'overallTarget');
            if (!existsTargetScore) return true;
          }
          return false;
        });
      }

      // Sort by DSE year (desc), then class, then student number
      /*return students.sort((a, b) => {
        if (a.yearDSE !== b.yearDSE) return (b.yearDSE || 0) - (a.yearDSE || 0);
        const classA = a.class?.toUpperCase() || '';
        const classB = b.class?.toUpperCase() || '';
        if (classA !== classB) return classA.localeCompare(classB);
        return (a.studentNumber || '').toString().localeCompare(b.studentNumber || '');
      });*/

      // Show students who selected client programs first (highlight in specific colors)
      return students.sort((a, b) => {
        if (a.selectedClientProgram && !b.selectedClientProgram) return -1;
        if (!a.selectedClientProgram && b.selectedClientProgram) return 1;
        return 0;
      });
    });

    const calculateBest5Scores = (student: User) => {
      const targetCondition = getValidJUPASChoiceConditions().find(c => c.isClientProgram);
      const targetProgram = programs.value.find(p => p.id === targetCondition?.targetPrograms[0]?.id) || {};
      const userSubjects = getUserSubjects(allSubjects.value, student); 
      const { totalScores } = calculateSumWeightedScores(allSubjects.value, userSubjects, student.userSubjectGrades || [], targetProgram, 'schoolPredict');
      return totalScores.toFixed(1);
    };
    const defaultStudentColDefs = [
      { 
        headerName: 'Select', 
        checkboxSelection: true,
        headerCheckboxSelection: true,
        pinned: "left",
      },
      { 
        headerName: 'Action', 
        cellRenderer: (params: any) => `<ion-button size="small" class="details-btn">Details</ion-button>`, 
        onCellClicked: (params: any) => openUserDetails(params.data),
        filter: false,
        sortable: false,
      },
      { field: "schoolId", headerName: "School", filter: true, floatingFilter: true, valueFormatter: (params: any) => params.value?.toUpperCase(), },
      { field: "fullName", headerName: "Full Name", filter: true, floatingFilter: true, },
      { field: "chineseName", headerName: "Chinese Name", filter: true, floatingFilter: true, },
      { headerName: "Form", valueGetter: (params: any) => getSecStudentForm(params.data.yearDSE), filter: true, floatingFilter: true, },
      { field: "rankInForm", headerName: "Rank in Form", filter: true, floatingFilter: true, },
      { 
        headerName: "AchieveJUPAS", 
        valueGetter: (params: any) => getStudentProgramCount(params.data), 
        filter: 'agNumberColumnFilter', 
        floatingFilter: true, 
        valueFormatter: (params: any) => params.value >= 20 ? 'Complete' : `${params.value}/20`,
        cellStyle: (params: any) => {
          return params.value >= 20 ? { backgroundColor: 'var(--ion-color-success-tint)', color: 'var(--ion-color-success-contrast)' } : {};
        }
      },
      { 
        headerName: "Parent Consent", 
        valueGetter: (params: any) => hasParentConsent(params.data), 
        filter: 'agNumberColumnFilter', 
        floatingFilter: true, 
        valueFormatter: (params: any) => params.value ? 'Yes' : 'No',
        cellStyle: (params: any) => {
          return params.value == 'Yes' ? { backgroundColor: 'var(--ion-color-success-tint)', color: 'var(--ion-color-danger)' } : {};
        }
      },
      { field: "phone", headerName: "Phone", filter: true, floatingFilter: true, },
      { field: "userInWaGroup", headerName: "In WhatsApp Group?", filter: true, floatingFilter: true, },
      { cellRenderer: (params: any) => `<a href="${params.data.waGroupLink}" target="_blank">Group Link</a>`, headerName: "WhatsApp Group Link", filter: false, floatingFilter: false, },
      { headerName: "Best 5 Scores", filter: 'agNumberColumnFilter', floatingFilter: true, valueGetter: (params: any) => calculateBest5Scores(params.data), },
    ];
    const studentColDefs = ref(defaultStudentColDefs);

    // Filtered schools
    const filteredSchools = computed(() => {
      let schools = allSchools.value.slice();

      // School filter
      if (filters.schoolIds.length > 0) {
        schools = schools.filter(s => filters.schoolIds.includes(s.id));
      }
      // District filter
      if (filters.schoolDistricts.length > 0) {
        schools = schools.filter(s => {
          return filters.schoolDistricts.includes(s.district);
        });
      }
      // Band filter
      if (filters.selectedBands.length > 0) {
        schools = schools.filter(s => {
          return filters.selectedBands.includes(s.band);
        });
      }

      return schools.map(s => ({
        ...s,
        signedMOU: s.userConsentRecords?.some(r => r.target == 'mou'),
        principalRegistered: allTeachers.value.some(t => t.teacher?.schoolRoles?.toLowerCase().split(" , ").includes("principal") && t.schoolId === s.id),
        vicePrincipalRegistered: allTeachers.value.some(t => t.teacher?.schoolRoles?.toLowerCase().includes("vice principal") && t.schoolId === s.id),
        careerTeacherRegistered: allTeachers.value.some(t => t.teacher?.schoolRoles?.toLowerCase().includes("career") && t.schoolId === s.id),
        numOfRegisteredTeachers: allTeachers.value.filter(t => t.schoolId === s.id && t.teacher?.employmentStatus == "Active").length,
      }));
    });

    // Get entities by selectedView
    const currSelectedEntities = computed(() => {
      switch (selectedView.value) {
        case 'secondary-school-student':
          return filteredStudents.value;
        case 'teacher':
          return filteredTeachers.value;
        case 'school':
          return filteredSchools.value;
      }
      return [];
    });

    // Visible schools
    const visibleSchoolIds = computed(() => {
      const targetUsers = currSelectedEntities.value;
      if (selectedView.value === 'school') return allSchools.value.map(s => s.id);
      return [...new Set(targetUsers.map(s => s.schoolId))].filter(s => s);
    });
    const visibleSchoolDistricts = computed(() => {
      return [...new Set(allSchools.value.map(s => s.district))].sort();
      //const targetUsers = currSelectedEntities.value;
      //if (selectedView.value === 'school') return [...new Set(allSchools.value.map(s => s.district))].sort();
      //return [...new Set(targetUsers.map(s => s.schoolId).map(schoolId => allSchools.value.find(sch => sch.id == schoolId)?.district))].filter(d => d).sort();
    });

    // Recipient
    const recipientStudents = computed<any>(() => (selectedStudents.value.length > 0 ? selectedStudents.value : filteredStudents.value));
    const recipientTeachers = computed<any>(() => (selectedTeachers.value.length > 0 ? selectedTeachers.value : filteredTeachers.value));

    // School Table
    const openSchoolFormModal = async (schoolId) => {
      const school = allSchools.value.find(s => s.id == schoolId);
      await openModal(SchoolFormModal, { school })
    }
    const achievejupasStatus = ['Active', 'Interested', 'Not Interested', 'Inactive'];
    const schoolColDefs = ref([
      { field: "nameShort", headerName: "Name", filter: true, floatingFilter: true, valueFormatter: (params: any) => params.value?.toUpperCase(), pinned: 'left', },
      { field: "band", headerName: "Band", filter: true, floatingFilter: true, pinned: 'left', },
      { field: "achievejupasStatus", headerName: "AchieveJUPAS Status", filter: true, floatingFilter: true, editable: true, cellEditor: "agSelectCellEditor", cellEditorParams: { values: achievejupasStatus, }, },
      { field: "signedMOU", headerName: "Signed MOU?", filter: true, floatingFilter: true, },
      { field: "achievejupasT1Briefing", headerName: "Term 1 Briefing", filter: true, floatingFilter: true, editable: true, },
      { field: "achievejupasT2Briefing", headerName: "Term 2 Briefing", filter: true, floatingFilter: true, editable: true, },
      { field: "showPredictedScoresToStudents", headerName: "Show Predicted Scores To Students?", filter: true, floatingFilter: true, editable: true, },
      { field: "remarks", headerName: "Remarks", filter: true, floatingFilter: true, editable: true, },
      { field: "principalRegistered", headerName: "Principal Registered?", filter: true, floatingFilter: true, },
      { field: "vicePrincipalRegistered", headerName: "Vice Principal Registered?", filter: true, floatingFilter: true, },
      { field: "careerTeacherRegistered", headerName: "Career Teacher Registered?", filter: true, floatingFilter: true, },
      { field: "numOfRegisteredTeachers", headerName: "Number of Registered Teachers", filter: true, floatingFilter: true, },
      { field: "name", headerName: "Full Name", filter: true, floatingFilter: true, },
      { field: "nameChi", headerName: "Chinese Name", filter: true, floatingFilter: true, },
      { field: "district", headerName: "District", filter: true, floatingFilter: true, },
      { field: "type", headerName: "Type", filter: true, floatingFilter: true, },
      { field: "language", headerName: "Language", filter: true, floatingFilter: true, },
      { field: "numF4Downloads", headerName: "Number of F4 Downloads", filter: 'agNumberColumnFilter', floatingFilter: true, },
      { field: "numF5Downloads", headerName: "Number of F5 Downloads", filter: 'agNumberColumnFilter', floatingFilter: true, },
      { field: "numF6Downloads", headerName: "Number of F6 Downloads", filter: 'agNumberColumnFilter', floatingFilter: true, },
    ]); // Column Definitions: Defines the columns to be displayed.

    const refreshTableHeaders = () => {
      if (filterAppliedSessionId.value == 'all') {
        // Default columns
        studentColDefs.value = defaultStudentColDefs;
      }
      else if (filterAppliedSessionId.value == 'joined-more-than-1') {
        // Show all sessions with badges
        studentColDefs.value = [
          { 
            headerName: 'Select', 
            checkboxSelection: true,
            headerCheckboxSelection: true,
            pinned: "left",
          },
          { field: "schoolId", headerName: "School", filter: true, floatingFilter: true, valueFormatter: (params: any) => params.value?.toUpperCase() },
          { field: "fullName", headerName: "Full Name", filter: true, floatingFilter: true },
          { headerName: "Form", valueGetter: (params: any) => getSecStudentForm(params.data.yearDSE), filter: true, floatingFilter: true },
          {
            headerName: "Sessions Joined",
            cellRenderer: (params: any) => {
              const student = params.data;
              if (student.filteredSessionResponses) {
                return student.filteredSessionResponses.map((sr: any) => 
                  `<ion-badge style="margin-right: 4px; background-color: var(--ion-color-primary); color: white; padding: 3px 8px; border-radius: 10px; font-size: 12px;">${filterSessions[sr.sessionId]}</ion-badge>`
                ).join(' ');
              }
              return '';
            },
            filter: true,
            floatingFilter: true,
          },
          { 
            headerName: 'Action', 
            cellRenderer: (params: any) => `<ion-button size="small" class="details-btn">Details</ion-button>`, 
            onCellClicked: (params: any) => openUserDetails(params.data),
            filter: false,
            sortable: false,
          }
        ];
      }
      else {
        // Show specific session status
        const newColDefs: any = [
          { 
            headerName: 'Select', 
            checkboxSelection: true,
            headerCheckboxSelection: true,
            pinned: "left",
          },
          { 
            headerName: 'Action', 
            cellRenderer: (params: any) => `<ion-button size="small" class="details-btn">Details</ion-button>`, 
            onCellClicked: (params: any) => openUserDetails(params.data),
            filter: false,
            sortable: false,
          },
          { field: "schoolId", headerName: "School", filter: true, floatingFilter: true, valueFormatter: (params: any) => params.value?.toUpperCase() },
          { field: "fullName", headerName: "Full Name", filter: true, floatingFilter: true },
          {
            field: "nominated", filter: true, sortable: true,
            headerValueGetter: (params: any) => {
              return `Nominated (${filteredStudents.value.filter(s => getStudentSessionResp(s, 'nominatedBy') != null).length})`;
            },
            valueGetter: (params: any) => getStudentSessionResp(params.data, 'nominatedBy') != null,
            cellRenderer: (params: any) => params.value ? '✓' : undefined,
          },
          {
            field: "interviewPassed", filter: true, sortable: true,
            headerValueGetter: (params: any) => {
              return `Interview Passed (${filteredStudents.value.filter(s => getStudentSessionResp(s, 'interviewStatus')?.includes('Accepted')).length})`;
            },
            valueGetter: (params: any) => getStudentSessionResp(params.data, 'interviewStatus')?.includes('Accepted'),
            cellRenderer: (params: any) => (params.value ? '✓' : undefined),
          },
          {
            field: "applied", filter: true, sortable: true,
            headerValueGetter: (params: any) => {
              return `Applied (${filteredStudents.value.filter(s => ['Attended', 'Yes', 'Confirmed'].includes(getStudentSessionResp(s))).length})`;
            },
            valueGetter: (params: any) => (['Yes'].includes(getStudentSessionResp(params.data))),
            cellRenderer: (params: any) => (params.value ? '✓' : undefined),
          },
          {
            field: "confirmed", filter: true, sortable: true,
            headerValueGetter: (params: any) => {
              return `Confirmed (${filteredStudents.value.filter(s => ['Confirmed'].includes(getStudentSessionResp(s))).length})`;
            },
            valueGetter: (params: any) => (['Confirmed'].includes(getStudentSessionResp(params.data))),
            cellRenderer: (params: any) => (params.value ? '✓' : undefined),
          },
          {
            field: "attended", filter: true, sortable: true,
            headerValueGetter: (params: any) => {
              return `Attended (${filteredStudents.value.filter(s => ['Attended'].includes(getStudentSessionResp(s))).length})`;
            },
            valueGetter: (params: any) => (['Attended'].includes(getStudentSessionResp(params.data))),
            cellRenderer: (params: any) => (params.value ? '✓' : undefined),
          },
          {
            field: "withdrawn", filter: true, sortable: true,
            headerValueGetter: (params: any) => {
              return `Withdrawn (${filteredStudents.value.filter(s => ['No'].includes(getStudentSessionResp(s))).length})`;
            },
            valueGetter: (params: any) => (['No'].includes(getStudentSessionResp(params.data))),
            cellRenderer: (params: any) => (params.value ? '✓' : undefined),
          },
        ];
        studentColDefs.value = newColDefs;
      }
    }

    // Program List
    const programColDefs = ref<any[]>([
      { field: "displayName", headerName: "Name", filter: true, floatingFilter: true, pinned: 'left', },
      //{ field: "programType", headerName: "Type", filter: true, floatingFilter: true, pinned: 'left', }, // currently all programs are JUPAS
      { field: "jupasUrl", headerName: "JUPAS URL", filter: true, floatingFilter: true, cellRenderer: (params: any) => `<a href="${params.value}" target="_blank">${params.value}</a>`, },
      { field: "institutionNameShort", headerName: "Institution", filter: true, floatingFilter: true, },
      { field: "numOfPlaces", headerName: "# of Places", filter: true, floatingFilter: true, },
      { field: "interviewArrangements", headerName: "Interview", filter: true, floatingFilter: true, cellRenderer: (params: any) => params.value ? 'Yes' : 'No', },
      { field: "disciplines", headerName: "Disciplines", filter: true, floatingFilter: true, cellRenderer: (params: any) => params.value.map((d: any) => d.name).join(', '), },

      // Number of students who chose the program (A1-B3)
      { field: 'numOfA1B3', headerName: "A1-B3", filter: true, floatingFilter: true },

      // Median (weighted score)
      { field: 'scoreMedian', headerName: "Median", filter: true, floatingFilter: true },

      // Predicted score (avg) - times weighting first?
      { field: 'avgStudentScore', headerName: "Student Score", filter: true, floatingFilter: true },
    ]);
    const filteredPrograms = computed(() => {
      let res = programs.value.slice();
      
      if (filters.disciplineIds.length > 0) {
        res = res.filter(p => p.disciplines.some(d => filters.disciplineIds.includes(d.id)));
      }
      if (filters.disciplineGroupIds.length > 0) {
        res = res.filter(p => p.disciplines.some(d => filters.disciplineGroupIds.includes(d.disciplineGroupId)));
      }
      if (filters.institutionIds.length > 0) {
        res = res.filter(p => filters.institutionIds.includes(p.institutionId));
      }

      res = res.map(p => {
        if (p.programType != 'jupas') return p;
        const programId = p.id;
        const relatedStudents = allStudents.value.slice().filter(s => {
          if (s.yearDSE < getF6YearDSE()) return false; // skip graduated students
          return s.userPrograms?.some(up => up.reaction == 'like' && up.programId == programId && up.order && up.order <= 6)
        });
        
        // Number of students who chose the program (A1-B3)
        p.numOfA1B3 = relatedStudents.length;
        
        // Median (weighted score)
        p.scoreMedian = p.admissions?.[0]?.scoreMedian;

        // Average of students predicted score who selected the program
        let sum = 0, validCount = 0;
        relatedStudents.forEach(s => {
          const userSubjects = getUserSubjects(allSubjects.value, s); 
          const { totalScores } = calculateSumWeightedScores(allSubjects.value, userSubjects, s.userSubjectGrades || [], p, 'schoolPredict');
          if (totalScores) {
            sum += totalScores;
            validCount++;
          }
        });
        p.avgStudentScore = (validCount > 0 ? sum / validCount : 0).toFixed(1);
    
        return p;
      });

      return res;
    });
    
    onMounted(() => {
      if (!fetchedAllStudents.value || !fetchedAllTeachers.value) {
        store.dispatch('fetchAllStudentsAndTeachers'); // get all students & teachers
      }
      if (!fetchedAllSchools.value) {
        store.dispatch('fetchAllSchools'); // get all schools
      }
      fetchJupasSets();
    })

    watch(selectedView, () => {
      numOfVisibleItems.value = 20; // reset
    })
    watch(filteredTeachers, () => {
      selectedTeachers.value = []; // unselect after filters changed
    })
    watch(filteredStudents, () => {
      selectedStudents.value = []; // unselect after filters changed
      refreshTableHeaders();
    })
    
    // Function to apply row styling based on selectedClientProgram
    const getRowStyle = (params: any) => {
      if (params.data && params.data.selectedClientProgram) {
        return { background: '#add8e6' };
      }
      return undefined;
    }
    // Watch for filter changes and update the grid
    watch(filterAppliedSessionId, (newVal) => {
      refreshTableHeaders();
    }, { immediate: true })

    return {
      // icons
      add, close, checkmark, arrowUp, arrowForward, arrowBack,
      trashOutline, sendOutline, helpCircleOutline, createOutline, openOutline,
      chevronForwardOutline, informationCircleOutline, personAddOutline, pencil,
      downloadOutline, star, starOutline, arrowDown, addCircleOutline, settingsOutline,
      filter, closeCircle, logoWhatsapp,

      // variables
      user, electives,
      searchKeyword,
      dseYears,
      visibleSchoolIds,
      visibleSchoolDistricts,
      optionRankInForms: config.formOptions.rankInForm,
      
      // Mega student / teacher list
      selectedView,
      fetchedAllStudents, filteredStudents, // students
      fetchedAllTeachers, filteredTeachers, // teachers
      fetchedAllSchools, filteredSchools, // schools
      studentColDefs, teacherColDefs, schoolColDefs,

      // Programs
      filteredPrograms, programColDefs,
      disciplineGroups, disciplines,
      institutions,
      
      // Update selectedTeachers when selection changes
      onSelectionChanged: (event: any) => {
        const selectedRows = event.api.getSelectedRows();
        if (selectedView.value === 'teacher') {
          selectedTeachers.value = selectedRows.slice();
        } else if (selectedView.value === 'secondary-school-student') {
          selectedStudents.value = selectedRows.slice();
        }
      },
      
      agGridModules: [AllCommunityModule, RowSelectionModule, ClientSideRowModelModule],
      getRowStyle,
      onCellValueChanged: (event) => {
        console.log(event);
        const targetField = event.column.colDef.field;
        const schoolId = event.data.id, newValue = event.newValue;
        SchoolService.updateSchool(schoolId, { [targetField]: newValue }); // update DB        
      },

      // Teachers
      visibleSchoolRoles: computed(() => {
        const allRoles: string[] = [];
        for (const s of allTeachers.value) {
          const roles: any = s.teacher?.schoolRoles?.split(" , ").filter(r => r).map(r => r.toLowerCase());
          if (roles) allRoles.push(...roles);
        }
        return [...new Set(allRoles)].filter(s => s).sort();
      }),
      
      // Send Messages
      recipientStudents, recipientTeachers,
      recipientCountDescription: computed(() => {
        if (selectedView.value == 'secondary-school-student') {
          const targetStudents = selectedStudents.value.length > 0 ? selectedStudents.value : filteredStudents.value;
          const schoolIds = [...new Set(targetStudents.map(s => s?.schoolId))].filter(s => s);
          return `${targetStudents.length} student${targetStudents.length > 1 ? 's' : ''} from ${schoolIds.length} school${schoolIds.length > 1 ? 's' : ''}`;
        }
        else if (selectedView.value == 'teacher') {
          const targetTeachers = selectedTeachers.value.length > 0 ? selectedTeachers.value : filteredTeachers.value;
          const schoolIds = [...new Set(targetTeachers.map(t => t?.schoolId))].filter(s => s);
          return `${targetTeachers.length} teacher${targetTeachers.length > 1 ? 's' : ''} from ${schoolIds.length} school${schoolIds.length > 1 ? 's' : ''}`;
        }
        else if (selectedView.value == 'school') {
          return `${filteredSchools.value.length} school${filteredSchools.value.length > 1 ? 's' : ''}`;
        }
        else if (selectedView.value == 'program') {
          const institutionIds = [...new Set(filteredPrograms.value.map(p => p?.institutionId))].filter(s => s);
          return `${filteredPrograms.value.length} program${filteredPrograms.value.length > 1 ? 's' : ''} from ${institutionIds.length} institution${institutionIds.length > 1 ? 's' : ''}`;
        }
        return '';
      }),
      msgOptions, msgVariables,
      onImageFileChange: async (e: any) => {
        if (e.target.files && e.target.files[0]) {
          msgOptions.photoFile = e.target.files[0];
          const reader = new FileReader();
          reader.onload = (e: any) => {
            msgOptions.photo = e.target.result;
          }
          reader.readAsDataURL(msgOptions.photoFile as any); // convert to base64 string and preview it
          e.srcElement.value = null;
        }
      },
      clearUploadedImage: () => {
        msgOptions.photo = "";
        msgOptions.photoFile = null;
      },
      sendMessage: () => {
        const target = selectedView.value == 'secondary-school-student' ? 'student' : 'teacher';
        presentPrompt(`Confirm sending the message to the ${target}s?`, async () => {
          const users = (target == 'student' ? recipientStudents.value : recipientTeachers.value).map(s => {
            let msg = msgOptions.content; // find variables & prefill msgs for each student
            const findReplacePairs = {
              "{{name}}": s.fullName,
              "{{phone}}": s.phone,
            }
            for (const find in findReplacePairs) {
              const findRegExp = new RegExp(find, "gi");
              msg = msg.replace(findRegExp, findReplacePairs[find]);
            }
            return { waGroupId: s.waGroupId, phone: s.phone, msg: msg, }
          });
          InternalService.sendWhatsAppMsgToUsers(users, msgOptions.content, msgOptions.photoFile);
          presentToast('The TBC messages will be generated in 17/F WhatsApp Terminal -> Outbox shortly.');
        });
      },
      sendMsgsToRelatedClassTeachers: () => {
        presentPrompt(`Confirm sending the message to the related teachers?`, async () => {
          // Students who haven't joined any workshops
          const relatedStudents = recipientStudents.value.filter(s => !s.sessionResponses?.some(sr => sr.lessonId == 'work-workshop-2' && ['Yes', 'Confirmed', 'Attended'].includes(sr.response)));

          // Group students by schools first
          const groupedSchools = {};
          for (const s of relatedStudents) {
            const targetSchool = s?.schoolId || "Other";
            groupedSchools[targetSchool] = groupedSchools[targetSchool] || [];
            groupedSchools[targetSchool].push(s);
          }

          const teachers: any = [];
          for (const schoolId in groupedSchools) {
            const filteredStudents = relatedStudents.filter(s => s.schoolId == schoolId);

            // Group students by class
            const groupedClasses = {};
            for (const s of filteredStudents) {
              const targetClass = s?.class || "Other";
              groupedClasses[targetClass] = groupedClasses[targetClass] || [];
              groupedClasses[targetClass].push(s);
            }

            // Loop each class, Send to teachers who teach the class, if no class teachers then fallback
            for (const cl in groupedClasses) {
              const relatedStudents = groupedClasses[cl];
              const relatedTeachers = allTeachers.value.filter(t => t.schoolId == schoolId && t.teacher?.classRoles?.some(cr => ["Class teacher", "Ethics and Religious Studies teacher"].includes(cr.role) && cr.classes.includes(cl) && cr.status == "active"));
              for (const teacher of relatedTeachers) {
                let msg = msgOptions.content; // find variables & prefill msgs for each student
                const findReplacePairs = {
                  "{{phone}}": teacher.phone,
                  "{{name}}": teacher.fullName,
                  "{{student_list}}": relatedStudents.map(s => `${s.class}${formatStudentNumber(s.studentNumber)} ${s.fullName}`).join("\n"),
                }
                for (const find in findReplacePairs) {
                  const findRegExp = new RegExp(find, "gi");
                  msg = msg.replace(findRegExp, findReplacePairs[find]);
                }
                teachers.push({ waGroupId: teacher.waGroupId, phone: teacher.phone, msg, });
              }
            }
          }

          InternalService.sendWhatsAppMsgToUsers(teachers, msgOptions.content, msgOptions.photoFile);
          presentToast('The TBC messages will be generated in 17/F WhatsApp Terminal -> Outbox shortly.');
        });
      },
      insertVariableAtCursor: (key) => { // Function to insert variable at cursor position
        const textarea: any = document.querySelector('#messageTextarea textarea');
        if (textarea) {
          const startPos = textarea.selectionStart;
          const endPos = textarea.selectionEnd;
          const currentContent = msgOptions.content || '';

          // Insert the key at the cursor position
          msgOptions.content =
            currentContent.substring(0, startPos) +
            ` ${key} ` +
            currentContent.substring(endPos);

          // Move the cursor after the inserted variable
          const newCursorPos = startPos + key.length + 1; // +1 for the space
          nextTick(() => {
            textarea.focus();
            textarea.setSelectionRange(newCursorPos, newCursorPos);
          });
        }
      },

      // methods
      t, getQRCodeUrl,
      getStudentProgramCount,
      openUserDetails,
      getSecStudentForm,

      // --- JUPAS chip UI logic ---
      selectedJupasSetId,
      selectJupasSet,
      isJupasDirty,
      saveNewJupasSet,
      confirmAddSetPrompt,
      addSetPromptOpen,
      addSetPromptName,
      renameSetModalOpen,
      renameSetInitialName,
      startRenameSet,
      confirmRenameSet,
      localJupasConditions,
      jupasSets,

      // JUPAS Choice Conditions (filtering)
      getBandLabel, addChoiceFilter,
      openJUPASChoiceConditionModal: () => {
        if (localJupasConditions.value.length == 0) addChoiceFilter();
        modalState.jupasChoiceConditions = true;
      },
      closeJUPASChoiceConditionModal: () => {
        localJupasConditions.value = getValidJUPASChoiceConditions();
        // Only activate the filter if there are valid conditions
        filters.jupasChoiceActive = localJupasConditions.value.length > 0;
        modalState.jupasChoiceConditions = false;
      },
      openEntitySelectModal: async (targetCondition: JUPASChoiceCondition) => {
        // Check open Program / Discipline search select
        if (targetCondition.targetEntityType == 'Program') {
          const modal = await modalController.create({
            component: ABProgramSelectModal,
            componentProps: { prefilledPrograms: targetCondition.targetPrograms.slice() },
          });
          modal.onDidDismiss().then(({ data }) => {
            if (data && data.chosen) {
              targetCondition.targetPrograms = data.chosen;
            }
          });
          return modal.present();
        }
        if (targetCondition.targetEntityType == 'Discipline') {
          const modal = await modalController.create({
            component: ABDisciplineSelectModal,
            componentProps: { isListSelect: true, prefilledDisciplines: targetCondition.targetDisciplines.slice() },
          });
          modal.onDidDismiss().then(({ data }) => {
            if (data && data.chosen) {
              targetCondition.targetDisciplines = data.chosen;
            }
          });
          return modal.present();
        }
      },
      getTargetEntityNames: (targetCondition: JUPASChoiceCondition) => {
        switch (targetCondition.targetEntityType) {
          case 'Program':
            return targetCondition.targetPrograms.map(p => p.displayName).join('\n');
          case 'Discipline':
            return targetCondition.targetDisciplines.map(d => d.name).join('\n');
          case 'Discipline Group':
            return targetCondition.targetDisciplineGroups.map(dg => dg.name).join('\n');
        }
        return '';
      },
      getNumOfStudents: (bandIdx: number, targetCondition: JUPASChoiceCondition) => {
        return filteredStudents.value.filter(s => {
          const checkChoices = getCheckChoicesByJUPASCondition(s, targetCondition, bandIdx+1);
          return checkChoices.length > 0;
        }).length;
      },

      // Modals
      modalState,

      // popover
      popoverState, popoverEvent, setPopoverOpen,

      // Filters
      filterSessions, // Mainly workshop session (IAWork, BBAWork, ...)
      filters, // selected filters
      filterAppliedSessionId,
      updateFilters: (key, val) => {
        if (key == 'jupasChoiceActive') {
          selectJupasSet('new'); // reset
        }
        filters[key] = val;
      },
      selectedFilterCheckboxVals,
      openSchoolSelectModal: async () => {
        if (!modalState.schoolSelect) {
          modalState.schoolSelect = true; // prevent duplicate open
          const modal = await modalController.create({
            component: SchoolSelectModal,
            componentProps: {
              // For student list only show schools with registered students
              schools: (selectedView.value == 'secondary-school-student' ? schoolsWithStudents.value : allSchools.value),
              isMultiSelect: true,
              prefilledSchoolIds: filters.schoolIds,
            }
          });
          modal.onDidDismiss().then(({ data }) => {
            if (data && data.selectedSchoolIds) {
              console.log(data.selectedSchoolIds);
              filters.schoolIds = data.selectedSchoolIds;
            }
            modalState.schoolSelect = false;
          });
          return modal.present();
        }
      },

      // --- Generic Select All logic for ion-select multi ---
      // --- Robust Select All logic for ion-select multi ---
      isAllSelected: (condition) => {
        const allOptions = Array.from({ length: 20 }, (_, i) => i + 1);
        return allOptions.every(val => (condition.orders || []).includes(val));
      },
      ordersProxy: (condition) => {
        const allOptions = Array.from({ length: 20 }, (_, i) => i + 1);
        const arr = condition.orders || [];
        if (allOptions.every(val => arr.includes(val))) {
          return ['__select_all__', ...allOptions];
        }
        return arr;
      },
      onOrdersChange: (event, condition) => {
        const allOptions = Array.from({ length: 20 }, (_, i) => i + 1);
        const valArr = event.detail.value || [];
        const key = condition.id; // could use condition.id if available
        const prev = prevSelectedValues.value[key] || [];
        const SELECT_ALL = '__select_all__';

        const prevHasAll = prev.includes(SELECT_ALL);
        const currHasAll = valArr.includes(SELECT_ALL);
        const prevReal = prev.filter(v => v !== SELECT_ALL);
        const currReal = valArr.filter(v => v !== SELECT_ALL);
        const allSelected = allOptions.every(val => currReal.includes(val));

        // Case 1: User just checked Select All
        if (!prevHasAll && currHasAll) condition.orders = [...allOptions];

        // Case 2: User just unchecked Select All (should clear all)
        else if (prevHasAll && !currHasAll) condition.orders = [];

        // Case 3: User unchecked an individual option while Select All was checked (partial selection)
        else if (prevHasAll && currHasAll && !allSelected) condition.orders = currReal;

        // Case 4: User checked all items manually (should auto-check Select All visually)
        else if (!currHasAll && allSelected) condition.orders = [...allOptions];

        // Default: normal selection/deselection
        else condition.orders = currReal;

        // Save current as prev for next event
        prevSelectedValues.value[key] = [...valArr];
      },
    }
  },
});
</script>

<style scoped>
  .filter-items ion-icon {
    margin-top: 8px;
    margin-bottom: 8px;
  }

  ion-content {
    flex: 1;
    height: calc(100vh - var(--ion-safe-area-top, 0px) - var(--ion-safe-area-bottom, 0px));
    --overflow: scroll;
  }
  ion-header {
    flex-shrink: 0;
  }

  ion-checkbox {
    margin-top: 9px;
    margin-bottom: 9px;
    margin-inline-end: 16px;
  }
  ion-toolbar {
    --min-height: 40px;
  }
  ion-title {
    font-weight: normal;
    padding-right: 8px;
    padding-top: 5px;
    padding-bottom: 5px;
  }
  ion-title p {
    font-size: 14px;
  }
  ion-segment ion-segment-button {
    height: 20px;
    min-height: 20px;
  }
  ion-item ion-label {
    margin: 0;
  }

  ion-item {
    --min-height: 28px;
  }

  /**
   * Dual knob range slider (e.g. best 5 scores)
   */
  ion-range {
    padding-top: 0 !important;
  }
  ion-range::part(tick) {
    background: var(--ion-color-medium-tint);
  }
  ion-range::part(tick-active) {
    background: var(--ion-color-primary-tint);
  }
  ion-range::part(pin) {
    display: inline-flex;
    align-items: center;
    justify-content: center;

    background: var(--ion-color-primary);
    color: white;

    border-radius: 50%;
    transform: scale(1.01);

    top: 10px;
    z-index: 9999;

    min-width: 28px;
    height: 28px;
    transition: transform 120ms ease, background 120ms ease;
  }
  ion-range::part(pin)::before {
    content: none;
  }
  ion-range::part(knob) {
    background: var(--ion-color-primary);
  }
  ion-range::part(bar) {
    background: var(--ion-color-medium-tint);
  }
  ion-range::part(bar-active) {
    background: var(--ion-color-primary);
  }

  /**
   * Score filter with label
   */
  .score-filter {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 0 16px;
  }
  .score-label {
    color: var(--ion-color-medium);
    font-size: 14px;
    white-space: nowrap;
    min-width: 50px;
  }
  .score-filter ion-range {
    flex: 1;
  }

  ion-item ion-buttons {
    min-width: 28px !important;
    height: 28px !important;
    width: 36px;
  }

  /* Make AG Grid checkboxes more visible and clickable */
  .ag-theme-alpine .ag-checkbox-input {
    width: 18px !important;
    height: 18px !important;
    cursor: pointer !important;
  }
  
  /* Ensure checkbox column has pointer cursor */
  .ag-theme-alpine .ag-checkbox-column {
    cursor: pointer;
  }
</style>