<template>
  <ion-card class="class-bar" style="margin-bottom: 0" :disabled="isDisabled">
    <ion-card-content style="padding: 0">
      <ion-item color="primary" lines="full">
        <ion-label style="margin: 4px 0">
          <h2 style="font-weight: 500">{{ title }}</h2>
        </ion-label>
      </ion-item>
    </ion-card-content>
  </ion-card>
</template>

<script lang="ts">
// vue
import { computed } from 'vue';

// icon
import { checkmarkCircle, alertCircle, } from 'ionicons/icons';

// components
import { IonCard, IonCardContent, IonItem, IonIcon, IonLabel, IonNote, } from '@ionic/vue';

export default {
  props: [
    "title",
    "isDisabled",
  ],
  components: {
    IonCard, IonCardContent, IonItem, IonIcon, IonLabel, IonNote,
  },
  setup() {
    return { checkmarkCircle, alertCircle, }
  }
}
</script>

<style scoped>
  ion-item {
    --min-height: 16px;
  }
  ion-item::part(native) {
    padding-inline-start: 8px;
  }
</style>