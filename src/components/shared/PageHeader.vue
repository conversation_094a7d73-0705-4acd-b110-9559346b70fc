<template>
  <!--
    Header for home page (visitor) - fdmt.hk
    -->
  <ion-header v-if="selectedView == 'home' || !user.id" style="background: rgb(0, 80, 125); box-shadow: none">
    <ion-grid class="ion-no-padding" fixed>
      <ion-toolbar style="--background: transparent; --min-height: 122px">
        <!-- FDMT Logo -->
        <ion-title slot="start" style="flex: initial">
          <logo-img :useWhiteLogo="true" class="big-logo"></logo-img>
        </ion-title>

        <!-- Action / Social Buttons -->
        <ion-buttons slot="end">
          <!--<ion-button size="small" fill="solid" color="primary" router-link="/login" v-if="!user.id">
            Login
          </ion-button>-->

          <!-- Instagram -->
          <ion-button fill="solid" shape="round" color="primary" target="_blank" href="https://www.instagram.com/jupas.scope">
            <ion-icon slot="icon-only" :icon="logoInstagram"></ion-icon>
          </ion-button>

          <!-- Linkedin -->
          <ion-button fill="solid" shape="round" color="primary" target="_blank" href="https://www.linkedin.com/company/fdmt">
            <ion-icon slot="icon-only" :icon="logoLinkedin"></ion-icon>
          </ion-button>

          <!-- YouTube -->
          <ion-button fill="solid" shape="round" color="primary" target="_blank" href="https://www.youtube.com/user/FDMTvideo">
            <ion-icon slot="icon-only" :icon="logoYoutube"></ion-icon>
          </ion-button>

          <ion-button fill="clear" size="small" class="no-text-transform ion-no-margin" href="tel:+85224961533"
                      style="height: 48px; color: #fff">
            <ion-icon slot="start" :icon="callOutline"></ion-icon>
            +852 2496 1533<br />
            M-F 9:00-18:00<br />
          </ion-button>
        </ion-buttons>
      </ion-toolbar>

      <slot />
    </ion-grid>
  </ion-header>

  <!--
    Header for university staff
    -->
  <ion-header v-else-if="user.isUniversityClient">
    <ion-grid class="ion-no-padding" fixed :style="{ 'background': !userRelatedClient?.bannerLink ? '#004976' : `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
                                                                    url('${getProxyImgLink(userRelatedClient?.bannerLink)}') center / cover no-repeat` }">
      <!-- AchieveBot Title -->
      <ion-toolbar style="padding: 0 8px; --background: transparent; --min-height: 90px">
        <ion-title class="ion-text-center" style="padding: 0; color: #fff">
          <ion-label class="ion-text-wrap">
            <!--<h2 class="main-title">AchieveBot <small>@ {{ userRelatedClient.fullName }}</small></h2>-->
            <!--<h2 class="main-title"><small>AchieveBot @</small> {{ userRelatedClient.fullName }}</h2>-->
            <h2 class="main-title">{{ userRelatedClient.department }}</h2>
            <span class="sub-title"><small>powered by</small> AchieveBot</span>
          </ion-label>
        </ion-title>
      </ion-toolbar>

      <ion-toolbar style="padding: 0 8px 4px; --background: transparent; --min-height: 28px">
        <!-- Back button -->
        <ion-buttons style="height: 24px" slot="start" v-if="showBackButton">
          <ion-back-button style="--color: #fff" default-href="/home"></ion-back-button>
        </ion-buttons>

        <!-- FDMT Logo -->
        <ion-title slot="start" class="ion-no-padding" style="flex: initial">
          <logo-img :useWhiteLogo="true"></logo-img>
        </ion-title>

        <!-- jobEX Programme Logo -->
        <ion-title slot="start" class="ion-no-padding" style="flex: initial" v-if="userRelatedClient?.logoLink">
          <img style="height: 30px; vertical-align: bottom; margin-left: 5px" :src="userRelatedClient?.logoLink" />
        </ion-title>

        <div slot="end" class="ion-text-center">
          <!-- User Name -->
          <ion-chip router-link="/profile" v-if="!hideUserProfileToolbar">
            <ion-avatar style="margin-right: 4px; height: 16px; width: 16px">
              <img :src="getAppSheetFileLink('user', user.profilePic || 'https://ionicframework.com/docs/img/demos/avatar.svg')" />
            </ion-avatar>
            <ion-label>
              <span class="header-title">{{ user.chineseName || user.preferredName || user.fullName || 'Profile' }}</span>
            </ion-label>
          </ion-chip>

          <!-- WhatsApp Group Link (TBC: show client WhatsApp group?)
          <ion-button fill="clear" size="small" color="success" target="_blank" :href="user.waGroupLink" @click="onClickWhatsAppButton()"
                      style="vertical-align: middle; margin: 0; font-size: 11px" v-if="user.waGroupLink">
            <ion-icon slot="icon-only" :icon="logoWhatsapp"></ion-icon>
          </ion-button>-->
        </div>
      </ion-toolbar>
      <slot />
    </ion-grid>
  </ion-header>

  <!--
    Header for university students
    -->
  <ion-header v-else-if="user.isUniversityStudent">
    <ion-grid class="ion-no-padding" fixed :style="{ 'background': !userRelatedJobEX?.bannerLink ? '#004976' : `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
                                                                    url('${getProxyImgLink(userRelatedJobEX?.bannerLink)}') center / cover no-repeat` }">
      <!-- AchieveBot Title -->
      <ion-toolbar style="padding: 0 8px; --background: transparent; --min-height: 90px">
        <ion-title class="ion-text-center" style="padding: 0; color: #fff">
          <ion-label class="ion-text-wrap">
            <!--<h2 class="main-title">AchieveBot <small>@ {{ userRelatedJobEX && userRelatedJobEX['Department'] }}</small></h2>-->
            <h2 class="main-title">{{ userRelatedJobEX ? userRelatedJobEX['Department'] : 'jobEX' }}</h2>
            <span class="sub-title"><small>powered by</small> AchieveBot</span>
          </ion-label>
        </ion-title>
      </ion-toolbar>

      <ion-toolbar style="padding: 0 8px 4px; --background: transparent; --min-height: 28px">
        <!-- Back button -->
        <ion-buttons style="height: 24px" slot="start" v-if="showBackButton">
          <ion-back-button style="--color: #fff" default-href="/home"></ion-back-button>
        </ion-buttons>

        <!-- FDMT Logo -->
        <ion-title slot="start" class="ion-no-padding" style="flex: initial">
          <logo-img :useWhiteLogo="true"></logo-img>
        </ion-title>

        <!-- jobEX Programme Logo -->
        <ion-title slot="start" class="ion-no-padding" style="flex: initial" v-if="userRelatedJobEX && userRelatedJobEX.logoLink">
          <img style="height: 30px; vertical-align: bottom; margin-left: 5px" :src="userRelatedJobEX.darkModeLogoLink || userRelatedJobEX.logoLink" />
        </ion-title>

        <div slot="end" class="ion-text-center">
          <!-- Student Name & Class -->
          <ion-chip router-link="/profile" v-if="!hideUserProfileToolbar">
            <ion-avatar style="margin-right: 4px; height: 16px; width: 16px">
              <img :src="getAppSheetFileLink('user', user.profilePic || 'https://ionicframework.com/docs/img/demos/avatar.svg')" />
            </ion-avatar>
            <ion-label>
              <span class="header-title">{{ user.chineseName || user.preferredName || user.fullName || 'Profile' }}</span>
            </ion-label>
          </ion-chip>

          <!-- WhatsApp Group Link -->
          <ion-button fill="clear" size="small" color="success" target="_blank" :href="user.waGroupLink" @click="onClickWhatsAppButton()"
                      style="vertical-align: middle; margin: 0; font-size: 11px" v-if="user.waGroupLink">
            <ion-icon slot="icon-only" :icon="logoWhatsapp"></ion-icon>
          </ion-button>
        </div>
      </ion-toolbar>
      <slot />
    </ion-grid>
  </ion-header>

  <!--
    Header for secondary school students & teachers
    -->
  <ion-header v-else>
    <ion-grid class="ion-no-padding" fixed :style="{ 'background': `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
                                                                    url('${getProxyImgLink(userRelatedSchool?.bannerLink)}') center / cover no-repeat` }">
      <!-- AchieveBot Title -->
      <ion-toolbar style="padding: 0 8px; --background: transparent" :style="{ '--min-height': isAB3ParentView ? '60px' : '90px' }">
        <ion-title class="ion-text-center" style="padding: 0; color: #fff">
          <ion-label class="ion-text-wrap" v-if="user.isSecondaryStudent">
            <!--<span class="sub-title" style="line-height: 1.5">JUPAS resources <small>for</small></span><br />
            <span class="sub-title"><b>{{ userRelatedSchool?.nameShort }}</b> students <small>of</small> various DSE subjects</span><br />-->
            <!--<span class="sub-title"><b>{{ userRelatedSchool?.nameShort }}</b> students <small>of</small> various DSE subjects</span><br />-->
            <span class="sub-title"><b>{{ userRelatedSchool?.nameShort }}</b></span><br />
            <span class="sub-title"><small>powered by</small> AchieveBot</span>
          </ion-label>

          <ion-label class="ion-text-wrap" v-else>
            <!--<h2 class="main-title">AchieveBot <small>@ {{ userRelatedSchool?.nameShort }}</small></h2>-->
            <!--<span class="sub-title" style="line-height: 1.5">JUPAS resources <small>for</small></span><br />-->
            <span class="sub-title"><b>{{ userRelatedSchool?.nameShort }}</b></span><br />
            <!--<span class="sub-title"><b>{{ userRelatedSchool?.nameShort }}</b> teachers <small>of</small> various DSE subjects</span><br />-->
            <!--<h2 class="main-title">
              {{ userRelatedSchool?.nameShort }} <span class="sub-title">teachers of every subject</span>
            </h2>-->
            <span class="sub-title"><small>powered by</small> AchieveBot</span>
          </ion-label>
        </ion-title>
      </ion-toolbar>

      <ion-toolbar style="padding: 0 8px 4px; --background: transparent; --min-height: 28px">
        <!-- Back button -->
        <ion-buttons style="height: 24px" slot="start" v-if="showBackButton">
          <ion-back-button style="--color: #fff" default-href="/home"></ion-back-button>
        </ion-buttons>

        <!-- FDMT Logo -->
        <ion-title slot="start" class="ion-no-padding" style="flex: initial">
          <logo-img :useWhiteLogo="true"></logo-img>
        </ion-title>

        <!-- School Logo (hide because of non-transparent background - not looking good)
        <ion-title slot="end" class="ion-no-padding" style="flex: initial" :style="{ 'visibility': userRelatedSchool && userRelatedSchool.logoLink }">
          <img style="height: 30px; vertical-align: bottom; margin-left: 5px" :src="userRelatedSchool?.logoLink" />
        </ion-title>-->

        <div slot="end" class="ion-text-center">
          <div v-if="isAB3ParentView">
            <!-- AB3 Parent's view (page header info) -->
            <ion-chip>
              <ion-label>
                <span class="header-title" v-if="user.class">{{ user.class }}{{ formatStudentNumber(user.studentNumber) }} {{ user.chineseName || user.fullName }}</span>
                <span class="header-title" v-else>{{ user.chineseName || user.preferredName || user.fullName || 'Student' }}</span>
              </ion-label>
            </ion-chip>
            
            <ion-button class="ion-no-margin" color="light" size="small" fill="clear" @click="openImageModal(getProxyImgLink(`https://docs.google.com/presentation/d/1v8O0j-EYeGItQuS8N-lEj9qJQsV6oXOYtCG_y3x4F-U/export/jpeg?pageid=g2fa5c90539b_0_42`))">
              <ion-icon slot="icon-only" :icon="helpCircleOutline"></ion-icon>
            </ion-button>
          </div>

          <div v-else>
            <!-- Student Name & Class -->
            <ion-chip router-link="/profile" v-if="!hideUserProfileToolbar">
              <!--<ion-avatar style="margin-right: 4px; height: 16px; width: 16px">
                <img :src="getAppSheetFileLink('user', user.profilePic || 'https://ionicframework.com/docs/img/demos/avatar.svg')" />
              </ion-avatar>-->
              <ion-label>
                <span class="header-title" v-if="user.isSecondaryStudent && user.class">{{ user.class }}{{ formatStudentNumber(user.studentNumber) }}</span>
                <span class="header-title" v-else>{{ user.chineseName || user.preferredName || user.fullName || 'Profile' }}</span>
              </ion-label>
              <!--<ion-icon color="warning" :icon="star" v-if="user.secondarySchoolStudent && user.secondarySchoolStudent.group == 'ucircle'"></ion-icon>-->
            </ion-chip>

            <!-- WhatsApp Group Link -->
            <ion-button fill="clear" size="small" color="success" target="_blank" :href="user.waGroupLink" @click="onClickWhatsAppButton()"
                        style="vertical-align: middle; margin: 0; font-size: 11px" v-if="user.waGroupLink">
              <ion-icon slot="icon-only" :icon="logoWhatsapp"></ion-icon>
            </ion-button>
          </div>
        </div>
      </ion-toolbar>

      <slot />

    </ion-grid>
  </ion-header>
</template>

<script lang="ts">
import { computed } from 'vue';

// icons
import { logoWhatsapp, gridOutline, menu, star, helpCircleOutline, logoInstagram, logoLinkedin, logoYoutube, callOutline, } from 'ionicons/icons';

// components
import { IonHeader, IonGrid, IonToolbar, IonTitle, IonChip, IonAvatar, IonLabel, IonButtons, IonButton, IonIcon,
          IonBackButton, IonFab, IonFabButton, } from '@ionic/vue';

// composables
import { utils } from '@/composables/utils';

// store
import { useStore } from '@/store';

export default {
  props: ["showBackButton", "hideSchoolLogo", "hideUserProfileToolbar", "isAB3ParentView", "selectedView"],
  components: { IonHeader, IonGrid, IonToolbar, IonTitle, IonChip, IonAvatar, IonLabel, IonButtons, IonButton, IonIcon,
                IonBackButton, IonFab, IonFabButton, },
  setup() {
    const { openModal, formatStudentNumber, getProxyImgLink, getAppSheetFileLink, openImageModal, } = utils();

    const store = useStore();
    const user = computed(() => store.state.user);
    const userRelatedSchool = computed(() => store.getters.userRelatedSchool);
    const userRelatedJobEX = computed(() => store.getters.userRelatedJobEX);
    const userRelatedClient = computed(() => store.getters.userRelatedClient);
    const currDark = computed(() => store.state.user.darkTheme);

    return {
      // icon
      logoWhatsapp, gridOutline, menu, star, helpCircleOutline, logoInstagram, logoLinkedin, logoYoutube, callOutline,
      
      // variables
      user,
      userRelatedSchool,
      userRelatedJobEX,
      userRelatedClient,
      currDark,

      // methods
      openImageModal,
      getAppSheetFileLink,
      getProxyImgLink,
      formatStudentNumber,
      onClickWhatsAppButton: () => {
        store.dispatch('setUserJoinedWAGroup');
      }
    }
  } 
}
</script>

<style scoped>
  ion-chip {
    height: 24px;
    margin-right: 0;
    --color: #fff;
    --background: #000;
    margin: 0;
  }
  .header-title {
    line-height: 10px;
    font-size: 11px;
  }
  ion-button::part(native) {
    padding: 4px;
  }
  .big-logo {
    width: 100px;
  }
  .main-title {
    font-weight: bold;
    font-size: 36px;
  }
  .sub-title {
    font-size: 18px;
  }
  .sub-title small {
    font-size: 55%;
    color: #C3C3C3;
  }
  @media only screen and (min-width: 768px) {
    .main-title {
      font-size: 60px;
    }
    .sub-title {
      font-size: 22px;
    }
    .big-logo {
      width: 180px;
    }
  }
</style>