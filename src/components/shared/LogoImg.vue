<template>
  <!--<img width="40" :src="schoolLogo" v-if="school" />-->
  <img width="45" :src="logoWhite" v-if="!withText && useWhiteLogo == true" />
  <img class="logo-text-img" :src="logoWithTextWhite" v-else-if="(useWhiteLogo || currDark == true) && withText" />
  <img class="logo-text-img" :src="logoWithText" v-else-if="currDark != true && withText" />
  <img class="logo-img" :src="logoWhite" v-else-if="currDark == true && !withText" />
  <img class="logo-img" :src="logo" v-else-if="currDark != true && !withText" />
</template>

<script lang="ts">
import { computed } from 'vue';
import config from '@/config';
import { useStore } from '@/store';

export default {
  props: [
    "withText",
    "useWhiteLogo",
  ],
  setup() {
    const store = useStore();
    const currDark = computed(() => store.state.user.darkTheme);

    const { logo, logoWithText, logoWithTextWhite, logoWhite } = config;
    return { logo, logoWithText, logoWithTextWhite, logoWhite, currDark }
  }
}
</script>

<style scoped>
  .logo-img {
    height: 25px;
  }
  .logo-text-img {
    width: 180px;
    padding: 0 5px;
  }
</style>