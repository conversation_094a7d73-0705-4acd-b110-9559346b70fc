<template>
  <div v-if="expandAll">
    <ion-item lines="none" :color="headerColor || 'fdmtred'" v-if="!noHeader">
      <ion-label :class="{'ion-padding-start': !noPadding}">
        <slot name="header"></slot>
      </ion-label>
    </ion-item>
    <div :class="{'ion-padding-start': !noPadding}">
      <slot></slot>
    </div>
  </div>
  <ion-accordion-group :value="value" v-else>
    <ion-accordion style="--min-height: 24px" :value="value" :toggle-icon="noAccordion ? null : chevronDown" :readonly="!!noAccordion">
      <ion-item lines="none" slot="header" :color="headerColor || 'fdmtred'"><!--color="fdmtredl"-->
        <ion-label :class="{'ion-padding-start': !noPadding}">
          <slot name="header"></slot>
        </ion-label>
      </ion-item>
      <div slot="content">
        <slot></slot>
      </div>
    </ion-accordion>
  </ion-accordion-group>
</template>

<script setup lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, watch, reactive, defineProps, } from 'vue';

// icons
import { add, close, arrowBack, checkmarkCircle, alertCircle, ellipsisHorizontal, ellipsisVertical, pencil, chevronDown, } from 'ionicons/icons';

// components
import { IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonAccordion, IonAccordionGroup, IonList, IonCard, } from '@ionic/vue';

// composables / services
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';

const props = defineProps({
  value: String,
  noPadding: Boolean,
  noAccordion: Boolean,
  headerColor: String,
  noHeader: Boolean,
  expandAll: Boolean,
});

const { t } = useI18n();
const { openModal, } = utils();
</script>

<style scoped>
  ion-item {
    --min-height: 32px;
  }
  ion-item::part(native) {
    padding-inline-start: 0 !important;
  }
</style>
