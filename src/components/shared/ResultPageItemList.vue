<template>
  <ion-list style="border-bottom: 1px solid var(--ion-color-dark)" :style="{ 'padding': `0 0 0 ${noPadding ? '0' : '40px'}` }">
    <!--<ion-item lines="full" button @click="onClickListHeader()">
      <ion-label class="ion-text-wrap"><p>{{ headerTitle }}</p></ion-label>
      <ion-buttons>
        <ion-button><ion-icon size="small" slot="icon-only" :icon="add"></ion-icon></ion-button>
        <ion-button><ion-icon size="small" slot="icon-only" :icon="undefined"></ion-icon></ion-button>
      </ion-buttons>
    </ion-item>-->

    <slot />
  </ion-list>
</template>

<script lang="ts">
// icon
import { checkmarkCircle, alertCircle, pencil, add, } from 'ionicons/icons';

// components
import { IonCard, IonCardContent, IonItem, IonIcon, IonLabel, IonNote,
        IonList, IonButtons, IonButton, } from '@ionic/vue';

export default {
  props: [
    "headerTitle",
    "noPadding",
    "icon",
  ],
  emits: [
    "listHeaderClicked"
  ],
  components: {
    IonCard, IonCardContent, IonItem, IonIcon, IonLabel, IonNote,
    IonList, IonButtons, IonButton,
  },
  setup(props, { emit }) {
    return {
      // icons
      checkmarkCircle, alertCircle, pencil, add,

      // methods
      onClickListHeader: () => {
        emit('listHeaderClicked');
      },
    }
  }
}
</script>

<style scoped>
  ion-item ion-buttons {
    min-width: 32px !important;
    height: 32px !important;
  }
  ion-item ion-buttons ion-button::part(native) {
    padding-inline-start: 0;
    padding-inline-end: 0;
  }
</style>