<template>
  <ion-item lines="none" class="section-header" color="primary" slot="header" v-if="isAccordionHeader"
            :class="{ 'short-item': size == 'small' }">
    <ion-label>
      <h3 v-if="size == 'small'"><b>{{ title }}</b></h3>
      <h2 v-html="title" v-else></h2>
      <p v-html="content" v-if="content"></p>
    </ion-label>
  </ion-item>
  <ion-list-header class="section-header" color="primary" :class="{ 'short-item': size == 'small' }">
    <ion-label>
      <h3 v-if="size == 'small'"><b>{{ title }}</b></h3>
      <h2 v-html="title" v-else></h2>
      <p v-html="content" v-if="content"></p>
    </ion-label>
  </ion-list-header>
</template>

<script lang="ts">
// components
import { IonListHeader, IonLabel, IonItem, } from '@ionic/vue';

export default {
  props: [ "title", "content", "isAccordionHeader", "size" ],
  components: { IonListHeader, IonLabel, IonItem, },
}
</script>

<style scoped>
  .short-item {
    min-height: 28px;
    --min-height: 28px;
  }
  .section-header {
    margin-bottom: 8px;
  }
  .section-header h2 {
    font-weight: bold;
    font-size: 20px;
  }
  .section-header p {
    color: #fff;
  }
</style>