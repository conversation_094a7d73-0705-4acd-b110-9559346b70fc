<template>
  <!--<ion-accordion-group :value="group">
    <ion-accordion :value="group">-->
      <ion-item lines="full" slot="header" style="--min-height: 16px">
        <ion-label class="ion-text-wrap" style="color: #888888; margin: 0">
          <b><span>{{ group }}</span></b>
        </ion-label>
      </ion-item>
      <div class="ion-padding-start" slot="content">
        <slot />
      </div>
    <!--</ion-accordion>
  </ion-accordion-group>-->
</template>

<script lang="ts">
// Vue reactivity
import { ref, defineComponent, computed, watch, reactive } from 'vue';

// icons
import { add, close, arrowBack, checkmarkCircle, alertCircle, ellipsisHorizontal, ellipsisVertical, pencil, } from 'ionicons/icons';

// components
import { IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonAccordion, IonAccordionGroup, IonList, } from '@ionic/vue';

// composables / services
import { useI18n } from 'vue-i18n';
import { utils } from '@/composables/utils';
import { useStore } from '@/store';

export default {
  props: ["group"],
  components: { IonItem, IonLabel, IonIcon, IonButtons, IonButton, IonAccordion, IonAccordionGroup, IonList, },
  setup(props) {
    const { t } = useI18n();
    const { openModal, } = utils();
    const store = useStore();
    const user = computed(() => store.state.user);

    // return variables & methods to be used in template HTML
    return {
      // icons
      add, close, arrowBack,
      checkmarkCircle, alertCircle, ellipsisHorizontal, ellipsisVertical, pencil,

      // variables

      // methods
      t,
    }
  },
};
</script>

<style scoped>
  ion-item::part(native) {
    padding-inline-start: 12px !important;
  }
  ion-item {
    --background: var(--ion-background-color) !important;
    --border-width: 0 !important;
  }
</style>
