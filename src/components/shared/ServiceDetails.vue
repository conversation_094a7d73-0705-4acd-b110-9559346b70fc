<template>
  <ion-header v-if="!hideHeader">
    <ion-grid class="ion-no-padding" fixed>
      <!--
        Header (show for event only)
      -->
      <ion-toolbar v-if="event && (user.isSecondaryStudent || !user.teacher)">
        <ion-buttons slot="start">
          <ion-button v-if="isModal" slot="icon-only" @click="closeModal()"><ion-icon :icon="arrowBack"></ion-icon></ion-button>
          <ion-back-button default-href="/home" v-else></ion-back-button>
        </ion-buttons>
        <ion-title class="ion-no-padding">
          <ion-label class="ion-text-wrap">
            <h2>
              <span v-if="event.status == 'TBC'">[Tentative] </span>
              <b>{{ event.name }}</b>
            </h2>
            <p v-if="event.anchorEventId == 'lms-scholarship'">Deadline: <i>{{ event.formattedDateTime }}</i></p>
            <p v-else>
              <i>{{ event.formattedDateTime }} <span v-if="event.mode">[{{ event.mode == 'Online' ? 'Online' : `at ${event.venue || 'Campus'}` }}]</span></i>
            </p>
          </ion-label>
        </ion-title>

        <ion-button class="no-text-transform" style="margin-right: 2px" slot="end" color="success" @click.stop="promptUpdateEventResponse(event, 'Yes')"
                    :disabled="isUserApplied(event.userResponse)" v-if="(user.id || user.phone) && isAllowApply()">
                    <!--:disabled="isUserApplied(event.userResponse) || ['work-workshop-2'].includes(event.anchorEventId)"-->
          <ion-label>{{ isUserApplied(event.userResponse) ? 'Applied' : 'Apply' }}</ion-label>
        </ion-button>

        <ion-button slot="end" color="primary" class="no-text-transform" @click="openLoginModal()" v-else>
          <ion-label>Login to apply</ion-label>
        </ion-button>
      </ion-toolbar>

      <!--
        Header (show for teachers) - Service
        -->
      <ion-toolbar v-else>
        <!-- Back Button -->
        <ion-buttons slot="start">
          <ion-button slot="icon-only" @click="closeModal()" v-if="isModal"><ion-icon :icon="arrowBack"></ion-icon></ion-button>
          <ion-back-button default-href="/home" v-else></ion-back-button>
        </ion-buttons>

        <!-- Title -->
        <ion-title style="padding-left: 0">
          <ion-label class="ion-text-wrap">
            <h2 v-if="event && event.name">
              <b>{{  event.name }}</b><small> ({{ event.date }})</small>
            </h2>
            <h2 v-else>
              <b>{{ selectedService.name }}</b>
            </h2>
          </ion-label>
        </ion-title>

        <!-- Service Application (one-off & not time-bounding e.g. club / scholarship) -->
        <ion-button class="no-text-transform" style="margin-right: 2px" slot="end" color="success"
                    v-if="serviceId == 'bluebird-seed' && !hideApplyBtn" @click="applyForBluebirdSeed()" :disabled="hasAppliedForBluebirdSeed()">
          <ion-label>{{ hasAppliedForBluebirdSeed() ? 'Applied' : 'Apply' }}</ion-label>
        </ion-button>

        <!-- Special: Bluebird - route to bluebird.fdmt.hk -->
        <ion-button class="no-text-transform" style="margin-right: 2px" slot="end" color="success"
                    v-if="serviceId == 'bluebird'" href="https://bluebird.fdmt.hk" target="_blank">
          <ion-label>Apply</ion-label>
        </ion-button>

        <!-- Apply button (for students to select sessions) -->
        <ion-button class="no-text-transform" style="margin-right: 2px" slot="end" color="success"
                    :disabled="serviceSessions.some(ev => isUserApplied(ev.userResponse))"
                    v-else-if="isAllowApply() && (isSecondaryStudentView || user.isSecondaryStudent || !user.teacher) && serviceSessions.length > 0 && !isClientView"
                    @click="promptApplyServiceSessions()">
          <ion-label>{{ serviceSessions.some(ev => isUserApplied(ev.userResponse)) ? 'Applied' : 'Apply' }}</ion-label>
        </ion-button>

        <!-- Unsubscribe Button (for teachers)
        <ion-button class="no-text-transform" style="margin-right: 2px" slot="end" fill="clear" color="medium" size="small"
                    @click.stop="promptUpdateEventResponse(event, event.userResponse?.response == 'Unsubscribed' ? '' : 'Unsubscribed')"
                    v-if="user.teacher && event">
          <ion-label>{{ event.userResponse?.response == 'Unsubscribed' ? 'Subscribe' : 'Unsubscribe' }}</ion-label>
        </ion-button> -->
      </ion-toolbar>
    </ion-grid>
  </ion-header>

  <!--
    Main Content
    -->
  <ion-content :fullscreen="true">
    <ion-grid style="padding-top: 0" fixed>
      <!-- Video Consent -->
      <div style="height: 460px" v-if="event?.needParentConsent">
        <ParentConsentSignatureModal target="shooting" :targetEvent="event" :hideHeader="true" @submitSignature="handleParentConsentSignature" />
      </div>

      <!--
        Event Actions (for applied students)
        -->
      <!--<ion-card v-if="appliedEvent() && isUserApplied(appliedEvent().userResponse) && (!user.teacher || appliedEvent().group == 'Teacher Event')">-->
      <ion-card v-if="appliedEvent() && isUserApplied(appliedEvent().userResponse)">
        <event-card class="ion-no-margin" :ev="appliedEvent()" :user="user" :noStrip="true" :hideEventName="true" :disableCardClick="true"></event-card>
      </ion-card>

      <!--
        Example work video & past shooting moments
      -->
      <div v-if="isOutstandingParticipantView">
        <!-- Video -->
        <iframe class="responsive-embed" style="width: 100%; height: 380px" :src="selectedService?.embeddedYTLink" frameborder="0" allowfullscreen v-if="selectedService?.embeddedYTLink"></iframe>

        <!-- Slides: Past Shooting Moments (Service Photos) -->
        <div>
          <ion-item class="ion-margin-top" color="primary"><ion-label class="ion-text-wrap"><p>Past shooting moments</p></ion-label></ion-item>
          <slides :imageLinks="selectedService?.shootingPhotoLinks" v-if="selectedService?.shootingPhotoLinks && selectedService?.shootingPhotoLinks.length > 0"></slides>
          <slides :imageLinks="[1,2,3,4,5,6,7].map(i => require(`@/assets/shooting/${i}.jpg`))" v-else></slides>
        </div>
      </div>

      <!--
        Video Shooting Session View
         -->
      <div v-else-if="selectedService?.nature == 'Video'">
        <!-- Video -->
        <iframe class="responsive-embed" style="width: 100%; height: 380px" :src="event?.videoLink || selectedService?.embeddedYTLink" frameborder="0" allowfullscreen
                v-if="event?.videoLink || selectedService?.embeddedYTLink"></iframe>

        <!-- Slides: Past Shooting Moments -->
        <div>
          <ion-item class="ion-margin-top" color="primary"><ion-label class="ion-text-wrap"><p>Past shooting moments</p></ion-label></ion-item>
          <slides :imageLinks="selectedService?.shootingPhotoLinks" v-if="selectedService?.shootingPhotoLinks && selectedService?.shootingPhotoLinks.length > 0"></slides>
          <slides :imageLinks="[1,2,3,4,5,6,7].map(i => require(`@/assets/shooting/${i}.jpg`))" v-else></slides>
        </div>

        <!-- Certificates -->
        <ion-item class="ion-margin-top" color="primary"><ion-label class="ion-text-wrap"><p>Certificates</p></ion-label></ion-item>
        <ion-row v-if="getServicePhotosByType('Work Ambassador Certificate').length > 0">
          <ion-col v-for="(p, idx) in getServicePhotosByType('Work Ambassador Certificate')" :key="idx"><img style="cursor: pointer" :src="p.photoLink" @click="openImageModal(p.photoLink)" /></ion-col>
        </ion-row>
        <ion-row>
          <ion-col><img style="cursor: pointer" :src="require('@/assets/dummy_cert_1.jpg')" @click="openImageModal(require('@/assets/dummy_cert_1.jpg'))" /></ion-col>
          <ion-col><img style="cursor: pointer" :src="require('@/assets/dummy_cert_2.jpg')" @click="openImageModal(require('@/assets/dummy_cert_2.jpg'))" /></ion-col>
        </ion-row>
      </div>

      <!--
        Student View (for apply / confirm / ...)
        -->
      <!--<div v-if="event && (user.isSecondaryStudent || !user.teacher)">-->
      <div v-else-if="(!user.teacher || isSecondaryStudentView) && !isClientView">
        <div class="ion-text-center">
          <!-- Video Google Slide -->
          <iframe :src="selectedService?.embeddedVideoSlideLink" frameborder="0" width="100%" height="445px"
                  allowfullscreen="true" mozallowfullscreen="true" webkitallowfullscreen="true" v-if="selectedService?.embeddedVideoSlideLink"></iframe>

          <!-- Video -->
          <iframe class="responsive-embed" style="width: 100%; height: 380px" :src="event?.videoLink || selectedService?.embeddedYTLink" frameborder="0" allowfullscreen
                  v-else-if="event?.videoLink || selectedService?.embeddedYTLink"></iframe>

          <!-- Poster Image -->
          <img style="max-height: 500px; cursor: pointer" :src="getProxyImgLink(event?.posterLink || selectedService?.posterLink)" v-else-if="event?.posterLink || selectedService?.posterLink" />
        </div>

        <!-- Workshop info -->
        <ion-accordion-group v-if="selectedService?.description">
          <ion-accordion value="details">
            <ion-item style="--border-width: 0px !important" slot="header" color="medium">
              <ion-label class="ion-text-wrap">
                <p>All details (including school circular)</p>
              </ion-label>
            </ion-item>
            <ion-card style="margin: 1.5px" slot="content">
              <ion-card-content>
                <div v-if="selectedService?.description" style="user-select: text">
                  <div style="white-space: pre-line; color: var(--ion-color-dark)" v-html="getLinkifiedText(selectedService?.description)"></div>
                  <br />
                </div>
              </ion-card-content>
            </ion-card>
          </ion-accordion>
        </ion-accordion-group>

        <div v-if="selectedService?.nature == 'Workshop'">
          <!-- Slides: Past Shooting Moments -->
          <ion-item class="ion-margin-top" color="primary">
            <ion-label class="ion-text-wrap"><p>Past Workshop moments</p></ion-label>
          </ion-item>

          <!-- Certificates -->
          <ion-item class="ion-margin-top" color="primary"><ion-label class="ion-text-wrap"><p>Certificates</p></ion-label></ion-item>
          <ion-row v-if="getServicePhotosByType('Workshop Certificate').length > 0">
            <ion-col v-for="(p, idx) in getServicePhotosByType('Workshop Certificate')" :key="idx"><img style="cursor: pointer" :src="p.photoLink" @click="openImageModal(p.photoLink)" /></ion-col>
          </ion-row>
          <ion-row v-else>
            <ion-col><img style="cursor: pointer" :src="require('@/assets/dummy_cert_1.jpg')" @click="openImageModal(require('@/assets/dummy_cert_1.jpg'))" /></ion-col>
            <ion-col><img style="cursor: pointer" :src="require('@/assets/dummy_cert_2.jpg')" @click="openImageModal(require('@/assets/dummy_cert_2.jpg'))" /></ion-col>
          </ion-row>
        </div>
      </div>

      <!--
        Client View
      -->
      <div v-else-if="isClientView">
        <ion-accordion-group v-if="selectedService?.description" value="details" style="margin-top: 10px;">
          <ion-accordion value="details">
            <ion-item style="--border-width: 0px !important" slot="header" color="fdmtred">
              <ion-label class="ion-text-wrap">
                <!--<p>Requirements / Extra Information</p>-->
                <p>Friendly reminders</p>
              </ion-label>
            </ion-item>
            <ion-card style="margin: 1.5px" slot="content">
              <ion-card-content>
                <div v-if="selectedService?.description" style="user-select: text">
                  <div style="white-space: pre-line; color: var(--ion-color-dark)" v-html="getLinkifiedText(selectedService?.description)"></div>
                  <br />
                </div>
              </ion-card-content>
            </ion-card>
          </ion-accordion>
        </ion-accordion-group>


        <!-- Response Options -->
        <teacher-response-questions v-if="!hideTeacherResponse" :serviceId="serviceId" group="service" :prefilledOption="prefilledOption" :hideSubmitButton="false"
                                    :serviceSessions="serviceSessions || [event]" :event="event" :isClientView="true"></teacher-response-questions>

        <!-- Meeting info (20240814: No reminder section is needed in the current design)
        <ion-accordion-group v-if="selectedService?.remarks" value="meeting" style="margin-top: 10px;">
          <ion-accordion value="meeting">
            <ion-item style="--border-width: 0px !important" slot="header" color="fdmtred">
              <ion-label class="ion-text-wrap">
                <p>Reminder (Discussions from last meeting)</p>
              </ion-label>
            </ion-item>
            <ion-card style="margin: 1.5px" slot="content">
              <ion-card-content>
                <div v-if="selectedService?.remarks" style="user-select: text">
                  <div style="white-space: pre-line; color: var(--ion-color-dark)" v-html="getLinkifiedText(selectedService?.remarks)"></div>
                  <br />
                </div>
              </ion-card-content>
            </ion-card>
          </ion-accordion>
        </ion-accordion-group>
         -->

      </div>

      <!--
        Teacher View (for nomination / submit preferences)
        -->
      <div v-else-if="user.teacher">

        <!-- Events/Services that require publicize / nominate -->
        <div v-if="isWorkEvent()">
          <!-- Video Google Slide -->
          <iframe :src="selectedService?.embeddedVideoSlideLink" frameborder="0" width="100%" height="445px"
                  allowfullscreen="true" mozallowfullscreen="true" webkitallowfullscreen="true" v-if="selectedService?.embeddedVideoSlideLink"></iframe>

          <!-- Subject career prospects
          <ion-item color="light" lines="full" button detail v-if="relatedSubject || relatedClient?.topic" @click="openCareerProspects()">
            <ion-label class="ion-text-wrap">
              {{ relatedSubject }} {{ relatedClient?.topic }} careers (teachers’ optional self-learning)
            </ion-label>
          </ion-item>-->

          <!-- Step 1 -->
          <div :style="{ 'opacity': isCompletedServiceAction('Show a 3-min video during class') ? 0.5 : 1 }">
            <ion-item color="primary">
              <ion-label class="ion-text-wrap">
                <p>
                  1. Share the 3-min video (if any) in the above slide (<i>accessible at <a target="_blank" :href="selectedService?.slideLink" style="color: #fff"><u>{{ selectedService?.slideLink?.replace("https://", "") }}</u></a></i>) during your class for students' QR code enrollment.
                  <br /><br />Please update the status below afterwards to stop continual reminders.
                  <!--1. Publicize the below slide (<a target="_blank" :href="selectedService?.slideLink" style="color: #fff"><u>{{ selectedService?.slideLink?.replace("https://", "") }}</u></a>)
                  during class for applicants to scan the QR code (Their names will then be in step 2)-->
                  <!--1. Publicize the above-->
                  <!--<ion-button class="no-text-transform" color="fdmtredl" @click="completeServiceAction('Show a 3-min video during class')"
                              v-if="!isCompletedServiceAction('Show a 3-min video during class')">Mark as completed [5-min action]</ion-button>
                  <ion-button v-else class="no-text-transform" color="success" @click="completeServiceAction('Show a 3-min video during class')">
                    <ion-icon slot="start" :icon="checkmark"></ion-icon>
                    Completed
                  </ion-button>-->
                </p>
                <!--<p v-if="selectedService?.slideLink">(Slide accessible at <a target="_blank" :href="selectedService?.slideLink" style="color: #fff"><u>{{ selectedService?.slideLink?.replace("https://", "") }}</u></a>)</p>-->
              </ion-label>
              <ion-button color="light" class="no-text-transform ion-no-margin" style="margin-left: 4px; height: auto"
                          @click="circularAccordion.$el.value = circularAccordion.$el.value ? '' : 'circular'">Click here<br />for<br />Circular/<br />Details</ion-button>
            </ion-item>
            <ion-accordion-group ref="circularAccordion">
              <ion-accordion value="circular">
                <div slot="content">
                  <ion-card style="margin: 1.5px">
                    <ion-card-content>
                      <div v-if="selectedService?.description">
                        <div class="allowSelectOneClick" style="white-space: pre-line; color: var(--ion-color-dark)" v-html="getLinkifiedText(selectedService?.description)"></div>
                        <br />
                        <ion-button expand="block" class="no-text-transform" size="small" @click.stop="copyText(htmlToPlainText(selectedService?.description), 'Details copied.')">
                          Copy
                        </ion-button>
                      </div>
                    </ion-card-content>
                  </ion-card>
                </div>
              </ion-accordion>
            </ion-accordion-group>
          </div>

          <!--
            Response tracked by classes (main: Work workshops) - Publicize / Nominate / Skip
          -->
          <ion-item v-show="new Date() < new Date(event?.startTimeStr)" class="ion-text-wrap" v-for="cl in getServiceRelatedClasses()" :key="cl"
                    :color="getServiceTeacherClassResponse(cl) == OPTION_IDS.skip ? 'medium' : (getServiceTeacherClassResponse(cl) ? 'success' : undefined)">
            <ion-select justify="start" label-placement="start" :label="cl" placeholder="Your status" interface="popover" :value="getServiceTeacherClassResponse(cl)"
                        @ionChange="completeServiceAction($event, cl)" style="min-height: 26px; padding-left: 16px">
              <ion-select-option v-for="opt in publicizeResponseOptions()" :key="opt.id" :value="opt.id">{{ opt?.title }}</ion-select-option>
            </ion-select>
          </ion-item>

          <!--<teacher-response-questions :serviceId="serviceId" group="service" :prefilledOption="prefilledOption"
                                      :serviceSessions="serviceSessions || [event]" :event="event"></teacher-response-questions>-->

          <!-- Step 2: nominate students -->
          <ion-item class="ion-margin-top" color="primary">
            <ion-label class="ion-text-wrap">
              <!--<p>2. Nominate after verifying applicants' names & mobiles.</p>-->
              <p>2. After students' QR enrollment, their names will appear below.  Please verify their names & mobile then nominate them.  You can revoke them later.</p>
            </ion-label>
          </ion-item>

          <ion-card style="overflow-y: scroll; height: 60vh; margin-inline: 0">
            <school-student-modal :hideHeader="true" :schoolId="user.schoolId" :maxYearDSE="selectedService.targetForms.includes('F.4') ? getF4YearDSE() : getF5YearDSE()"
                                  :nominatingEvent="user.isAdmin ? (event || serviceSessions[0] || {}) : event || serviceSessions[0]" :service="selectedService"></school-student-modal>
          </ion-card>

          <!-- Step 3: Photos & Certs -->
          <ion-item class="ion-margin-top" color="primary" target="_blank" :href="`https://fdmt.hk/r/${user.schoolId}`" detail button v-if="['Visit', 'Workshop'].includes(selectedService.nature)">
            <ion-label class="ion-text-wrap">
              <p>3. Attendance report, photos & certificates</p>
            </ion-label>
          </ion-item>

          <!-- Slides: Workshop Photos -->
          <div v-if="(selectedService.eventPhotoLinks || []).length > 0">
            <slides :imageLinks="selectedService.eventPhotoLinks"></slides>
            <ion-item color="light" detail button :href="selectedService.eventAlbumLink" target="_blank" v-if="selectedService.eventAlbumLink">
              <ion-label class="ion-text-wrap"><p>More photos</p></ion-label>
            </ion-item>
          </div>

          <!-- Slides: Outstanding Student Video Sharing -->
          <div v-if="selectedService.pastStudentSharingVideoIds?.length > 0">
            <ion-item class="ion-margin-top" color="primary">
              <ion-label class="ion-text-wrap"><p>Past Outstanding Students' Sharing</p></ion-label>
            </ion-item>

            <slides :videoIds="selectedService.pastStudentSharingVideoIds"></slides>
          </div>
        </div>

        <!-- Survey (e.g. workshop names) -->
        <div v-else-if="isSurvey">
          <teacher-response-questions :serviceId="serviceId" group="survey" :isSurvey="true" v-if="!hideTeacherResponse"></teacher-response-questions>
        </div>

        <!-- Service without dates (book a time) -->
        <div v-else>
          <div class="ion-text-center">
            <!-- Video Google Slide -->
            <iframe :src="selectedService?.embeddedVideoSlideLink" frameborder="0" width="100%" height="445px"
                    allowfullscreen="true" mozallowfullscreen="true" webkitallowfullscreen="true" v-if="selectedService?.embeddedVideoSlideLink"></iframe>

            <!-- Poster Image -->
            <img style="max-height: 500px; cursor: pointer" :src="getProxyImgLink(event?.posterLink || selectedService?.posterLink)" @click="openServiceSlideLink()" v-else />

            <!-- Extra Poster Images -->
            <div v-for="posterLink in selectedService?.extraPosterLinks" :key="posterLink">
              <img style="max-height: 500px; cursor: pointer" :src="getProxyImgLink(posterLink)" @click="openImageModal(getProxyImgLink(posterLink))" />
            </div>
          </div>

          <!-- AchieveJUPAS: try out the tool -->
          <ion-item color="success" button detail @click="openMockJUPASToolModal()" v-if="selectedService?.id == 'mock-jupas'">
            <ion-label><span>Try out the tool</span></ion-label>
          </ion-item>

          <!-- Event info / descriptions -->
          <ion-accordion-group v-if="selectedService?.description">
            <ion-accordion>
              <ion-item style="--border-width: 0px !important" slot="header" color="fdmtred">
                <ion-label>
                  <span v-if="selectedService?.id == 'mock-jupas'">Invited schools just need to arrange</span>
                  <span v-else>Details</span>
                </ion-label>
                <ion-button size="small" @click.stop="copyText(htmlToPlainText(selectedService?.description), 'Text copied.')" class="no-text-transform">
                  Copy
                </ion-button>
              </ion-item>
              <div slot="content">
                <ion-card style="margin: 1.5px">
                  <ion-card-content>
                    <div v-if="selectedService?.description">
                      <div class="allowSelectOneClick" style="white-space: pre-line; color: var(--ion-color-dark)" v-html="getLinkifiedText(selectedService?.description)"></div>
                    </div>
                  </ion-card-content>
                </ion-card>
              </div>
            </ion-accordion>
          </ion-accordion-group>

          <div v-if="!hideTeacherResponse && !['bluebird-seed'].includes(serviceId)">
            <!-- Special Handling: AchieveJUPAS briefing -->
            <div v-if="['achievejupas-b1', 'achievejupas-b2'].includes(serviceId)">
              <teacher-response-questions :serviceId="serviceId" group="service" :prefilledOption="prefilledOption"
                                          :serviceSessions="serviceSessions || [event]" :event="event" :showSubmitBtn="true"></teacher-response-questions>
            </div>

            <!-- Normal Services -->
            <div v-else>
              <ion-item color="primary">
                <ion-label class="ion-text-wrap">
                  <p>
                    1. Please update the status for each class below afterwards to stop continual reminders
                  </p>
                </ion-label>
              </ion-item>

              <!-- Class sessions (school-based) - tracked by classes -->
              <div v-if="selectedService.isAllowProposeDates && !['Visit', 'Workshop'].includes(selectedService.nature)">
                <ion-item class="ion-text-wrap" v-for="cl in getServiceRelatedClasses()" :key="cl"
                          :color="getServiceTeacherClassResponse(cl) == OPTION_IDS.skip ? 'medium' : (getServiceTeacherClassResponse(cl) ? 'success' : undefined)">
                  <!-- Show scheduled session time -->
                  <ion-label class="ion-padding-start" v-if="getServiceTeacherClassResponse(cl) == OPTION_IDS.bookTime">
                    <p>
                      {{ cl }}
                      <span style="padding-left: 12px">
                        {{ getServiceTeacherClassResponse(cl, 'preferredSessionDates') }} {{ getServiceTeacherClassResponse(cl, 'preferredSessionTime') }}
                      </span>
                    </p>
                  </ion-label>

                  <!-- Choose preference -->
                  <ion-select v-else justify="start" label-placement="start" :label="cl" placeholder="Your choice" interface="popover" :value="getServiceTeacherClassResponse(cl)"
                              @ionChange="completeServiceAction($event, cl)" style="min-height: 26px; padding-left: 16px">
                    <ion-select-option v-for="opt in proposeDateResponseOptions()" :key="opt.id" :value="opt.id">{{ opt.title }}</ion-select-option>
                  </ion-select>
                </ion-item>

                <!-- For indicating booking time -->
                <ion-modal class="form-modal stack-modal" :is-open="bookTimeData.isModalOpened" :backdrop-dismiss="false" :show-backdrop="true">
                  <ion-header>
                    <ion-toolbar>
                      <ion-buttons slot="start">
                        <ion-button slot="icon-only" @click="bookTimeData.isModalOpened = false"><ion-icon :icon="arrowBack"></ion-icon></ion-button>
                      </ion-buttons>
                      <ion-title class="ion-text-left" style="padding-left: 0">
                        <ion-label><h2><b>Book a time for {{ bookTimeData.targetClass }}</b></h2></ion-label>
                      </ion-title>
                    </ion-toolbar>
                  </ion-header>
                  <ion-content class="ion-padding">
                    <teacher-response-questions :serviceId="serviceId" group="service" :serviceSessions="serviceSessions || [event]" :event="event"
                                                :targetClass="bookTimeData.targetClass" :prefilledOptionId="bookTimeData.prefilledOptionId"></teacher-response-questions>
                  </ion-content>
                </ion-modal>
              </div>

              <!-- University campus events (proposed -> publicize / nominate) -->
              <div v-else>
                <teacher-response-questions :serviceId="serviceId" group="service" :prefilledOption="prefilledOption"
                                            :serviceSessions="serviceSessions || [event]" :event="event"></teacher-response-questions>
              </div>
            </div>
          </div>
        </div><hr />

        <!-- Proposed Sessions / Sessions related to the school (show proposed by) -->
        <ion-accordion-group v-if="schoolProposedSessions().length > 0">
          <!-- Grouped by Future & Past? (or show future first then show past) -->
          <ion-accordion style="--min-height: 24px">
            <ion-item lines="full" slot="header" color="light">
              <ion-label><p>My school's session(s)</p></ion-label>
            </ion-item>
            <div slot="content">
              <ion-item lines="full" v-for="ev in schoolProposedSessions()" :key="ev"
                        button detail>
                  <ion-label class="ion-text-wrap" style="line-height: 1">
                    <p><b style="color: var(--ion-color-dark)">{{ ev.name }}</b> ({{ ev.date }})</p>
                    <p v-if="ev.classes || ev.studentForms">{{ ev.classes || ev.studentForms }}</p>
                    <p v-if="ev.inChargeTeacherNames">In charge: {{ ev.inChargeTeacherNames }}</p>
                    <small v-else-if="getMockJUPASDeadline(ev, 'inChargeTeacherNames')">
                      In charge: {{ getMockJUPASDeadline(ev, 'inChargeTeacherNames') }}
                    </small>
                    <p><small v-if="getMockJUPASDeadline(ev)">Deadline for students: {{ getMockJUPASDeadline(ev) }}</small></p>
                  </ion-label>
                </ion-item>
            </div>
          </ion-accordion>
        </ion-accordion-group>

        <!-- Recent Responses / Actions -->
        <ion-accordion-group v-if="recentResponses().length > 0">
          <ion-accordion style="--min-height: 24px">
            <ion-item lines="full" slot="header" color="light">
              <ion-label><p>My previous responses/actions</p></ion-label>
            </ion-item>
            <div slot="content">
              <ion-item v-for="(resp, idx) in recentResponses().slice(0, 10)" :key="idx" lines="full">
                <ion-label class="ion-text-wrap">
                  <p>
                    <!--[{{ resp.type == 'action' ? 'Action' : 'Response' }}]--><span v-if="resp.targetClass">[{{ resp.targetClass }}]</span> {{ resp.response }}
                    <br /><small>{{ formatDate(resp.updatedAt) }}</small>
                  </p>
                </ion-label>
              </ion-item>
            </div>
          </ion-accordion>
        </ion-accordion-group>
      </div>

      <!-- Bluebird Seed -->
      <div v-if="serviceId == 'bluebird-seed'">
        <ion-button class="no-text-transform" expand="block" href="https://bluebird.fdmt.hk/regulations" target="_blank">
          <ion-icon slot="start" :icon="documentOutline"></ion-icon>
          Bluebird Seed member regulations
        </ion-button>

        <!-- Parent Consent -->
        <ion-item lines="none" button @click="openBBSParentConsentModal()"
                  v-if="user.userConsentRecords?.find(r => r.target == 'bluebird-seed')">
          <ion-label class="ion-text-wrap" style="padding-left: 28px"><h3><b>Parent Consent Obtained</b></h3></ion-label>
          <ion-icon slot="end" :icon="checkmarkCircle" color="success"></ion-icon>
        </ion-item>

        <ion-item lines="none" color="danger" button detail @click="openBBSParentConsentModal()" v-else-if="hasAppliedForBluebirdSeed()">
          <ion-label class="ion-text-wrap" style="padding-left: 28px">
            <h3><b>Parent Consent Required!</b></h3>
            <p>To join Bluebird Seed, we need your parent's or guardian's consent.</p>
          </ion-label>
          <ion-button class="no-text-transform" slot="end">Start</ion-button>
        </ion-item>
      </div>

      <!-- Bluebird -->
      <div v-if="serviceId == 'bluebird'">
        <!-- Video -->
        <iframe class="responsive-embed" style="width: 100%; height: 380px" src="https://www.youtube.com/embed/Wv37M8o0X8E?si=zQH16jdzwr5CsxRw"
                frameborder="0" allowfullscreen></iframe>
        <ion-button class="no-text-transform" expand="block" href="https://bluebird.fdmt.hk/regulations" target="_blank">
          <ion-icon slot="start" :icon="documentOutline"></ion-icon>
          Bluebird regulations
        </ion-button>
      </div>
    </ion-grid>

    <!--
      Ack: video slide WhatsApp sent (not in use)
      -->
    <ion-modal class="ack-modal" :is-open="isAckModalOpened">
      <div style="padding: 10px 20px">
        <p>The video slide link will be sent to your WhatsApp group (<a target="_blank" :href="user.waGroupLink">{{ user.waGroupLink }}</a>) in minutes for your forwarding.</p>
      </div>
      <ion-item lines="none">
        <ion-buttons slot="end" @click="isAckModalOpened = false">
          <ion-button color="primary">OK</ion-button>
        </ion-buttons>
      </ion-item>
    </ion-modal>
  </ion-content>

  <!--
    Footer: Action Buttons
  -->
  <ion-footer v-if="event && (user.isAdmin || !user.teacher)">
    <ion-toolbar class="ion-text-center" v-show="(user.isAdmin || showActionBtns) && isUserApplied(event.userResponse)">
      <ion-grid style="padding-top: 0" fixed>
        <!-- Confirm to attend button -->
        <ion-button fill="outline" class="no-text-transform" color="success" size="small"
                    @click.stop="promptUpdateEventResponse(event, 'Confirmed')" :disabled="event.userResponse?.response == 'Confirmed'">
          <ion-label class="ion-text-wrap">
            <h2><b>{{ event.userResponse?.response == "Confirmed" ? 'Confirmed 已確認出席' : 'Confirm 按此確認出席' }}</b></h2>
          </ion-label>
          <ion-icon slot="end" :icon="checkmark" v-show="event.userResponse?.response == 'Confirmed'"></ion-icon>
        </ion-button>

        <!-- Withdraw button -->
        <ion-button fill="outline" class="no-text-transform" color="danger" size="small" @click.stop="promptUpdateEventResponse(event, 'No')">
          <ion-label class="ion-text-wrap">
            <h2><b>Withdraw 按此取消報名</b></h2>
          </ion-label>
        </ion-button>
      </ion-grid>
    </ion-toolbar>
  </ion-footer>
</template>

<script lang="ts">
// Vue reactivity
import { computed, defineComponent, onMounted, reactive, ref, watch } from 'vue';

// components
import { IonRow, IonCol, IonHeader, IonToolbar, IonTitle, IonContent, IonButtons, IonButton, IonBackButton, IonIcon,
        IonCard, IonCardHeader, IonCardContent, IonLabel, IonGrid, IonFooter, IonSegment, IonSegmentButton,
        IonList, IonListHeader, IonItem, IonRadioGroup, IonRadio, IonNote, IonInput, IonDatetime,
        IonModal, IonChip, IonSelect, IonSelectOption, IonTextarea, IonAccordionGroup, IonAccordion,
        modalController, loadingController, alertController, } from '@ionic/vue';
import TeacherResponseQuestions from '@/components/teacher/TeacherResponseQuestions.vue';
import SchoolStudentModal from '@/components/teacher/SchoolStudentModal.vue';
import SchoolTeacherModal from '@/components/teacher/SchoolTeacherModal.vue';
import SLPModal from '@/components/secondary/ABSLPModal.vue';
import Slides from '@/components/shared/Slides.vue';
import AchieveJUPASResultPageModal from '@/components/achievejupas/AchieveJUPASResultPageModal.vue';
import ABProfessionSelectModal from '@/components/pss/profession/ABProfessionSelectModal.vue';
import ParentConsentSignatureModal from '@/components/modals/ParentConsentSignatureModal.vue';
import LoginPage from '@/pages/LoginPage.vue';

// composables
import { useStore } from '@/store';
import { utils } from '@/composables/utils';
import { useI18n } from 'vue-i18n';

// icons
import { checkmark, arrowBack, alertCircleOutline, documentOutline, checkmarkCircle, } from 'ionicons/icons';

// types
import { Client, Service, Session, TeacherResponseOption, User, } from '@/types';

// services
import EventService from '@/services/EventService';
import TeacherService from '@/services/TeacherService';
import CommonService from '@/services/CommonService';

export default defineComponent({
  props: ["serviceId", "event", "userId", "showActionBtns",
          "showUnsubscribePrompt", "showApplyPrompt", "isSurvey",
          "showConfirmPrompt", "showWithdrawPrompt", "hideTeacherResponse",
          "prefilledOption", "isModal", "hideHeader", "isSecondaryStudentView",
          "isClientView", "hideApplyBtn", "isOutstandingParticipantView"],
  components: { IonRow, IonCol, IonHeader, IonToolbar, IonTitle, IonContent, IonButtons, IonButton, IonBackButton, IonIcon,
                IonCard, IonCardHeader, IonCardContent, IonLabel, IonGrid, IonFooter, IonSegment, IonSegmentButton,
                IonList, IonListHeader, IonItem, IonRadioGroup, IonRadio, IonNote, IonInput, IonDatetime, IonModal, IonChip,
                IonSelect, IonSelectOption, IonTextarea, IonAccordionGroup, IonAccordion,
                TeacherResponseQuestions, SchoolStudentModal, Slides, ParentConsentSignatureModal, },
  setup(props) {
    const store = useStore();
    const { t } = useI18n();
    const { openImageModal, presentPrompt, promptWithdrawReason, formatDate, getProxyImgLink, getLinkifiedText,
            sendLinkToMyWhatsApp, openBrowser, openModal, sleep, closeModal, copyText, htmlToPlainText, presentAlert,
            getF4YearDSE, getF5YearDSE, presentToast, getSecStudentForm, uniqueId, isUserApplied, } = utils();

    const relatedEvents = computed<Session[]>(() => store.getters.getRelatedEvents(props.event?.id));
    const selectedService = computed<Service>(() => store.getters.getServiceById(props.serviceId));
    const relatedClient = computed<Client>(() => store.getters.getClientById(selectedService.value.clientId || props.event?.clientId));
    const allSessions = computed<Session[]>(() => store.state.sessions);
    const serviceSessions = computed<Session[]>(() => store.getters.getApplicationSessionsByServiceId(props.serviceId));
    const workshopSessions = computed<Session[]>(() => store.getters.getApplicationSessionsByServiceId(props.serviceId, 'work-workshop-2'));
    const slpSession = computed<Session>(() => store.getters.getSLPSessionByServiceId(props.serviceId));
    const user = computed<User>(() => store.state.user);
    const userSchoolRoles = computed(() => (user.value.teacher?.schoolRoles || "").split(" , ").filter(r => r));
    const firstSchoolRole = computed(() => userSchoolRoles.value[0] || "");
    const relatedSubject = computed(() => {
      const schoolRole = store.state.schoolRoles.find(r => r.title === firstSchoolRole.value);
      return schoolRole?.relatedSubject ? `${schoolRole.alias}-related` : '';
    });
    const schoolTeachers = computed<User[]>(() => store.state.schoolTeachers); // for getting responses of other teachers
    const isAckModalOpened = ref(false);
    const circularAccordion = ref(null);

    // For class-based response options
    const teacherResponseOptions = computed<TeacherResponseOption[]>(() => store.state.teacherResponseOptions);
    const OPTION_IDS = {
      playedVideo: 'o-7f6e61d0',
      pubInOtherMeans: 'o-02bd98ac',
      bookTime: 'o-6858aa15',
      findingTime: 'o-08a9ae49',
      skip: 'o-e8602212',
    };
    const bookTimeData = reactive({
      isModalOpened: false,
      targetClass: "",
      prefilledOptionId: OPTION_IDS.bookTime,
    });

    // Event Response (for students)
    const getActionVerb = (response) => {
      switch (response) {
        case 'Yes':
          return 'Apply for';
        case 'Confirmed':
          return 'Confirm to attend';
        case 'No':
          return 'Cancel your application for';
        case '':
          return 'Subscribe';
        case 'Unsubscribed':
          return 'Stop receiving the WhatsApp messages from'
        default:
          return response;
      }
    }
    const updateEventResponse = async (targetEvent, response, withdrawReason = null, workshopTimePreference = null, userConsentRecordId = null) => {
      const { phone, schoolId, currIntakeId } = user.value;
      const loading = await loadingController.create({ duration: 15000 });
      await loading.present();
      const responseObj = await EventService.updateEventResponse(targetEvent, response, (phone || props.userId), schoolId, currIntakeId, undefined, undefined, withdrawReason, workshopTimePreference, userConsentRecordId);
      if (user.value?.id) { // logged in
        store.commit('injectUserEventResponse', responseObj);
      }
      loading.dismiss();
    }
    const promptApplyWorkshopSessions = async (nominationSession) => {
      // Retrieve work-workshop-2 session for time preference indication
      const inputs: any = workshopSessions.value.map(s => ({
        name: 'targetSessionId',
        label: s.formattedDateTime,
        type: 'radio',
        value: s,
      }));
      const alert = await alertController.create({
        header: `Choose your preferred date & time`,
        inputs,
        buttons: [
          {
            text: t('cancel'),
            role: 'cancel',
            cssClass: 'secondary',
          }, {
            text: t('submit'),
            handler: (value) => {
              if (value) {
                updateEventResponse(nominationSession, "Yes", null, value.formattedDateTime);
                presentToast("Thank you for your application.");
              } else {
                return false;
              }
            }
          }
        ]
      });
      await alert.present();
    }
    const promptUpdateEventResponse = async (targetEvent: Session, response: any) => {
      if (response == 'Confirmed' && targetEvent.needParentConsent) {
        return; // switched to inline parent signature modal
        /*
        // Show parent consent modal (like AchieveJUPAS) -> submit along with confirm attendance
        const modal = await modalController.create({
          component: (ParentConsentSignatureModal as any),
          componentProps: { target: 'shooting', targetEvent, },
        });
        modal.onDidDismiss().then(({ data }) => {
          if (data && data.userConsentRecordId) {
            updateEventResponse(targetEvent, "Confirmed", null, null, data.userConsentRecordId);
          }
        });
        return modal.present();
        */
      } else {
        let msg = `${getActionVerb(response)} <b>${targetEvent.name}</b>`;
        if (targetEvent.startTime) msg += ` on <b>${targetEvent.formattedDateTime}</b>?`;
        if (response == "No") {
          await promptWithdrawReason(msg, async (reason: any) => {
            await updateEventResponse(targetEvent, response, reason); // Ask for withdrawal reason
          });
        }
        else if (response == "Yes" && ['work-workshop-nomination'].includes(targetEvent.anchorEventId)) {
          await promptApplyWorkshopSessions(targetEvent); // Select time preference (workshops)
        }
        else {
          await presentPrompt(msg, () => {
            updateEventResponse(targetEvent, response); // SLP: direct apply (no interview required)
          });
        }
      }
    }

    // INIT
    onMounted(() => {
      const { event, showUnsubscribePrompt, showApplyPrompt, showConfirmPrompt, showWithdrawPrompt, } = props;
      if (showUnsubscribePrompt) {
        setTimeout(() => {
          promptUpdateEventResponse(event, 'Unsubscribed');
        }, 200);
      }
      /* Not suitable to prompt on enter (users may want to read details first before applications)
      else if (showApplyPrompt) {
        if (user.value.isAdmin || !user.value.teacher) {
          setTimeout(() => {
            if (event.userResponse?.response != 'Confirmed') {
              promptUpdateEventResponse(event, 'Yes');
            }
          }, 200);
        }
      }
      */
      else if (showConfirmPrompt) {
        setTimeout(() => {
          promptUpdateEventResponse(event, 'Confirmed');
        }, 200);
      }
      else if (showWithdrawPrompt) {
        setTimeout(() => {
          promptUpdateEventResponse(event, 'No');
        }, 200);
      }
    });

    // 3. return variables & methods to be used in template HTML
    return {
      // icons
      checkmark, arrowBack, alertCircleOutline, documentOutline, checkmarkCircle,

      // variables
      user,
      selectedService, serviceSessions,
      relatedClient,
      isAckModalOpened,
      relatedSubject,

      // methods
      getLinkifiedText, htmlToPlainText,
      getF4YearDSE, getF5YearDSE,
      closeModal, copyText,
      formatDate, getProxyImgLink, openImageModal,
      sendSlideToMyWhatsApp: async () => {
        const { posterLink, videoLink, slideLink, } = selectedService.value;
        const { phone, waGroupId } = user.value;
        const loading = await loadingController.create({});
        await loading.present();
        sendLinkToMyWhatsApp("", phone, waGroupId, posterLink, videoLink, slideLink, false);
        await sleep(2);
        loading.dismiss();
        isAckModalOpened.value = true;
      },
      openServiceSlideLink: () => {
        const { slideLink, posterLink } = selectedService.value;
        if (slideLink && slideLink.includes("fdmt.hk/")) {
          openBrowser(selectedService.value.slideLink);
        } else {
          openImageModal(getProxyImgLink(posterLink));
        }
      },
      openSchoolTeacherModal: () => {
        openModal(SchoolTeacherModal, { service: selectedService.value, });
      },

      // Event application
      relatedEvents,
      promptUpdateEventResponse,
      handleParentConsentSignature: async (userConsentRecordId: any) => {
        updateEventResponse(props.event, "Confirmed", null, null, userConsentRecordId);
      },

      // Prompt apply service sessions (check if contains work-workshop-nomination)
      promptApplyServiceSessions: async () => {
        if (user.value.teacher && !user.value.isSecondaryStudent) {
          presentAlert("Only students can apply for this.");
          return false;
        }
        if (props.serviceId == 'lms-scholarship') {
          // Special handling: LMS scholarship
          return await presentPrompt("Confirm apply?", () => {
            updateEventResponse(serviceSessions.value[0], "Yes");
          });
        }
        const nominationSession = serviceSessions.value.find(s => s.anchorEventId == 'work-workshop-nomination');
        if (nominationSession) {
          // Workshop sessions (select time preference)
          await promptApplyWorkshopSessions(nominationSession);
        }
        else {
          const sessions = serviceSessions.value.filter(s => s.anchorEventId == selectedService.value.anchorEventId);
          //const sessions = workshopSessions.value.length > 0 ? workshopSessions.value : serviceSessions.value;
          // Normal work sessions (e.g. Work SLP): Show all upcoming sessions for selection
          const inputs: any = sessions.map(s => ({
            name: 'targetSessionId',
            label: s.formattedDateTime,
            type: 'radio',
            value: s,
          }));
          const alert = await alertController.create({
            header: `Choose your preferred date & time`,
            inputs,
            buttons: [
              {
                text: t('cancel'),
                role: 'cancel',
                cssClass: 'secondary',
              }, {
                text: t('submit'),
                handler: (value) => {
                  if (value) {
                    updateEventResponse(value, "Yes");
                    presentToast("Thank you for your application.")
                  } else {
                    return false;
                  }
                }
              }
            ]
          });
          await alert.present();
        }
      },

      isUserApplied,

      // Get applied event (direct access / via service details)
      appliedEvent() {
        if (serviceSessions.value.find(s => s.anchorEventId == 'work-workshop-nomination')) {
          return workshopSessions.value.find(s => isUserApplied(s.userResponse)); // confirmed workshop session
        }
        return props.event || serviceSessions.value.find(s => isUserApplied(s.userResponse));
      },

      // Student event tool support (mainly for demo)
      slpSession, openSLPModal: async () => {
        const { serviceId } = props;
        const ev = slpSession.value;
        return await openModal(SLPModal, { ev, serviceId, relatedProgramId: ev?.relatedProgramId, relatedClientId: selectedService.value.clientId, }, "", false)
      },

      // Work Service (enable complicated steps)
      isWorkEvent: () => { // events for publicization / nomination only
        const { type, id, isAllowProposeDates, } = selectedService.value;
        return (id == 'ucircle' || type == 'Work') && (props.event || !isAllowProposeDates);
      },
      circularAccordion,
      isCompletedServiceAction: (actionTitle) => {
        // Check if teacher completed the action
        const { id: serviceId, currIntakeYear: intakeYear } = selectedService.value;
        return user.value.teacherResponses?.find(r => r.serviceId == serviceId && r.response == actionTitle && r.type == 'action' && (!intakeYear || r.intakeYear == intakeYear));
      },

      // Logs (responses / proposed sessions)
      recentResponses: () => {
        const { id: serviceId, currIntakeYear: intakeYear } = selectedService.value;
        return (user.value.teacherResponses || []).filter(r => r.serviceId == serviceId && (!intakeYear || r.intakeYear == intakeYear));
      },
      schoolProposedSessions: () => {
        const { id: serviceId } = selectedService.value; // get sessions by clientId, anchorEventId & user schoolId
        const sessions = allSessions.value.filter(s => s.serviceId == serviceId && s.clientSchoolId == user.value.schoolId);
        return [
          ...sessions.filter(s => new Date(s.startTimeStr) > new Date()).sort((a, b) => new Date(a.startTimeStr) < new Date(b.startTimeStr) ? -1 : 1), // future sessions
          ...sessions.filter(s => new Date(s.startTimeStr) < new Date()).sort((a, b) => new Date(a.startTimeStr) < new Date(b.startTimeStr) ? 1 : -1), // past sessions
        ];
      },
      getMockJUPASDeadline: (mainSession, targetKey = 'date') => {
        if (mainSession.serviceId != 'mock-jupas') return null;
        const deadlineSessions = store.state.sessions.filter(s => s.anchorEventId == 'c0b3a6362' && s.clientSchoolId == user.value.schoolId && new Date(s.date) > new Date(mainSession.date))
                                                      .sort((a, b) => (new Date(a.date) > new Date(b.date) ? -1 : 1));
        return deadlineSessions.length > 0 ? deadlineSessions[0][targetKey] : "";
      },

      // Check form eligibility
      isAllowApply: () => {
        const { targetForms, anchorEventId, clientId, } = selectedService.value;
        const attendedBefore = allSessions.value.find(s => s.anchorEventId == anchorEventId && s.clientId == clientId && s.userResponse?.attended == 'Yes') != null;
        if (attendedBefore) return false; // e.g. BBAWork workshop, not allow duplicate application / nomination
        if (user.value.isAdmin || !targetForms) return true;
        return targetForms.includes(getSecStudentForm(user.value.yearDSE));
      },

      // AchieveJUPAS
      openMockJUPASToolModal: async () => (await openModal(AchieveJUPASResultPageModal, {})),

      // Track responses by classess
      OPTION_IDS, bookTimeData,
      completeServiceAction: async (ev, targetClass = "") => {
        const optionId = ev.detail.value;
        const { id: serviceId, currIntakeYear: intakeYear, name: serviceName, } = selectedService.value;
        const { event } = props;
        const { id: sessionId, name: eventName, date: eventDate } = (event || {});
        const resp = user.value.teacherResponses?.find(r => {
          if (props.event && r.sessionId != sessionId) return false; // Match event response
          return r.serviceId == serviceId && r.targetClass == targetClass && (!intakeYear || r.intakeYear == intakeYear)
        }) || {};

        // If book time, open modal (teacherResponseQuestions) for filling info
        if (optionId == OPTION_IDS.bookTime) {
          ev.target.value = ""; // prevent select UI changing value
          bookTimeData.targetClass = targetClass;
          bookTimeData.isModalOpened = true;
          return true;
        }

        // Other cases: update teacher response for that class directly
        const loading = await loadingController.create({ });
        await loading.present();
        const teacherResponse: any = {
          id: `r${uniqueId()}`,
          ...(resp || {}),
          optionId: optionId,
          response: (teacherResponseOptions.value.find(opt => opt.id == optionId) || {}).title || "",
          type: props.event ? 'event' : 'service', // previous: type = 'action'
          intakeYear,
          sessionId, serviceId,
          eventName: props.event ? `${eventName} (${eventDate})` : serviceName,
          targetClass,
        }
        TeacherService.upsertTeacherResponse(props.event, props.serviceId, teacherResponse, user.value.phone, user.value.schoolId, user.value.id, []);
        store.commit('upsertTeacherResponse', { ...teacherResponse, updatedAt: new Date().toString() });
        loading.dismiss();
      },
      getServiceTeacherClassResponse: (targetClass, targetKey = 'optionId') => {
        // Get class-based responses (from the teacher)
        const { id: serviceId, currIntakeYear: intakeYear } = selectedService.value;
        for (const t of schoolTeachers.value) {
          const resp = t.teacherResponses?.find(r => {
            if (props.event && r.sessionId != props.event.id) return false; // Match event response
            return r.serviceId == serviceId && r.targetClass == targetClass && (!intakeYear || r.intakeYear == intakeYear)
          });
          if (t.id == user.value.id) return resp ? resp[targetKey] || "" : ""; // user response, just return
          if (resp?.optionId == OPTION_IDS.bookTime) return targetKey ? resp[targetKey] : resp; // not allow scheduling for more than 1 session
        }
        return "";
      },
      getServiceRelatedClasses: () => {
        const { targetForms } = selectedService.value;
        const classes: any = [];
        if (targetForms.includes('F.6')) classes.push('6A','6B','6C','6D','6E');
        if (targetForms.includes('F.5')) classes.push('5A','5B','5C','5D','5E');
        if (targetForms.includes('F.4')) classes.push('4A','4B','4C','4D','4E');
        if (classes.length == 0) classes.push('5A','5B','5C','5D','5E'); // default F.5 students only
        return classes;
      },

      publicizeResponseOptions: () => {
        return [OPTION_IDS.playedVideo, OPTION_IDS.pubInOtherMeans, OPTION_IDS.skip].map(id => teacherResponseOptions.value.find(opt => opt.id == id)).filter(opt => opt);
      },
      proposeDateResponseOptions: () => {
        return [OPTION_IDS.bookTime, OPTION_IDS.findingTime, OPTION_IDS.skip].map(id => teacherResponseOptions.value.find(opt => opt.id == id)).filter(opt => opt);
      },

      // Subject-related career prospects
      openCareerProspects: async () => {
        openModal(ABProfessionSelectModal, { client: relatedClient.value });
      },

      // (For guest users: login & apply if available)
      openLoginModal: () => {
        openModal(LoginPage, {}, "login-modal");
      },

      // Bluebird Seed
      openBBSParentConsentModal: async () => (await openModal(ParentConsentSignatureModal, { target: 'bluebird-seed'})),

      // Bluebird Seed Application
      hasAppliedForBluebirdSeed: () => {
        return user.value.serviceResponses?.some(sr => sr.serviceId === 'bluebird-seed' && sr.response === 'Yes');
      },

      applyForBluebirdSeed: async () => {
        // First submit the application
        presentPrompt('Confirm to apply for Bluebird Seed?', async () => {
          const loading = await loadingController.create({});
          await loading.present();

          const responseId = `r${uniqueId()}`;
          const serviceId = 'bluebird-seed';
          const response = 'Yes';
          const { phone, fullName, schoolId, } = user.value;
          const serviceName = selectedService.value.name;

          CommonService.upsertServiceResponse(responseId, serviceId, response, phone, serviceName, fullName, schoolId);
          store.commit('upsertUserServiceResponse', { id: responseId, serviceId, response, phone, serviceName, });

          loading.dismiss();
          presentToast('Your application has been submitted successfully.');
        });
      },

      // Client Photos
      getServicePhotosByType: (type: any) => {
        const { clientPhotos } = selectedService.value || {};
        return clientPhotos ? clientPhotos.filter(p => p.type == type) : [];
      }
    }
  }
})
</script>

<style scoped>
  ion-toolbar {
    --min-height: 28px;
  }
  ion-accordion ion-item[slot="header"] {
    --border-width: 0 !important;
  }
  ion-item ion-icon[slot="start"] {
    margin-top: 2px;
    margin-bottom: 4px;
    margin-inline-end: 8px !important;
  }
</style>
