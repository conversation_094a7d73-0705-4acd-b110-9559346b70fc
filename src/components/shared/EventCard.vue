<template>
  <!--
    Mainly For AB4 in home page (TBD? Home page view may need to be updated)
    -->
  <ion-accordion-group v-if="useAccordionView" :value="ev.userResponse?.attended == 'Yes' ? 'event' : null">
    <ion-accordion style="--min-height: 24px" value="event">
      <ion-item lines="full" slot="header">
        <ion-label style="padding-left: 28px">
          <p v-if="showStudentFormsOnly">
            <span v-if="ev.classes || ev.studentForms"><b>{{ ev.classes || ev.studentForms }}</b> ({{ ev.date }}) [Roll call, feedback]</span>
            <span v-else><b>{{ ev.date }}</b> [Roll call, feedback]</span>
          </p>
          <p v-else>
            <span v-if="ev.formattedName" style="white-space: pre-line" v-html="ev.formattedName"></span>
            <b style="color: var(--ion-color-dark)" v-else>{{ ev.name }}</b>
            <span v-if="ev.description">: {{ ev.description }}</span>
            <small v-show="ev.serviceId"> (<u>Details</u>)</small>
            <span v-if="ev.classes || ev.studentForms"> ({{ ev.classes || ev.studentForms }})</span>
          </p>
        </ion-label>
      </ion-item>
      <ion-list class="ion-text-center ion-no-padding" slot="content">
        <!-- Take Attendance -->
        <ion-item style="--min-height: 24px" @click.stop="openRollCallCodePrompt(ev)" color="light" lines="full" button :detail="ev.userResponse?.attended != 'Yes'"
                  v-if="ev.rollCallCode" :disabled="(!user.isAdmin && !isSessionStarted(ev)) || ev.userResponse?.attended == 'Yes'">
          <ion-icon style="padding-left: 48px" slot="start" :icon="ev.userResponse?.attended == 'Yes' ? checkmarkCircle : alertCircle"
                    :color="ev.userResponse?.attended == 'Yes' ? 'success' : 'danger'"></ion-icon>
          <ion-label>
            <h3>Take attendance</h3>
          </ion-label>
        </ion-item>

        <div v-if="ev.userResponse?.attended == 'Yes'">
          <!-- Tasks during & after lessons -->
          <ion-item color="light" v-for="task in anchorEventTasks.filter(t => t.anchorEventId == ev.anchorEventId && t.showAfterAttended)" :key="task.id"
                    lines="full" button :detail="checkShowTaskDetailIcon(task, ev)" @click.stop="checkOpenTaskModal(task, ev)">
            <ion-spinner slot="start" v-if="isFormQuestionTask(task) && loadingUserFormResponses"></ion-spinner>
            <ion-icon style="padding-left: 48px" slot="start" v-if="isFormQuestionTask(task) && !loadingUserFormResponses"
                      :icon="isSubmittedTaskForm(task, ev) ? checkmarkCircle : alertCircle"
                      :color="isSubmittedTaskForm(task, ev) ? 'success' : 'danger'"></ion-icon>
            <ion-label>
              <h3>{{ task.title }}</h3>
            </ion-label>
          </ion-item>
        </div>
      </ion-list>
    </ion-accordion>
  </ion-accordion-group>
    
  <ion-card class="event-card" :class="{ 'no-strip': noStrip, 'unsubscribed-event': ev.userResponse?.response == 'Unsubscribed' }" button
            @click.stop="(showTeacherActions || disableCardClick) ? null : openServiceModal(ev.serviceId, ev)" v-else>

    <!-- Event Details -->
    <ion-item lines="none" v-show="!hideEventDetails && !hideEventName" :class="{ 'past-event': isPastEvent(ev) }">
      <ion-label class="ion-text-wrap ion-no-margin">
        <p v-if="showStudentFormsOnly && (ev.classes || ev.studentForms)">
          <span>{{ ev.classes || ev.studentForms }}</span>
        </p>
        <p v-else>
          <span v-if="ev.formattedName" style="white-space: pre-line" v-html="ev.formattedName"></span>
          <b style="color: var(--ion-color-dark)" v-else>{{ ev.name }}</b>
          <span v-if="ev.description">: {{ ev.description }}</span>
          <small v-show="ev.serviceId"> (<u>Details</u>)</small>
          <span v-if="ev.classes || ev.studentForms"> ({{ ev.classes || ev.studentForms }})</span>
        </p>
        <!--<p style="line-height: 1" v-if="ev.remarks"><small>{{ ev.remarks }}</small></p>-->
      </ion-label>
    </ion-item>
    
    <ion-list class="ion-no-padding">
      <div v-show="!hideEventDetails">
        <!-- Event Date & Time -->
        <ion-item lines="full" button>
          <ion-label class="ion-text-wrap ion-no-margin">
            <h2>
              <span v-if="ev.status == 'TBC'">[Tentative] </span>
              <i>{{ ev.formattedDateTime }}</i>
              <span v-if="ev.employerName"> ({{ ev.employerName }})</span>
            </h2>
            <p>{{ ev.mode == 'Online' ? 'Online' : `${ev.venue || 'Campus'}` }}</p>
            <p style="line-height: 1"><small>{{ ev.language }}</small></p>
            <p v-if="ev.remainingSeats != ''"><small><b>{{ Math.max(0, ev.remainingSeats) }}</b> seat{{ev.remainingSeats > 1 ? 's' : ''}} left</small></p>
          </ion-label>
          <!--<ion-badge>3 nominated</ion-badge>-->
          <!--
          <ion-button class="no-text-transform" color="medium" size="small" fill="clear"
                      @click.stop="updateEventResponse(ev, ev.userResponse?.response == 'Unsubscribed' ? '' : 'Unsubscribed')"
                      v-show="!user.teacher && ['', 'No', 'Unsubscribed'].includes(ev.userResponse?.response || '')">
            <small>{{ ev.userResponse?.response == 'Unsubscribed' ? 'Unsubscribed' : 'Unsubscribe' }}</small>
          </ion-button>-->
        </ion-item>

        <!-- Teacher-only Actions -->
        <ion-accordion-group v-if="user.teacher && showTeacherActions">
          <ion-accordion style="--min-height: 24px">
            <ion-item class="teacher-actions-toggle-item" lines="full" slot="header" style="--min-height: 24px; --background: #800020">
              <ion-label class="ion-text-wrap">
                <p style="color: #fff">For teachers / principals</p>
              </ion-label>
            </ion-item>
            <ion-list class="ion-no-padding" slot="content">
              <!-- Nominate -->
              <ion-item color="light" lines="full" button detail @click.stop="openStudentModal(ev)"
                        v-if="!['jobEX'].includes(ev.group) && !user.isSecondaryStudent && (user.teacher || user.isAdmin)">
                <!--<ion-icon slot="start" :icon="peopleOutline"></ion-icon>-->
                <ion-label>
                  <h3><b>Browse applicants / Nominate</b></h3>
                </ion-label>
              </ion-item>

              <!-- Submit Choice -->
              <ion-item color="light" lines="full" button detail @click.stop="openServiceModal(ev.serviceId, ev)" v-if="user.teacher && ev.serviceId">
                <ion-icon slot="start" :icon="relatedTeacherResponse ? checkmarkCircle : alertCircle"
                                        :color="relatedTeacherResponse ? 'success' : 'danger'"></ion-icon>
                <ion-label>
                  <h3><b>Indicate your choice</b></h3>
                </ion-label>
              </ion-item>
            </ion-list>
          </ion-accordion>
        </ion-accordion-group>
      </div>

      <!-- Apply / Withdraw -->
      <div v-show="!hideApplyButton && !['ABS', 'Others'].includes(ev.group)">
        <ion-item @click.stop="(user.teacher || user.isAdmin) ? updateEventResponse(ev, 'No') : undefined" color="success" button
                  v-if="ev.userResponse?.confirmed == 'Yes'">
          <ion-icon slot="start" :icon="checkmarkCircle"></ion-icon>
          <ion-label>Applied</ion-label>
        </ion-item>

        <ion-item @click.stop="updateEventResponse(ev, 'Yes')" color="light" button detail v-else-if="!['work-workshop-2'].includes(ev.anchorEventId)">
          <ion-label>
            <h3>Apply</h3>
          </ion-label>
        </ion-item>
      </div>

      <!-- Teacher Nominate Teacher -->
      <ion-item color="danger" lines="full" button detail v-show="ev.group == 'Teacher Event' && ev.userResponse?.confirmed == 'Yes'" @click.stop="openTeacherModal(ev)">
        Invite 1 teacher to join
      </ion-item>

      <!-- Action Buttons -->
      <div v-show="ev.group == 'ABS' || ev.userResponse?.confirmed == 'Yes'">
        <!-- Tasks before lesson (pre-seminar) -->
        <ion-item v-for="task in anchorEventTasks.filter(t => t.anchorEventId == ev.anchorEventId && !t.showAfterAttended && (!isFormQuestionTask || t.formQuestions.length > 0))"
                  :key="task.id" color="light" lines="full" button :detail="checkShowTaskDetailIcon(task, ev)" @click.stop="checkOpenTaskModal(task, ev)">
          <ion-spinner slot="start" v-if="isFormQuestionTask(task) && loadingUserFormResponses"></ion-spinner>
          <ion-icon slot="start" :icon="isSubmittedTaskForm(task, ev) ? checkmarkCircle : alertCircle" :color="isSubmittedTaskForm(task, ev) ? 'success' : 'danger'"
                    v-if="isFormQuestionTask(task) && !loadingUserFormResponses"></ion-icon>
          <ion-label>
            <h3><b>{{ task.title }}</b></h3>
            <p>{{ task.group }}</p>
          </ion-label>
        </ion-item>

        <!--
          Join Online Meeting (Enabled 60 mins before session starts)
        -->
        <ion-item color="light" lines="full" target="_blank" :href="ev.onlineMeetingLink" button detail
                  v-if="ev.mode == 'Online' && ev.onlineMeetingLink" :disabled="!isSessionStarted(ev)">
          <ion-icon slot="start" :icon="logInOutline"></ion-icon>
          <ion-label>
            <h3>Meeting link</h3>
            <small>Will be enabled 60 minutes before session starts</small>
            <!--<p v-if="ev.onlineMeetingPsw">Passcode: {{ ev.onlineMeetingPsw }}</p>-->
          </ion-label>
        </ion-item>

        <!--
          Take Attendance (Enabled 60 mins before session starts)
        -->
        <ion-item @click.stop="openRollCallCodePrompt(ev)" color="light" lines="full" button :detail="ev.userResponse?.attended != 'Yes'"
                  v-if="ev.rollCallCode" :disabled="!user.isAdmin && !isSessionStarted(ev)">
          <ion-icon slot="start" :icon="ev.userResponse?.attended == 'Yes' ? checkmarkCircle : alertCircle"
                    :color="ev.userResponse?.attended == 'Yes' ? 'success' : 'danger'"></ion-icon>
          <ion-label>
            <h3><b>Take attendance</b></h3>
            <small>Will be enabled after session starts</small>
          </ion-label>
        </ion-item>

        <!-- (For workshop only) Vote for best presenter -->
        <ion-item color="light" lines="full" button detail @click.stop="openSessionStudentListModal(ev.id)"
                  v-if="['work-workshop-2'].includes(ev.anchorEventId)">

          <ion-icon slot="start" :icon="user.userUsers.some(uu => uu.sessionId == ev.id) ? checkmarkCircle : alertCircle"
                                  :color="user.userUsers.some(uu => uu.sessionId == ev.id) ? 'success' : 'danger'"></ion-icon>
          <ion-label>
            <h3><b>Vote for best presenters</b></h3>
            <small>During Lesson</small>
          </ion-label>
        </ion-item>

        <!-- SLP Modal -->
        <ion-item color="light" @click.stop="openSLPModal()" lines="full" button detail
                  v-if="['work-campus-visit','work-workshop-2'].includes(ev.anchorEventId) && (['readonly', 'full'].includes(user.slp?.accessType || ''))">
                  <!--v-if="['work-campus-visit','work-workshop-2'].includes(ev.anchorEventId)">-->

          <ion-icon slot="start" :icon="user.slp?.gpt ? checkmarkCircle : alertCircle"
                                  :color="user.slp?.gpt ? 'success' : 'danger'"></ion-icon>
          <ion-label>
            <h3><b>AchieveSLP</b></h3>
            <small>During Lesson</small>
          </ion-label>
        </ion-item>

        <!-- Tasks during & after lessons -->
        <div v-if="ev.userResponse?.attended == 'Yes'">
          <ion-item v-for="task in anchorEventTasks.filter(t => t.anchorEventId == ev.anchorEventId && t.showAfterAttended && t.group != 'After Lesson')" :key="task.id"
                    lines="full" button :detail="checkShowTaskDetailIcon(task, ev)" @click.stop="checkOpenTaskModal(task, ev)" color="light">
            <!-- Profession Survey -->
            <ion-icon slot="start" v-if="isProfessionSurveyTask(task)"
                      :icon="submittedProfessionResponses ? checkmarkCircle : alertCircle"
                      :color="submittedProfessionResponses ? 'success' : 'danger'"></ion-icon>

            <!-- Form Question -->
            <ion-spinner slot="start" v-if="isFormQuestionTask(task) && loadingUserFormResponses"></ion-spinner>
            <ion-icon slot="start" v-if="isFormQuestionTask(task) && !loadingUserFormResponses"
                      :icon="isSubmittedTaskForm(task, ev) ? checkmarkCircle : alertCircle"
                      :color="isSubmittedTaskForm(task, ev) ? 'success' : 'danger'"></ion-icon>

            <ion-label>
              <h3><b>{{ task.title }}</b></h3>
              <small>{{ task.group }}</small>
            </ion-label>
          </ion-item>

          <!-- After Lesson (e.g. Feedback Form) -->
          <ion-item v-for="task in anchorEventTasks.filter(t => t.anchorEventId == ev.anchorEventId && t.showAfterAttended && t.group == 'After Lesson' && (!isFormQuestionTask(t) || getFilteredFormQuestions(t).length > 0))"
                    :key="task.id" lines="full" button :detail="checkShowTaskDetailIcon(task, ev)" @click.stop="checkOpenTaskModal(task, ev)"
                    :color="isSubmittedTaskForm(task, ev) ? 'success' : 'light'">

            <!-- Form Question -->
            <ion-spinner slot="start" v-if="isFormQuestionTask(task) && loadingUserFormResponses"></ion-spinner>
            <ion-icon slot="start" v-if="isFormQuestionTask(task) && !loadingUserFormResponses"
                      :class="{ 'icon-xxl': isFeedbackForm(task) && isSubmittedTaskForm(task, ev) }"
                      :icon="isSubmittedTaskForm(task, ev) ? checkmarkCircle : alertCircle"
                      :color="isSubmittedTaskForm(task, ev) ? undefined : 'danger'"></ion-icon>

            <ion-label v-if="isFeedbackForm(task) && isSubmittedTaskForm(task, ev)">
              <h1><b>Feedback form filled</b></h1>
            </ion-label>
            <ion-label v-else>
              <h3><b>{{ task.title }}</b></h3>
              <small>{{ task.group }}</small>
            </ion-label>
          </ion-item>
        </div>
      </div>
    </ion-list>
  </ion-card>
</template>

<script lang="ts">
// Vue reactivity
import { computed, ref } from 'vue';

// icons
import { checkmarkCircle, alertCircle, logInOutline, eyeOutline, peopleOutline, pencil, } from 'ionicons/icons';

// components
import { IonCard, IonList, IonItem, IonLabel, IonIcon, IonSpinner, IonChip, IonButton,
        IonAccordionGroup, IonAccordion,
        loadingController, alertController, modalController } from '@ionic/vue';
import ServiceModal from '@/components/modals/ServiceModal.vue';
import StudentModal from '@/components/teacher/SchoolStudentModal.vue';

import ABProfessionSurveyModal from '@/components/pss/profession/ABProfessionSurveyModal.vue'; // ABS only
import FormQuestionModal from '@/components/modals/FormQuestionModal.vue'; // Event Tasks
import SLPModal from '@/components/secondary/ABSLPModal.vue';
import SchoolTeacherModal from '@/components/teacher/SchoolTeacherModal.vue';
import SessionStudentListModal from '@/components/modals/SessionStudentListModal.vue';

// composables
import { useStore } from '@/store';
import { utils } from '@/composables/utils';

// services
import EventService from '@/services/EventService';

// types
import { AnchorEventTask, Session } from '@/types';
import SLPService from '@/services/SLPService';
import PortalService from '@/services/PortalService';
import CommonService from '@/services/CommonService';

export default {
  props: [
    "ev",
    "user",
    "noStrip",
    "hideApplyButton",
    "hideEventDetails",
    "hideEventName",
    "showTeacherActions",
    "showStudentFormsOnly",
    "useAccordionView",
    "disableCardClick"
  ],
  components: { IonCard, IonList, IonItem, IonLabel, IonIcon, IonSpinner, IonChip, IonButton,
                IonAccordionGroup, IonAccordion, },
  setup(props) {
    const { presentAlert, openModal, promptRollCallCode, presentPrompt } = utils();
    
    // store data
    const store = useStore();
    const relatedTeacherResponse = computed(() => store.getters.getTeacherResponseByEventId(props.ev?.id));
    const anchorEventTasks = computed<AnchorEventTask[]>(() => store.state.anchorEventTasks);
    const loadingUserFormResponses = computed(() => store.state.loadingUserFormResponses);
    const userFormResponses = computed(() => store.state.userFormResponses);
    const submittedProfessionResponses = computed(() => store.state.submittedProfessionResponses);
    const user = computed(() => store.state.user);

    // For SLP support / JUPAS choice support
    const loadingPortalData = computed(() => store.state.loadingPortalData); 
    const fetchedJUPASChoiceSupportData = ref(false);

    // ABS related methods
    const getFilteredFormQuestions = (task: AnchorEventTask) => (task.formQuestions.filter(q => (q.workServiceIds.length == 0 || q.workServiceIds.includes(props.ev.serviceId))));
    const isFormQuestionTask = (task: AnchorEventTask) => (task.formQuestions.length > 0);
    const isFeedbackForm = (task: AnchorEventTask) => (isFormQuestionTask(task) && task.title.toLowerCase().includes("feedback"));
    const isProfessionSurveyTask = (task: AnchorEventTask) => (task.id == "abs-profession-survey");
    const getTaskResponses = (tid: string, eid: string) => (userFormResponses.value.filter(r => r.sessionId == eid && r.taskId == tid));
    const isSubmittedTaskForm = (task: AnchorEventTask, ev: Session) => (getTaskResponses(task.id, ev.id).length > 0);

    return {
      // icons
      checkmarkCircle, alertCircle, logInOutline, eyeOutline, peopleOutline, pencil,

      // methods
      openServiceModal: async (serviceId: any, event: any) => {
        if (serviceId || event.posterLink) {
          await openModal(ServiceModal, { serviceId, event, hideTeacherResponse: true });
        }
      },

      isPastEvent: (ev: any) => {
        return ev.userResponse?.attended != 'Yes' && new Date().getTime() > new Date(ev.endTimeStr).getTime()+60*60000;
      },

      openRollCallCodePrompt: async (targetEvent: Session) => {
        if (targetEvent.userResponse?.attended == 'Yes') {
          return; // users already taken roll call
        }
        await promptRollCallCode(async (rollCallCode: any) => {
          const loading = await loadingController.create({ duration: 30000 });
          await loading.present();
          if (rollCallCode.toLowerCase() == (targetEvent.rollCallCode || '').toLowerCase()) {
            if (['work-seminar', 'work-campus-visit', 'work-workshop-2'].includes(targetEvent.anchorEventId)) {
              // Enable SLP Access for Work Seminars & Campus workshop
              const existSLPRecord = store.state.user.slp != null;
              SLPService.enableUserSLPAccess(existSLPRecord);
              store.commit('upsertUserSLP', { accessType: 'full' });
            }
            const { phone, schoolId, currIntakeId } = props.user;
            const responseObj = await EventService.updateEventResponse(targetEvent, "Attended", phone, schoolId, currIntakeId, rollCallCode);
            targetEvent.userResponse = responseObj;
            //if (targetEvent.userResponse) targetEvent.userResponse.attended = 'Yes';
            //else targetEvent.userResponse = { attended: 'Yes' };
            store.commit('injectUserEventResponse', { ...responseObj, lessonId: targetEvent.anchorEventId });
          } else {
            presentAlert("Incorrect roll call code. Please try again.");
          }
          alertController.dismiss();
          loading.dismiss();
        });
      },
      updateEventResponse: async (targetEvent: Session, response: any) => {
        if (props.user.teacher && !["Teacher Event"].includes(targetEvent.group)) {
          presentAlert("This function is for students only");
        } else {
          const action = response == 'Yes' ? 'Apply for' : (response == 'No' ? 'Withdraw your application for' : 'Unsubscribe');
          const msg = `${action} event <b>${targetEvent.name}</b> on <b>${targetEvent.formattedDateTime}</b>?`;
          await presentPrompt(msg, async () => {
            const loading = await loadingController.create({ });
            await loading.present();
            const { phone, schoolId, currIntakeId } = props.user;
            const responseObj = await EventService.updateEventResponse(targetEvent, response, phone, schoolId, currIntakeId);
            store.commit('injectUserEventResponse', { ...responseObj, lessonId: targetEvent.anchorEventId });
            loading.dismiss();
          });
        }
      },
      isSessionStarted: (ev: any) => {
        //return new Date(new Date(ev.startTimeStr).getTime() - 60*60*1000).getTime() < new Date().getTime();
        return new Date(ev.date).getTime() < new Date().getTime();
      },

      // Event Tasks
      anchorEventTasks,
      loadingUserFormResponses, submittedProfessionResponses,
      isProfessionSurveyTask,
      isFeedbackForm, isFormQuestionTask, isSubmittedTaskForm,
      getFilteredFormQuestions,
      checkOpenTaskModal: async (task: AnchorEventTask, ev: Session) => {
        if (isProfessionSurveyTask(task)) return await openModal(ABProfessionSurveyModal, {}); // ABS Only
        if (isFormQuestionTask(task)) {
          const { id: taskId, title, } = task;
          const formTitle = `${ev.anchorEventName} ${title}`; // modal title
          const oldResponses = getTaskResponses(taskId, ev.id);
          const formQuestionIds = getFilteredFormQuestions(task).filter(q => (q.workServiceIds.length == 0 || q.workServiceIds.includes(ev.serviceId))).map(q => q.id);
          return await openModal(FormQuestionModal, { formTitle, formQuestionIds, taskId, sessionId: ev.id, oldResponses });
        }
      },
      checkShowTaskDetailIcon: (task: AnchorEventTask, ev: Session) => {
        if (isProfessionSurveyTask(task)) return !submittedProfessionResponses.value;
        if (isFormQuestionTask(task)) return !isSubmittedTaskForm(task, ev);
        return false;
      },

      // For Teacher ONLY
      relatedTeacherResponse,
      openStudentModal: async (nominatingEvent: Session) => (await openModal(StudentModal, { nominatingEvent })),
      openTeacherModal: async (teacherEvent: Session) => {
          await openModal(SchoolTeacherModal, { teacherEvent });
      },

      // SLP / JUPAS choice support modals
      openSLPModal: async () => {
        const { ev } = props;
        return await openModal(SLPModal, { ev, serviceId: ev?.serviceId, relatedProgramId: ev?.relatedProgramId }, "", false)
      },

      // Voting for best presenters / outstanding participants
      openSessionStudentListModal: async (sessionId) => (await openModal(SessionStudentListModal, { sessionId, isStudentView: true })),
    }
  }
}
</script>

<style scoped>
  .no-strip {
    border-left: none !important;
  }
  ion-item ion-icon[slot="start"] {
    margin-inline-end: 12px;
    margin-top: 0;
    margin-bottom: 0;
  }
  ion-item::part(native) {
    padding-inline-start: 12px !important;
  }
  .teacher-actions-toggle-item::part(native) {
    height: 24px;
  }
  .teacher-actions-toggle-item ion-icon {
    color: #fff !important;
  }
  .event-card {
    /*border-left: 8px solid #EF6C00;*/
  }
  .event-card ion-chip {
    height: 28px;
  }
  .event-card ion-card-header {
    padding-bottom: 8px !important;
  }
  .past-event {
    opacity: 0.5;
  }
  .unsubscribed-event ion-list {
    opacity: 0.5;
  }
</style>