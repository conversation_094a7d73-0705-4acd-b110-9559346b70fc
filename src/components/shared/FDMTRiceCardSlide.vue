<template>
  <div style="height: 100%">
    <swiper
      class="card-slides"
      :effect="'cards'"
      :modules="modules"
    >
      <swiper-slide class="card-slide" style="background-color: var(--ion-color-primary)">
        <ion-row>
          <ion-col size="12">
            <logo-img :useWhiteLogo="true" :withText="true" style="width: 250px" />
          </ion-col>
        </ion-row>
      </swiper-slide>
    </swiper>
  </div>
</template>

<script lang="ts">
// Vue reactivity
import { computed, defineComponent, onMounted, reactive, ref, watchEffect } from 'vue';

// icons
import { } from 'ionicons/icons';

// Swiper
import 'swiper/swiper.min.css';
import 'swiper/modules/effect-cards/effect-cards.min.css';
import 'swiper/modules/navigation/navigation.min.css';
import '@ionic/vue/css/ionic-swiper.css';
import { Swiper, SwiperSlide } from 'swiper/vue/swiper-vue';
import { EffectCards, Navigation, } from 'swiper';

// components
import { IonicSlides, IonCol, IonRow, } from '@ionic/vue';

export default {
  props: [],
  emits: [],
  components: { IonCol, IonRow, Swiper, SwiperSlide, },
  setup(props, { emit }) {
    return {
      // swiper
      modules: [EffectCards, IonicSlides, Navigation],
    }
  }
}
</script>

<style scoped>
</style>