<template>
  <!--
    <PERSON><PERSON> Swiper (slot storing swiper-slide)
    -->
  <div v-if="useSlotAsSwiperSlides">
    <swiper
        style="min-height: 150px; height: 15vh; width: 90vw; max-width: 400px"
        :navigation="true"
        :id="sliderId || 'card-swiper-slides'"
        :grabCursor="true"
        :modules="modules"
        :slides-per-view="1"
        v-if="!delayLoading"
    >
      <slot />
    </swiper>
  </div>

  <div v-else-if="homeBanners">
    <swiper
      class="responsive-swiper"
      id="main-slides"
      :pagination="true"
      :navigation="true"
      :modules="modules"
      :slides-per-view="1"
      :spaceBetween="10"
      :autoplay="{ delay: 5000 }"
      :speed="2000"
      :loop="true"
      :effect="'fade'"
    >
      <!-- FDMT White Tram Slide -->
      <swiper-slide>
        <video id="video-player" class="slide-media" muted autoplay playsinline loop>
          <source :src="require('@/assets/home/<USER>')" type="video/mp4">
        </video>
      </swiper-slide>

      <!-- Data-driven career education -->
      <swiper-slide class="slide-container">
        <div class="slide-text">
          <p>Data-driven career education<br /><small>across universities and secondary schools</small></p>
        </div>
        <video id="video-player" class="slide-media" muted autoplay playsinline loop>
          <source :src="require('@/assets/home/<USER>')" type="video/mp4">
        </video>
      </swiper-slide>

      <!-- 500+ secondary school teachers -->
      <swiper-slide class="slide-container">
        <div class="slide-text">
          <p>500+ secondary school teachers help<br />connect passionate students</p>
        </div>
        <img class="slide-media" :src="require('@/assets/home/<USER>')" />
      </swiper-slide>

      <!-- FDMT Blue Tram Slide -->
      <swiper-slide>
        <video id="video-player" class="slide-media" muted autoplay playsinline loop>
          <source :src="require('@/assets/home/<USER>')" type="video/mp4">
        </video>
      </swiper-slide>

      <!-- AI-empowered career education -->
      <swiper-slide class="slide-container">
        <div class="slide-text">
          <p>AI-empowered career education</p>
        </div>
        <img class="slide-media" :src="require('@/assets/home/<USER>')" />
      </swiper-slide>

      <!-- Many Photos -->
      <swiper-slide class="slide-container">
        <img class="slide-media" :src="require('@/assets/website/banner.jpeg')" />
      </swiper-slide>
    </swiper>
  </div>
    
  <div style="height: 100%" v-if="videoIds">
    <swiper
      id="main-slides"
      :pagination="true"
      :navigation="true"
      :modules="modules"
      :slides-per-view="1"
      :spaceBetween="10"
      :thumbs="{ swiper: thumbsSwiper }"
    >
      <swiper-slide v-for="vid in videoIds" :key="vid">
        <iframe class="responsive-embed" style="width: 80%; height: 380px" :src="`https://www.youtube.com/embed/${vid}`" frameborder="0" allowfullscreen></iframe>
      </swiper-slide>
    </swiper>

    <swiper
      @swiper="setThumbsSwiper"
      :spaceBetween="10"
      :slidesPerView="3"
      :freeMode="true"
      :watchSlidesProgress="true"
      :modules="modules"
    >
      <swiper-slide v-for="vid in videoIds" :key="vid">
        <img :src="`https://img.youtube.com/vi/${vid}/mqdefault.jpg`" />
      </swiper-slide>
    </swiper>
  </div>

  <div style="height: 100%" v-else-if="imageLinks">
    <swiper
      id="main-slides"
      :pagination="true"
      :navigation="true"
      :modules="modules"
      :slides-per-view="1"
      :spaceBetween="10"
      :thumbs="{ swiper: thumbsSwiper }"
    >
      <swiper-slide v-for="imgLink in imageLinks" :key="imgLink">
        <img :src="imgLink" />
      </swiper-slide>
    </swiper>

    <swiper
      @swiper="setThumbsSwiper"
      :spaceBetween="10"
      :slidesPerView="3"
      :freeMode="true"
      :watchSlidesProgress="true"
      :modules="modules"
    >
      <swiper-slide v-for="imgLink in imageLinks" :key="imgLink">
        <img :src="imgLink" />
      </swiper-slide>
    </swiper>
  </div>
</template>

<script lang="ts">
// Vue reactivity
import { computed, defineComponent, onMounted, reactive, ref, watchEffect } from 'vue';

// icons
import { } from 'ionicons/icons';

// Swiper
import 'swiper/swiper.min.css';
import 'swiper/modules/effect-fade/effect-fade.min.css';
import 'swiper/modules/effect-cards/effect-cards.min.css';
import 'swiper/modules/navigation/navigation.min.css';
import '@ionic/vue/css/ionic-swiper.css';
import { Swiper, SwiperSlide } from 'swiper/vue/swiper-vue';
import { EffectCards, FreeMode, Navigation, Pagination, Scrollbar, Thumbs, Autoplay, EffectFade, } from 'swiper';

// components
import { IonicSlides, IonCol, IonRow, } from '@ionic/vue';

// composables
import { utils } from '@/composables/utils';

export default {
  props: [
    "videoIds", "imageLinks", "homeBanners",
    "useSlotAsSwiperSlides",
    "sliderId",
  ],
  emits: [],
  components: { IonCol, IonRow, Swiper, SwiperSlide, },
  setup(props, { emit }) {
    const { isMobileWeb } = utils();

    const delayLoading = ref(true);
    const thumbsSwiper = ref(null);

    const setThumbsSwiper = (swiper) => {
      thumbsSwiper.value = swiper;
    }
    onMounted(() => {
      setTimeout(() => {
        // Init card slides
        delayLoading.value = false;
      }, 200);
    })

    return {
      thumbsSwiper, setThumbsSwiper,

      isMobileWeb,
      delayLoading,

      // swiper modules
      modules: [IonicSlides, EffectCards, Navigation, Pagination, FreeMode, Scrollbar, Thumbs, Autoplay, EffectFade],
    }
  }
}
</script>

<style scoped>
  .slide-container {
    position: relative;
    width: 100%;
  }
  .slide-media {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
  }
  .slide-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    color: white;
    text-align: center;
    width: 80%;
    /* Optional: add text shadow for better visibility */
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  }
  .slide-text p {
    font-size: clamp(1.2rem, 3vw, 2.5rem);
    line-height: 1.4;
    margin: 0;
  }
  /* Optional: add background overlay for better text visibility */
  .slide-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3); /* semi-transparent overlay */
    pointer-events: none;
  }

  /* Responsive adjustments */
  .responsive-swiper {
    height: 680px;
  }
  @media (max-width: 768px) {
    .responsive-swiper {
      height: 250px;
    }
  }
</style>