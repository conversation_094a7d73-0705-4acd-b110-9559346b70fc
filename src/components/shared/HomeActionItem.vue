<template>
<!--<ion-item :color="isLesson ? 'light' : undefined" -->
  <ion-item :color="color || undefined" lines="full" button :detail="!isLoading" :disabled="isDisabled || isLoading">
    <ion-icon slot="start" :icon="getItemIcon()" :color="getItemIconColor()"
                            :class="{ 'extra-indent': !isLesson && !noIndent }" v-if="!hideIcon"></ion-icon>
    <!--<ion-label class="ion-text-wrap" :class="{ 'extra-indent': hideIcon == true }">-->
    <ion-label class="ion-text-wrap" :class="{ 'extra-indent': hideIcon == true && !noIndent }" :color="textColor || undefined">
      <span><b>{{ title }}</b></span>
    </ion-label>
    <!--
    <ion-note class="ion-no-padding ion-no-margin" style="margin-top: 4px" slot="end" :class="{ 'full-height-text': !extraNote2 }"
              v-if="extraNote1 || extraNote2">
      {{ extraNote1 }}<br v-if="extraNote2" />
      {{ extraNote2 }}
    </ion-note>
    -->
    <ion-spinner slot="end" v-if="isLoading"></ion-spinner>
  </ion-item>
</template>

<script lang="ts">
// vue
import { computed } from 'vue';

// icon
import { checkmarkCircle, alertCircle, createOutline, pencil,
        handRightOutline, peopleOutline, personOutline, calendarClearOutline, calendarOutline, } from 'ionicons/icons';

// components
import { IonItem, IonIcon, IonLabel, IonNote, IonSpinner, } from '@ionic/vue';

export default {
  props: [
    "color",
    "title",
    "extraNote1",
    "extraNote2",
    "isDone",
    "hideIcon",
    "emptyIcon",
    "isLesson",
    "isDisabled",
    "noIndent",
    "isLoading",
    "textColor"
  ],
  components: {
    IonItem, IonIcon, IonLabel, IonNote, IonSpinner,
  },
  setup(props) {
    return {
      // icons
      checkmarkCircle, alertCircle, createOutline, pencil,
      handRightOutline, peopleOutline, personOutline, calendarClearOutline, calendarOutline,

      // methods
      getItemIcon: () => {
        const { emptyIcon, isDone, isLesson } = props;
        return null;
        /*
        if (emptyIcon) return null;
        //if (isDone) return checkmarkCircle;
        //if (isLesson) return calendarClearOutline;
        //if (isLesson) return calendarOutline;
        //if (isLesson) return createOutline;
        return createOutline;*/
      },
      getItemIconColor: () => {
        const { isDone, isLesson } = props;
        //if (isDone) return 'success';
        //return (isLesson ? undefined : 'danger')
        return undefined;
      }
    }
  }
}
</script>

<style scoped>
  ion-item::part(native) {
    padding-inline-start: 0;
  }
  .extra-indent {
    margin-left: 16px !important;
  }
  ion-icon[slot="start"] {
    margin: 2px;
  }
  .full-height-text {
    line-height: 24px;
  }
</style>