/**
  Segment Buttons
 */
.red-indicator-segment ion-segment-button::part(indicator-background) {
  background: var(--ion-color-fdmtred) !important;
}
.blue-indicator-segment ion-segment-button::part(indicator-background) {
  background: var(--ion-color-primary) !important;
}

/**
 * Half Image
 */
.half-image {
  width: 100%;
  aspect-ratio: 4/1;
  background-size: cover;
  background-position: center bottom; /* or 'center bottom' for bottom half */
}

/* Styling for the Details button in AG Grid */
.ag-theme-alpine .details-btn,
.ag-theme-alpine-dark .details-btn {
  --padding-top: 4px;
  --padding-bottom: 4px;
  --padding-start: 8px;
  --padding-end: 8px;
  margin: 0;
  height: 28px;
  text-transform: none;
}

/**
 * Home page (teacher / student views)
 */
.class-switcher-toolbar {
  background: var(--ion-color-fdmtred);
  --background: var(--ion-color-fdmtred);
  padding-left: 4px;
}
.class-switcher {
  grid-auto-columns: auto;
  justify-content: flex-start;
}
.class-switcher ion-segment-button {
  min-width: 36px !important;
  width: min-content;
}
body.blue-bg, body.blue-bg .view-switcher-toolbar {
  background: rgb(0, 80, 125); /* For home icon page (fdmt.hk) only */
  --background: rgb(0, 80, 125); /* For home icon page (fdmt.hk) only */
}
body.blue-bg ion-content, body.blue-bg ion-segment {
  --background: transparent;
}
body.blue-bg .blue-bg-text, body.blue-bg .blue-bg-text a {
  color: #fff !important;
}
.sticky-toolbar {
  position: sticky;
  top: 0;
  z-index: 1000;
  --background: var(--ion-background-color);
  --min-height: 36px;
}
.view-switcher {
  --background: var(--ion-background-color);
}
body.blue-bg .view-switcher, .class-switcher {
  color: #fff !important;
  --background: transparent;
}
body.blue-bg .view-switcher ion-segment-button, .class-switcher ion-segment-button {
  --color-checked: rgb(66, 66, 66) !important;
  --background-checked: #fff;
}
body.blue-bg .view-switcher ion-segment-button::part(indicator-background), .class-switcher ion-segment-button::part(indicator-background) {
  background: #fff;
}

.outer-card div[slot="content"] {
  /*padding-left: 16px;*/
}
.outer-card {
  box-shadow: none;
  margin-inline: 1px;
}
.outer-card ion-item {
  --border-width: 0 !important;
  --background: var(--ion-background-color) !important;
}
.class-bar ion-item ion-icon {
  margin-inline-start: 16px;
}
.class-bar {
  margin-inline: 0;
}
.ion-text-wrap.sc-ion-label-md-h, [text-wrap].sc-ion-label-md-h {
  line-height: 1.25;
}

/**
 * AchieveJUPAS
 */
.band-a { --background: #ffcccc; }
.band-b { --background: #ffda8f; }
.band-c { --background: #ffff99; }
.band-d { --background: #99ffcc; }
.band-e { --background: #b2d7f8; }
.band-other { --background: var(--ion-color-medium); }

.add-tag-btn {
  color: var(--ion-color-medium);
  margin: 0;
  height: 24px;
  width: 24px;
  min-height: 24px;
  --padding-start: 0;
  --padding-end: 0;
  vertical-align: middle;
}

.add-tag-btn ion-icon {
  font-size: 20px;
  margin: 0;
}

/**
 * ChatGPT e.g. explanation (smaller font size)
 */
.gpt-explanation p {
  font-size: 11px !important;
  line-height: 1.5 !important;
}
.trimmed-gpt-explanation p {
  font-size: 13px !important;
  line-height: 1.5 !important;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  line-clamp: 3;
  -webkit-line-clamp: 3; 
  -webkit-box-orient: vertical;
}
.gpt-textarea {
  --placeholder-font-weight: bold;
}
.message p, .message ol {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  line-height: 1.5;
}

/**
 * Result page add button
 */
.add-item-btn {
  text-transform: none;
  font-size: 16px;
}

/**
 * Small modal for action ack
 */
ion-modal.ack-modal {
  --width: 300px !important;
  --height: fit-content !important;
  --border-radius: 6px;
}
.small-modal::part(content) {
  --width: 300px !important;
  --height: 65% !important;
}
.login-modal::part(content) {
  --max-width: 600px;
  --height: 300px !important;
  overflow: scroll;
}
ion-modal.stack-modal {
  --box-shadow: 0 28px 48px rgba(0, 0, 0, 0.4) !important;
  --backdrop-opacity: var(--ion-backdrop-opacity, 0.32) !important;
}
.form-modal::part(content) {
  --max-width: 600px;
  --height: 400px !important;
  overflow: scroll;
}

/**
 * Icon sizes
 */
.icon-xxl {
  font-size: 64px;
  margin: 0 auto;
}

/**
  * Filter Panel
  */
.nav-category-btn {
  width: 30px !important;
  height: 24px !important;
}
.nav-category-btn::part(native) {
  padding: 0 !important;
}
.filter-group-segment ion-label {
  font-size: 12px !important;
}
.filter-group-segment::-webkit-scrollbar {
  display: none;
}
.filter-group-segment ion-segment-button {
  text-transform: none;
  margin-top: 0;
  margin-bottom: 0;
  min-height: 24px;
  min-width: 35px;
}
.filter-group-segment ion-segment-button::part(native) {
  --padding-start: 8px;
  --padding-end: 8px;
}
.material-div {
  height: 27vh;
  overflow: scroll;
  padding: 8px;
  padding-bottom: 0;
}
.material-div ion-chip {
  font-size: 11px;
  height: 24px;
  margin: 1px;
  padding-top: 3px;
  padding-bottom: 3px;
}

/**
 * Card Slides (professions / disciplines)
 */
.program-slides {
  margin-top: 16px;
  width: 100%;
  min-height: 260px;
  height: 70vh;
  overflow: hidden;
  --swiper-navigation-sides-offset: 0px !important;
  --swiper-navigation-top-offset: 220px !important;
  position: relative;
}
.program-slide {
  padding: 0 12px;
  background-color: transparent !important;
  display: block !important;
  height: 100%;
}
.program-slide-upper-section {
  border-radius: 18px;
  background-color: var(--ion-color-primary) !important;
  padding: 4px;
  position: relative;
  z-index: 1;
}
.program-slide-upper-section ion-fab {
  /*top: 46px;
  right: 60px;*/
}
.program-slide-upper-section ion-fab-button {
  margin: 0;
}
.program-slide-lower-section {
  height: 50vh;
  padding-bottom: 200px;
  overflow-y: scroll;
  padding-top: 8px;
  -webkit-overflow-scrolling: touch;
  position: relative;
  z-index: 0;
}
@media (max-width: 768px) {
  .program-slide {
    padding: 0 4px;
  }
}

.program-slides .swiper-button-prev,
.program-slides .swiper-button-next {
  height: 32px;
  width: 32px;
  margin-top: 0;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.program-slides .swiper-button-prev {
  left: 20px;
}
.program-slides .swiper-button-next {
  right: 20px;
}
.program-slides .swiper-button-prev::after,
.program-slides .swiper-button-next::after {
  font-size: 1rem;
  font-weight: bold;
  color: var(--ion-color-primary);
}

.active-tag-primary {
  color: #fff;
  background-color: var(--ion-color-primary) !important;
}
.active-tag {
  color: #fff;
  background-color: var(--ion-color-secondary) !important;
}
.chips-toolbar ion-col {
  padding: 0;
}
.valign {
  display: flex;
  align-items: center;
}
.highlighted-border {
  border: 3px solid var(--ion-color-dark);
}
.card-slides {
  margin-top: 16px;
  width: 80vw;
  max-width: 400px;
  min-height: 260px;
  height: 60vh;
  max-height: 420px;
  --swiper-navigation-sides-offset: -100px !important;
}
.card-slides h1 {
  margin-top: 0 !important;
}
.card-slide {
  border-radius: 18px;
  font-size: 22px;
  font-weight: bold;
  color: #fff;
}
.card-slide:nth-child(1n) {
  background-color: rgb(206, 17, 17);
}
.card-slide:nth-child(2n) {
  background-color: rgb(0, 140, 255);
}
.card-slide:nth-child(3n) {
  background-color: rgb(10, 184, 111);
}
.card-slide:nth-child(4n) {
  background-color: rgb(211, 122, 7);
}
.card-slide:nth-child(5n) {
  background-color: rgb(118, 163, 12);
}
.card-slide:nth-child(6n) {
  background-color: rgb(180, 10, 47);
}
.card-slide:nth-child(7n) {
  background-color: rgb(35, 99, 19);
}
.card-slide:nth-child(8n) {
  background-color: rgb(0, 68, 255);
}
.card-slide:nth-child(9n) {
  background-color: rgb(218, 12, 218);
}
.card-slide:nth-child(10n) {
  background-color: rgb(54, 94, 77);
}

/**
 * Tag Input Container
 */
.tag-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 8px;
  border-radius: 16px;
  margin: 0 8px;
}

.tag-input {
  --background: transparent;
  --padding-start: 8px;
  --padding-end: 8px;
  max-width: 200px;
  min-height: 48px !important;
}

.tag-input-container ion-button {
  margin: 0;
  height: 24px;
  width: 24px;
  --padding-start: 0;
  --padding-end: 0;
}