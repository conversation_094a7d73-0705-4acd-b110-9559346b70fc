@media only screen and (max-width: 768px) {
  .responsive-embed {
    height: 250px !important;
  }
  .slide-embed {
    height: 330px !important;
  }
  .short-segment-btn {
    width: min-content !important;
  }
}
.short-segment-btn {
  margin: 0 auto !important;
}
@media only screen and (min-width: 768px) {
  ion-modal:not(.datetime-modal) {
    --width: 600px !important;
    --height: 95vh !important;
    --max-height: 1600px;
  }
}

/**
 * Global
 */
 h2 {
  line-height: 1;
 }
:root {
  --ms-max-height: 30rem;
}
.allowSelectOneClick {
  -webkit-touch-callout:all !important; /* iOS Safari */
  -webkit-user-select:all !important;   /* Chrome/Safari/Opera */
  -khtml-user-select:all !important;    /* Konqueror */
  -moz-user-select:all !important;      /* Firefox */
  -ms-user-select:all !important;       /* Internet Explorer/Edge */
  user-select:all !important;           /* Non-prefixed version */
}
ion-grid {
  --ion-grid-width-lg: 992px;
  --ion-grid-width-xl: 1024px;
}
.searchbar-input.sc-ion-searchbar-md, .item.sc-ion-label-md-h, .item .sc-ion-label-md-h, ion-select {
  font-size: 14px !important;
  white-space: normal !important;
}
input[type=checkbox], input[type=radio] {
  margin-right: 10px;
  transform: scale(1.2);
}
ion-segment-button.ios {
  --border-radius: 18px !important;
}

/**
 * ion-accordion
 */
ion-accordion ion-item {
  /*--border-width: 0 0 1px 0;*/
}
ion-accordion ion-item ion-icon[slot="end"] {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

/**
 * ion-textarea
 */
.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md {
  transform: none !important;
  padding-top: 5px;
  margin-bottom: -5px;
}
ion-textarea {
  font-size: 14px !important;
}

/**
 * ion-card
 */
ion-card {
  border-radius: 0;
}
ion-card-title.ios {
  font-size: 20px;
}
.no-border-card {
  box-shadow: none;
}

/**
 * ion-item / ion-label
 */
ion-item {
  --min-height: 24px;
}
ion-item ion-label {
  margin: 3px 0px;
}
ion-label[slot="end"] p {
  text-align: right;
}

/**
 * ion-select (mainly alert interface)
 */
ion-select::part(text), ion-select::part(label) {
  white-space: normal;
}
.multiple-select-alert {
  --min-width: 320px;
}
.alert-checkbox-label {
  white-space: pre-line !important;
}
.alert-tappable.sc-ion-alert-md {
  height: 56px !important;
}
.alert-radio-label {
  white-space: pre-line !important;
  font-size: 13px !important;
}
.alert-tappable.sc-ion-alert-md {
  height: 40px !important;
}

/**
 * Badge / fixed element
 */
.top-badge {
  position: absolute;
  top: 2px;
}
.bottom-badge {
  position: absolute;
  bottom: 2px;
}
.top-left-fixed {
  position: fixed;
  top: 0;
  left: 0;
}
.top-left-badge {
  position: absolute;
  top: 0;
  left: 0;
}
.top-right-badge {
  position: absolute;
  top: 0px;
  right: 0px;
}
.bottom-left {
  position: fixed;
  bottom: 0;
  left: 0;
}
.bottom-right {
  position: absolute;
  bottom: 2px;
  right: 2px;
}

/**
 * Scrollbar
 */
.horizontal-scroll {
  overflow-x: auto;
  white-space: nowrap;
}
div::-webkit-scrollbar {
  display: none;
}

/**
 * Others (helper CSS)
 */
 .disabled {
  opacity: 0.5;
}
.no-text-transform {
  text-transform: none;
}
hr {
  margin-top: 1rem;
  margin-bottom: 1rem;
  border: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
.small-chip {
  font-size: 11px;
  height: 26px;
}
.spin {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.ion-text-nowrap {
  text-overflow: ellipsis;
  overflow: hidden;
}