{"name": "atlas", "version": "0.0.1", "private": true, "description": "", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e", "lint": "vue-cli-service lint", "deploy": "firebase deploy --project fdmt-atlas", "deploy-hosting": "firebase deploy --project fdmt-atlas --only hosting", "deploy-functions": "firebase deploy --project fdmt-atlas --only functions"}, "dependencies": {"@amcharts/amcharts5": "^5.9.11", "@capacitor-community/barcode-scanner": "^2.0.0", "@capacitor-firebase/authentication": "^0.5.1", "@capacitor/android": "^3.0.0", "@capacitor/browser": "^1.0.0", "@capacitor/camera": "^1.0.0", "@capacitor/core": "^3.0.0", "@capacitor/ios": "^3.0.0", "@capacitor/splash-screen": "^1.0.0", "@ionic/core": "^7.1.1", "@ionic/pwa-elements": "^3.0.2", "@ionic/vue": "^7.1.1", "@ionic/vue-router": "^7.1.1", "@stripe/stripe-js": "^1.11.0", "@supabase/supabase-js": "^2.43.5", "@types/pdfmake": "^0.2.10", "ag-grid-vue3": "^33.2.4", "compressorjs": "^1.0.7", "cordova-plugin-ionic": "5.4.7", "cordova-plugin-whitelist": "^1.3.4", "core-js": "^3.6.5", "firebase": "^9.23.0", "jsonh": "^0.0.6", "jsonrepair": "^3.8.0", "jspdf": "^2.5.2", "linkify-html": "^4.1.3", "linkifyjs": "^4.1.3", "markdown-it": "^14.1.0", "moment": "^2.29.4", "onesignal-cordova-plugin": "^2.11.3", "register-service-worker": "^1.7.1", "signature_pad": "^5.0.4", "swiper": "^9.3.2", "vue": "^3.3.4", "vue-i18n": "^9.0.0-rc.4", "vue-router": "^4.2.2", "vuex": "^4.1.0"}, "devDependencies": {"@capacitor/cli": "^3.0.0", "@types/jest": "^24.0.19", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-e2e-cypress": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-pwa": "^4.5.10", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-plugin-unit-jest": "~4.5.0", "@vue/cli-service": "4.5.19", "@vue/compiler-sfc": "^3.1.1", "@vue/eslint-config-typescript": "^5.0.2", "@vue/test-utils": "^2.0.0-0", "eslint": "^6.7.2", "eslint-plugin-vue": "^7.0.0-0", "typescript": "~4.3.5", "vue-jest": "^5.0.0-0"}}