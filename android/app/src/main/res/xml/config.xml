<?xml version='1.0' encoding='utf-8'?>
<widget version="1.0.0" xmlns="http://www.w3.org/ns/widgets" xmlns:cdv="http://cordova.apache.org/ns/1.0">
  <access origin="*" />
  
  <feature name="IonicCordovaCommon">
    <param name="android-package" value="com.ionicframework.common.IonicCordovaCommon" onload="true"/>
  </feature>

  <feature name="Whitelist">
    <param name="android-package" value="org.apache.cordova.whitelist.WhitelistPlugin"/>
    <param name="onload" value="true"/>
  </feature>

  <feature name="OneSignalPush">
    <param name="android-package" value="com.plugin.gcm.OneSignalPush"/>
  </feature>

  
  <preference name="KeyboardDisplayRequiresUserAction" value="false" />
  <preference name="AndroidLaunchMode" value="singleTask" />
</widget>