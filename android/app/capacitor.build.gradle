// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_1_8
      targetCompatibility JavaVersion.VERSION_1_8
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-community-barcode-scanner')
    implementation project(':capacitor-firebase-auth')
    implementation "com.onesignal:OneSignal:3.16.0"
}
apply from: "../../node_modules/cordova-plugin-ionic/src/android/cordovapluginionic.gradle"
apply from: "../../node_modules/onesignal-cordova-plugin/build-extras-onesignal.gradle"

if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
