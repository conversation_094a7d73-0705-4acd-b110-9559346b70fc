export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      access_links: {
        Row: {
          is_expired: boolean | null
          link: string | null
          logged_in_at: string | null
          max_access_minutes: number | null
          program_id: string | null
          school_id: string | null
          short_link: string | null
          token: string
        }
        Insert: {
          is_expired?: boolean | null
          link?: string | null
          logged_in_at?: string | null
          max_access_minutes?: number | null
          program_id?: string | null
          school_id?: string | null
          short_link?: string | null
          token: string
        }
        Update: {
          is_expired?: boolean | null
          link?: string | null
          logged_in_at?: string | null
          max_access_minutes?: number | null
          program_id?: string | null
          school_id?: string | null
          short_link?: string | null
          token?: string
        }
        Relationships: []
      }
      booking_items: {
        Row: {
          anchor_event_id: string | null
          client_id: string | null
          created_at: string
          created_by: string | null
          description: string | null
          duration_minutes: number | null
          end_time: string | null
          id: string
          meeting_channel: string | null
          poster_slide_link: string | null
          start_time: string | null
          target_user_type: string | null
          title: string | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          anchor_event_id?: string | null
          client_id?: string | null
          created_at?: string
          created_by?: string | null
          description?: string | null
          duration_minutes?: number | null
          end_time?: string | null
          id: string
          meeting_channel?: string | null
          poster_slide_link?: string | null
          start_time?: string | null
          target_user_type?: string | null
          title?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          anchor_event_id?: string | null
          client_id?: string | null
          created_at?: string
          created_by?: string | null
          description?: string | null
          duration_minutes?: number | null
          end_time?: string | null
          id?: string
          meeting_channel?: string | null
          poster_slide_link?: string | null
          start_time?: string | null
          target_user_type?: string | null
          title?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: []
      }
      booking_raw_responses: {
        Row: {
          created_at: string
          data: Json | null
          id: string
          status: string | null
        }
        Insert: {
          created_at?: string
          data?: Json | null
          id?: string
          status?: string | null
        }
        Update: {
          created_at?: string
          data?: Json | null
          id?: string
          status?: string | null
        }
        Relationships: []
      }
      booking_records: {
        Row: {
          anchor_event_id: string | null
          booking_item_id: string | null
          booking_slot_id: string
          client_id: string | null
          created_at: string
          email: string | null
          end_time: string | null
          full_name: string | null
          generated_session_id: string | null
          id: string
          phone: string | null
          preferred_name: string | null
          processed_at: string | null
          remarks: string | null
          school_id: string | null
          school_roles: string | null
          start_time: string | null
          title: string | null
          user_id: string | null
          wa_group_id: string | null
          year_of_study: string | null
        }
        Insert: {
          anchor_event_id?: string | null
          booking_item_id?: string | null
          booking_slot_id?: string
          client_id?: string | null
          created_at?: string
          email?: string | null
          end_time?: string | null
          full_name?: string | null
          generated_session_id?: string | null
          id?: string
          phone?: string | null
          preferred_name?: string | null
          processed_at?: string | null
          remarks?: string | null
          school_id?: string | null
          school_roles?: string | null
          start_time?: string | null
          title?: string | null
          user_id?: string | null
          wa_group_id?: string | null
          year_of_study?: string | null
        }
        Update: {
          anchor_event_id?: string | null
          booking_item_id?: string | null
          booking_slot_id?: string
          client_id?: string | null
          created_at?: string
          email?: string | null
          end_time?: string | null
          full_name?: string | null
          generated_session_id?: string | null
          id?: string
          phone?: string | null
          preferred_name?: string | null
          processed_at?: string | null
          remarks?: string | null
          school_id?: string | null
          school_roles?: string | null
          start_time?: string | null
          title?: string | null
          user_id?: string | null
          wa_group_id?: string | null
          year_of_study?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "public_booking_records_booking_item_id_fkey"
            columns: ["booking_item_id"]
            isOneToOne: false
            referencedRelation: "booking_items"
            referencedColumns: ["id"]
          },
        ]
      }
      booking_slots: {
        Row: {
          booking_record_id: string | null
          created_at: string
          duration_minutes: number | null
          end_time: string | null
          gcal_event_id: string | null
          gcal_event_link: string | null
          gcal_id: string | null
          id: string
          start_time: string | null
          status: string | null
          updated_at: string | null
        }
        Insert: {
          booking_record_id?: string | null
          created_at?: string
          duration_minutes?: number | null
          end_time?: string | null
          gcal_event_id?: string | null
          gcal_event_link?: string | null
          gcal_id?: string | null
          id: string
          start_time?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          booking_record_id?: string | null
          created_at?: string
          duration_minutes?: number | null
          end_time?: string | null
          gcal_event_id?: string | null
          gcal_event_link?: string | null
          gcal_id?: string | null
          id?: string
          start_time?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      cached_step1_questions: {
        Row: {
          data: string | null
          group_key: string
          updated_at: string | null
        }
        Insert: {
          data?: string | null
          group_key: string
          updated_at?: string | null
        }
        Update: {
          data?: string | null
          group_key?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      data: {
        Row: {
          id: number
          json_data: string | null
          updated_at: string | null
        }
        Insert: {
          id?: number
          json_data?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: number
          json_data?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      employer_job_applications: {
        Row: {
          created_at: string
          employer_id: string | null
          employer_job_id: string
          id: string
          position: string | null
          position_url: string | null
          post_date: string | null
          program_id: string | null
          reason: string | null
          user_id: string
          year_of_study: string | null
        }
        Insert: {
          created_at?: string
          employer_id?: string | null
          employer_job_id: string
          id: string
          position?: string | null
          position_url?: string | null
          post_date?: string | null
          program_id?: string | null
          reason?: string | null
          user_id: string
          year_of_study?: string | null
        }
        Update: {
          created_at?: string
          employer_id?: string | null
          employer_job_id?: string
          id?: string
          position?: string | null
          position_url?: string | null
          post_date?: string | null
          program_id?: string | null
          reason?: string | null
          user_id?: string
          year_of_study?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "employer_job_applications_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      employer_jobs: {
        Row: {
          created_at: string
          created_by: string | null
          employer_id: string | null
          id: string
          position: string | null
          position_url: string | null
          post_date: string | null
          remarks: string | null
          session_id: string | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          employer_id?: string | null
          id: string
          position?: string | null
          position_url?: string | null
          post_date?: string | null
          remarks?: string | null
          session_id?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          created_at?: string
          created_by?: string | null
          employer_id?: string | null
          id?: string
          position?: string | null
          position_url?: string | null
          post_date?: string | null
          remarks?: string | null
          session_id?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "employer_jobs_employer_id_fkey"
            columns: ["employer_id"]
            isOneToOne: false
            referencedRelation: "employers"
            referencedColumns: ["id"]
          },
        ]
      }
      employers: {
        Row: {
          company_url: string | null
          created_at: string
          created_by: string | null
          id: string
          name: string | null
          portal_employer_id: number | null
          report_long_link: string | null
          report_short_link: string | null
          short_name: string | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          company_url?: string | null
          created_at?: string
          created_by?: string | null
          id: string
          name?: string | null
          portal_employer_id?: number | null
          report_long_link?: string | null
          report_short_link?: string | null
          short_name?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          company_url?: string | null
          created_at?: string
          created_by?: string | null
          id?: string
          name?: string | null
          portal_employer_id?: number | null
          report_long_link?: string | null
          report_short_link?: string | null
          short_name?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: []
      }
      secondary_school_students: {
        Row: {
          created_at: string | null
          group: string | null
          how_to_know: string | null
          is_prefect: boolean | null
          mock_jupas_is_rational: boolean | null
          mock_jupas_segment: string | null
          phone_call_status: string | null
          q1_ans: string | null
          q2_ans: string | null
          q3_ans: string | null
          referrer_name: string | null
          referrer_phone: string | null
          referrer_role: string | null
          remarks: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          group?: string | null
          how_to_know?: string | null
          is_prefect?: boolean | null
          mock_jupas_is_rational?: boolean | null
          mock_jupas_segment?: string | null
          phone_call_status?: string | null
          q1_ans?: string | null
          q2_ans?: string | null
          q3_ans?: string | null
          referrer_name?: string | null
          referrer_phone?: string | null
          referrer_role?: string | null
          remarks?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          group?: string | null
          how_to_know?: string | null
          is_prefect?: boolean | null
          mock_jupas_is_rational?: boolean | null
          mock_jupas_segment?: string | null
          phone_call_status?: string | null
          q1_ans?: string | null
          q2_ans?: string | null
          q3_ans?: string | null
          referrer_name?: string | null
          referrer_phone?: string | null
          referrer_role?: string | null
          remarks?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "secondary_school_students_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      subjects: {
        Row: {
          category: string | null
          group_name: string | null
          id: number
          name: string | null
          short_name: string | null
          type: string | null
        }
        Insert: {
          category?: string | null
          group_name?: string | null
          id?: number
          name?: string | null
          short_name?: string | null
          type?: string | null
        }
        Update: {
          category?: string | null
          group_name?: string | null
          id?: number
          name?: string | null
          short_name?: string | null
          type?: string | null
        }
        Relationships: []
      }
      teacher_class_roles: {
        Row: {
          classes: string | null
          created_at: string
          id: string
          remarks: string | null
          role: string | null
          status: string | null
          subject: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          classes?: string | null
          created_at?: string
          id?: string
          remarks?: string | null
          role?: string | null
          status?: string | null
          subject?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          classes?: string | null
          created_at?: string
          id?: string
          remarks?: string | null
          role?: string | null
          status?: string | null
          subject?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "teacher_class_roles_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "teachers"
            referencedColumns: ["user_id"]
          },
        ]
      }
      teacher_response_answers: {
        Row: {
          answer: string | null
          created_at: string
          event_name: string | null
          option_id: string | null
          question_id: string
          question_title: string | null
          service_id: string | null
          teacher_response_id: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          answer?: string | null
          created_at?: string
          event_name?: string | null
          option_id?: string | null
          question_id: string
          question_title?: string | null
          service_id?: string | null
          teacher_response_id: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          answer?: string | null
          created_at?: string
          event_name?: string | null
          option_id?: string | null
          question_id?: string
          question_title?: string | null
          service_id?: string | null
          teacher_response_id?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "teacher_response_answers_question_id_fkey"
            columns: ["question_id"]
            isOneToOne: false
            referencedRelation: "teacher_response_questions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "teacher_response_answers_teacher_response_id_fkey"
            columns: ["teacher_response_id"]
            isOneToOne: false
            referencedRelation: "teacher_responses"
            referencedColumns: ["id"]
          },
        ]
      }
      teacher_response_logs: {
        Row: {
          assigned_by: string | null
          created_at: string
          est_num_of_students: string | null
          id: number
          other_ans: string | null
          preferred_session_dates: string | null
          preferred_session_time: string | null
          prev_response: string | null
          prev_role: string | null
          resp_type: string | null
          response: string | null
          role: string | null
          session_id: string | null
          teacher_response_id: string | null
        }
        Insert: {
          assigned_by?: string | null
          created_at?: string
          est_num_of_students?: string | null
          id?: number
          other_ans?: string | null
          preferred_session_dates?: string | null
          preferred_session_time?: string | null
          prev_response?: string | null
          prev_role?: string | null
          resp_type?: string | null
          response?: string | null
          role?: string | null
          session_id?: string | null
          teacher_response_id?: string | null
        }
        Update: {
          assigned_by?: string | null
          created_at?: string
          est_num_of_students?: string | null
          id?: number
          other_ans?: string | null
          preferred_session_dates?: string | null
          preferred_session_time?: string | null
          prev_response?: string | null
          prev_role?: string | null
          resp_type?: string | null
          response?: string | null
          role?: string | null
          session_id?: string | null
          teacher_response_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "teacher_response_logs_teacher_response_id_fkey"
            columns: ["teacher_response_id"]
            isOneToOne: false
            referencedRelation: "teacher_responses"
            referencedColumns: ["id"]
          },
        ]
      }
      teacher_response_options: {
        Row: {
          action_create: string | null
          created_at: string | null
          created_by: string | null
          id: string
          immediate_action: string | null
          note: string | null
          order: number | null
          question_id: string | null
          show_if_exist_sessions: boolean | null
          status: string | null
          target_role: string | null
          title: string | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          action_create?: string | null
          created_at?: string | null
          created_by?: string | null
          id: string
          immediate_action?: string | null
          note?: string | null
          order?: number | null
          question_id?: string | null
          show_if_exist_sessions?: boolean | null
          status?: string | null
          target_role?: string | null
          title?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          action_create?: string | null
          created_at?: string | null
          created_by?: string | null
          id?: string
          immediate_action?: string | null
          note?: string | null
          order?: number | null
          question_id?: string | null
          show_if_exist_sessions?: boolean | null
          status?: string | null
          target_role?: string | null
          title?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "teacher_response_options_question_id_fkey"
            columns: ["question_id"]
            isOneToOne: false
            referencedRelation: "teacher_response_questions"
            referencedColumns: ["id"]
          },
        ]
      }
      teacher_response_questions: {
        Row: {
          action_create: string | null
          anchor_event_id: string | null
          created_at: string
          created_by: string | null
          group: string | null
          id: string
          is_required: boolean | null
          order: number | null
          related_teacher_response_field: string | null
          service_id: string | null
          show_in_option_ids: string | null
          status: string | null
          subtitle: string | null
          title: string | null
          type: string | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          action_create?: string | null
          anchor_event_id?: string | null
          created_at?: string
          created_by?: string | null
          group?: string | null
          id: string
          is_required?: boolean | null
          order?: number | null
          related_teacher_response_field?: string | null
          service_id?: string | null
          show_in_option_ids?: string | null
          status?: string | null
          subtitle?: string | null
          title?: string | null
          type?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          action_create?: string | null
          anchor_event_id?: string | null
          created_at?: string
          created_by?: string | null
          group?: string | null
          id?: string
          is_required?: boolean | null
          order?: number | null
          related_teacher_response_field?: string | null
          service_id?: string | null
          show_in_option_ids?: string | null
          status?: string | null
          subtitle?: string | null
          title?: string | null
          type?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: []
      }
      teacher_response_statuses: {
        Row: {
          created_at: string
          created_by: string | null
          description: string | null
          id: string
          name: string | null
          order: number | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          description?: string | null
          id: string
          name?: string | null
          order?: number | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string
          created_by?: string | null
          description?: string | null
          id?: string
          name?: string | null
          order?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
      teacher_responses: {
        Row: {
          arrival_time: string | null
          assigned_by: string | null
          classes: string | null
          client_id: string | null
          created_at: string | null
          end_time: string | null
          est_num_of_students: string | null
          event_date_time: string | null
          event_name: string | null
          generated_session_id: string | null
          id: string
          intake_year: string | null
          is_date_suitable: string | null
          option_id: string | null
          other_ans: string | null
          phone: string | null
          preferred_session_dates: string | null
          preferred_session_time: string | null
          preferred_venue: string | null
          remarks: string | null
          response: string | null
          role: string | null
          room_available_time: string | null
          school_id: string | null
          service_id: string | null
          session_id: string | null
          status: string | null
          student_forms: string | null
          target_class: string | null
          target_form: string | null
          type: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          arrival_time?: string | null
          assigned_by?: string | null
          classes?: string | null
          client_id?: string | null
          created_at?: string | null
          end_time?: string | null
          est_num_of_students?: string | null
          event_date_time?: string | null
          event_name?: string | null
          generated_session_id?: string | null
          id: string
          intake_year?: string | null
          is_date_suitable?: string | null
          option_id?: string | null
          other_ans?: string | null
          phone?: string | null
          preferred_session_dates?: string | null
          preferred_session_time?: string | null
          preferred_venue?: string | null
          remarks?: string | null
          response?: string | null
          role?: string | null
          room_available_time?: string | null
          school_id?: string | null
          service_id?: string | null
          session_id?: string | null
          status?: string | null
          student_forms?: string | null
          target_class?: string | null
          target_form?: string | null
          type?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          arrival_time?: string | null
          assigned_by?: string | null
          classes?: string | null
          client_id?: string | null
          created_at?: string | null
          end_time?: string | null
          est_num_of_students?: string | null
          event_date_time?: string | null
          event_name?: string | null
          generated_session_id?: string | null
          id?: string
          intake_year?: string | null
          is_date_suitable?: string | null
          option_id?: string | null
          other_ans?: string | null
          phone?: string | null
          preferred_session_dates?: string | null
          preferred_session_time?: string | null
          preferred_venue?: string | null
          remarks?: string | null
          response?: string | null
          role?: string | null
          room_available_time?: string | null
          school_id?: string | null
          service_id?: string | null
          session_id?: string | null
          status?: string | null
          student_forms?: string | null
          target_class?: string | null
          target_form?: string | null
          type?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "teacher_responses_assigned_by_fkey"
            columns: ["assigned_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "teacher_responses_option_id_fkey"
            columns: ["option_id"]
            isOneToOne: false
            referencedRelation: "teacher_response_options"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "teacher_responses_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "teacher_responses_user_id_fkey1"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "teachers"
            referencedColumns: ["user_id"]
          },
        ]
      }
      teacher_responses_raw: {
        Row: {
          assigned_by: string | null
          created_at: string
          data: string | null
          event_name: string | null
          id: string
          is_ack_msg_sent: boolean | null
          processed_at: string | null
          service_id: string | null
          user_id: string | null
        }
        Insert: {
          assigned_by?: string | null
          created_at?: string
          data?: string | null
          event_name?: string | null
          id?: string
          is_ack_msg_sent?: boolean | null
          processed_at?: string | null
          service_id?: string | null
          user_id?: string | null
        }
        Update: {
          assigned_by?: string | null
          created_at?: string
          data?: string | null
          event_name?: string | null
          id?: string
          is_ack_msg_sent?: boolean | null
          processed_at?: string | null
          service_id?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      teachers: {
        Row: {
          advisor_candidate: string | null
          created_at: string | null
          employment_status: string | null
          is_receiving_msgs: boolean | null
          prefilled_service_id: string | null
          remarks: string | null
          school_roles: string | null
          source: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          advisor_candidate?: string | null
          created_at?: string | null
          employment_status?: string | null
          is_receiving_msgs?: boolean | null
          prefilled_service_id?: string | null
          remarks?: string | null
          school_roles?: string | null
          source?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          advisor_candidate?: string | null
          created_at?: string | null
          employment_status?: string | null
          is_receiving_msgs?: boolean | null
          prefilled_service_id?: string | null
          remarks?: string | null
          school_roles?: string | null
          source?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "public_teachers_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      university_clients: {
        Row: {
          created_at: string | null
          employment_status: string | null
          institution: string | null
          position: string | null
          remarks: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          employment_status?: string | null
          institution?: string | null
          position?: string | null
          remarks?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          employment_status?: string | null
          institution?: string | null
          position?: string | null
          remarks?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "university_clients_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      university_students: {
        Row: {
          created_at: string | null
          jobEX: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          jobEX?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          jobEX?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "university_students_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_browsed_professions: {
        Row: {
          profession_id: number
          updated_at: string | null
          user_id: string
        }
        Insert: {
          profession_id: number
          updated_at?: string | null
          user_id: string
        }
        Update: {
          profession_id?: number
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "public_user_browsed_professions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_browsed_sectors: {
        Row: {
          sector_id: number
          updated_at: string | null
          user_id: string
        }
        Insert: {
          sector_id: number
          updated_at?: string | null
          user_id: string
        }
        Update: {
          sector_id?: number
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_browsed_sectors_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_browsed_segments: {
        Row: {
          segment_id: number
          updated_at: string | null
          user_id: string
        }
        Insert: {
          segment_id: number
          updated_at?: string | null
          user_id: string
        }
        Update: {
          segment_id?: number
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_browsed_segments_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_chatbot_prompts: {
        Row: {
          bot_response: string | null
          created_at: string
          id: string
          profession_id: number | null
          prompt: string | null
          type: string | null
          user_id: string | null
        }
        Insert: {
          bot_response?: string | null
          created_at?: string
          id: string
          profession_id?: number | null
          prompt?: string | null
          type?: string | null
          user_id?: string | null
        }
        Update: {
          bot_response?: string | null
          created_at?: string
          id?: string
          profession_id?: number | null
          prompt?: string | null
          type?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      user_cl_claims: {
        Row: {
          created_at: string
          exp_elaboration: string | null
          exp_ids: string[] | null
          id: string
          text: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string
          exp_elaboration?: string | null
          exp_ids?: string[] | null
          id: string
          text?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string
          exp_elaboration?: string | null
          exp_ids?: string[] | null
          id?: string
          text?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_cl_claims_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_cl_exps: {
        Row: {
          created_at: string
          id: string
          text: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string
          id: string
          text?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          text?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_cl_exps_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_cl_records: {
        Row: {
          created_at: string
          id: string
          user_cl_claims: Json | null
          user_cl_exps: Json | null
          user_id: string | null
        }
        Insert: {
          created_at?: string
          id: string
          user_cl_claims?: Json | null
          user_cl_exps?: Json | null
          user_id?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          user_cl_claims?: Json | null
          user_cl_exps?: Json | null
          user_id?: string | null
        }
        Relationships: []
      }
      user_claims: {
        Row: {
          claim_id: string
          created_at: string | null
          elaboration: string | null
          order: number | null
          selected: boolean | null
          text: string | null
          type: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          claim_id?: string
          created_at?: string | null
          elaboration?: string | null
          order?: number | null
          selected?: boolean | null
          text?: string | null
          type?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          claim_id?: string
          created_at?: string | null
          elaboration?: string | null
          order?: number | null
          selected?: boolean | null
          text?: string | null
          type?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_claims_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_disciplines: {
        Row: {
          action: string | null
          app_state: Json | null
          bot_explanation: string | null
          created_at: string
          discipline_id: number
          order: number | null
          reaction: string | null
          reason: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          action?: string | null
          app_state?: Json | null
          bot_explanation?: string | null
          created_at?: string
          discipline_id: number
          order?: number | null
          reaction?: string | null
          reason?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          action?: string | null
          app_state?: Json | null
          bot_explanation?: string | null
          created_at?: string
          discipline_id?: number
          order?: number | null
          reaction?: string | null
          reason?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_disciplines_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_electives: {
        Row: {
          action: string | null
          app_state: Json | null
          created_at: string
          elective_id: string
          order: number | null
          program_id: number
          reaction: string | null
          reason: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          action?: string | null
          app_state?: Json | null
          created_at?: string
          elective_id: string
          order?: number | null
          program_id: number
          reaction?: string | null
          reason?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          action?: string | null
          app_state?: Json | null
          created_at?: string
          elective_id?: string
          order?: number | null
          program_id?: number
          reaction?: string | null
          reason?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_electives_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_employer_jobs: {
        Row: {
          action: string | null
          app_state: Json | null
          created_at: string
          employer_job_id: string
          order: number | null
          reaction: string | null
          reason: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          action?: string | null
          app_state?: Json | null
          created_at?: string
          employer_job_id: string
          order?: number | null
          reaction?: string | null
          reason?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          action?: string | null
          app_state?: Json | null
          created_at?: string
          employer_job_id?: string
          order?: number | null
          reaction?: string | null
          reason?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_employer_jobs_job_id_fkey"
            columns: ["employer_job_id"]
            isOneToOne: false
            referencedRelation: "employer_jobs"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_employer_jobs_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_form_responses: {
        Row: {
          answer: string | null
          created_at: string | null
          question_id: string
          question_title: string | null
          session_id: string
          task_id: string
          user_id: string
        }
        Insert: {
          answer?: string | null
          created_at?: string | null
          question_id: string
          question_title?: string | null
          session_id: string
          task_id: string
          user_id: string
        }
        Update: {
          answer?: string | null
          created_at?: string | null
          question_id?: string
          question_title?: string | null
          session_id?: string
          task_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_form_responses_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_gpt_request_logs: {
        Row: {
          bot_url: string | null
          created_at: string
          device_platforms: string | null
          id: number
          ip_address: string | null
          override_bot_name: string | null
          prompt: string | null
          response_status_text: string | null
          tag_id: string | null
          tag_text: string | null
          user_elaboration: string | null
          user_id: string | null
        }
        Insert: {
          bot_url?: string | null
          created_at?: string
          device_platforms?: string | null
          id?: number
          ip_address?: string | null
          override_bot_name?: string | null
          prompt?: string | null
          response_status_text?: string | null
          tag_id?: string | null
          tag_text?: string | null
          user_elaboration?: string | null
          user_id?: string | null
        }
        Update: {
          bot_url?: string | null
          created_at?: string
          device_platforms?: string | null
          id?: number
          ip_address?: string | null
          override_bot_name?: string | null
          prompt?: string | null
          response_status_text?: string | null
          tag_id?: string | null
          tag_text?: string | null
          user_elaboration?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      user_liked_professions: {
        Row: {
          created_at: string | null
          profession_id: number
          user_id: string
        }
        Insert: {
          created_at?: string | null
          profession_id: number
          user_id: string
        }
        Update: {
          created_at?: string | null
          profession_id?: number
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_liked_professions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_liked_sectors: {
        Row: {
          created_at: string | null
          sector_id: number
          user_id: string
        }
        Insert: {
          created_at?: string | null
          sector_id: number
          user_id: string
        }
        Update: {
          created_at?: string | null
          sector_id?: number
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_liked_sectors_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_liked_segments: {
        Row: {
          created_at: string | null
          segment_id: number
          user_id: string
        }
        Insert: {
          created_at?: string | null
          segment_id: number
          user_id: string
        }
        Update: {
          created_at?: string | null
          segment_id?: number
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_liked_segments_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_nomination_records: {
        Row: {
          created_at: string | null
          id: string
          nominated_by: string | null
          nominees: string | null
          processed_at: string | null
          raw: string | null
          target_response: string | null
          target_session_ids: string | null
          target_user_ids: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          nominated_by?: string | null
          nominees?: string | null
          processed_at?: string | null
          raw?: string | null
          target_response?: string | null
          target_session_ids?: string | null
          target_user_ids?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          nominated_by?: string | null
          nominees?: string | null
          processed_at?: string | null
          raw?: string | null
          target_response?: string | null
          target_session_ids?: string | null
          target_user_ids?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_nomination_records_nominated_by_fkey"
            columns: ["nominated_by"]
            isOneToOne: false
            referencedRelation: "teachers"
            referencedColumns: ["user_id"]
          },
        ]
      }
      user_professions: {
        Row: {
          action: string | null
          app_state: Json | null
          bot_explanation: string | null
          created_at: string
          order: number | null
          profession_id: number
          reaction: string | null
          reason: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          action?: string | null
          app_state?: Json | null
          bot_explanation?: string | null
          created_at?: string
          order?: number | null
          profession_id: number
          reaction?: string | null
          reason?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          action?: string | null
          app_state?: Json | null
          bot_explanation?: string | null
          created_at?: string
          order?: number | null
          profession_id?: number
          reaction?: string | null
          reason?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "public_user_professions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_program_logs: {
        Row: {
          created_at: string
          id: string
          selected_program_ids: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          selected_program_ids?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          selected_program_ids?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      user_programs: {
        Row: {
          action: string | null
          app_state: Json | null
          bot_explanation: string | null
          created_at: string
          order: number | null
          program_id: number
          reaction: string | null
          reason: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          action?: string | null
          app_state?: Json | null
          bot_explanation?: string | null
          created_at?: string
          order?: number | null
          program_id: number
          reaction?: string | null
          reason?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          action?: string | null
          app_state?: Json | null
          bot_explanation?: string | null
          created_at?: string
          order?: number | null
          program_id?: number
          reaction?: string | null
          reason?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "public_user_programs_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_segments: {
        Row: {
          app_state: Json | null
          created_at: string
          order: number | null
          profession_id: number
          reaction: string | null
          reason: string | null
          segment_id: number
          updated_at: string | null
          user_id: string
        }
        Insert: {
          app_state?: Json | null
          created_at?: string
          order?: number | null
          profession_id: number
          reaction?: string | null
          reason?: string | null
          segment_id: number
          updated_at?: string | null
          user_id: string
        }
        Update: {
          app_state?: Json | null
          created_at?: string
          order?: number | null
          profession_id?: number
          reaction?: string | null
          reason?: string | null
          segment_id?: number
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_segments_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_session_response_logs: {
        Row: {
          created_at: string
          id: number
          prev_response: string | null
          response: string | null
          user_session_response_id: string | null
        }
        Insert: {
          created_at?: string
          id?: number
          prev_response?: string | null
          response?: string | null
          user_session_response_id?: string | null
        }
        Update: {
          created_at?: string
          id?: number
          prev_response?: string | null
          response?: string | null
          user_session_response_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_session_response_logs_user_session_response_id_fkey"
            columns: ["user_session_response_id"]
            isOneToOne: false
            referencedRelation: "user_session_responses"
            referencedColumns: ["id"]
          },
        ]
      }
      user_session_responses: {
        Row: {
          assigned_workshop_session: string | null
          attended: string | null
          confirmed: string | null
          created_at: string | null
          event_date_time: string | null
          event_name: string | null
          id: string
          interview_rank: number | null
          interview_status: string | null
          interviewer_comments: string | null
          is_ack_msg_sent: boolean | null
          is_outstanding_student: boolean | null
          lesson_id: string | null
          nominated_by: string | null
          phone: number | null
          response: string | null
          roll_call_code: string | null
          roll_call_time: string | null
          session_id: string | null
          student_src: string | null
          updated_at: string | null
          user_id: string | null
          user_jobex_intake_id: string | null
          user_school_id: string | null
          withdraw_reason: string | null
          workshop_time_preference: string | null
        }
        Insert: {
          assigned_workshop_session?: string | null
          attended?: string | null
          confirmed?: string | null
          created_at?: string | null
          event_date_time?: string | null
          event_name?: string | null
          id: string
          interview_rank?: number | null
          interview_status?: string | null
          interviewer_comments?: string | null
          is_ack_msg_sent?: boolean | null
          is_outstanding_student?: boolean | null
          lesson_id?: string | null
          nominated_by?: string | null
          phone?: number | null
          response?: string | null
          roll_call_code?: string | null
          roll_call_time?: string | null
          session_id?: string | null
          student_src?: string | null
          updated_at?: string | null
          user_id?: string | null
          user_jobex_intake_id?: string | null
          user_school_id?: string | null
          withdraw_reason?: string | null
          workshop_time_preference?: string | null
        }
        Update: {
          assigned_workshop_session?: string | null
          attended?: string | null
          confirmed?: string | null
          created_at?: string | null
          event_date_time?: string | null
          event_name?: string | null
          id?: string
          interview_rank?: number | null
          interview_status?: string | null
          interviewer_comments?: string | null
          is_ack_msg_sent?: boolean | null
          is_outstanding_student?: boolean | null
          lesson_id?: string | null
          nominated_by?: string | null
          phone?: number | null
          response?: string | null
          roll_call_code?: string | null
          roll_call_time?: string | null
          session_id?: string | null
          student_src?: string | null
          updated_at?: string | null
          user_id?: string | null
          user_jobex_intake_id?: string | null
          user_school_id?: string | null
          withdraw_reason?: string | null
          workshop_time_preference?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_session_responses_nominated_by_fkey"
            columns: ["nominated_by"]
            isOneToOne: false
            referencedRelation: "teachers"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "user_session_responses_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_slps: {
        Row: {
          access_type: string | null
          created_at: string | null
          gpt: string | null
          original: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          access_type?: string | null
          created_at?: string | null
          gpt?: string | null
          original?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          access_type?: string | null
          created_at?: string | null
          gpt?: string | null
          original?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_slps_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_subject_grades: {
        Row: {
          created_at: string
          created_by: string | null
          grade: string | null
          source: string | null
          subject_id: number
          updated_at: string | null
          updated_by: string | null
          user_id: string
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          grade?: string | null
          source?: string | null
          subject_id: number
          updated_at?: string | null
          updated_by?: string | null
          user_id: string
        }
        Update: {
          created_at?: string
          created_by?: string | null
          grade?: string | null
          source?: string | null
          subject_id?: number
          updated_at?: string | null
          updated_by?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_subject_grades_subject_id_fkey"
            columns: ["subject_id"]
            isOneToOne: false
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_subject_grades_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_users: {
        Row: {
          app_state: Json | null
          created_at: string
          grade: string | null
          order: number | null
          reaction: string | null
          reason: string | null
          session_id: string
          target_user_id: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          app_state?: Json | null
          created_at?: string
          grade?: string | null
          order?: number | null
          reaction?: string | null
          reason?: string | null
          session_id?: string
          target_user_id: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          app_state?: Json | null
          created_at?: string
          grade?: string | null
          order?: number | null
          reaction?: string | null
          reason?: string | null
          session_id?: string
          target_user_id?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_users_target_user_id_fkey"
            columns: ["target_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_users_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_verification_records: {
        Row: {
          created_at: string | null
          id: number
          target_user_id: string | null
          verification_result: string | null
          verified_by: string | null
        }
        Insert: {
          created_at?: string | null
          id?: number
          target_user_id?: string | null
          verification_result?: string | null
          verified_by?: string | null
        }
        Update: {
          created_at?: string | null
          id?: number
          target_user_id?: string | null
          verification_result?: string | null
          verified_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_verification_records_target_user_id_fkey"
            columns: ["target_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_verification_records_verified_by_fkey"
            columns: ["verified_by"]
            isOneToOne: false
            referencedRelation: "teachers"
            referencedColumns: ["user_id"]
          },
        ]
      }
      user_wa_grps: {
        Row: {
          assigned_at: string | null
          id: string | null
          invite_link: string | null
          row: number
        }
        Insert: {
          assigned_at?: string | null
          id?: string | null
          invite_link?: string | null
          row?: number
        }
        Update: {
          assigned_at?: string | null
          id?: string | null
          invite_link?: string | null
          row?: number
        }
        Relationships: []
      }
      users: {
        Row: {
          abs_intake_id: string | null
          access_disabled: boolean | null
          chinese_name: string | null
          class: string | null
          class_last_updated_at: string | null
          client_id: string | null
          consultation: string | null
          created_at: string | null
          curr_intake_id: string | null
          dark_theme: boolean | null
          device_platforms: string | null
          email: string | null
          expected_grad_year: string | null
          first_applied_event_ids: string | null
          first_selected_discipline_names: string | null
          full_name: string | null
          gender: string | null
          heard_of_profession_ids: string | null
          "How do you know this program?": string | null
          id: string
          is_ambassador: boolean | null
          is_cv_ready: boolean | null
          is_phone_verified: boolean | null
          jobEX: string | null
          l1: string | null
          l2: string | null
          l3: string | null
          last_logged_in_at: string | null
          last_updated_by: string | null
          login_count: number | null
          nationality: string | null
          nominated_by: string | null
          not_heard_of_profession_ids: string | null
          phone: number | null
          preferred_lang: string | null
          preferred_name: string | null
          profession_survey_response_updated_at: string | null
          profile_doc: string | null
          profile_pic: string | null
          program_id: string | null
          rank_in_form: string | null
          referrer: string | null
          roles: string | null
          row: number
          school_id: string | null
          show_step1: boolean | null
          show_step2: boolean | null
          show_step3: boolean | null
          src_session_id: string | null
          step1_option_ids: string | null
          step1_ordered_profession_ids: string | null
          step1_ordered_profession_names: string | null
          step1_profession_action: string | null
          step1_profession_reason: string | null
          step2_discipline_action: string | null
          step2_discipline_ids: string | null
          step2_discipline_names: string | null
          step2_discipline_reason: string | null
          step3_program_ids: string | null
          step3_program_names: string | null
          student_id: string | null
          student_number: string | null
          studying_curriculum: string | null
          studying_electives: string | null
          studying_stream: string | null
          uni_email: string | null
          updated_at: string | null
          user_in_wa_group: boolean | null
          verification_result: string | null
          verification_result_updated_at: string | null
          wa_group_id: string | null
          wa_group_link: string | null
          year_DSE: string | null
          year_of_study: string | null
        }
        Insert: {
          abs_intake_id?: string | null
          access_disabled?: boolean | null
          chinese_name?: string | null
          class?: string | null
          class_last_updated_at?: string | null
          client_id?: string | null
          consultation?: string | null
          created_at?: string | null
          curr_intake_id?: string | null
          dark_theme?: boolean | null
          device_platforms?: string | null
          email?: string | null
          expected_grad_year?: string | null
          first_applied_event_ids?: string | null
          first_selected_discipline_names?: string | null
          full_name?: string | null
          gender?: string | null
          heard_of_profession_ids?: string | null
          "How do you know this program?"?: string | null
          id: string
          is_ambassador?: boolean | null
          is_cv_ready?: boolean | null
          is_phone_verified?: boolean | null
          jobEX?: string | null
          l1?: string | null
          l2?: string | null
          l3?: string | null
          last_logged_in_at?: string | null
          last_updated_by?: string | null
          login_count?: number | null
          nationality?: string | null
          nominated_by?: string | null
          not_heard_of_profession_ids?: string | null
          phone?: number | null
          preferred_lang?: string | null
          preferred_name?: string | null
          profession_survey_response_updated_at?: string | null
          profile_doc?: string | null
          profile_pic?: string | null
          program_id?: string | null
          rank_in_form?: string | null
          referrer?: string | null
          roles?: string | null
          row?: number
          school_id?: string | null
          show_step1?: boolean | null
          show_step2?: boolean | null
          show_step3?: boolean | null
          src_session_id?: string | null
          step1_option_ids?: string | null
          step1_ordered_profession_ids?: string | null
          step1_ordered_profession_names?: string | null
          step1_profession_action?: string | null
          step1_profession_reason?: string | null
          step2_discipline_action?: string | null
          step2_discipline_ids?: string | null
          step2_discipline_names?: string | null
          step2_discipline_reason?: string | null
          step3_program_ids?: string | null
          step3_program_names?: string | null
          student_id?: string | null
          student_number?: string | null
          studying_curriculum?: string | null
          studying_electives?: string | null
          studying_stream?: string | null
          uni_email?: string | null
          updated_at?: string | null
          user_in_wa_group?: boolean | null
          verification_result?: string | null
          verification_result_updated_at?: string | null
          wa_group_id?: string | null
          wa_group_link?: string | null
          year_DSE?: string | null
          year_of_study?: string | null
        }
        Update: {
          abs_intake_id?: string | null
          access_disabled?: boolean | null
          chinese_name?: string | null
          class?: string | null
          class_last_updated_at?: string | null
          client_id?: string | null
          consultation?: string | null
          created_at?: string | null
          curr_intake_id?: string | null
          dark_theme?: boolean | null
          device_platforms?: string | null
          email?: string | null
          expected_grad_year?: string | null
          first_applied_event_ids?: string | null
          first_selected_discipline_names?: string | null
          full_name?: string | null
          gender?: string | null
          heard_of_profession_ids?: string | null
          "How do you know this program?"?: string | null
          id?: string
          is_ambassador?: boolean | null
          is_cv_ready?: boolean | null
          is_phone_verified?: boolean | null
          jobEX?: string | null
          l1?: string | null
          l2?: string | null
          l3?: string | null
          last_logged_in_at?: string | null
          last_updated_by?: string | null
          login_count?: number | null
          nationality?: string | null
          nominated_by?: string | null
          not_heard_of_profession_ids?: string | null
          phone?: number | null
          preferred_lang?: string | null
          preferred_name?: string | null
          profession_survey_response_updated_at?: string | null
          profile_doc?: string | null
          profile_pic?: string | null
          program_id?: string | null
          rank_in_form?: string | null
          referrer?: string | null
          roles?: string | null
          row?: number
          school_id?: string | null
          show_step1?: boolean | null
          show_step2?: boolean | null
          show_step3?: boolean | null
          src_session_id?: string | null
          step1_option_ids?: string | null
          step1_ordered_profession_ids?: string | null
          step1_ordered_profession_names?: string | null
          step1_profession_action?: string | null
          step1_profession_reason?: string | null
          step2_discipline_action?: string | null
          step2_discipline_ids?: string | null
          step2_discipline_names?: string | null
          step2_discipline_reason?: string | null
          step3_program_ids?: string | null
          step3_program_names?: string | null
          student_id?: string | null
          student_number?: string | null
          studying_curriculum?: string | null
          studying_electives?: string | null
          studying_stream?: string | null
          uni_email?: string | null
          updated_at?: string | null
          user_in_wa_group?: boolean | null
          verification_result?: string | null
          verification_result_updated_at?: string | null
          wa_group_id?: string | null
          wa_group_link?: string | null
          year_DSE?: string | null
          year_of_study?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never
