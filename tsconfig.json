{"compilerOptions": {"noImplicitAny": false, "target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "suppressImplicitAnyIndexErrors": true, "sourceMap": true, "allowJs": true, "baseUrl": ".", "types": ["webpack-env", "jest"], "paths": {"@/*": ["src/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts", "tests/**/*.tsx", "src/store/mutations.js", "src/store/actions.js", "src/store/index.js"], "exclude": ["node_modules"]}