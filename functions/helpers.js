// Mainly for AB3 parent view (encrypt user ID in the public link)
const key = "235012";
const alphanumeric = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';

function encryptStr(str) {
  const keyDigits = String(key).split('').map(Number);
  let result = '';
  for (let i = 0; i < str.length; i++) {
    const char = str[i];
    if (alphanumeric.includes(char)) {
      const currentIndex = alphanumeric.indexOf(char);
      const shift = keyDigits[i % keyDigits.length];
      const newIndex = (currentIndex + shift) % alphanumeric.length;
      result += alphanumeric[newIndex];
    } else {
      result += char; // If character is not alphanumeric, leave it unchanged
    }
  }
  return result;
}
function decryptStr(str) {
  const keyDigits = String(key).split('').map(Number);
  let result = '';
  for (let i = 0; i < str.length; i++) {
    const char = str[i];
    if (alphanumeric.includes(char)) {
      const currentIndex = alphanumeric.indexOf(char);
      const shift = keyDigits[i % keyDigits.length];
      const newIndex = (currentIndex - shift + alphanumeric.length) % alphanumeric.length;
      result += alphanumeric[newIndex];
    } else {
      result += char; // If character is not alphanumeric, leave it unchanged
    }
  }
  return result;
}


const crypt = (text) => {
  const textToChars = (text) => text.split("").map((c) => c.charCodeAt(0));
  const byteHex = (n) => ("0" + Number(n).toString(16)).substr(-2);
  const applySaltToChar = (code) => textToChars(salt).reduce((a, b) => a ^ b, code);

  return text.split("").map(textToChars).map(applySaltToChar).map(byteHex).join("");
};
const decrypt = (encoded) => {
  const textToChars = (text) => text.split("").map((c) => c.charCodeAt(0));
  const applySaltToChar = (code) => textToChars(salt).reduce((a, b) => a ^ b, code);
  return encoded.match(/.{1,2}/g).map((hex) => parseInt(hex, 16)).map(applySaltToChar)
                .map((charCode) => String.fromCharCode(charCode)).join("");
};

// Mainly for API Calls

const popObjVal = (obj, key) => {
  const val = obj[key];
  delete obj[key];
  return val;
}
const toCamel = (str) => {
  return str.replace(/([-_][a-z])/ig, ($1) => {
    return $1.toUpperCase()
      .replace('-', '')
      .replace('_', '');
  });
}
const getUrlParams = (obj) => {
  return Object.keys(obj).map(key => (`${key}=${encodeURIComponent(obj[key])}`)).join('&');
}
const convertKeysToCamelCase = (o) => {
  let newO, origKey, newKey, value;
  if (o instanceof Array) {
    return o.map(function(value) {
        if (typeof value === "object") {
          value = module.exports.convertKeysToCamelCase(value)
        }
        return value
    })
  } else {
    newO = {};
    for (origKey in o) {
      if (Object.prototype.hasOwnProperty.call(o, origKey)) {
        if (origKey === "_RowNumber") { // skip AppSheet row number property
          newO[origKey] = o[origKey];
        } else {
          newKey = toCamel(origKey);
          value = o[origKey]
          if (value instanceof Array || (value != null && value.constructor === Object)) {
            value = module.exports.convertKeysToCamelCase(value)
          }
          newO[newKey] = value
        }
      }
    }
  }
  return newO
}
const parseSheetRows = (rows) => {
  return rows.map(r => {
    delete r.rowNum;
    delete r.rowData;
    return convertKeysToCamelCase(r);
  });
}

module.exports = {
  popObjVal,
  getUrlParams,
  uniqueId: () => Math.random().toString(36).slice(-8),
  randInt: (min, max) => Math.floor(Math.random() * (max - min + 1) + min),
  sleep: (s) => {
    return new Promise((resolve) => {
      setTimeout(resolve, s * 1000);
    });
  },
  convertKeysToCamelCase,
  parseSheetRows,
  encryptStr, decryptStr,
}