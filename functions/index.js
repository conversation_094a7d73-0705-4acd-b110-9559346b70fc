const { onCall } = require("firebase-functions/v2/https");
const moment = require('moment-timezone');
const { callSupabaseApi, callAppSheetApi, callPortalApi, queryPortalData, updateSupabaseRow, } = require('./api');
const { convertKeysToCamelCase } = require('./helpers');
const jsonh = require("./api/jsonh");
const googleapi = require('./googleapi');

const parseJsonhStr = (str) => (convertKeysToCamelCase(str ? jsonh.parse(str) : []));

const TARGET_REGION = 'asia-east2'; // Hong Kong

// Set to HK timezone globally (UTC+08:00)
moment.tz.setDefault("Asia/Hong_Kong");

/**
 * Get private slide image URL
 */
exports.getSlideImageUrl = onCall({ region: TARGET_REGION }, async (request) => {
  const { presentationLink } = request.data;
  return await googleapi.getSlideImageUrl(presentationLink);
});

/**
 * Metabase API (for query portal data)
 */
exports.queryPortalData = onCall({ region: TARGET_REGION }, async (request) => {
  return await queryPortalData(request.data.query);
});

/**
 * One-time access links
 */
exports.checkOnetimeAccessToken = onCall({ region: TARGET_REGION }, async (request) => {
  const { token } = request.data;
  const res = await callSupabaseApi("GET", "access_links", {}, { 'token': `eq.${token}`, 'is_expired': `not.is.true` });
  if (res.length > 0) {
    const row = res[0];
    if (row.loggedInAt == "") { // set the link as used (time expiry)
      updateSupabaseRow("access_links", { 'token': `eq.${token}` }, { 'logged_in_at': moment() });
    }
    return row;
  }
  return null;
});
exports.setOnetimeAccessTokenExpired = onCall({ region: TARGET_REGION }, async (request) => {
  return await updateSupabaseRow("access_links", { 'token': `eq.${request.data.token}` }, { 'is_expired': true });
});

/**
 * Data API
 */
exports.getAppData = onCall({ region: TARGET_REGION, concurrency: 500, memory: '512MiB' }, async (request) => {
  const { type, schoolId, programId } = request.data;
  const parseStep1Questions = (jsonData) => {
    const step1Options = parseJsonhStr(jsonData["step1Options"]);
    const allStep1Questions = parseJsonhStr(jsonData["step1Questions"]);
    let step1Questions = allStep1Questions;
    if (schoolId || programId) {
      step1Questions = step1Questions.filter(q => (["beacon1", schoolId].includes(q.schoolId) || q.programId == programId));
    }
    return step1Questions.map(q => {
      q.options = step1Options.filter(opt => opt.questionId == q.id && opt.status != 'Deleted');
      return q;
    });
  }
  const getStep1QuestionsByGroupKey = async () => {
    const res = await callSupabaseApi('GET', 'cached_step1_questions', {}, { 'group_key': `in.(beacon1,${schoolId},${programId})` });
    const arr = [], arr2d = res.map(r => parseStep1Questions(JSON.parse(r['data'])));
    for (const row of arr2d) {
      for (const q of row) arr.push(q);
    }
    return arr;
  }

  if (type == 'public') {
    // Public Data
    const res = await callSupabaseApi('GET', 'data', {}, { id: 'eq.1' });
    const jsonData = JSON.parse(res[0]['jsonData']);
    const sessions = parseJsonhStr(jsonData["sessions"]);
    const subjects = parseJsonhStr(jsonData["subjects"]);

    return {
      cards: parseJsonhStr(jsonData["cards"]),
      subjects,
      electives: subjects.filter(s => s.type == 'Elective'),
      settings: parseJsonhStr(jsonData["settings"])[0],
      schools: parseJsonhStr(jsonData["schools"]),
      absIntakes: parseJsonhStr(jsonData["absIntakes"]).map(intake => ({
        ...intake,
        //sessions: sessions.filter(s => s.absIntakeId == intake.id),
        sessions: sessions.filter(s => s.clientSchoolId == intake.schoolId),
      })),
      sessions,
      services: parseJsonhStr(jsonData["services"]),
      anchorEventTasks: parseJsonhStr(jsonData["anchorEventTasks"]),
      formQuestions: parseJsonhStr(jsonData["formQuestions"]),
      lessons: parseJsonhStr(jsonData["lessons"]),

      disciplineGroups: parseJsonhStr(jsonData["disciplineGroups"]),
      teacherResponseQuestions: parseJsonhStr(jsonData["teacherResponseQuestions"]),
      teacherResponseOptions: parseJsonhStr(jsonData["teacherResponseOptions"]),

      disciplineClaims: parseJsonhStr(jsonData["disciplineClaims"]),

      // University students
      jobEXes: parseJsonhStr(jsonData["jobEXes"]),

      // Client view
      clients: parseJsonhStr(jsonData["clients"]),
      advertisements: parseJsonhStr(jsonData["advertisements"]),

      // School Roles
      schoolRoles: parseJsonhStr(jsonData["schoolRoles"]),
    };
  } else if (type == "step1Questions") { // prevent refetching heavy portal data on switch school
    return {
      step1Questions: await getStep1QuestionsByGroupKey(),
    }
  } else {
    // Portal data
    const [res, step1Questions] = await Promise.all([
      callSupabaseApi('GET', 'data', {}, { id: 'eq.2' }),
      getStep1QuestionsByGroupKey(),
    ]);
    const jsonData = JSON.parse(res[0]['jsonData']);

    return jsonh.pack([{
      step1Questions: jsonh.stringify(step1Questions),
      programs: jsonData["programs"],
      institutions: jsonData["institutions"],
      sectors: jsonData["sectors"],
      segments: jsonData["segments"],
      disciplines: jsonData["disciplines"],
      professions: jsonData["professions"],
      professionTabs: jsonData["professionTabs"],
      disciplineGroups: jsonData["disciplineGroups"],
      /*programs: parseJsonhStr(jsonData["programs"]),
      institutions: parseJsonhStr(jsonData["institutions"]),
      sectors: parseJsonhStr(jsonData["sectors"]),
      segments: parseJsonhStr(jsonData["segments"]),
      disciplines: parseJsonhStr(jsonData["disciplines"]),
      professions: parseJsonhStr(jsonData["professions"]),
      professionTabs: parseJsonhStr(jsonData["professionTabs"]),*/
    }]);
  }
});

// Portal
exports.getPortalProgramData = onCall({ region: TARGET_REGION }, async (request) => {
  const { programId, } = request.data;
  const endpoint = `programs/${programId}`;
  const [program, contacts] = await Promise.all([
    callPortalApi(endpoint, null),
    queryPortalData(`SELECT cr.id, c.name, c.source_link, cr.entity_type, cr.entity_id, cr.relation FROM contact_relation cr, contact c
                      WHERE c.id = cr.contact_id AND cr.contact_id IN
                      (SELECT contact_id FROM contact_relation WHERE entity_type = 'program' AND entity_id = ${programId} AND relation = 'Alumni')`)
  ]);
  return { ...program, alumniContactRelations: contacts.map(([id, name, sourceLink, entityType, entityId, relation]) => ({ id, name, sourceLink, entityType, entityId, relation }))};
});
exports.getExtraInfosByGroupId = onCall({ region: TARGET_REGION }, async (request) => {
  const relatedExtraInfo = {};
  const res = await queryPortalData(`SELECT e.type, e.name, e.content, l.name FROM extra_info e
                              LEFT JOIN location l ON e.location_id = l.id
                              WHERE extra_info_group_id = ${request.data.extraInfoGroupId}`);
  for (const info of res) {
    const [type, name, content, locationName] = info;
    if (request.data.includeAllKeys || !type.startsWith('_')) {
      (relatedExtraInfo[type] = relatedExtraInfo[type] || []).push({ name, content, location: locationName })
    }
  }
  return relatedExtraInfo;
});
exports.callPortalApi = onCall({ region: TARGET_REGION }, async (request) => {
  const { endpoint, payload } = request.data;
  return await callPortalApi(endpoint, payload);
})

/**
 * General / Common API
 */
exports.createNewFeedback = onCall({ region: TARGET_REGION }, async (request) => {
  const { name, email, feedback, photoLink, programId, } = request.data;
  return await callAppSheetApi("Add", "user_messages", null, [{
    'user_id': request.auth ? request.auth.uid : "",
    'name': name,
    'email': email,
    'message': feedback,
    'photo': photoLink,
    'program_id': programId,
  }]);
});

exports.user = require('./lib/user');
exports.event = require('./lib/event');
exports.abs = require('./lib/abs');
exports.teacher = require('./lib/teacher');
exports.slp = require('./lib/slp');
exports.jobEX = require('./lib/jobEX');
exports.booking = require('./lib/booking');
exports.chat = require('./lib/chat');
exports.achievejupas = require('./lib/achievejupas');
exports.internal = require('./lib/internal');
exports.school = require('./lib/school');
exports.video = require('./lib/video');
exports.service = require('./lib/service');