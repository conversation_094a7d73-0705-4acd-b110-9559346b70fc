const fs = require('fs');
const {google} = require('googleapis');

const CREDENTIALS_PATH = 'credentials.json';
let oAuth2Client = null;

/**
 * Creates a new script project, upload a file, and log the script's URL.
 * @param {google.auth.OAuth2} auth An authorized OAuth2 client.
 */
async function callAppsScript(functionName, params=[]) {
  if (oAuth2Client === null) {
    const credentials = JSON.parse(fs.readFileSync(CREDENTIALS_PATH));
    const {client_secret, client_id, redirect_uris} = credentials.installed;
    oAuth2Client = new google.auth.OAuth2(client_id, client_secret, redirect_uris[0]);
    oAuth2Client.setCredentials({
      refresh_token: credentials.refresh_token
    });
  }
  const script = google.script({version: 'v1', auth: oAuth2Client});
  const res = await script.scripts.run({
    scriptId: 'AKfycbxwMIfB98ipNTLNvhLNoCa-4wpB7knmDElE4UkIAsRtYobacfBhKsoyhi3UxdZVunIJ',
    resource: {
      function: functionName,
      parameters: [...params, process.env.GCLOUD_PROJECT],
      //devMode: true
    }
  });
  return res.data;
}

module.exports = {
  callAppsScriptFunc: async (funcName, params=[]) => {
    return await callAppsScript(funcName, params);
  }
}