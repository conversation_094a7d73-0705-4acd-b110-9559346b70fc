const axios = require('axios').default;
const jsonh = require("./jsonh");
const { convertKeysToCamelCase, sleep, randInt, getUrlParams, } = require('../helpers');

const config = {
  FrontAppSheet: {
    appId: "170c398b-7679-4260-bc09-24323d647078",
    accessKey: "V2-pSrHG-4g41U-LZ4iE-PLK6T-qZM6Y-JY8lO-2Y8EJ-nlGDc",
  },
  AppSheet: {
    appId: "5fc9059c-6827-4e7d-b2cd-f30648825d10",
    accessKey: "V2-VIXwF-zCUHK-uyEvQ-uiBt8-szyEX-Rut7l-057Y0-8ZfvQ",
  },
  portalApiUrl: "https://api.portal.fdmt.hk/achieve-bot",
  portalApiKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiYWRtaW4iLCJpYXQiOjE1ODAwNTIzNjcsImF1ZCI6InBvcnRhbF9hcHAiLCJpc3MiOiJwb3J0YWxfYXBpIiwic3ViIjoiMjQ3In0.E5G85ygozQP9nJCm7O6AIDBRR5uUpkEfZImCemjt-U4",

  Supabase: {
    apiUrl: "https://istuyswzjtuletbqejcr.supabase.co",
    //anonKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlzdHV5c3d6anR1bGV0YnFlamNyIiwicm9sZSI6ImFub24iLCJpYXQiOjE2NzQ5MjA0MDUsImV4cCI6MTk5MDQ5NjQwNX0.uC_eYyozFYI0LVjZ6YZQHj0U3K92I48pGV054f4P33E",
    anonKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlzdHV5c3d6anR1bGV0YnFlamNyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTY3NDkyMDQwNSwiZXhwIjoxOTkwNDk2NDA1fQ.a_W5y6iM5hW1p-LYIJwNMoowQRsd1a05iHcnloGIRj8",
  }
}

let SESSION_ID = null;

// Create a single supabase client for interacting with your database
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(config.Supabase.apiUrl, config.Supabase.anonKey);

/**
 * API call functions
 */
const callSupabaseApi = async (method, tableName, customHeaders = null, filters = {}, payload = null) => {
  const { apiUrl, anonKey } = config.Supabase;
  let requestUrl = `${apiUrl}/rest/v1/${tableName}`;
  const options = {
    method,
    headers: {
      'content-type': 'application/json',
      "apikey": anonKey,
      "Authorization": `Bearer ${anonKey}`,
      ...(customHeaders || {}),
    },
  };
  //if (filters) requestUrl += "?" + getUrlParams(filters);
  if (filters) {
    for (const [key, val] of Object.entries(filters)) {
      if (val === "" || val == null) filters[key] = "is.null";
      else if (Array.isArray(val)) filters[key] = `in.(${val.join(",")})`;
      else if (key != 'select' && !val.toString().includes(".")) filters[key] = `eq.${val}`;
    }
    requestUrl += "?" + getUrlParams(filters);
  }
  if (payload) options.data = JSON.stringify(payload);

  let tryCount = 0;
  const maxTries = 5; // retry on failure
  while (true) {
    try {
      const res = await axios(requestUrl, options);
      if (res.data && Array.isArray(res.data)) {
        for (let i = 0; i < res.data.length; i++) {
          res.data[i] = convertKeysToCamelCase(res.data[i]);
        }
      }
      return res.data;
    } catch (error) {
      if (++tryCount > maxTries) {
        console.log(error);
        throw error;
      }
      await sleep(randInt(1, 3));
    }
  }
}
const getSupabaseRows = (table, filters) => (callSupabaseApi('GET', table, {}, filters));
const insertSupabaseRows = (table, rows) => (callSupabaseApi('POST', table, { "Prefer": "return=representation" }, "", rows));
const upsertSupabaseRows = (table, rows) => (callSupabaseApi('POST', table, { "Prefer": "resolution=merge-duplicates" }, "", rows));
const updateSupabaseRow = (table, filters, payload) => (callSupabaseApi('PATCH', table, { "Prefer": "return=representation" }, filters, payload));
const deleteSupabaseRows = (table, filters) => (callSupabaseApi('DELETE', table, {}, filters));

const SB_TABLES = ["user", "user_nomination_records", "user_browsed_professions", "user_browsed_sectors", "user_browsed_segments",
                    "user_liked_professions", "user_liked_sectors", "user_liked_segments", "Session User Responses",
                    "teacher_responses"];
const callAppSheetApi = async (action, tableName, selector = null, rows = null, appId = null, accessKey = null) => {
  let tryCount = 0;
  const maxTries = 5; // retry on failure
  appId = appId || config.AppSheet.appId;
  accessKey = accessKey || config.AppSheet.accessKey;
  const timezone = SB_TABLES.includes(tableName) ? "UTC" : "China Standard Time"; // use UTC in Supbase tables
  const payload = { "Action": action, "Properties": { "Timezone": timezone } };
  if (selector) payload["Properties"]["Selector"] = selector;
  if (rows) payload["Rows"] = rows;
  const url = `https://api.appsheet.com/api/v2/apps/${appId}/tables/${tableName}/Action`;

  while (true) {
    try {
      const response = await axios.post(url, payload, { headers: { "applicationAccessKey": accessKey } });
      if (response.data["Rows"]) response.data = response.data["Rows"]; // for Add or Edit action
      for (let i = 0; i < response.data.length; i++) {
        response.data[i] = convertKeysToCamelCase(response.data[i]);
      }
      return response.data;
    } catch (error) {
      if (++tryCount > maxTries) {
        console.log(error);
        throw error;
      }
      await sleep(randInt(1, 3));
    }
  }
}
const callFrontAppSheetApi = async (action, tableName, selector = null, rows = null) => {
  return await callAppSheetApi(action, tableName, selector, rows, config.FrontAppSheet.appId, config.FrontAppSheet.accessKey);
}

const callPortalApi = async (endpoint, payload) => {
  const url = `https://api.portal.fdmt.hk/${endpoint}`;
  const options = { headers: { "Authorization": `Bearer ${config.portalApiKey}` } };
  if (payload) options.data = payload;
  const response = await axios.get(url, options);
  if (response.data && Array.isArray(response.data)) {
    for (let i = 0; i < response.data.length; i++) {
      response.data[i] = convertKeysToCamelCase(response.data[i]);
    }
  }
  return response.data;
}

const queryMetabase = async (query, username, password) => {
  if (SESSION_ID == null) {
    const payload = { "username": username, "password": password };
    const res = await axios.post("https://metabase.portal.fdmt.hk/api/session", payload);
    SESSION_ID = res.data.id;
  }
  const payload = {
    "database": 2,
    "native": { "query": query },
    "type": "native"
  };
  return (await axios.post("https://metabase.portal.fdmt.hk/api/dataset", payload, { headers: { "X-Metabase-Session": SESSION_ID} })).data.data.rows;
}

const queryPortalData = async (query) => {
 const { METABASE_USERNAME, METABASE_PASSWORD } = process.env;
 const baseQuery = `${query} LIMIT`;
 let start = 0;
 const limit = 2000; // max rows to return from Metabase each round
 let res = await queryMetabase(`${baseQuery} ${start},${limit}`, METABASE_USERNAME, METABASE_PASSWORD);
 let rows = res;
 while (res.length >= limit) {
   start += limit;
   res = await queryMetabase(`${baseQuery} ${start},${limit}`, METABASE_USERNAME, METABASE_PASSWORD);
   rows = rows.concat(res);
 }
 return rows;
}

module.exports = {
  supabase,
  getSupabaseRows, insertSupabaseRows, updateSupabaseRow, upsertSupabaseRows, deleteSupabaseRows,
  callSupabaseApi,
  callAppSheetApi,
  callFrontAppSheetApi,
  callPortalApi,
  queryMetabase,
  queryPortalData,
}