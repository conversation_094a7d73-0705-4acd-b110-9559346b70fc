const { google } = require('googleapis');
const { uniqueId } = require('./helpers');

const auth = new google.auth.GoogleAuth({
  keyFile: './client_secrets.json',
  scopes: [
    'https://www.googleapis.com/auth/spreadsheets',
    'https://www.googleapis.com/auth/presentations.readonly',
  ],
});
const sheets = google.sheets({version: 'v4', auth});
const slides = google.slides({ version: 'v1', auth });

// Sheet Helper Functions
let ss;
const store = {};
const spreadsheetId = "1Xd8Hdm4sZkaGxvcHloLNKOF8lj77WnAAbC8O5xbyqQI"; // Cockpit
const SHEET_IDS = {
}
const atlasSpreadsheetId = "1NCOKb8JHJGGG3oXM7H08D_j2g0puUkZouai5nWFQ-Ww";
const ATLAS_SHEET_IDS = {
  'CareerPlan': 2099699634,
}
const statSpreadsheetId = "1AUF_hGVAJlkhNzNfww86hpObgXLjXMQfNWhZjIBhIzM"
const STAT_SHEET_IDS = {
  "PageAccessRecord": 2059683287,
}

function getColLetter(column) {
  let temp, letter = '';
  while (column > 0) {
    temp = (column - 1) % 26;
    letter = String.fromCharCode(temp + 65) + letter;
    column = (column - temp - 1) / 26;
  }
  return letter;
}
function getSheetProps(sheet) {
  const { title, gridProperties } = sheet.properties;
  const { rowCount, columnCount } = gridProperties;
  return { title, rowCount, columnCount };
}
function getSheetById(ss, id) {
  for (const sheet of ss.sheets) {
    if (sheet.properties.sheetId == id) return sheet;
  }
  return null;
}
async function getHeadersOfSheet(sheet) {
  const { title } = getSheetProps(sheet);
  return sheet ? (await sheets.spreadsheets.values.get({ spreadsheetId: sheet.ssId, range: `${title}!1:1` })).data.values[0] : [];
}
async function getAllRows(sheet) {
  const { title, rowCount, columnCount } = getSheetProps(sheet);
  return rowCount <= 1 ? [] : (await sheets.spreadsheets.values.get({ spreadsheetId: sheet.ssId, range: `${title}!A2:${getColLetter(columnCount)}` })).data.values;
}
async function getRowValuesOfSheet(sheet, rowNum) {
  const { title, rowCount, columnCount } = getSheetProps(sheet);
  if (rowNum == null) rowNum = 2;
  return rowCount <= 1 ? [] : (await sheets.spreadsheets.values.get({ spreadsheetId: sheet.ssId, range: `${title}!A${rowNum}:${getColLetter(columnCount)}${rowNum}` })).data.values[0];
}

function rowMatchSearch(targetObj, keyValSearchPairs, caseInSensitive) {
  for (const objKey in keyValSearchPairs) {
    if (!(objKey in targetObj)) return false; // invalid field
    let values = keyValSearchPairs[objKey], containsMatch = false;
    values = Array.isArray(values) ? values : [values];
    for (let j = 0; j < values.length && !containsMatch; j++) {
      const objVal = values[j];
      if (!caseInSensitive) {
        if (targetObj[objKey] == objVal) containsMatch = true;
      } else { // case insensitive
        if (targetObj[objKey].toString().toLowerCase() == objVal.toString().toLowerCase()) containsMatch = true;
      }
    }
    if (!containsMatch) return false;
  }
  return true; // all pass
}
function getObjData(rowContents, rowNum, headers) {
  const numberFields = [], trimFields = [], booleanFields = [], dateFields = [];
  const obj = { rowNum, rowData: rowContents };
  for (const i in rowContents) {
    const field = headers[i];
    let cellVal = rowContents[i];
    if (numberFields.includes(field)) cellVal = Number(cellVal);
    else if (trimFields.includes(field)) cellVal = cellVal.trim();
    else if (booleanFields.includes(field)) cellVal = (cellVal == 1);
    else if (dateFields.includes(field)) cellVal = new Date(cellVal);
    obj[field] = cellVal;
  }
  return obj;
}
async function updateSheet(sheet, headers, objs) {
  const data = [];
  for (const obj of objs) {
    for (const field in obj) {
      const colIdx = headers.indexOf(field);
      if (colIdx !== -1) {
        data.push({ range: `${sheet.properties.title}!${getColLetter(colIdx+1)}${obj.rowNum}`, values: [[obj[field]]] })
      }
    }
  }
  return (await sheets.spreadsheets.values.batchUpdate({ spreadsheetId: sheet.ssId, requestBody: { valueInputOption: "USER_ENTERED", data } })).data;
}
async function insertObjsToSheet(sheet, headers, objs) {
  const values = [], range = `${sheet.properties.title}!A:A`;
  for (const obj of objs) {
    const contents = [];
    for (const field of headers) contents.push(obj[field] || "");
    values.push(contents);
  }
  return (await sheets.spreadsheets.values.append({ spreadsheetId: sheet.ssId, range, valueInputOption: "USER_ENTERED", insertDataOption: "INSERT_ROWS", requestBody: { values } })).data;
}

async function initEntity_(entityName, sheetId, targetSs) {
  if (ss === undefined) ss = (await sheets.spreadsheets.get({ spreadsheetId })).data;
  const sheet = getSheetById(targetSs || ss, sheetId);
  sheet.ssId = (targetSs || ss).spreadsheetId;
  store[entityName] = { sheet, headers: await getHeadersOfSheet(sheet) };
}
async function getStoreEntity(entityName) {
  if (!(entityName in store)) {
    if (ATLAS_SHEET_IDS[entityName]) {
      const targetSs = (await sheets.spreadsheets.get({ spreadsheetId: atlasSpreadsheetId })).data;
      await initEntity_(entityName, ATLAS_SHEET_IDS[entityName], targetSs);
    } else if (STAT_SHEET_IDS[entityName]) {
      const targetSs = (await sheets.spreadsheets.get({ spreadsheetId: statSpreadsheetId })).data;
      await initEntity_(entityName, STAT_SHEET_IDS[entityName], targetSs);
    } else {
      await initEntity_(entityName, SHEET_IDS[entityName]);
    }
  }
  return store[entityName];
}
async function insertEntity_(entityName, obj) {
  const entity = await getStoreEntity(entityName);
  return await insertObjsToSheet(entity.sheet, entity.headers, [obj])
}
async function insertEntities_(entityName, objs) {
  const entity = await getStoreEntity(entityName);
  return await insertObjsToSheet(entity.sheet, entity.headers, objs)
}
async function updateEntity_(entityName, targetRow, newObj) {
  const entity = await getStoreEntity(entityName);
  newObj.rowNum = targetRow;
  return await updateSheet(entity.sheet, entity.headers, [newObj]);
}
async function updateEntities_(entityName, newObjs) {
  const entity = await getStoreEntity(entityName);
  return await updateSheet(entity.sheet, entity.headers, newObjs);
}
async function getEntity_(entityName, queryObj, caseInSensitive, targetKey) {
  const entity = await getStoreEntity(entityName), allRows = await getAllRows(entity.sheet);
  queryObj = queryObj || {};
  for (const i in allRows) {
    const obj = getObjData(allRows[i], +i+2, entity.headers);
    if (rowMatchSearch(obj, queryObj, caseInSensitive)) return targetKey ? obj[targetKey] : obj;
  }
  return null;
}
async function getEntityByRowNum_(entityName, rowNum) {
  const entity = await getStoreEntity(entityName);
  return getObjData(await getRowValuesOfSheet(entity.sheet, rowNum), rowNum, entity.headers);
}
async function getEntities_(entityName, queryObj, caseInSensitive) {
  const entity = await getStoreEntity(entityName), allRows = await getAllRows(entity.sheet), entities = [];
  queryObj = queryObj || {};
  for (const i in allRows) {
    const obj = getObjData(allRows[i], +i+2, entity.headers);
    if (rowMatchSearch(obj, queryObj, caseInSensitive)) entities.push(obj);
  }
  return entities;
}
async function getGroupedEntities_(entityName, groupedBy) {
  const entity = await getStoreEntity(entityName), allRows = await getAllRows(entity.sheet), groupedEntities = {};
  for (const i in allRows) {
    const obj = getObjData(allRows[i], +i+2, entity.headers);
    const groupKey = obj[groupedBy];
    if (groupKey) (groupedEntities[groupKey] = groupedEntities[groupKey] || []).push(obj);
  }
  return groupedEntities;
}

/**
 * Main Exporting Functions
 */
module.exports = {
  // Export slide as PNG (mainly for private presentation)
  getSlideImageUrl: async (presentationLink) => {
    const presentationId = presentationLink.match(/presentation\/d\/(.*?)(\/|$)/)?.[1];
    const slideId = presentationLink.match(/slide=id\.(.*?)($|&)/)?.[1];
    const result = await slides.presentations.pages.getThumbnail({
      presentationId,
      pageObjectId: slideId.split("id.").pop(),
    });
    return result.data.contentUrl;
  },

  // University Student Career Plan (To be migrated to Supabase)
  getCareerPlanByUserId: async (userId) => {
    return await getEntity_('CareerPlan', { "user_id": userId });
  },

  // Analytics
  insertPageAccessRecord: async (userId, pageKey, accessTime, leaveTime) => {
    insertEntity_('PageAccessRecord', {
      "id": `p${uniqueId()}`,
      "user_id": userId,
      "page_key": pageKey,
      "access_time": accessTime,
      "leave_time": leaveTime,
    })
  }
};