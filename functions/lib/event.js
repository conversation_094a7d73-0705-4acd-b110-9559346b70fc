const { onCall } = require("firebase-functions/v2/https");
const { callAppSheetApi, callFrontAppSheetApi, callSupabaseApi, updateSupabaseRow,
        insertSupabaseRows, upsertSupabaseRows, getSupabaseRows, } = require('../api');
const TARGET_REGION = 'asia-east2'; // Hong Kong
const { uniqueId, convertKeysToCamelCase, popObjVal, } = require('../helpers');

const moment = require('moment-timezone');
moment.tz.setDefault("Asia/Hong_Kong");

exports.getSessionsWithQuota = onCall({ region: TARGET_REGION }, async (request) => {
  return await callFrontAppSheetApi("Find", "Sessions", `FILTER(Sessions, AND( ISNOTBLANK([quota]), [date] > TODAY() ))`);
});

/**
 * Updates session information
 */
exports.updateSession = onCall({ region: TARGET_REGION }, async (request) => {
  const {id, date, venue, startTime, remarks, status} = request.data.session
  return await callFrontAppSheetApi("Edit", "Sessions", null, [{
    "id": id,
    "date": date,
    "venue": venue,
    "start_time": startTime,
    "remarks": remarks,
    "status": status
  }]);
});

/**
 * Mainly for saving professors' comments to students
 */
exports.upsertUserUsers = onCall({ region: TARGET_REGION }, async (request) => {
  const userUserRows = request.data.userUsers.map(uu => ({
    'user_id': uu.userId || request.auth.uid,
    'target_user_id': uu.targetUserId,
    'session_id': uu.sessionId || 'unknown',
    'reaction': uu.reaction,
    'reason': uu.reason || null,
    'order': uu.order || null,
    'created_at': uu.createdAt || new Date(),
    'updated_at': new Date(),
    'grade': uu.grade || null,
  }));
  return await upsertSupabaseRows("user_users", userUserRows);
});

/**
 * Get Event Applicants / Participants (user_session_responses -> users)
 */
exports.getSessionParticipants = onCall({ region: TARGET_REGION }, async (request) => {
  const { sessionId } = request.data;
  const res = await callSupabaseApi('GET', 'user_session_responses', {}, {
    'select': `*,user:users(*,user_browsed_professions(profession_id),user_liked_professions(profession_id)` +
              `,user_browsed_segments(segment_id),user_liked_segments(segment_id)` +
              `,user_browsed_sectors(sector_id),user_liked_sectors(sector_id)` +
              `,formResponses:user_form_responses(*)` +
              `,universityStudent:university_students(*),secondarySchoolStudent:secondary_school_students(*)` +
              `,userDisciplines:user_disciplines(discipline_id,reaction,action,reason,created_at,app_state,order),userProfessions:user_professions(profession_id,reaction,reason,action,created_at,app_state,order)` +
              `,userPrograms:user_programs(program_id,reaction,reason,created_at,app_state,order,plan_id)` +
              `,userUsers:user_users!user_users_user_id_fkey(*),votedByUsers:user_users!user_users_target_user_id_fkey(*))`,
    'session_id': sessionId,
    //'response': ["Attended", "Yes", "Confirmed"],
    'response': ["Attended"], // 20250702: only show attended students (for better reporting)
    'users.user_programs.order': 'order.asc',
    'users.user_programs.reaction': 'like',
    'users.user_professions.order': 'order.asc',
    'users.user_users.session_id': sessionId,
  });
  return res.map(r => {
    const user = popObjVal(r, 'user');
    return { ...user, sessionResponse: r };
  }).filter(r => r.roles != 'admin'); // skip admin user
});

/**
 * Form Responses (mainly feedback forms)
 */
exports.upsertFormResponses = onCall({ region: TARGET_REGION }, async (request) => {
  const { sessionId, taskId, responses } = request.data;
  const formattedSessionId = sessionId || 'all'; // if no session ID then set default value
  const userId = request.auth.uid;
  callFrontAppSheetApi("Add", "Raw Form Responses", null, [{
    "user_id": userId,
    "session_id": formattedSessionId, // event session
    "task_id": taskId, // anchor event task
    "data": JSON.stringify(responses),
  }]);
  return await upsertSupabaseRows("user_form_responses", responses.map(r => ({
    "user_id": userId,
    "session_id": formattedSessionId,
    "task_id": taskId,
    "question_id": r.questionId,
    "question_title": r.questionTitle,
    "answer": r.answer,
  })));
});

/**
 * Work Event Responses
 */
exports.updateEventResponse = onCall({ region: TARGET_REGION, concurrency: 500 }, async (request) => {
  const { targetEvent: ev, response, rollCallCode, type, withdrawReason, schoolId, jobEXIntakeId,
          workshopTimePreference, userConsentRecordId, } = request.data;
  let { phone, userId } = request.data;
  if (userId && userId.toString().length == 8) {
    phone = userId;
    userId = '';
  }
  let responseObj = {
    "user_jobex_intake_id": jobEXIntakeId,
    "user_school_id": schoolId,
    "lesson_id": ev.anchorEventId,
    "session_id": ev.id,
    "user_id": (request.auth ? request.auth.uid : userId) || '',
    "event_name": ev.displayName,
    "event_date_time": ev.formattedDateTime,
    "phone": phone,
    "response": response,
    "is_ack_msg_sent": false, // for sending WhatsApp notifications
    "updated_at": new Date(),
  };
  if (userConsentRecordId) responseObj["parent_consent_record_id"] = userConsentRecordId;
  if (withdrawReason) responseObj["withdraw_reason"] = withdrawReason; // mainly for response = No
  if (workshopTimePreference) responseObj['workshop_time_preference'] = workshopTimePreference; // for BBAWork / HealthcareWork
  if (rollCallCode) {
    responseObj = {
      ...responseObj,
      'attended': 'Yes',
      'roll_call_code': rollCallCode,
      'roll_call_time': moment().format('YYYY/MM/DD HH:mm:ss'),
    };
  } else {
    responseObj['confirmed'] = (["Yes", "Confirmed"].includes(response) ? "Yes" : "No");
  }

  if (ev.userResponse && ev.userResponse.id) {
    //callFrontAppSheetApi("Edit", "Session User Responses", null, [{ id: ev.userResponse.id, ...responseObj }]);
    updateSupabaseRow("user_session_responses", { id: `eq.${ev.userResponse.id}` }, responseObj);
    responseObj = { ...ev.userResponse, ...responseObj };
  } else {
    // Not yet logged in / not yet response
    if (!responseObj['user_id']) {
      const existingEvResponse = (await getSupabaseRows("user_session_responses", { 'session_id': `eq.${ev.id}`, 'phone': `eq.${phone}` }))[0];
      if (existingEvResponse) {
        responseObj['user_id'] = existingEvResponse.userId;
        updateSupabaseRow("user_session_responses", { id: `eq.${existingEvResponse.id}` }, responseObj);
        responseObj = { ...existingEvResponse, ...responseObj };
      } else {
        // Find user by phone number
        const user = (await callSupabaseApi('GET', 'users', {}, {
          'phone': `eq.${phone}`,
          'select': `*`,
        }))[0];
        if (user) {
          responseObj['user_id'] = user.id;
          responseObj.id = `r${uniqueId()}`;
          insertSupabaseRows("user_session_responses", [responseObj]);
        }
      }
    }
    else if (responseObj['user_id'] || responseObj['phone']) {
      responseObj.id = `r${uniqueId()}`;
      //callFrontAppSheetApi("Add", "Session User Responses", null, [responseObj]);
      insertSupabaseRows("user_session_responses", [responseObj]);
    }
  }
  return convertKeysToCamelCase(responseObj);
});