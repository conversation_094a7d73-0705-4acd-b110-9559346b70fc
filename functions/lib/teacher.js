const { onCall } = require("firebase-functions/v2/https");
const { callSupabaseApi, updateSupabaseRow, upsertSupabaseRows, callAppSheetApi, callFrontAppSheetApi, insertSupabaseRows } = require('../api');
const TARGET_REGION = 'asia-east2'; // Hong Kong
const { uniqueId, convertKeysToCamelCase, } = require('../helpers');

/**
 * Teacher API
 */

/**
 * Get Mock JUPAS stat for a school
 */
exports.getSchoolMockJUPASResults = onCall({ region: TARGET_REGION }, async (request) => {
  const { schoolId, yearDSE, bandOrders, } = request.data;
  const filters = {
    'school_id': `eq.${schoolId}`,
    'roles': `secondary-school-student`,
    'select': `id,userPrograms:user_programs(program_id,order)`,
    'user_programs.reaction': 'like',
  };
  if (yearDSE) {
    filters['year_DSE'] = `eq.${yearDSE}`;
  } else {
    filters['or'] = `(year_DSE.is.null,year_DSE.gte.${new Date().getFullYear()})`;
  }
  const groupedProgramIds = {}, targetBandOrders = bandOrders || ['1', '2', '3']; // Default: A1,A2,A3
  for (const order of targetBandOrders) groupedProgramIds[order] = [];

  const res = await callSupabaseApi('GET', 'users', {}, filters);
  for (const user of res) {
    for (const up of user.userPrograms) {
      const { order, programId } = up;
      if (order && order.toString() in groupedProgramIds) {
        groupedProgramIds[order].push(programId);
      }
    }
  }
  return groupedProgramIds;
});

/**
 * Get users (students / teachers) of teacher's school
 */
exports.getUsersBySchoolId = onCall({ region: TARGET_REGION }, async (request) => {
  const { schoolId, isAdmin } = request.data;
  //const selector = isAdmin ? `in.(${schoolId},beacon1)` : `eq.${schoolId}`;
  const selector = `eq.${schoolId}`;
  const res = await callSupabaseApi('GET', 'users', {}, {
    'school_id': selector,
    'or': `(year_DSE.is.null,year_DSE.gte.${new Date().getFullYear()})`,
    'select': `id,full_name,preferred_name,chinese_name,phone,email,gender,roles,wa_group_id,wa_group_link,user_in_wa_group,profile_pic,school_id,class,student_number` +
              `,studying_curriculum,studying_electives,year_DSE,verification_result,rank_in_form` +
              `,relatedTags:tag_users(tag_id)` +
              `,userDisciplines:user_disciplines(discipline_id,reaction,action,reason,created_at,app_state,order)` +
              `,userProfessions:user_professions(profession_id,reaction,reason,action,created_at,app_state,order)` +
              `,userPrograms:user_programs(program_id,reaction,reason,created_at,app_state,order,plan_id)` +
              `,userSubjectGrades:user_subject_grades(*)` +
              `,userConsentRecords:user_consent_records(created_at,target,signature_img)` +
              `,sessionResponses:user_session_responses(*)` +
              `,secondarySchoolStudent:secondary_school_students(*)` +
              `,teacher:teachers(*,classRoles:teacher_class_roles(*))` +
              `,teacherResponses:teacher_responses!teacher_responses_user_id_fkey(*,assignor:users!teacher_responses_assigned_by_fkey(id,full_name),questionAnswers:teacher_response_answers(question_id,question_title,answer,updated_at)))`,
    'user_disciplines.reaction': 'like',
    'user_professions.reaction': 'like',
    'user_programs.reaction': 'like',
    'user_programs.order': 'order.asc',
    'user_professions.order': 'order.asc',
    'user_disciplines.order': 'order.asc',
  });
  for (const user of res) {
    user.tagIds = (user.relatedTags || []).map(obj => obj.tagId);
  }
  return res;
});

/**
 * Verify a student
 */
exports.updateUserVerificationResult = onCall({ region: TARGET_REGION }, async (request) => {
  const { verificationResult, userId } = request.data;
  const updatingObj = {
    'verification_result': verificationResult,
    'verification_result_updated_at': new Date(),
  }
  callSupabaseApi('POST', 'user_verification_records', {}, {}, {
    'target_user_id': userId,
    'verification_result': verificationResult,
    'verified_by': request.auth.uid,
  });
  return await callSupabaseApi('PATCH', 'users', {}, { id: `eq.${userId}` }, updatingObj);
});

/**
 * Teacher responses on specific session / service
 */
exports.updateTeacher = onCall({ region: TARGET_REGION }, async (request) => {
  const { userId, employmentStatus, schoolRoles, classRoles,
          userPhone, userWAGrpId, schoolId, } = request.data;
  
  let notificationMsg = "";

  // Check upsert teacher class role rows
  if (classRoles && classRoles.length > 0) {
    await upsertSupabaseRows('teacher_class_roles', classRoles.map(cr => ({
      "id": cr.id,
      "user_id": userId,
      "role": cr.role,
      "classes": cr.classes,
      "subject": null,
      "remarks": cr.remarks || null,
      "status": cr.status || "active",
      "created_at": cr.createdAt || new Date().toISOString(),
      "updated_at": new Date().toISOString(),
    })));

    notificationMsg = `Thanks for updating your roles via ab.fdmt.hk/update-roles.
Your latest role(s) in class(es): ${classRoles.filter(cr => cr.status != 'removed').map(cr => (`*${cr.role} (${cr.classes})*`)).join(' & ')}`;
  }

  if (schoolRoles) {
    notificationMsg = `Thanks for updating your roles via ab.fdmt.hk/update-roles.
Your latest role(s) in ${schoolId?.toUpperCase() || 'school'}: *${schoolRoles}*`;
  }
  
  if (notificationMsg) {
    // Send WhatsApp to teachers if school roles / class roles changed
    callFrontAppSheetApi("Add", "GAS Commands", null, [{
      "func_name": "teacherSendWhatsAppMsgToGrp",
      "param1": `@852${userPhone} ${notificationMsg}`,
      "param2": userPhone,
      "param3": userWAGrpId,
      //"extra_params": [],
    }]);
  }

  // Check update teacher object
  const updatingObj = { 'updated_at': new Date().toISOString() };

  if (employmentStatus !== undefined) updatingObj['employment_status'] = employmentStatus;
  if (schoolRoles !== undefined) updatingObj['school_roles'] = schoolRoles;

  return Object.keys(updatingObj).length > 1 ? await updateSupabaseRow('teachers', { 'user_id': `eq.${userId}` }, updatingObj) : false;
});

exports.upsertTeacherResponse = onCall({ region: TARGET_REGION }, async (request) => {
  const logObj = (obj) => {
    try {
      console.log(JSON.stringify(obj));
    } catch (e) {
      console.error(e);
    }
  }
  const { targetEvent, serviceId, teacherResponse, schoolId, questionAnswers, } = request.data;
  const { response, optionId, estNumOfStudents, preferredSessionDates, preferredSessionTime, preferredVenue, eventName,
          studentForms, classes, arrivalTime, roomAvailableTime, endTime, type,
          assignedTo, role, assignedBy, intakeYear, targetClass, } = teacherResponse;
  let { phone, userId } = request.data;
  if (userId && userId.toString().length == 8) {
    phone = userId;
    userId = '';
  }
  userId = (userId && userId.toString().length > 8) ? userId : (request.auth ? request.auth.uid : '');

  // Default: response to specific services / sessions
  const ev = targetEvent || {};
  let responseObj = {
    "session_id": ev.id || "",
    "service_id": serviceId || "",
    "user_id": userId,
    "event_name": eventName,
    "event_date_time": ev.formattedDateTime,
    "phone": phone || "",
    "response": response,
    "option_id": optionId || null,
    "est_num_of_students": estNumOfStudents,
    //"preferred_session_dates": Array.isArray(preferredSessionDates) ? preferredSessionDates.join(" , ") : "",
    "preferred_session_dates": preferredSessionDates,
    "preferred_session_time": preferredSessionTime,
    "preferred_venue": preferredVenue,
    "school_id": schoolId || "",
    "intake_year": intakeYear || "",
    "target_class": targetClass || "", // track response by classes

    // AB4 teacher form (session info updates)
    "student_forms": studentForms || "",
    "classes": classes || "",
    "arrival_time": arrivalTime || "",
    "room_available_time": roomAvailableTime || "",
    "end_time": endTime || "",

    // Mainly for Survey
    "type": type,
    "updated_at": new Date(),
  };

  if (assignedTo) {
    responseObj["type"] = "assign";
    responseObj["user_id"] = assignedTo; // target teacher (User ID)
    responseObj["assigned_by"] = assignedBy || request.auth.uid;
  }
  if (role) {
    responseObj["role"] = role; // assign role to teachers
    const latestEmploymentStatus = ["Inactive", "Retired"].includes(role) ? role : "Active";
    await updateSupabaseRow('teachers', { 'user_id': `eq.${responseObj["user_id"]}` }, { 'employment_status': latestEmploymentStatus });
  }
  logObj(responseObj);
  logObj(questionAnswers);

  // Add / update the teacher response row first
  /*
  if (teacherResponse && teacherResponse.id) {
    await updateSupabaseRow('teacher_responses', { id: `eq.${teacherResponse.id}` }, responseObj);
    responseObj = { ...teacherResponse, ...responseObj };
  } else {
    responseObj.id = `r${uniqueId()}`;
    await upsertSupabaseRows('teacher_responses', [responseObj]);
  }
  */
  responseObj.id = teacherResponse.id || `r${uniqueId()}`;
  await upsertSupabaseRows('teacher_responses', [responseObj]);
  responseObj = { ...teacherResponse, ...responseObj };

  // Save related question answers as well
  const questionAnswerRows = (questionAnswers || []).map(qa => ({
    'teacher_response_id': responseObj.id,
    'question_id': qa.questionId,
    'question_title': qa.questionTitle,
    'answer': qa.answer,
    'option_id': qa.optionId || "",
    'service_id': serviceId || "",
    'event_name': eventName,
    'user_id': userId,
    'updated_at': new Date(),
  }));
  if (questionAnswerRows.length > 0) {
    await upsertSupabaseRows("teacher_response_answers", questionAnswerRows);
  }

  // Trigger Apps Script handler (process new / updated teacher responses)
  const rawRespObj = convertKeysToCamelCase({ ...responseObj, questionAnswers: questionAnswerRows });
  const newRawTeacherResp = {
    'id': `trr-${uniqueId()}`,
    'user_id': userId,
    'event_name': eventName,
    'data': JSON.stringify(rawRespObj),
    'service_id': serviceId,
    'assigned_by': assignedBy || request.auth.uid,
    'is_ack_msg_sent': false,
  };
  insertSupabaseRows("teacher_responses_raw", [newRawTeacherResp]);
  //callFrontAppSheetApi("Add", "teacher_responses_raw", null, [newRawTeacherResp]);
  return rawRespObj;
});

/**
 * Send WhatsApp Command (Registration QR Code)
 */
exports.sendRegQRCodeToSelfWhatsAppGrp = onCall({ region: TARGET_REGION }, async (request) => {
  const { url, userPhone, userWAGrpId, posterImgLink, videoLink, slideLink, } = request.data;

  return await callFrontAppSheetApi("Add", "GAS Commands", null, [{
    "func_name": "teacherSendRegQRCodeToGrp",
    "param1": url,
    "param2": userPhone,
    "param3": userWAGrpId,
    "extra_params": [posterImgLink || "", videoLink || "", slideLink || ""].join("|"),
  }]);
});