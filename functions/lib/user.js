const { onCall } = require("firebase-functions/v2/https");
const { callAppSheetApi, callFrontAppSheetApi, callSupabaseApi, updateSupabaseRow, upsertSupabaseRows, insertSupabaseRows, getSupabaseRows, } = require('../api');
const TARGET_REGION = 'asia-east2'; // Hong Kong
const { uniqueId, convertKeysToCamelCase, } = require('../helpers');

const firebase = require('firebase');
const admin = require('firebase-admin');
admin.initializeApp({
  credential: admin.credential.cert(require("../fdmt-atlas-firebase-adminsdk.json"))
});
firebase.initializeApp({
  apiKey: "AIzaSyA3G_VzlBcpREyUF_UrVBQh-YM14t8JPXM",
  authDomain: "fdmt-atlas.firebaseapp.com",
  projectId: "fdmt-atlas",
  storageBucket: "fdmt-atlas.appspot.com",
  messagingSenderId: "784147710543",
  appId: "1:784147710543:web:77623618e6ef876e5ea03a"
});

const moment = require('moment-timezone');
moment.tz.setDefault("Asia/Hong_Kong");

// ********: ******** (SMS phone) -> ******** (WhatsApp phone) James
//admin.auth().updateUser("a38vF2Xnb2ZlEZTZJ3KGaNa0fqL2", { phoneNumber: `+852********` })

// ********: ******** -> ********
//admin.auth().updateUser("StCGAwifVdWvPCSu2C6NnJhsykc2", { phoneNumber: `+852********` })

//********: ******** -> ********
//admin.auth().updateUser("6LHn4UfFXzbEu67Idpag8s7Ycaa2", { phoneNumber: `+***********` })

// Account to be shared within department (e.g. for jobEX scheduling)
const createNewClientMainAccount = async (clientName, email, whatsAppGrpId, whatsAppGrpLink, jobEXName, programId, clientId) => {
  let res = await admin.auth().createUser({ email, emailVerified: true, password: "FDMTAB2024" });
  console.log(res);
  const userId = res.uid;

  // Add user to DB
  const userObj = {
    "id": userId,
    "full_name": clientName,
    "email": email,
    "wa_group_id": whatsAppGrpId || null,
    "wa_group_link": whatsAppGrpLink || null,
    "user_in_wa_group": true,
    "roles": "university-client",
    "jobEX": jobEXName || null,
    "program_id": programId || null,
    "client_id": clientId || null
  };
  res = await upsertSupabaseRows("users", userObj);
  console.log(res);
  
  // Add to child table (client)
  const universityClientObj = {
    "user_id": userId,
    "employment_status": "Active",
    "institution": clientName,
  }
  res = await upsertSupabaseRows('university_clients', universityClientObj);
  console.log(res);

  console.log(`Login link: https://ab.fdmt.hk/login/${userId}`);
}
//createNewClientMainAccount("LU HIST", "<EMAIL>", "<EMAIL>", "", "HST jobEX", "308", "LU_HIST").then(res => { console.log(res) });
//createNewClientMainAccount("LU IMCSP", "<EMAIL>", "<EMAIL>", "", "IMCSP jobEX", "38", "LU_IMCSP").then(res => { console.log(res) });
//createNewClientMainAccount("PolyU LMS", "<EMAIL>", "<EMAIL>", "", "LMS jobEX", "161", "PolyU_LMS").then(res => { console.log(res) });
//createNewClientMainAccount("PolyU BRE", "<EMAIL>", "<EMAIL>", "", "", "", "PolyU_BRE").then(res => { console.log(res) });
//createNewClientMainAccount("PolyU BAEAL", "<EMAIL>", "<EMAIL>", "", "BAEAL jobEX", "174", "PolyU_ENGL").then(res => { console.log(res) });
//createNewClientMainAccount("HKBU BUS", "<EMAIL>", "<EMAIL>", "", "", "109", "c0e83a709").then(res => { console.log(res) });
//createNewClientMainAccount("PolyU SN", "<EMAIL>", "<EMAIL>", "", "", "158", "c45a8af14").then(res => { console.log(res) });
//createNewClientMainAccount("CUHK PH", "<EMAIL>", "<EMAIL>", "", "JCSPHPC jobEX", "216", "c955c4b06").then(res => { console.log(res) });
//createNewClientMainAccount("LU GIA", "<EMAIL>", "<EMAIL>", "", "GIA jobEX", "1125", "caeb5730e").then(res => { console.log(res) });
//createNewClientMainAccount("LU MIBF", "<EMAIL>", "", "", "MIBF jobEX", "34", "LU_MIBF").then(res => { console.log(res) });

// For Teacher's nomination (create AB accounts for new nominees)
const createFirebaseUsersFromPhones = async (phoneNumbers) => {
  const promises = [];
  for (const phone of phoneNumbers) {
    console.log(phone);
    promises.push(admin.auth().createUser({ phoneNumber: `+852${phone}` }));
  }
  const res = await Promise.allSettled(promises);
  const users = res.filter(r => r.status == 'fulfilled').map(r => r.value);
  console.log(JSON.stringify(users));
  /*
  // NOTE: This case will happen if teachers nominate students from other schools (by phone)
  for (const phone of phoneNumbers) {
    const u = users.find(u => u.phoneNumber == `+852${phone}`);
    if (u == null) { // already created previously
      users.push(await admin.auth().getUserByPhoneNumber(`+852${phone}`))
    }
  }
  */
  return users;
}
/*
const tmpCreateFirebaseAuthUsers = async (users) => {
  const promises = [];
  for (const user of users) {
    console.log(user.phone);
    promises.push(admin.auth().createUser({ uid: user.id, phoneNumber: `+852${user.phone}` }));
  }
  const res = await Promise.allSettled(promises);
  const firebaseUsers = res.filter(r => r.status == 'fulfilled').map(r => r.value);
  console.log(JSON.stringify(firebaseUsers));
  console.log(firebaseUsers.length);
  console.log(res.filter(r => r.status == 'rejected'));
  for (const user of users) {
    const u = firebaseUsers.find(u => u.phoneNumber == `+852${user.phone}`);
    if (u == null) { // already created previously
      console.log(user.phone);
    }
  }
  return firebaseUsers;
};
const tmpCreateUsers = async () => {
  const users = await callSupabaseApi('GET', 'users', {}, {
    'roles': 'eq.university-student',
  });
  users.sort((a,b) => a.row-b.row);
  tmpCreateFirebaseAuthUsers(users.slice(300));
}
*/

const userSelector = `*,user_browsed_professions(profession_id),user_liked_professions(profession_id)` +
                    `,video_users(video_id,videos(*))` +
                    `,user_browsed_segments(segment_id),user_liked_segments(segment_id)` +
                    `,user_browsed_sectors(sector_id),user_liked_sectors(sector_id)` +
                    `,user_messages(*),claims:user_claims(*),slp:user_slps(*)` +
                    `,formResponses:user_form_responses(*),sessionResponses:user_session_responses(*),serviceResponses:user_service_responses(*)` +
                    `,universityStudent:university_students(*),secondarySchoolStudent:secondary_school_students(*)` +
                    `,universityClient:university_clients(*)` +
                    `,teacher:teachers(*,classRoles:teacher_class_roles(*))` +
                    `,teacherResponses:teacher_responses!teacher_responses_user_id_fkey(*,questionAnswers:teacher_response_answers(*))` +
                    `,userConsentRecords:user_consent_records(*)` +
                    `,userDisciplines:user_disciplines(discipline_id,reaction,action,reason,created_at,app_state,order),userProfessions:user_professions(profession_id,reaction,reason,action,created_at,app_state,order)` +
                    `,userPrograms:user_programs(program_id,reaction,reason,created_at,app_state,order,plan_id)` +
                    `,userElectives:user_electives(elective_id,program_id,reaction,reason,created_at,app_state,order)` +
                    `,userSubjectGrades:user_subject_grades(*)` +
                    `,userSegments:user_segments(segment_id,profession_id,reaction,reason,created_at,app_state,order)` +
                    `,clClaims:user_cl_claims(*),clExps:user_cl_exps(*),userEmployerJobs:user_employer_jobs(employer_job_id,reaction,reason,created_at,order)` +
                    `,userUsers:user_users!user_users_user_id_fkey(*),votedByUsers:user_users!user_users_target_user_id_fkey(*)` +
                    `,createdTags:tags!tags_created_by_fkey(*),relatedTags:tag_users(tag_id)`;
const parseUserData = (res) => {
  for (const user of res) {
    // Teacher-defined tags
    user.tagIds = (user.relatedTags || []).map(obj => obj.tagId);

    // Videos (parent consent)
    user.relatedVideos = (user.videoUsers || []).map(obj => obj.videos);

    // Professions
    user.likedProfessionIds = (user.userLikedProfessions || []).map(obj => obj.professionId);
    user.browsedProfessionIds = (user.userBrowsedProfessions || []).map(obj => obj.professionId);

    // Sectors
    user.likedSectorIds = (user.userLikedSectors || []).map(obj => obj.sectorId);
    user.browsedSectorIds = (user.userBrowsedSectors || []).map(obj => obj.sectorId);

    // Segments
    user.likedSegmentIds = (user.userLikedSegments || []).map(obj => obj.sectorId);
    user.browsedSegmentIds = (user.userBrowsedSegments || []).map(obj => obj.sectorId);
  }
  return res;
}

/**
 * User API
 */
exports.createFirebaseAuthUser = onCall({ region: TARGET_REGION }, async (request) => {
  const { phone } = request.data;
  let user = null;
  try {
    user = await admin.auth().getUserByPhoneNumber(`+852${phone}`);
  } catch (e) {
    console.error(e);
  }
  if (!user) user = await admin.auth().createUser({ phoneNumber: `+852${phone}` }); // create new user
  const token = await admin.auth().createCustomToken(user.uid);
  return { user, token };
});

exports.getNewUserWAGroup = onCall({ region: TARGET_REGION }, async (request) => {
  const groupRow = (await callSupabaseApi('GET', 'user_wa_grps', {}, { row: `eq.${request.data.userRowNumber}` }))[0];
  updateSupabaseRow('users', { row: `eq.${request.data.userRowNumber}` }, {
    "wa_group_id": groupRow.id,
    "wa_group_link": groupRow.inviteLink,
  });
  return groupRow;
});

exports.updateUser = onCall({ region: TARGET_REGION }, async (request) => {
  const { fullName, preferredName, chineseName, "class": studentClass, studentNumber, email, phone,
          schoolId, studyingElectives, yearDSE, rankInForm, absIntakeId, oldUser,
          heardOfProfessionIds, notHeardOfProfessionIds,
          userInWaGroup, isPhoneVerified, programId, clientId, } = request.data;
  const updatingObj = { 'last_updated_by': request.auth.uid, 'updated_at': new Date().toISOString() };
  const targetUserId = oldUser?.id || request.auth.uid;

  // Personal Info
  if (fullName) updatingObj["full_name"] = fullName;
  if (preferredName) updatingObj["preferred_name"] = preferredName;
  if (chineseName) updatingObj["chinese_name"] = chineseName;
  if (email) {
    /*
    if (email !== oldUser.email) { // email updated
      admin.auth().updateUser(request.auth.uid, { email });
    }
    */
    updatingObj["email"] = email;
  }
  if (phone) {
    try {
      await admin.auth().updateUser(request.auth.uid, { phoneNumber: `+852${phone}` });
      updatingObj["phone"] = phone;
    } catch (e) {
      console.error(e);
    }
  }
  if (isPhoneVerified !== undefined) updatingObj["is_phone_verified"] = isPhoneVerified;

  // Studying Info
  if (absIntakeId !== undefined) updatingObj["abs_intake_id"] = absIntakeId;
  if (schoolId) updatingObj["school_id"] = schoolId;
  if (studentClass) updatingObj["class"] = studentClass;
  if (studentNumber) updatingObj["student_number"] = studentNumber;
  if (studyingElectives) {
    updatingObj["studying_electives"] = Array.isArray(studyingElectives) ? studyingElectives.join(" , ") : (studyingElectives || "");
  }
  if (yearDSE) updatingObj["year_DSE"] = yearDSE;
  if (rankInForm) updatingObj["rank_in_form"] = rankInForm;

  // Profession Survey
  if (heardOfProfessionIds) updatingObj["heard_of_profession_ids"] = heardOfProfessionIds.join(" , ");
  if (notHeardOfProfessionIds) updatingObj["not_heard_of_profession_ids"] = notHeardOfProfessionIds.join(" , ");

  // WhatsApp
  if (userInWaGroup !== undefined) updatingObj["user_in_wa_group"] = userInWaGroup;

  // University Student / jobEX
  if (programId) updatingObj["program_id"] = programId;
  if (clientId) updatingObj["client_id"] = clientId;

  //return await callAppSheetApi("Edit", "user", null, [{ "id": targetUserId, ...updatingObj }]);
  return await callSupabaseApi('PATCH', 'users', {}, { id: `eq.${targetUserId}` }, updatingObj);
});

exports.createNewUser = onCall({ region: TARGET_REGION, concurrency: 500 }, async (request) => {
  const { fullName, preferredName, chineseName, gender, phone, schoolEmail, email, uid, devicePlatforms,
          schoolId, roleInSchool, rolesInClasses, "class": studentClass, studentNumber, studyingCurriculum,
          studyingElectives, yearDSE, rankInForm,
          userEventResponses, interestedDisciplineIds, interestedDisciplineNames,
          absIntakeId, roles, isPhoneVerified, existingFirebaseUser,

          // jobEX
          intakeId, programId, uniEmail,
          studentId, gradYear, yearOfStudy, studyingStream, studentStatus, howToKnow, myCredentialsSections,
          jobEXName, referrer,

          // University Staff
          institution, position,

          // UCircler
          group, q1Ans, q2Ans, q3Ans, isPrefect, referrerRole, referrerName, referrerPhone,

          prefilledServiceId, prefilledEventId, } = request.data;

  // First applied events
  const responseObjs = [], appliedEventIds = [];
  for (const evResp of (userEventResponses || [])) {
    responseObjs.push({
      "id": evResp.id,
      "session_id": evResp.sessionId,
      "user_id": request.auth.uid,
      "event_name": evResp.eventName,
      "event_date_time": evResp.eventDateTime,
      "phone": phone,
      "response": 'Yes',
      "confirmed": 'Yes',
      "user_school_id": schoolId || null,
    });
    appliedEventIds.push(evResp.sessionId);
  }

  // Add user to DB
  let userObj = {
    "id": uid,
    "full_name": fullName || "",
    "preferred_name": preferredName || "",
    "chinese_name": chineseName || "",
    "email": email || schoolEmail || "",
    "phone": phone,
    "gender": gender || "",
    "school_id": schoolId || "",
    "device_platforms": devicePlatforms || "",
    "roles": roles || "",
  };
  if (isPhoneVerified != undefined) userObj["is_phone_verified"] = isPhoneVerified;

  // Secondary Student
  let secondaryStudentObj;
  if (roles == "secondary-school-student") {
    userObj = {
      ...userObj,
      "class": studentClass,
      "student_number": studentNumber,
      "studying_curriculum": studyingCurriculum || "",
      "studying_electives": Array.isArray(studyingElectives) ? studyingElectives.join(" , ") : (studyingElectives || ""),
      "year_DSE": yearDSE,
      "rank_in_form": rankInForm,
      "first_applied_event_ids": appliedEventIds.join(" , ") || prefilledEventId || "",
      "first_selected_discipline_names": interestedDisciplineNames || "",
      "step2_discipline_ids": (interestedDisciplineIds || []).join(" , "),
      "step2_discipline_names": interestedDisciplineNames || "",
    }
    // for Supabase
    if (absIntakeId != undefined) userObj["abs_intake_id"] = absIntakeId;

    // New: UCircle students
    if (group == 'ucircle') {
      secondaryStudentObj = {
        "user_id": uid,
        //"group": group,
        "group": "ucircle (to be interviewed)",
        "q1_ans": q1Ans,
        "q2_ans": q2Ans,
        "q3_ans": q3Ans,
        "is_prefect": isPrefect,
        "referrer_phone": referrerPhone,
        "referrer_role": referrerRole,
        "referrer_name": referrerName,
        "how_to_know": howToKnow,
      }

      // Trigger UCircle application handler here
      callFrontAppSheetApi("Add", "UCircle Applications", null, [{
        'school': schoolId,
        'student_name': fullName,
        "how_to_know": howToKnow,
        ...secondaryStudentObj,
      }]);
    } else {
      secondaryStudentObj = {
        "user_id": uid, // default child row
      }
    }
  }

  // Teacher
  let teacherObj, newClassRoleRows = [];
  if (roles == "teacher") {
    teacherObj = {
      "user_id": uid,
      "school_roles": roleInSchool.join(" , "),
      "employment_status": "Active",
      "prefilled_service_id": prefilledServiceId || "",
    }
    if (rolesInClasses && rolesInClasses.length > 0) {
      newClassRoleRows = rolesInClasses.map(ric => ({
        "id": ric.id,
        "user_id": uid,
        "role": ric.role,
        "classes": ric.classes,
        "remarks": ric.remarks,
      }));
    }
  }

  // University Student
  let universityStudentObj; // no need for now
  if (roles == "university-student") {
    userObj = {
      ...userObj,
      "program_id": programId,
      "uni_email": uniEmail,
      "nationality": studentStatus,
      "year_of_study": yearOfStudy,
      "studying_stream": studyingStream || "",
      "expected_grad_year": gradYear,
      "student_id": studentId,
      "How do you know this program?": howToKnow,
      "curr_intake_id": intakeId,
      "referrer": referrer,
      "jobEX": jobEXName,
    }

    // pre-fill user sections
    /*
    callAppSheetApi("Add", "user_sections", null, myCredentialsSections.map(section => {
      return {
        "user_id": uid,
        "title": section,
      }
    }));
    */
    universityStudentObj = {
      "user_id": uid,
    }
  }

  // University Staff / Clients
  let universityClientObj;
  if (roles == "university-client") {
    userObj = {
      ...userObj,
      "uni_email": uniEmail,
      "client_id": "",
    }
    universityClientObj = {
      "user_id": uid,
      "employment_status": "Active",
      "institution": institution, // manually typed by user
      "position": position, // manually typed by user (job title)
    }
  }
  console.log(JSON.stringify(userObj));

  // Check if the user already exists in DB (in case double registration)
  if (existingFirebaseUser) {
    const res = await callSupabaseApi('GET', 'users', {}, {
      'id': `eq.${uid}`,
      'select': `*,formResponses:user_form_responses(*),sessionResponses:user_session_responses(*)`,
    });
    if (res && res.length > 0) {
      const existingSBUser = res[0];

      // Update with latest info if exists
      const updatedUser = await updateSupabaseRow("users", { id: `eq.${uid}` }, userObj);
      if (teacherObj) {
        await upsertSupabaseRows('teachers', teacherObj);
        if (newClassRoleRows.length > 0) {
          upsertSupabaseRows('teacher_class_roles', newClassRoleRows); // Teacher's roles in classes
          teacherObj.classRoles = newClassRoleRows;
        }
        if (updatedUser[0]) updatedUser[0].teacher = convertKeysToCamelCase(teacherObj);
      }
      if (secondaryStudentObj) {
        await upsertSupabaseRows('secondary_school_students', secondaryStudentObj);
        if (updatedUser[0]) updatedUser[0].secondarySchoolStudent = convertKeysToCamelCase(secondaryStudentObj);
      }
      if (universityClientObj) {
        await upsertSupabaseRows('university_clients', universityClientObj);
        if (updatedUser[0]) updatedUser[0].universityClient = convertKeysToCamelCase(universityClientObj);
      }

      // Check existing Session Responses & update
      for (let i = responseObjs.length-1; i >= 0; i--) {
        const obj = responseObjs[i];
        const existingResp = existingSBUser.sessionResponses.find(resp => resp.sessionId == obj.session_id);
        if (existingResp) {
          callFrontAppSheetApi("Edit", "Session User Responses", null, [{ ...obj, id: existingResp.id }]);
          responseObjs.splice(i, 1);
        }
      }
      if (responseObjs.length > 0) callFrontAppSheetApi("Add", "Session User Responses", null, responseObjs);
      return updatedUser;
    }
  }

  // Add user to DB
  const newUser = await insertSupabaseRows("users", userObj);

  // Add to teacher child table as well
  if (teacherObj) {
    await upsertSupabaseRows('teachers', teacherObj);
    if (newClassRoleRows.length > 0) {
      upsertSupabaseRows('teacher_class_roles', newClassRoleRows); // Teacher's roles in classes
      teacherObj.classRoles = newClassRoleRows;
    }
    if (newUser[0]) newUser[0].teacher = convertKeysToCamelCase(teacherObj);
  }

  // Add to secondary student child table
  if (secondaryStudentObj) {
    await upsertSupabaseRows('secondary_school_students', secondaryStudentObj);
    if (newUser[0]) newUser[0].secondarySchoolStudent = convertKeysToCamelCase(secondaryStudentObj);
  }

  // Add to university client child table
  if (universityClientObj) {
    await upsertSupabaseRows('university_clients', universityClientObj);
    if (newUser[0]) newUser[0].universityClient = convertKeysToCamelCase(universityClientObj);
  }

  // Add to event responses for first selected events (after creating users)
  if (responseObjs.length > 0) {
    callFrontAppSheetApi("Add", "Session User Responses", null, responseObjs);
  }

  // Return new user object (with row)
  return newUser;
});

exports.checkPhoneExists = onCall({ region: TARGET_REGION }, async (request) => {
  const { phone, userId, forceReturnToken } = request.data;
  if (!phone && !userId) return false;
  try {
    if (!phone) {
      if (forceReturnToken) {
        const [user, userInDB] = await Promise.all([
          admin.auth().getUser(userId),
          getSupabaseRows('users', { 'id': `eq.${userId}` })
        ]);
        if (userInDB.length == 0 || userId != user.uid) return false;
        const token = await admin.auth().createCustomToken(userId);
        return {'s': 200, 't': token};
      }
    } else {
      let user;
      try {
        const [userInFirebase, userInDB] = await Promise.all([
          admin.auth().getUserByPhoneNumber(`+852${phone}`),
          getSupabaseRows('users', { 'phone': `eq.${phone}` }),
        ]);
        if (userInDB.length == 0 || !userInFirebase.uid) return false;
        user = userInFirebase;
      } catch (e) {
        const userInDB = await getSupabaseRows('users', { 'wa_phone_number': `eq.${phone}` }); // get user by WhatsApp number (e.g. 62329561)
        if (userInDB.length == 0) return false;
        user = await admin.auth().getUser(userInDB[0].id);
      }
      if (forceReturnToken || userId) {
        // log in with user ID (prefilled link)
        if (!forceReturnToken && userId != user.uid) return false;
        const token = await admin.auth().createCustomToken(user.uid);
        return {'s': 200, 't': token};
      }
      return user;
    }
  } catch (e) {
    return false;
  }
});

exports.signInWithPhoneAndPassword = onCall({ region: TARGET_REGION }, async (request) => {
  const { phone, password } = request.data;
  if (phone === null) return {'s': 400, 'm': 'missingPhone'};
  try {
    const user = await admin.auth().getUserByPhoneNumber(`+852${phone}`);
    await firebase.auth().signInWithEmailAndPassword(user.email, password);
    const token = await admin.auth().createCustomToken(user.uid);
    return {'s': 200, 't': token};
  } catch (e) {
    return {'s': 400, 'm': 'wrongPassword'};
  }
});
exports.getUserById = onCall({ region: TARGET_REGION }, async (request) => {
  const res = await callSupabaseApi('GET', 'users', {}, {
    'id': `eq.${request.data.userId}`,
    'select': userSelector,
    'teacher_responses.order': 'updated_at.desc',
  });
  return parseUserData(res);
});
exports.getUserByPhone = onCall({ region: TARGET_REGION }, async (request) => {
  const res = await callSupabaseApi('GET', 'users', {}, {
    'phone': `eq.${request.data.phone}`,
    'select': `*,secondarySchoolStudent:secondary_school_students(*)`,
  });
  return res[0];
});

exports.getLoggedInUser = onCall({ region: TARGET_REGION, concurrency: 500, }, async (request) => {
  const res = await callSupabaseApi('GET', 'users', {}, {
    'id': `eq.${request.auth.uid}`,
    'select': userSelector,
    'teacher_responses.order': 'updated_at.desc',
  });
  if (res.length > 0) {
    const { row: rowNum, waGroupLink, roles, } = res[0];
    try {
      const updatingObj = {
        "last_logged_in_at": moment().format('YYYY/M/D HH:mm:ss'),
        "login_count": (res[0].loginCount || 0) + 1,
      };
      if (!['university-client'].includes(roles) && !waGroupLink) {
        // get user WA group
        const { id: groupId, inviteLink } = (await callSupabaseApi('GET', 'user_wa_grps', {}, { row: `eq.${rowNum}` }))[0];
        updatingObj["wa_group_id"] = groupId;
        updatingObj["wa_group_link"] = inviteLink;
        res[0].waGroupId = groupId;
        res[0].waGroupLink = inviteLink;
      }
      updateSupabaseRow('users', { row: `eq.${rowNum}` }, updatingObj);
    } catch (e) {
      console.log(e);
    }
  }
  return parseUserData(res);
});
exports.updateLoggedInUserProfilePic = onCall({ region: TARGET_REGION }, async (request) => {
  const { imageLink } = request.data;
  //return await callAppSheetApi("Edit", "user", null, [{ "id": request.auth.uid, "profile_pic": imageLink }]);
  return await updateSupabaseRow('users', { id: `eq.${request.auth.uid}` }, { 'profile_pic': imageLink });
});
exports.updateUserDarkThemeSetting = onCall({ region: TARGET_REGION }, async (request) => {
  const { enabledDarkTheme } = request.data;
  return await updateSupabaseRow('users', { id: `eq.${request.auth.uid}` }, { 'dark_theme': enabledDarkTheme });
});
exports.updateUserAppLanguage = onCall({ region: TARGET_REGION }, async (request) => {
  const { locale } = request.data;
  return await updateSupabaseRow('users', { id: `eq.${request.auth.uid}` }, { 'preferred_lang': locale });
});


/**
 * User Browsed Professions
 */
exports.addUserBrowsedProfession = onCall({ region: TARGET_REGION }, async (request) => {
  return await callAppSheetApi("Add", "user_browsed_professions", null, [{ 'user_id': request.auth.uid, 'profession_id': request.data.professionId }]);
});

/**
 * User Liked Profession
 */
 exports.addUserLikedProfession = onCall({ region: TARGET_REGION }, async (request) => {
  return await callAppSheetApi("Add", "user_liked_professions", null, [{ 'user_id': request.auth.uid, 'profession_id': request.data.professionId }]);
});
exports.removeUserLikedProfession = onCall({ region: TARGET_REGION }, async (request) => {
  return await callAppSheetApi("Delete", "user_liked_professions", null, [{ 'user_id': request.auth.uid, 'profession_id': request.data.professionId }]);
});

/**
 * User Browsed Sectors / Segments
 */
exports.addUserBrowsedSector = onCall({ region: TARGET_REGION }, async (request) => {
  return await callAppSheetApi("Add", "user_browsed_sectors", null, [{ 'user_id': request.auth.uid, 'sector_id': request.data.sectorId }]);
});
exports.addUserBrowsedSegment = onCall({ region: TARGET_REGION }, async (request) => {
  return await callAppSheetApi("Add", "user_browsed_segments", null, [{ 'user_id': request.auth.uid, 'segment_id': request.data.segmentId }]);
});

/**
 * User Liked Sectors / Segments
 */
exports.addUserLikedSector = onCall({ region: TARGET_REGION }, async (request) => {
  return await callAppSheetApi("Add", "user_liked_sectors", null, [{ 'user_id': request.auth.uid, 'sector_id': request.data.sectorId }]);
});
exports.removeUserLikedSector = onCall({ region: TARGET_REGION }, async (request) => {
  return await callAppSheetApi("Delete", "user_liked_sectors", null, [{ 'user_id': request.auth.uid, 'sector_id': request.data.sectorId }]);
});
exports.addUserLikedSegment = onCall({ region: TARGET_REGION }, async (request) => {
  return await callAppSheetApi("Add", "user_liked_segments", null, [{ 'user_id': request.auth.uid, 'segment_id': request.data.segmentId }]);
});
exports.removeUserLikedSegment = onCall({ region: TARGET_REGION }, async (request) => {
  return await callAppSheetApi("Delete", "user_liked_segments", null, [{ 'user_id': request.auth.uid, 'segment_id': request.data.segmentId }]);
});


/**
 * Teacher
 */

// Nomination
exports.insertNominationRecord = onCall({ region: TARGET_REGION }, async (request) => {
  const { targetUsers, targetSessionIds, targetResponse, } = request.data;

  // Raw request for records
  const rawNominees = JSON.stringify(targetUsers);

  // Check & create Firebase & DB users for new students
  let newUsers = [];
  const newStudents = targetUsers.filter(u => u.isNewStudent);
  if (newStudents.length > 0) {
    const userRecords = await createFirebaseUsersFromPhones(newStudents.map(s => s.phone));
    for (let i = newStudents.length-1; i >= 0; i--) {
      const stud = newStudents[i];
      const relatedRecord = userRecords.find(rec => `+852${stud.phone}` == rec.phoneNumber);
      if (relatedRecord) {
        // replace with Firebase Auth UID
        stud.id = relatedRecord.uid;
      }
      else {
        // invalid nominee (maybe from other schools)
        newStudents.splice(i, 1);
        const idx = targetUsers.findIndex(u => u.phone == stud.phone);
        targetUsers.splice(idx, 1);
      }
    }

    // Add new students to DB
    if (newStudents.length > 0) {
      newUsers = await callSupabaseApi("POST", "users", { "Prefer": "return=representation" }, {}, newStudents.map(s => ({
        "id": s.id,
        "full_name": s.fullName || "",
        "phone": s.phone,
        "class": s.class,
        "school_id": s.schoolId,
        "is_phone_verified": false,
        'verification_result': "Yes", // as the student is nominated by teachers
        'verification_result_updated_at': new Date(),
        "nominated_by": request.auth.uid,
      })));
    }
  }

  // Insert nomination records (for further processing)
  callFrontAppSheetApi("Add", "user_nomination_records", null, [{
    'target_user_ids': targetUsers.map(u => u.id).join(" , "),
    'target_session_ids': targetSessionIds.join(" , "),
    'nominees': JSON.stringify(targetUsers),
    'raw': rawNominees,
    'nominated_by': request.auth.uid,
    'target_response': targetResponse,
  }]);

  // Return newly created users (if any)
  return newUsers;
});