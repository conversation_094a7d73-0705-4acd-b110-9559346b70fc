const { onCall } = require("firebase-functions/v2/https");
const sheetapi = require('../googleapi');
const TARGET_REGION = 'asia-east2'; // Hong Kong

const { callFrontAppSheetApi, callAppSheetApi, callSupabaseApi, upsertSupabaseRows, deleteSupabaseRows, insertSupabaseRows, } = require('../api');
const { uniqueId, convertKeysToCamelCase } = require('../helpers');

const moment = require('moment-timezone');
moment.tz.setDefault("Asia/Hong_Kong");

exports.getUsersByProgramId = onCall({ region: TARGET_REGION }, async (request) => {
  const { programId } = request.data;
  const res = await callSupabaseApi('GET', 'users', {}, {
    'program_id': `eq.${programId}`,
    'select': `*,user_browsed_professions(profession_id),user_liked_professions(profession_id)` +
              `,user_browsed_segments(segment_id),user_liked_segments(segment_id)` +
              `,user_browsed_sectors(sector_id),user_liked_sectors(sector_id)` +
              `,formResponses:user_form_responses(*),sessionResponses:user_session_responses(*)` +
              `,teacher:teachers(*),universityStudent:university_students(*),secondarySchoolStudent:secondary_school_students(*)`
  });
  return res;
});

/**
 * Employer (for guest lecture & job application)
 */
exports.getEmployers = onCall({ region: TARGET_REGION }, async (request) => {
  return await callSupabaseApi('GET', 'employers', {}, {
    'select': `*,jobs:employer_jobs(*)`,
  });
});
exports.upsertUserEmployerJobs = onCall({ region: TARGET_REGION }, async (request) => {
  const { userEmployerJobs, employerId, programId, yearOfStudy, } = request.data;
  const userId = request.auth.uid;

  // Insert job applications (to be shown in employer report)
  insertSupabaseRows('employer_job_applications', userEmployerJobs.filter(uj => uj.reaction == 'like').map(uj => ({
    'id': `eja${uniqueId()}`,
    'user_id': uj.userId || userId,
    'employer_job_id': uj.employerJobId,
    'employer_id': employerId,
    'position': uj.position,
    'position_url': uj.positionUrl,
    'post_date': uj.postDate,
    'program_id': programId,
    'year_of_study': yearOfStudy,
  })));

  // Insert user_employer_job (record preferences)
  const rows = userEmployerJobs.map(uj => ({
    'employer_job_id': uj.employerJobId,
    'user_id': uj.userId || userId,
    'reaction': uj.reaction,
    'reason': uj.reason || null,
    'order': uj.order || null,
    'created_at': uj.createdAt || new Date(),
    'updated_at': new Date(),
    'app_state': uj.appState || {},
  }));
  return await upsertSupabaseRows("user_employer_jobs", rows);
});

/**
 * CL (User Claims & Past Experiences)
 */
exports.upsertUserClaims = onCall({ region: TARGET_REGION }, async (request) => {
  const { userClaims, userExps } = request.data;
  const userId = request.auth.uid;
  upsertSupabaseRows("user_cl_records", { "id": `clr-${uniqueId()}`, "user_id": userId, "user_cl_claims": userClaims, "user_cl_exps": userExps });
  await deleteSupabaseRows("user_cl_claims", { user_id: `eq.${userId}` });
  upsertSupabaseRows("user_cl_claims", userClaims.map(uc => ({
    'id': uc.id,
    'user_id': userId,
    'text': uc.text,
    'exp_ids': uc.expIds || [],
    'exp_elaboration': uc.expElaboration || "",
    'created_at': uc.createdAt || new Date(),
    'updated_at': new Date(),
  })));
  await deleteSupabaseRows("user_cl_exps", { user_id: `eq.${userId}` });
  upsertSupabaseRows("user_cl_exps", userExps.map(ue => ({
    'id': ue.id,
    'user_id': userId,
    'text': ue.text,
    'created_at': ue.createdAt || new Date(),
    'updated_at': new Date(),
  })));
  return true;
});

/**
 * Career Plan
 */
exports.saveUserCareerPlan = onCall({ region: TARGET_REGION }, async (request) => {
  const cleanData = (arr) => {
    for (let i = 0; i < arr.length; i++) {
      const { id, name } = arr[i]
      arr[i] = { id, name };
    }
  }
  const { careerPlan } = request.data;
  delete careerPlan.filteredSegments;
  cleanData(careerPlan.orderedProfessions);
  cleanData(careerPlan.targetSegments);
  cleanData(careerPlan.targetEmployers);
  return await callAppSheetApi("Add", "career_plans", null, [{
    'user_id': request.auth.uid,
    'data': JSON.stringify(careerPlan),
    'ordered_professions': careerPlan.orderedProfessions.map(p => p.name).join(" , "),
    'target_segments': careerPlan.targetSegments.map(s => s.name).join(" , "),
    'target_employers': careerPlan.targetEmployers.map(e => e.name).join(" , "),
    'selected_profession_group': careerPlan.selectedProfessionGroup,
    'selected_profession_tab': careerPlan.selectedProfessionTab,
    'claims': [careerPlan.claim1, careerPlan.claim2, careerPlan.claim3].join(" , "),
  }]);
})

exports.getUserCareerPlan = onCall({ region: TARGET_REGION }, async (request) => {
  let res = await sheetapi.getCareerPlanByUserId(request.auth.uid);
  if (res) {
    delete res.rowNum;
    delete res.rowData;
    res = convertKeysToCamelCase(res);
  }
  return res;
});


/**
 * My Credentials
 */
exports.getUserCredentials = onCall({ region: TARGET_REGION }, async (request) => {
  const [userCredentials, userSections] = await Promise.all([
    callAppSheetApi("Find", "my_credentials", `ORDERBY( FILTER('my_credentials', [user_id] = ${request.auth.uid}), [order], TRUE )`),
    callAppSheetApi("Find", "user_sections", `FILTER('user_sections', [user_id] = ${request.auth.uid})`),
  ])
  return { userCredentials, userSections };
});

exports.addUserCredentials = onCall({ region: TARGET_REGION }, async (request) => {
  const newRows = request.data.credentials.map(cr => {
    cr.id = uniqueId();
    cr.userId = request.auth.uid;
    cr.userSectionId = cr.section.id;
    cr.section = cr.section.title;
    return {
      'id': cr.id,
      'user_id': cr.userId,
      'user_section_id': cr.section.id,
      'headline': cr.headline,
      'from_year_month': cr.fromYearMonth,
      'to_year_month': cr.toYearMonth,
      'bullet_point': cr.bulletPoint,
    }
  });
  callAppSheetApi("Add", "my_credentials", null, newRows);
  return request.data.credentials;
});
exports.upsertUserCredential = onCall({ region: TARGET_REGION }, async (request) => {
  const { id, section, headline, fromYearMonth,toYearMonth, bulletPoint } = request.data.credential;
  const credObj = {
    'user_id': request.auth.uid,
    'user_section_id': section.id,
    'headline': headline,
    'from_year_month': fromYearMonth,
    'to_year_month': toYearMonth,
    'bullet_point': bulletPoint,
  };
  if (id == null) return await callAppSheetApi("Add", "my_credentials", null, [credObj]);
  return await callAppSheetApi("Edit", "my_credentials", null, [{ id, ...credObj }]);
});

exports.deleteUserCredential = onCall({ region: TARGET_REGION }, async (request) => {
  return await callAppSheetApi("Delete", "my_credentials", null, [{ 'id': request.data.credentialId }]);
});

exports.updateUserCredentials = onCall({ region: TARGET_REGION }, async (request) => {
  return await callAppSheetApi("Edit", "my_credentials", null, request.data.credentials.map(cr => {
    return {
      'user_id': cr.userId,
      'id': cr.id,
      'section': cr.section,
      'headline': cr.headline,
      'from_year_month': cr.fromYearMonth,
      'to_year_month': cr.toYearMonth,
      'order': cr.order,
    }
  })); // mainly for updating the bullet point orders
});

exports.syncCredentialsToProfileDoc = onCall({ region: TARGET_REGION }, async (request) => {
  return await callFrontAppSheetApi("Add", "GAS Commands", null, [{
    "func_name": "syncLatestCV",
    "param1": request.auth.uid
  }]);
});

/**
 * User Credentials Sections
 */
exports.upsertUserSection = onCall({ region: TARGET_REGION }, async (request) => {
  const { id, title, order } = request.data.section;
  const obj = {
    'id': id,
    'user_id': request.auth.uid,
    'title': title,
    'order': order,
  };
  if (obj.id == null) {
    obj.id = uniqueId();
    callAppSheetApi("Add", "user_sections", null, [obj]);
  } else {
    callAppSheetApi("Edit", "user_sections", null, [obj]);
  }
  return { id: obj.id, userId: obj.user_id, title, order };
});

exports.updateUserSections = onCall({ region: TARGET_REGION }, async (request) => {
  return await callAppSheetApi("Edit", "user_sections", null, request.data.sections.map(s => {
    return {
      'user_id': s.userId,
      'id': s.id,
      'title': s.title,
      'order': s.order,
    }
  }));
});

exports.deleteUserSection = onCall({ region: TARGET_REGION }, async (request) => {
  return await callAppSheetApi("Delete", "user_sections", null, [{ 'id': request.data.sectionId }]);
});