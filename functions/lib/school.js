const { onCall } = require("firebase-functions/v2/https");
const { getSupabaseRows, updateSupabaseRow, upsertSupabaseRows, callAppSheetApi, callFrontAppSheetApi, insertSupabaseRows } = require('../api');
const TARGET_REGION = 'asia-east2'; // Hong Kong
const { uniqueId, convertKeysToCamelCase, } = require('../helpers');

/**
 * School API
 */

const schoolSelector = `*,userConsentRecords:user_consent_records(*)`;

exports.getSchoolById = onCall({ region: TARGET_REGION }, async (request) => {
  const { schoolId } = request.data;
  return await getSupabaseRows('schools', { 'id': `eq.${schoolId}`, 'select': schoolSelector, 'user_consent_records.target': 'eq.mou' });
});

exports.getSchools = onCall({ region: TARGET_REGION }, async (request) => {
  return await getSupabaseRows('schools', { 'select': schoolSelector, 'order': 'name_short.asc', 'user_consent_records.target': 'eq.mou' });
});

exports.updateSchool = onCall({ region: TARGET_REGION }, async (request) => {
  const { schoolId, data, } = request.data;
  const { achievejupasStatus, achievejupasT1Briefing, achievejupasT2Briefing, showPredictedScoresToStudents, remarks, } = data;
  const payload = {};
  if (achievejupasStatus !== undefined) payload['achievejupas_status'] = achievejupasStatus;
  if (achievejupasT1Briefing !== undefined) payload['achievejupas_t1_briefing'] = achievejupasT1Briefing;
  if (achievejupasT2Briefing !== undefined) payload['achievejupas_t2_briefing'] = achievejupasT2Briefing;
  if (showPredictedScoresToStudents !== undefined) payload['show_predicted_scores_to_students'] = showPredictedScoresToStudents;
  if (remarks !== undefined) payload['remarks'] = remarks;
  if (Object.keys(payload).length > 0) {
    payload['updated_at'] = new Date().toISOString();
    payload['updated_by'] = request.auth.uid;
    return await updateSupabaseRow('schools', { 'id': `eq.${schoolId}` }, payload);
  }
  return false;
});