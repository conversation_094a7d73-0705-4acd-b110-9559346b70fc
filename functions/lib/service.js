const { onCall } = require("firebase-functions/v2/https");
const { callAppSheetApi, callFrontAppSheetApi, callSupabaseApi, updateSupabaseRow, insertSupabaseRows, upsertSupabaseRows, } = require('../api');
const TARGET_REGION = 'asia-east2'; // Hong Kong
const { uniqueId, convertKeysToCamelCase, popObjVal, } = require('../helpers');

const moment = require('moment-timezone');
moment.tz.setDefault("Asia/Hong_Kong");

/**
 * Form Responses (mainly feedback forms)
 */
exports.upsertServiceResponse = onCall({ region: TARGET_REGION }, async (request) => {
  const { responseId, serviceId, response, phone, serviceName, fullName, schoolId, } = request.data;
  const userId = request.auth ? request.auth.uid : null;

  callFrontAppSheetApi('Add', 'Outbox', null, [{
    'Sent on': '',
    'Group name': '<EMAIL>', // Rm 10 HR
    'Message': `*${fullName}* (${phone}) from *${schoolId?.toUpperCase()}* has applied for *${serviceName}*`,
    'Project type': 'upsertServiceResponse',
  }]);
  
  await upsertSupabaseRows("user_service_responses", [{
    "id": responseId || `r${uniqueId()}`,
    "user_id": userId,
    "service_id": serviceId,
    "service_name": serviceName,
    "response": response,
    "phone": phone,
  }]);
});

/**
 * Work Event Responses
 */
exports.updateEventResponse = onCall({ region: TARGET_REGION, concurrency: 500 }, async (request) => {
  const { targetEvent: ev, response, rollCallCode, type, withdrawReason, schoolId, jobEXIntakeId, workshopTimePreference } = request.data;
  let { phone, userId } = request.data;
  if (userId && userId.toString().length == 8) {
    phone = userId;
    userId = '';
  }
  let responseObj = {
    "user_jobex_intake_id": jobEXIntakeId,
    "user_school_id": schoolId,
    "lesson_id": ev.anchorEventId,
    "session_id": ev.id,
    "user_id": (request.auth ? request.auth.uid : userId) || '',
    "event_name": ev.displayName,
    "event_date_time": ev.formattedDateTime,
    "phone": phone,
    "response": response,
    "is_ack_msg_sent": false, // for sending WhatsApp notifications
    "updated_at": new Date(),
  };
  if (withdrawReason) responseObj["withdraw_reason"] = withdrawReason; // mainly for response = No
  if (workshopTimePreference) responseObj['workshop_time_preference'] = workshopTimePreference; // for BBAWork / HealthcareWork
  if (rollCallCode) {
    responseObj = {
      ...responseObj,
      'attended': 'Yes',
      'roll_call_code': rollCallCode,
      'roll_call_time': moment().format('YYYY/MM/DD HH:mm:ss'),
    };
  } else {
    responseObj['confirmed'] = (["Yes", "Confirmed"].includes(response) ? "Yes" : "No");
  }
  if (ev.userResponse && ev.userResponse.id) {
    //callFrontAppSheetApi("Edit", "Session User Responses", null, [{ id: ev.userResponse.id, ...responseObj }]);
    updateSupabaseRow("user_session_responses", { id: `eq.${ev.userResponse.id}` }, responseObj);
    responseObj = { ...ev.userResponse, ...responseObj };
  } else {
    if (responseObj['user_id'] || responseObj['phone']) {
      responseObj.id = `r${uniqueId()}`;
      //callFrontAppSheetApi("Add", "Session User Responses", null, [responseObj]);
      insertSupabaseRows("user_session_responses", [responseObj]);
    }
  }
  return convertKeysToCamelCase(responseObj);
});