const { onCall } = require("firebase-functions/v2/https");
const { supabase, callSupabaseApi, updateSupabaseRow, upsertSupabaseRows, callAppSheetApi, callFrontAppSheetApi, insertSupabaseRows, getSupabaseRows } = require('../api');
const TARGET_REGION = 'asia-east2'; // Hong Kong
const { uniqueId, convertKeysToCamelCase, } = require('../helpers');
const jsonh = require("../api/jsonh");

/**
 * Admin API (for FDMT internal staff)
 */

const baseSelector = `id,full_name,preferred_name,chinese_name,phone,email,gender,roles,wa_group_id,wa_group_link,user_in_wa_group,profile_pic,school_id,class,student_number` +
                      `,studying_curriculum,studying_electives,year_DSE,verification_result,rank_in_form` +
                      `,relatedTags:tag_users(tag_id)`;

/**
 * Query students
 */
exports.queryUsers = onCall({ region: TARGET_REGION, memory: '1GiB' }, async (request) => {
  const { schoolIds, roles } = request.data;
  
  let selector = baseSelector;
  if (roles == 'teacher') {
    selector += `,teacher:teachers(*,classRoles:teacher_class_roles(*))` +
                `,teacherResponses:teacher_responses!teacher_responses_user_id_fkey(*,assignor:users!teacher_responses_assigned_by_fkey(id,full_name),questionAnswers:teacher_response_answers(question_id,question_title,answer,updated_at)))`;
  }
  else {
    selector += `,userPrograms:user_programs(program_id,reaction,reason,created_at,app_state,order,plan_id)` +
                `,userSubjectGrades:user_subject_grades(*)` +
                `,sessionResponses:user_session_responses(*)` +
                `,userConsentRecords:user_consent_records(created_at,target,signature_img)`;
  }
  let query = supabase.from('users');
  query = query.select(selector);
  query.eq('roles', roles || 'secondary-school-student');
  if (roles == 'secondary-school-student') {
    query.order('order', { ascending: true, referencedTable: 'user_programs' });
    query.eq('user_programs.reaction', 'like');
    query.or(`year_DSE.is.null,year_DSE.gte.${new Date().getFullYear()}`); // skip graduated students
  }
  if (schoolIds && schoolIds.length > 0) query.in('school_id', schoolIds);
  //query.eq('user_in_wa_group', true); // TBC: only include students in WA groups (for sending messages)
  query.limit(100000); // max rows (escape API request limit)

  const res = await query;
  const users = convertKeysToCamelCase(res.data);
  if (roles == 'secondary-school-student') {
    for (const user of users) {
      user.tagIds = (user.relatedTags || []).map(obj => obj.tagId);
    }
  }
  return jsonh.pack(users);
});

/**
 * Get details of specific user (student / teacher)
 */
exports.getUserDetails = onCall({ region: TARGET_REGION }, async (request) => {
  const { userId } = request.data;
  const res = await callSupabaseApi('GET', 'users', {}, {
    'id': `eq.${userId}`,
    'select': baseSelector +
              `,userDisciplines:user_disciplines(discipline_id,reaction,action,reason,created_at,app_state,order)` +
              `,userProfessions:user_professions(profession_id,reaction,reason,action,created_at,app_state,order)` +
              `,userPrograms:user_programs(program_id,reaction,reason,created_at,app_state,order,plan_id)` +
              `,userSubjectGrades:user_subject_grades(*)` +
              `,sessionResponses:user_session_responses(*)` +
              `,secondarySchoolStudent:secondary_school_students(*)` +
              `,teacher:teachers(*,classRoles:teacher_class_roles(*))` +
              `,teacherResponses:teacher_responses!teacher_responses_user_id_fkey(*,assignor:users!teacher_responses_assigned_by_fkey(id,full_name),questionAnswers:teacher_response_answers(question_id,question_title,answer,updated_at)))`,
    'user_disciplines.reaction': 'like',
    'user_professions.reaction': 'like',
    'user_programs.reaction': 'like',
    'user_programs.order': 'order.asc',
    'user_professions.order': 'order.asc',
    'user_disciplines.order': 'order.asc',
  });
  for (const user of res) {
    user.tagIds = (user.relatedTags || []).map(obj => obj.tagId);
  }
  return res[0];
});

/**
 * Trigger Sending WhatsApp to Specific Students
 */
exports.sendWhatsAppMsgToUsers = onCall({ region: TARGET_REGION }, async (request) => {
  const { users, msg, imgLink } = request.data;

  const outboxRows = users.map(user => ({
    'Sent on': 'TBC',
    'Group name': user.waGroupId,
    'Phone': `852${user.phone}`,
    'Message': user.msg || msg, // prefilled or generic
    'Project type': 'sendWhatsAppMsgToUsers',
    'Image link': imgLink,
  }));
  return await callFrontAppSheetApi('Add', 'Outbox', null, outboxRows);
});

/**
 * JUPAS Condition Sets
 */
exports.getJUPASConditionSets = onCall({ region: TARGET_REGION }, async (request) => {
  const userId = request.auth.uid; // Only return sets for the authenticated user
  return await getSupabaseRows('jupas_choice_condition_sets', { 'user_id': userId, 'order': 'name.asc' });
});

exports.addJUPASConditionSet = onCall({ region: TARGET_REGION }, async (request) => {
  const { name, conditions } = request.data;
  const userId = request.auth.uid; // Insert user_id on creation
  return await insertSupabaseRows('jupas_choice_condition_sets', { id: `s${uniqueId()}`, name, conditions, 'user_id': userId });
});

exports.updateJUPASConditionSet = onCall({ region: TARGET_REGION }, async (request) => {
  const { id, name, conditions } = request.data;
  return await updateSupabaseRow('jupas_choice_condition_sets', { 'id': id, }, { name, conditions });
});

exports.deleteJUPASConditionSet = onCall({ region: TARGET_REGION }, async (request) => {
  const { id } = request.data;
  return await deleteSupabaseRow('jupas_choice_condition_sets', { 'id': id });
});