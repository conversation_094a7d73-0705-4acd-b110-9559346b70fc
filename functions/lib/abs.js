const { onCall } = require("firebase-functions/v2/https");
const { callSupabaseApi, callAppSheetApi, upsertSupabaseRows, insertSupabaseRows, } = require('../api');
const sheetapi = require('../googleapi');
const TARGET_REGION = 'asia-east2'; // Hong Kong
const { uniqueId, parseSheetRows, encryptStr, decryptStr, } = require('../helpers');

const moment = require('moment-timezone');
moment.tz.setDefault("Asia/Hong_Kong");

const fd = (date) => (moment(date).format('YYYY/MM/DD HH:mm:ss'));

/**
 * [For Parent]
 */
exports.getEncryptedUserId = onCall({ region: TARGET_REGION }, async (request) => {
  return encryptStr(request.data.userId);
});
exports.getStudentByUserId = onCall({ region: TARGET_REGION }, async (request) => {
  const res = await callSupabase<PERSON><PERSON>('GET', 'users', {}, {
    'id': `eq.${decryptStr(request.data.userId)}`,
    'select': `*,user_browsed_professions(profession_id),user_liked_professions(profession_id)` +
              `,claims:user_claims(*),slp:user_slps(*)` +
              `,formResponses:user_form_responses(*),sessionResponses:user_session_responses(*)` +
              `,secondarySchoolStudent:secondary_school_students(*)` +
              `,userDisciplines:user_disciplines(discipline_id,reaction,action,reason,created_at,app_state,order),userProfessions:user_professions(profession_id,reaction,reason,action,created_at,app_state,order)` +
              `,userPrograms:user_programs(program_id,reaction,reason,created_at,app_state,order,plan_id)` +
              `,userElectives:user_electives(elective_id,program_id,reaction,reason,created_at,app_state,order)`
  });
  return res[0];
});

/**
 * [For Teacher] Add / Update / Delete Step 1 tabs / tags
 */
exports.insertStep1ChangeRecords = onCall({ region: TARGET_REGION }, async (request) => {
  return await callAppSheetApi("Add", "step1_change_records", null, request.data.records.map(r => ({
    "user_id": request.auth.uid,
    "option_id": r.optionId,
    "question_id": r.questionId,
    "type": r.type,
    "new_text": r.newText,
    "prev_text": r.prevText,
  })));
});

const getBaseUserReactionObj = (ur, request) => ({
  'user_id': ur.userId || request.auth.uid,
  'reaction': ur.reaction,
  'reason': ur.reason || null,
  'order': ur.order || null,
  'created_at': ur.createdAt,
  'updated_at': new Date(),
  'app_state': ur.appState || {},
});

/**
 * User Segment Reaction & Reasons
 */
exports.upsertUserSegments = onCall({ region: TARGET_REGION }, async (request) => {
  const rows = request.data.userSegments.map(us => ({
    ...getBaseUserReactionObj(us, request),
    'segment_id': us.segmentId,
    'profession_id': us.professionId,
  }));
  return await upsertSupabaseRows("user_segments", rows);
});

/**
 * User Profession Reaction & Reasons
 */
exports.upsertUserProfessions = onCall({ region: TARGET_REGION }, async (request) => {
  const userProfessionRows = request.data.userProfessions.map(up => ({
    ...getBaseUserReactionObj(up, request),
    'profession_id': up.professionId,
    'bot_explanation': up.botExplanation || null,
  }));
  return await upsertSupabaseRows("user_professions", userProfessionRows);
});

/**
 * User Discipline Reaction & Reasons
 */
exports.upsertUserDisciplines = onCall({ region: TARGET_REGION }, async (request) => {
  const cleanAppState = (appState) => {
    if (appState && appState.selectedOption) {
      const { id, name, nameChinese } = appState.selectedOption;
      if (id && name) appState.selectedOption = { id, name, nameChinese }; // profession option
    }
    return appState;
  }
  const userDisciplineRows = request.data.userDisciplines.map(ud => ({
    ...getBaseUserReactionObj(ud, request),
    'discipline_id': ud.disciplineId,
    'action': ud.action || null,
    'app_state': ud.appState ? cleanAppState(ud.appState) : {},
    'bot_explanation': ud.botExplanation || null,
  }));
  return await upsertSupabaseRows("user_disciplines", userDisciplineRows);
});

/**
 * User Program Reaction (for Mock JUPAS / F6 program selects)
 */
exports.upsertUserPrograms = onCall({ region: TARGET_REGION }, async (request) => {
  const { userPrograms } = request.data;
  insertSupabaseRows('user_program_logs', {
    'user_id': request.auth.uid,
    'selected_program_ids': userPrograms.filter(up => up.reaction == 'like').sort((a,b) => (a.order-b.order)).map(up => up.programId).join(" , "),
  });
  const userProgramRows = userPrograms.map(up => ({
    ...getBaseUserReactionObj(up, request),
    'program_id': up.programId,
    'bot_explanation': up.botExplanation || null,
  }));
  return await upsertSupabaseRows("user_programs", userProgramRows);
});

/**
 * User Elective Reaction & Reasons
 */
exports.upsertUserElectives = onCall({ region: TARGET_REGION }, async (request) => {
  const rows = request.data.userElectives.map(ue => ({
    ...getBaseUserReactionObj(ue, request),
    'elective_id': ue.electiveId,
    'program_id': ue.programId,
  }));
  return await upsertSupabaseRows("user_electives", rows);
});

/**
 * Profession Responses (Step 0)
 */
exports.upsertProfessionResponses = onCall({ region: TARGET_REGION }, async (request) => {
  return await callAppSheetApi("Add", "user_profession_responses", null, request.data.responses.map(r => ({
    "user_id": request.auth.uid,
    "profession_id": r.professionId,
    "response": r.response,
    "created_at": fd(r.createdAt),
  })));
});

/**
 * User Choices (Step 1 / 2 / 3)
 */
exports.saveUserChoices = onCall({ region: TARGET_REGION }, async (request) => {
  const { step1OptionIds, step1OrderedProfessionIds, step1ProfessionReason, step1ProfessionAction, step2DisciplineIds, step2DisciplineReason, step2DisciplineAction, step3ProgramIds, } = request.data;
  const responseObj = {};
  if (step1OptionIds != undefined) responseObj["step1_option_ids"] = step1OptionIds;
  if (step1OrderedProfessionIds != undefined) responseObj["step1_ordered_profession_ids"] = step1OrderedProfessionIds;
  if (step1ProfessionReason != undefined) responseObj["step1_profession_reason"] = step1ProfessionReason;
  if (step1ProfessionAction != undefined) responseObj["step1_profession_action"] = step1ProfessionAction;
  if (step2DisciplineIds != undefined) responseObj["step2_discipline_ids"] = step2DisciplineIds;
  if (step2DisciplineReason != undefined) responseObj["step2_discipline_reason"] = step2DisciplineReason;
  if (step2DisciplineAction != undefined) responseObj["step2_discipline_action"] = step2DisciplineAction;
  if (step3ProgramIds != undefined) responseObj["step3_program_ids"] = step3ProgramIds;

  callAppSheetApi("Add", "user_choice_records", null, [{ "user_id": request.auth.uid, ...responseObj }]);
  return await callAppSheetApi("Edit", "user", null, [{ "id": request.auth.uid, ...responseObj }]);
});

/**
 * Profession keyword (manual search)
 */
exports.insertProfessionSearchKeywordRecord = onCall({ region: TARGET_REGION }, async (request) => {
  return await callAppSheetApi("Add", "user_search_records", null, [{
    "user_id": request.auth.uid,
    "target": "profession",
    "keyword": request.data.keyword,
  }]);
});

/**
 * Analytics
 */
exports.insertPageAccessRecord = onCall({ region: TARGET_REGION }, async (request) => {
  const { pageKey, accessTime, leaveTime, uid } = request.data;
  const userId = request.auth ? request.auth.uid : (uid || "");
  return await sheetapi.insertPageAccessRecord(userId, pageKey, fd(accessTime), leaveTime ? fd(leaveTime) : "");
});