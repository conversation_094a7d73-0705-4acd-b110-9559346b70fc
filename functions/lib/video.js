const { onCall } = require("firebase-functions/v2/https");
const { getSupabaseRows, updateSupabaseRow, upsertSupabaseRows, callAppSheetApi, callFrontAppSheetApi, insertSupabaseRows } = require('../api');
const TARGET_REGION = 'asia-east2'; // Hong Kong
const { uniqueId, convertKeysToCamelCase, } = require('../helpers');

/**
 * Video API
 */

exports.getVideoById = onCall({ region: TARGET_REGION }, async (request) => {
  const { videoId } = request.data;
  return await getSupabaseRows('videos', { 'id': `eq.${videoId}` });
});