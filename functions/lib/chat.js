const { onCall } = require("firebase-functions/v2/https");
const TARGET_REGION = 'asia-east2'; // Hong Kong
const { callSupabaseApi, } = require('../api');

/**
 * Retrieve user chat history (with chatbot)
 */
exports.getUserChatbotPrompts = onCall({ region: TARGET_REGION }, async (request) => {
  const { type, professionId, } = request.data;
  const filters = {
    'user_id': `eq.${request.auth.uid}`,
    'order': 'created_at.asc',
    'select': `id,prompt,bot_response,type,created_at`,
  };
  if (type) filters['type'] = `eq.${type}`;
  if (professionId) filters['profession_id'] = `eq.${professionId}`;
  return await callSupabaseApi('GET', 'user_chatbot_prompts', {}, filters);
});

/**
 * Insert chat history
 */
exports.insertUserChatbotPrompts = onCall({ region: TARGET_REGION }, async (request) => {
  const { prompts } = request.data;
  return await callSupabaseApi('POST', 'user_chatbot_prompts', {}, {}, prompts.map(p => ({
    'id': p.id,
    'user_id': request.auth.uid,
    'prompt': p.content,
    'bot_response': p.botResponse,
    'type': p.type,
    'profession_id': p.professionId || null,
  })));
});