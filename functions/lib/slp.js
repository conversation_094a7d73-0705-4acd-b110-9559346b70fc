const { onCall } = require("firebase-functions/v2/https");
const { callAppSheetApi, callFrontAppSheetApi, upsertSupabaseRows, insertSupabaseRows, } = require('../api');
const TARGET_REGION = 'asia-east2'; // Hong Kong
const axios = require('axios').default;

/**
 * GPT request log
 */
exports.insertGPTRequestLog = onCall({ region: TARGET_REGION }, async (request) => {
  const { prompt, tagId, tagText, userElaboration, botUrl, overrideBotName, devicePlatforms, responseStatusText, userId, } = request.data;
  
  return await insertSupabaseRows("user_gpt_request_logs", {
    "user_id": request.auth ? request.auth.uid : (userId || null),
    "prompt": prompt,
    "tag_id": tagId,
    "tag_text": tagText,
    "user_elaboration": userElaboration,
    "bot_url": botUrl,
    "override_bot_name": overrideBotName,
    "device_platforms": devicePlatforms || "",
    "ip_address": request.rawRequest ? request.rawRequest.ip : null,
    "response_status_text": responseStatusText || "",
  });
})

/**
 * User Claims
 */
exports.upsertUserClaims = onCall({ region: TARGET_REGION }, async (request) => {
  const rows = request.data.userClaims.map(uc => ({
    "user_id": request.auth.uid,
    "claim_id": uc.claimId,
    "text": uc.text,
    "elaboration": uc.elaboration,
    "selected": uc.selected,
    "order": uc.order,
    "type": uc.type || null,
    "created_at": uc.createdAt || new Date().toISOString(),
    "updated_at": new Date().toISOString(),
  }));
  return await upsertSupabaseRows("user_claims", rows);
  //return await callAppSheetApi("Add", "user_claims", null, rows);
});

/**
 * User SLP
 */
exports.upsertUserSLP = onCall({ region: TARGET_REGION }, async (request) => {
  //return await callFrontAppSheetApi("Add", "user_slps", null, [{
  return await callFrontAppSheetApi("Edit", "user_slps", null, [{
    "user_id": request.auth.uid,
    "original": request.data.original,
    "gpt": request.data.gpt,
  }]);
});

/**
 * Enable User SLP Access
 */
exports.enableUserSLPAccess = onCall({ region: TARGET_REGION }, async (request) => {
  return await callFrontAppSheetApi(request.data.existSLPRecord ? "Edit" : "Add", "user_slps", null, [{
    "user_id": request.auth.uid,
    "access_type": "full",
  }]);
});

/**
 * Send WhatsApp Command
 */
exports.sendSLPWhatsAppRecord = onCall({ region: TARGET_REGION }, async (request) => {
  const { msg, selectedSLPType, original, gpt, userPhone, userWAGrpId, imgLink, } = request.data;

  if (msg) {
    // Directly send the WhatsApp msg
    return await callFrontAppSheetApi("Add", "GAS Commands", null, [{
      "func_name": "slpSendWhatsAppToStudents",
      "param1": msg,
      "param2": userPhone,
      "param3": userWAGrpId,
      "extra_params": [imgLink || ""].join("|"),
    }]);
  } else {
    // Send SLP draft
    //callFrontAppSheetApi("Add", "user_slps", null, [{
    callFrontAppSheetApi("Edit", "user_slps", null, [{
      "user_id": request.auth.uid,
      "original": original,
      "gpt": gpt,
    }]);

    // Send out the WhatsApp
    const msgContent = `@852${userPhone} Thanks for using our SLP tool. Please find below your SLP draft for your records and further revision:

  ${selectedSLPType == 'original' ? original : gpt}`;

    return await callFrontAppSheetApi("Add", "GAS Commands", null, [{
      "func_name": "slpSendWhatsAppToStudents",
      "param1": msgContent,
      "param2": userPhone,
      "param3": userWAGrpId,
    }]);
  }
});


/**
 * Call ChatGPT API (not in use)
 * Problem: cannot do streaming response
 */
exports.chatGPTPolish = onCall({ region: TARGET_REGION }, async (request) => {
  const OPENAI_API_URL = `https://api.openai.com/v1`;
  //const OPENAI_API_KEY = "***************************************************";
  const OPENAI_API_KEY = "***************************************************";

  const { text } = request.data;

  const options = {
    method: "POST",
    headers: { "Authorization": `Bearer ${OPENAI_API_KEY}`, "Content-Type": "application/json" },
    data: JSON.stringify({
      //"model": "gpt-3.5-turbo",
      "model": "gpt-3.5-turbo-0613",
      //"messages": [{"role": "user", "content": `Please act as a "IT consultant" and introduce your job to secondary school students in Hong Kong in an interesting way. Please describe in detail your day-to-day work and also the most attractive and challenging parts of your job respectively. In the end, please recommend them getting into this career. No need to include your name in the sharing. Show the sharing in both Yue Cantonese and English.`}]
      "messages": [{
        "role": "user",
        //"content": `Below portion of Student Learning Profile is written by a Hong Kong F.5 student for JUPAS application. Can you please polish it in a natural way and complete the text in both English and 繁體中文?\n\n\n"${text}"`
        //"content": `Below portion of Student Learning Profile is written by a Hong Kong F.5 student for JUPAS application. Can you please polish it in a natural way and return the text in both English and 繁體中文:\n\n\n"${text}"`
        "content": `Can you please polish below paragraphs in a natural way and return it in both English and 繁體中文:\n\n\n"${text}"`
      }],
      //"stream": true,
    })
  };
  const res = (await axios(`${OPENAI_API_URL}/chat/completions`, options)).data;

  if (res.choices && res.choices.length > 0) {
    const result = res.choices[0].message.content
      .replace("English:", "")
      .replace("Yue Cantonese:", "")
      .replace("English：", "")
      .replace("Yue Cantonese：", "")
      .replace("[In Yue Cantonese]", "")
      .replace("[In English]", "")
      .replace("Yue Cantonese sharing:", "")
      .replace("English sharing:", "")
      .trim();
    console.log(result);
    return result;
  }
  return null;
});