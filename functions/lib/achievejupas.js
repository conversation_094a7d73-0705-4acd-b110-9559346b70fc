const { onCall } = require("firebase-functions/v2/https");
const { upsertSupabaseRows, getSupabaseRows, supabase, insertSupabaseRows, callFrontAppSheetApi, } = require('../api');
const TARGET_REGION = 'asia-east2'; // Hong Kong
const { uniqueId, parseSheetRows, encryptStr, decryptStr, convertKeysToCamelCase, } = require('../helpers');

const moment = require('moment-timezone');
moment.tz.setDefault("Asia/Hong_Kong");

const fd = (date) => (moment(date).format('YYYY/MM/DD HH:mm:ss'));

/**
 * AchieveJUPAS MOU / Parent's Consent
 */
exports.insertUserConsent = onCall({ region: TARGET_REGION }, async (request) => {
  const { id, schoolId, fullName, phone, waGroupId, target, videoId, videoLink, roles, response,
          signatureDataUrl, sessionId, sessionInfo, userId, } = request.data;
  
  const uid = userId || (request.auth ? request.auth.uid : null);
  
  // Handle signature upload if provided
  let signatureUrl = '';
  if (signatureDataUrl) {
    try {
      // Convert base64 to buffer
      const base64Data = signatureDataUrl.replace(/^data:image\/\w+;base64,/, '');
      const buffer = Buffer.from(base64Data, 'base64');
      
      // Upload to Supabase storage
      const fileName = `signatures/${target}_${uid || phone}_${Date.now()}.jpg`;
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('user-consents')
        .upload(fileName, buffer, {
          contentType: 'image/jpeg',
          cacheControl: '3600',
          upsert: true
        });
      
      if (uploadError) throw uploadError;
      
      // Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from('user-consents')
        .getPublicUrl(fileName);
        
      signatureUrl = publicUrl;
    } catch (error) {
      console.error('Error uploading signature:', error);
      throw new Error('Failed to upload signature');
    }
  }

  const res = await insertSupabaseRows('user_consent_records', [{
    "id": id || `uc${uniqueId()}`,
    "user_id": uid,
    "school_id": schoolId,
    "full_name": fullName,
    "target": target,
    "signature_img": signatureUrl, // mainly for parent consent
    "response": response,
    "roles": roles,
    "video_id": videoId,
    "video_link": videoLink,
    'session_id': sessionId,
    'session_info': sessionInfo,
    'phone': phone,
  }]);

  // Send WhatsApp acknowledgement
  const addOutboxRow = async (waGroupId, phone, msg, type, imgLink) => {
    const targetWAGrpId = waGroupId || '<EMAIL>'; // default send to beacon
    const targetPhone = waGroupId ? `852${phone}` : '';
    return await callFrontAppSheetApi('Add', 'Outbox', null, [{
      'Sent on': '',
      'Group name': targetWAGrpId,
      'Phone': targetPhone,
      'Message': msg,
      'Project type': type,
      'Image link': imgLink || '',
    }]);
  }
  if (target == 'mou') { // MOU approval
    const ACHIEVEJUPAS_MOU_PDF_LINK = "https://docs.google.com/feeds/download/documents/export/Export?id=1Xmk67g9b2cpiaCwkwmVIGbayzvq9xug-dKdHVN5-jbg&exportFormat=pdf"
    const msgContent = `@852${phone} Thank you for accepting the MOU at ${fd(new Date())}`;
    await addOutboxRow(waGroupId, phone, msgContent, 'sendMOUAckMsgToUser', ACHIEVEJUPAS_MOU_PDF_LINK);
    //await callFrontAppSheetApi("Add", "GAS Commands", null, [{ "func_name": "sendMOUAckMsgToUser", "param1": msgContent, "param2": phone, "param3": waGroupId, }]);
  }
  else if (target == 'achievejupas_parent_consent') { // parent consent (AchieveJUPAS)
    const msg = `@852${phone} Your parent's consent on using AchieveJUPAS has been received at ${fd(new Date())}`;
    await addOutboxRow(waGroupId, phone, msg, 'achievejupas_parent_consent', signatureUrl);
  }
  else if (target == 'video') { // parent consent (Video)
    const msg = `@852${phone} Parent's consent of *${fullName}* on the video (${videoLink}) has been received at ${fd(new Date())}`;
    await addOutboxRow(waGroupId, phone, msg, 'video_parent_consent', signatureUrl);
  }
  else if (target == 'shooting') {
    const msg = `@852${phone} Your parent's consent on joining the video session *${sessionInfo}* has been received at ${fd(new Date())}`;
    await addOutboxRow(waGroupId, phone, msg, 'video_parent_consent', signatureUrl);
  }
  else if (target == 'bluebird-seed') {
    const msg = `@852${phone} Your parent's consent on joining Bluebird Seed has been received at ${fd(new Date())}`;
    await addOutboxRow(waGroupId, phone, msg, 'video_parent_consent', signatureUrl);
  }

  return res;
});

/**
 * Consultation
 */
exports.getJUPASConsultationRecords = onCall({ region: TARGET_REGION }, async (request) => {
  const { targetUserId, } = request.data;
  return await getSupabaseRows('consultation_records', { user_id: request.auth.uid, target_user_id: targetUserId, order: 'updated_at.desc' });
});
exports.upsertJUPASConsultationRecord = onCall({ region: TARGET_REGION }, async (request) => {
  const { id, createdAt, targetUserId, date, time, venue, remarks } = request.data;
  return await upsertSupabaseRows('consultation_records', [{
    "id": id || `cr${uniqueId()}`,
    "date": date || "",
    "time": time || "",
    "venue": venue || "",
    "remarks": remarks || "",
    "user_id": request.auth.uid,
    "target_user_id": targetUserId,
    "created_at": createdAt || new Date().toISOString(),
    "updated_at": new Date().toISOString(),
  }]);
});

// Remarks / Comments
exports.insertUserComment = onCall({ region: TARGET_REGION }, async (request) => {
  const { userId, comment } = request.data;
  return await upsertSupabaseRows('user_comments', [{
    "id": `uc${uniqueId()}`,
    "from_user_id": request.auth.uid,
    "to_user_id": userId,
    "comment": comment,
    "type": "achievejupas",
  }]);
});
exports.getUserComments = onCall({ region: TARGET_REGION }, async (request) => {
  const { type, } = request.data;
  return await getSupabaseRows('user_comments', {
    type,
    to_user_id: request.auth.uid,
    order: 'created_at.desc',
    select: `*,from_user:users!user_comments_from_user_id_fkey(full_name)`,
  });
});


/**
 * Overall Target / School Prediction subject grades for specific student
 */
exports.upsertUserSubjectGrades = onCall({ region: TARGET_REGION }, async (request) => {
  const rows = request.data.userSubjectGrades.map(usg => ({
    'user_id': usg.userId || request.auth.uid,
    'subject_id': usg.subjectId,
    'source': usg.source,
    'created_at': usg.createdAt || new Date(),
    'updated_at': new Date(),
    'grade': usg.grade,
    'created_by': usg.createdBy || request.auth.uid,
    'updated_by': request.auth.uid,
  }));
  const res = await supabase.from('user_subject_grades').upsert(rows).select();
  if (res.data && Array.isArray(res.data)) {
    for (let i = 0; i < res.data.length; i++) {
      res.data[i] = convertKeysToCamelCase(res.data[i]);
    }
  }
  return res.data;
  //return await upsertSupabaseRows('user_subject_grades', rows);
});

/**
 * Upsert tecaher-defined tags (for grouping students)
 */
exports.upsertTeacherDefinedTag = onCall({ region: TARGET_REGION }, async (request) => {
  const { tagId, tagName, userIds, } = request.data;
  const newTagObj = {
    'id': tagId,
    'name': tagName,
    'type': 'AchieveJUPAS',
    'created_by': request.auth.uid,
    'updated_by': request.auth.uid,
  };
  await supabase.from('tags').upsert([newTagObj]).select();

  // Not transactional but still works for most cases
  const updatedTagUsers = userIds.map(uid => ({
    'user_id': uid,
    'tag_id': tagId,
  }));
  await supabase.from('tag_users').delete().eq('tag_id', tagId);
  await supabase.from('tag_users').upsert(updatedTagUsers); // important to await it, or it will be omitted (no rows will be created)

  return true;
});

/**
 * Delete teacher-defined tag
 */
exports.deleteTeacherDefinedTag = onCall({ region: TARGET_REGION }, async (request) => {
  const { tagId, } = request.data;
  return await supabase.from('tags').delete().eq('id', tagId);
});

/**
 * Add student to teacher-defined tags
 */
exports.addStudentToTeacherDefinedTag = onCall({ region: TARGET_REGION }, async (request) => {
  const { tagId, tagName, userId, } = request.data;
  const newTagObj = {
    'id': tagId,
    'name': tagName,
    'type': 'AchieveJUPAS',
    'created_by': request.auth.uid,
    'updated_by': request.auth.uid,
  };
  await supabase.from('tags').upsert([newTagObj]).select();
  await supabase.from('tag_users').upsert([{
    'user_id': userId,
    'tag_id': tagId,
  }]); // important to await it, or it will be omitted (no rows will be created)

  return true;
});

/**
 * Remove student from teacher-defined tag
 */
exports.removeStudentFromTeacherDefinedTag = onCall({ region: TARGET_REGION }, async (request) => {
  const { tagId, userId, } = request.data;
  return await supabase.from('tag_users').delete().eq('tag_id', tagId).eq('user_id', userId);
});