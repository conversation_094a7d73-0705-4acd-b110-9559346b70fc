const { onCall } = require("firebase-functions/v2/https");
const { callFrontAppSheetApi, callSupabaseApi, insertSupabaseRows, updateSupabaseRow, upsertSupabaseRows, } = require('../api');
const { uniqueId } = require("../helpers");
const TARGET_REGION = 'asia-east2'; // Hong Kong

/**
 * Get data: Booking Items & available timeslots
 */
const getMeetingHrsCalId = (staffName) => {
  staffName = staffName?.toLowerCase();
  if (staffName == 'chloe') return '<EMAIL>'
  return '<EMAIL>';
}
exports.getBookingData = onCall({ region: TARGET_REGION }, async (request) => {
  const targetCalendarId = getMeetingHrsCalId(request.data.staffName);
  const slotFilters = { 'status': `eq.available`, 'start_time': `gt.${new Date().toISOString()}`, 'order': 'start_time.asc' };
  if (targetCalendarId) slotFilters['gcal_id'] = `eq.${targetCalendarId}`;

  const [bookingItems, bookingSlots, bookingRecordsWithTargetDates] = await Promise.all([
    callSupabaseApi("GET", "booking_items", {}, { 'order': 'title.asc' }),
    callSupabaseApi("GET", "booking_slots", {}, slotFilters), // only retrieve available slots
    callSupabaseApi("GET", "booking_records", {}, { 'target_date': `not.is.null`, 'select': 'booking_item_id,target_date' }),
  ]);
  const reservedDates = bookingRecordsWithTargetDates.map(br => br.targetDate);
  return { bookingItems, bookingSlots, reservedDates, };
});

/**
 * Create new booking records
 */
exports.createNewBooking = onCall({ region: TARGET_REGION }, async (request) => {
  const { booking, bookingItem, bookingSlot } = request.data;
  const { fullName, preferredName, email, phone, yearOfStudy, bookingItemId, bookingSlotId, userId, waGroupId,
          remarks, schoolId, schoolRoles, targetDate, targetStartTime1, targetEndTime1, targetStartTime2, targetEndTime2, } = booking;
  const { startTime, endTime, } = bookingSlot;
  const { title, anchorEventId, clientId, durationMinutes, meetingChannel, } = bookingItem;
  console.log(JSON.stringify(request.data));

  let bookingStatus = 'pending'; // default pending
  const payload = {
    'id': `br-${uniqueId()}`,
    'booking_slot_id': bookingSlotId,
    'booking_item_id': bookingItemId,
    'full_name': fullName,
    'email': email,
    'phone': phone,
    'year_of_study': yearOfStudy,
    'preferred_name': preferredName,
    'start_time': startTime,
    'end_time': endTime,
    'title': title,
    'anchor_event_id': anchorEventId,
    'client_id': clientId,
    'user_id': userId || (request.auth ? request.auth.uid : ""),
    'wa_group_id': waGroupId,
    'remarks': remarks,
    'school_id': schoolId,
    'school_roles': schoolRoles,
    'target_date': targetDate,
    'target_start_time1': targetStartTime1,
    'target_end_time1': targetEndTime1,
    'target_start_time2': targetStartTime2,
    'target_end_time2': targetEndTime2,
  }
  if (bookingSlotId && bookingSlotId.toString().startsWith("bsl")) {
    try {
      // Check overlapped timeslots
      const from = new Date(startTime).toISOString(), to = new Date(endTime).toISOString();
      const relatedSlots = await callSupabaseApi("GET", "booking_slots", {}, {
        'or': `(and(start_time.lte.${from},end_time.gt.${from}),and(start_time.lt.${to},end_time.gte.${to}))`
      });
      if (relatedSlots.some(s => s.status == 'reserved')) {
        bookingStatus = 'failed';
      } else {
        // Insert booking record
        await insertSupabaseRows('booking_records', payload);

        // All related slots are available, reserve all of them
        console.log(`in.(${relatedSlots.map(s => s.id).join(",")})`);
        updateSupabaseRow('booking_slots', { id: `in.(${relatedSlots.map(s => s.id).join(",")})` }, {
          'updated_at': new Date(),
          'status': 'reserved',
          'booking_record_id': payload.id,
        });
        //updateSupabaseRow('booking_slots', { 'id': `eq.${bookingSlotId}` }, { 'status': 'reserved' });

        bookingStatus = 'success';
      }
    } catch (e) {
      console.error(e);
      bookingStatus = 'failed'; // change to failed (timeslots not available / used)
    }
  } else {
    if (targetDate) {
      await insertSupabaseRows('booking_records', payload); // Insert booking record
    }
    bookingStatus = 'success';
  }
  callFrontAppSheetApi("Add", 'booking_raw_responses', {}, [{ 'data': JSON.stringify({ ...payload, duration: durationMinutes, meetingChannel }), 'status': bookingStatus }]);

  return bookingStatus;
});