{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"lint": "eslint .", "serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "20"}, "main": "index.js", "dependencies": {"@supabase/supabase-js": "^2.48.1", "axios": "^0.21.1", "firebase": "^8.5.0", "firebase-admin": "^11.11.0", "firebase-functions": "^4.4.1", "googleapis": "^92.0.0", "moment": "^2.29.1", "moment-timezone": "^0.5.32", "openai": "^3.3.0", "stripe": "^8.130.0"}, "devDependencies": {"eslint": "^7.0.0", "eslint-plugin-promise": "^5.1.0", "firebase-functions-test": "^0.2.0"}, "private": true}