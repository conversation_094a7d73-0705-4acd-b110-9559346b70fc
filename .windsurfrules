## Tech Stack
- Ionic
- Vue 3 Composition API
- Vuex
- Very loose TypeScript (not strict and mostly any)
- Ionic Components + Custom CSS
- Firebase Cloud Functions
- Supabase Realtime
- Supabase Postgres

## State Management
- Use Vuex store with caution (only if necessary)
- Mutate props directly whenever possible for performance consideration

## Import Ionic Components

For Ionic Components, after import, it should be included in "components" property in export

For example,

import { IonHeader, } from "@ionic/vue";

export default {
  name: "UserLikedListPage",
  components: {
    IonHeader,
  },
  ...
}

## Vue 3 Composition API best practices

const vue3CompositionApiBestPractices = [
  "Use setup() function for component logic",
  "Utilize ref and reactive for reactive state",
  "Implement computed properties with computed()",
  "Use watch and watchEffect for side effects",
  "Implement lifecycle hooks with onMounted, onUpdated, etc.",
];

## Code Style and Structure
- Write concise, simple TypeScript code with accurate examples
- Use functional and declarative programming patterns; avoid classes
- Prefer iteration and modularization over code duplication
- Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError)

## Folder structure

const folderStructure = `
src/
  assets/
  components/
  composables/
  pages/
  router/
  services/
  store/
  theme/
  App.vue
  main.ts
  config.js
`;

## Additional instructions

const additionalInstructions = `
1. Use loose TypeScript
2. Implement proper props and emits definitions
3. Utilize Vue 3's Teleport component when needed
4. Implement proper error handling
5. Follow Vue 3 style guide and naming conventions
6. Use Vite for fast development and building
`;

## Format of return in setup()

Remember to return everything to be used in DOM (icons / variables / methods)

return {
    // icons
    ...,
    // variables
    ...,
    // methods
    ...,
}